﻿using System;
using System.Runtime.Serialization;
using Bcms.Common;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "ExchangeDAGReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ExchangeDAGReplication : Bcms.Common.Base.BaseEntity
    {
        //private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int GroupId { get; set; }

        [DataMember]
        public string DAGName { get; set; }

        [DataMember]
        public string IPAddress { get; set; }

        [DataMember]
        public string WitnessServer { get; set; }

        [DataMember]
        public string WitnessDirectory { get; set; }

        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get { return _replicationBase; }
        //    set { _replicationBase = value; }
        //}

        //[DataMember]
        //public IList<ExchangeDAGMonitoring> ExchangeDAGMonitoringList
        //{
        //    get
        //    {
        //        return _monitorings;
        //    }
        //    set
        //    {
        //        _monitorings = value;
        //    }
        //}

        #endregion Properties
    }
}