﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class DatabaseBackupInfo : BaseEntity
    {
        #region Properties

        [DataMember]
        public string BackupPath
        {
            get;
            set;
        }

        [DataMember]
        public string Time
        {
            get;
            set;
        }

        [DataMember]
        public string Server
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }
        [DataMember]
        public string MySqlBinDirectory
        {
            get;
            set;
        }


        #endregion

    }
}
