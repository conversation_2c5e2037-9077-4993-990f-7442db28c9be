﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MonitorService", Namespace = "http://www.BCMS.com/types")]
    public class MonitorService : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessFunctionId { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int Status { get; set; }

        [DataMember]
        public string ServicePath { get; set; }

        [DataMember]
        public int ServiceId { get; set; }


        #endregion

        #region Constructor

        public MonitorService()
            : base()
        {
        }

        #endregion
    }
}