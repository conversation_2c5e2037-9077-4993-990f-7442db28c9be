﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class HmcInfo : BaseEntity
    {
        #region Properties


        [DataMember]
        public int DSCLIServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PRConsoleIP
        {
            get;
            set;
        }

        [DataMember]
        public string PRUsername
        {
            get;
            set;
        }

        [DataMember]
        public string PRPassword
        {
            get;
            set;
        }
        [DataMember]
        public string DRConsoleIP
        {
            get;
            set;
        }
        [DataMember]
        public string DRUsername
        {
            get;
            set;
        }
        public string DRPassword
        {
            get;
            set;
        }

        #endregion
    }

}
