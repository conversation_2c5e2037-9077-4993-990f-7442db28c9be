﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "EmailAlert", Namespace = "http://www.Bcms.com/types")]
    public class EmailAlert
    {
        #region Properties

        [DataMember]
        public string Description
        {
            get;
            set;
        }

        [DataMember]
        public string InfraObjectName
        {
            get;
            set;
        }

        [DataMember]
        public DateTime DateAndTime
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public int EmailCount { get; set; }

        //[DataMember]
        //public int CreatorId { get; set; }

        //[DataMember]
        //public int UpdatorId { get; set; }

        #endregion Properties
    }
}
