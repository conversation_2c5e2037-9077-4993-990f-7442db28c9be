﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
     [Serializable]
    [DataContract(Name = "MAXDBReplmonitoring", Namespace = "http://www.BCMS.com/types")]
    public class MAXDBReplMonitoring : BaseEntity
    {
          #region Constructor

         public MAXDBReplMonitoring()
            : base()
        {
        }
        #endregion Constructor
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string LastGeneratedLogSeq
        {
            get;
            set;
        }

        [DataMember]
        public string LastappliedLogSeq
        {
            get;
            set;
        }
         
        [DataMember]
        public string LastTransLogSeq
        {
            get;
            set;
        }

        [DataMember]
        public string LastGeneratedLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string LastappliedLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogLocation
        {
            get;
            set;
        }
        [DataMember]
        public string DRLogLocation
        {
            get;
            set;
        }

        [DataMember]
        public string PRLastLogPage
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastLogPage
        {
            get;
            set;
        }

        [DataMember]
        public string datalag
        {
            get;
            set;
        }

        #endregion Properties
    }
}
