﻿using System;
using Bcms.ExceptionHandler;
using Jscape.Ssh;
using log4net;

namespace Bcms.Helper
{
    public static class SSHHelper
    {

        #region Variable

        private const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(SSHHelper));
       
        private delegate void ConnectionSSHDelegate(string hostName, string ipAddress, string userName, string password, string groupName, int groupId, string type, string jobName);

        #endregion

        public static void BeginConnect(string hostName, string ipAddress, string userName, string password, string groupName, int groupId, string type, string jobName)
        {
            var connection = new ConnectionSSHDelegate(Connect);

            connection.BeginInvoke(hostName, ipAddress, userName, password, groupName, groupId, type, jobName,null,null);
        }

        public static void Connect(string hostName, string ipAddress, string userName, string password, string groupName, int groupId, string type,string jobName)
        {
            if (ipAddress != string.Empty && userName != string.Empty && password != string.Empty)
            {
                var sshParams = new SshParameters(ipAddress, userName, password);

                var ssh = new Ssh(sshParams)
                              {
                                  LicenseKey = LicenseKey
                              };
                try
                {
                    ssh.Connect();

                    Logger.InfoFormat("{0} : {1}({2}) Connection Successfully established ", groupName, type, ipAddress);
                }
                catch (SshAuthenticationException authexc)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.AuthenticationFailed,
                                                          string.Format(
                                                              "Authentication Failed when connecting Server:{0} ({1})",
                                                              hostName, ipAddress), authexc);
                    ExceptionManager.Manage(bcmsException, jobName, groupId, groupName);
                }
                catch (SshTaskTimeoutException timeoutexe)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.TaskTimeOut,
                                                          string.Format("No Response From Server:{0} ({1})", hostName,
                                                                        ipAddress), timeoutexe);
                    ExceptionManager.Manage(bcmsException, jobName, groupId, groupName);
                }
                catch (SshException sshexc)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                                                          string.Format("Server {0} ({1}) is Not Reachable", hostName,
                                                                        ipAddress), sshexc);
                    ExceptionManager.Manage(bcmsException, jobName, groupId, groupName);
                }
                catch (Exception exc)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled,
                                                          string.Format(
                                                              "Error Occured while connecting Server:{0} ({1})",
                                                              hostName, ipAddress), exc);
                    ExceptionManager.Manage(bcmsException, jobName, groupId, groupName);
                }
                finally
                {
                    if (ssh.Connected)
                    {
                        ssh.Disconnect();
                    }
                }
            }
            else
            {
                Logger.ErrorFormat("{0} : Cannot Connect the server because of empty paramerter", groupName);
            }
        }

    }
}
