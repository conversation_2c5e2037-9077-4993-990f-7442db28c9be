﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Utility;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using BCMS.Common;

namespace Bcms.DataAccess
{
    public class SqlLogNativeDataAccess:BaseDataAccess
    {
        public static bool AddSqlNativeLogDetails(SqlLogNative sqllognative)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEMONITORLOGS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqllognative.GroupId);
                    Database.AddInParameter(cmd, Dbstring+"iPRServer", DbType.AnsiString, sqllognative.PRServer);
                    Database.AddInParameter(cmd, Dbstring+"iDRServer", DbType.AnsiString, sqllognative.DRServer);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseName", DbType.AnsiString, sqllognative.DatabaseName);
                    Database.AddInParameter(cmd, Dbstring+"iLast_GenLog", DbType.AnsiString, sqllognative.LastGenLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_ApplyLog", DbType.AnsiString, sqllognative.LastApplyLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_CopiedLog", DbType.AnsiString, sqllognative.LastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_last_backupLog", DbType.AnsiString, sqllognative.LSNlastbackupLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_last_restoredLog", DbType.AnsiString, sqllognative.LSNlastrestoredLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_Last_CopiedLog", DbType.AnsiString, sqllognative.LSNLastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Gen_Time", DbType.AnsiString, sqllognative.LastLogGenTime);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Appl_Time", DbType.AnsiString, sqllognative.LastLogApplTime);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Copy_Time", DbType.AnsiString, sqllognative.LastLogCopyTime);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag", DbType.AnsiString, sqllognative.DataLag);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlNativeLogDetails entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNativeLogStatusDetails(SqlLogNative sqllognative)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEMONITORSTATUS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqllognative.GroupId);
                    Database.AddInParameter(cmd, Dbstring+"iPRServer", DbType.AnsiString, sqllognative.PRServer);
                    Database.AddInParameter(cmd, Dbstring+"iDRServer", DbType.AnsiString, sqllognative.DRServer);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseName", DbType.AnsiString, sqllognative.DatabaseName);
                    Database.AddInParameter(cmd, Dbstring+"iLast_GenLog", DbType.AnsiString, sqllognative.LastGenLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_ApplyLog", DbType.AnsiString, sqllognative.LastApplyLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_CopiedLog", DbType.AnsiString, sqllognative.LastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_last_backupLog", DbType.AnsiString, sqllognative.LSNlastbackupLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_last_restoredLog", DbType.AnsiString, sqllognative.LSNlastrestoredLog);
                    Database.AddInParameter(cmd, Dbstring+"iLSN_Last_CopiedLog", DbType.AnsiString, sqllognative.LSNLastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Gen_Time", DbType.AnsiString, sqllognative.LastLogGenTime);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Appl_Time", DbType.AnsiString, sqllognative.LastLogApplTime);
                    Database.AddInParameter(cmd, Dbstring+"iLast_Log_Copy_Time", DbType.AnsiString, sqllognative.LastLogCopyTime);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag", DbType.AnsiString, sqllognative.DataLag);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting UpdateSqlNativeLogDetails entry ", exc);
            }
            return false;
        }

        public static string GetlogbyId(int groupId)
        {
            string logFileName = string.Empty;
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLNATIVE_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            logFileName = mySqlLogReader[0].ToString();
                        }
                    }
                }
                return logFileName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature GetlogbyId for group Id(" + groupId + ")", exc);

            }
        }

        public static bool SwitchPrimarySecondary(int groupId)
        {
            string logFileName = string.Empty;
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SwitchPrimary_secondary_Server"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature Switch Primary Secondary_Server for group Id(" + groupId + ")", exc);
            }
        }

        public static string GetfileNamebyId(int groupId)
        {
            string FileName = string.Empty;
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLNATIVE_GETBYID_FILENAME"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            FileName = mySqlLogReader[2].ToString();
                        }
                    }
                }
                return FileName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature GetfileNamebyId for group Id(" + groupId + ")", exc);

            }
        }

        public static bool CreateFileName(int groupId,string filename)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVE_CREATE_FILENAME"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
                    Database.AddInParameter(cmd, Dbstring+"iFileName", DbType.AnsiString, filename);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting CreateFileName entry ", exc);
            }
            return false;
        }

        public static bool UpdateFileName(string filename)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVE_UPDATE_FILENAME"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iFileName", DbType.AnsiString, filename);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting UpdateFileName entry ", exc);
            }
            return false;
        }


    }
}
