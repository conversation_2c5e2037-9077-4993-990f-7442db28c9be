﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseExchange", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseExchange : BaseEntity
    {
        #region Properties

        //DatabaseBase _databaseBase = new DatabaseBase();


        [DataMember]
        public int PRBaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }


        [DataMember]
        public string PRStorageGroupName
        {
            get;
            set;
        }

        [DataMember]
        public string PRMailBoxDBName
        {
            get;
            set;
        }

        [DataMember]
        public int DRBaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }


        [DataMember]
        public string DRStorageGroupName
        {
            get;
            set;
        }

        [DataMember]
        public string DRMailBoxDBName
        {
            get;
            set;
        }
        //[DataMember]
        //public DatabaseBase DatabaseBase
        //{
        //    get { return _databaseBase; }
        //    set { _databaseBase = value; }
        //}
        #endregion

        #region Constructor

        public DatabaseExchange()
            : base()
        {
        }

        #endregion
    }
}
