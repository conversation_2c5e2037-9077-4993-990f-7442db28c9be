﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using Bcms.DataAccess.Base;
using Bcms.Common;
using log4net;

namespace Bcms.DataAccess
{
    public class ExchangeDAGCompMonitorDataAccess : BaseDataAccess
    {

        private static readonly ILog Logger = LogManager.GetLogger(typeof(ExchangeDAGCompMonitorDataAccess));

        #region IExchangeDataAccess Members

        public static bool AddDAGComponantMonitor(BCMS.Common.ExchangeDAGComponantMonitor ComponantMonitor)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand(DbRoleName + "EXCDAG_COMPMON_CREATE"))
                {                    
                    Database.AddInParameter(dbCommand, Dbstring+"iSERVERID", DbType.Int32, ComponantMonitor.ServerId);
                    //Logger.InfoFormat("iSERVERID : " + ComponantMonitor.ServerId.ToString());

                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, ComponantMonitor.InfraobjectId);
                    //Logger.InfoFormat("iInfraobjectId : " + ComponantMonitor.InfraobjectId.ToString());

                    Database.AddInParameter(dbCommand, Dbstring+"iDATABASEID", DbType.Int32, ComponantMonitor.DataBaseId);
                    //Logger.InfoFormat("iDATABASEID : " + ComponantMonitor.DataBaseId.ToString());

                    Database.AddInParameter(dbCommand, Dbstring+"iPRIPADDRESS", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRIPAddress));
                    //Logger.InfoFormat("iPRIPADDRESS : " + ComponantMonitor.PRIPAddress);

                    Database.AddInParameter(dbCommand, Dbstring+"iDRIPADDRESS", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRIPAddress));
                    //Logger.InfoFormat("iDRIPADDRESS : " + ComponantMonitor.DRIPAddress);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRMAILBOXDATABASE", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRMailboxDatabase));
                    //Logger.InfoFormat("iPRMAILBOXDATABASE : " + ComponantMonitor.PRMailboxDatabase);

                    Database.AddInParameter(dbCommand, Dbstring+"iDRMAILBOXDATABASE", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRMailboxDatabase));
                    //Logger.InfoFormat("iDRMAILBOXDATABASE : " + ComponantMonitor.DRMailboxDatabase);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRMAILBOXDBSTATUS", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRMailboxDBStatus));
                   // Logger.InfoFormat("iPRMAILBOXDBSTATUS : " + ComponantMonitor.PRMailboxDBStatus);
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDRMAILBOXDBSTATUS", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRMailboxDBStatus));
                    //Logger.InfoFormat("iDRMAILBOXDBSTATUS : " + ComponantMonitor.DRMailboxDBStatus);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLASTGENLOG", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLastGenLog));
                    //Logger.InfoFormat("iPRLASTGENLOG : " + ComponantMonitor.PRLastGenLog);
                                        
                    Database.AddInParameter(dbCommand, Dbstring+"iDRLASTLOGREPLAYED", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLastLogReplayed));
                    //Logger.InfoFormat("iDRLASTLOGREPLAYED : " + ComponantMonitor.DRLastLogReplayed);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLASTGENLOGTIME", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLastGenLogTime));
                   // Logger.InfoFormat("iPRLASTGENLOGTIME : " + ComponantMonitor.PRLastGenLogTime);
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDRLASTREPLAYEDLOGTIME", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLastReplayedLogTime));
                   // Logger.InfoFormat("iDRLASTREPLAYEDLOGTIME : " + ComponantMonitor.DRLastReplayedLogTime);
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iCURRENTDATALAG", DbType.AnsiString, Convert.ToString(ComponantMonitor.CurrentDatalag));
                    //Logger.InfoFormat("iCURRENTDATALAG : " + ComponantMonitor.CurrentDatalag);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRCONTENTINDEXSTATE", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRContentIndexState));
                   // Logger.InfoFormat("iPRCONTENTINDEXSTATE : " + ComponantMonitor.PRContentIndexState);
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDRCONTENTINDEXSTATE", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRContentIndexState));
                    //Logger.InfoFormat("iDRCONTENTINDEXSTATE : " + ComponantMonitor.DRContentIndexState);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRCONTINDEXERRORMSG", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRContIndexErrorMsg) == string.Empty ? "N/A" : Convert.ToString(ComponantMonitor.PRContIndexErrorMsg));
                   // Logger.InfoFormat("iPRCONTINDEXERRORMSG : " + ComponantMonitor.PRContIndexErrorMsg);

                    Database.AddInParameter(dbCommand, Dbstring+"iDRCONTINDEXERRORMSG", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRContIndexErrorMsg) == string.Empty ? "N/A" : Convert.ToString(ComponantMonitor.DRContIndexErrorMsg));
                    //Logger.InfoFormat("iDRCONTINDEXERRORMSG : " + ComponantMonitor.DRContIndexErrorMsg);

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLATESTFULLBKPTIME", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLatestFullbkpTime) == string.Empty ? "N/A" : Convert.ToString(ComponantMonitor.PRLatestFullbkpTime));
                    //Logger.InfoFormat("iPRLATESTFULLBKPTIME : " + ComponantMonitor.PRLatestFullbkpTime);

                    Database.AddInParameter(dbCommand, Dbstring+"iDRLATESTFULLBKPTIME", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLatestFullbkpTime) == string.Empty ? "N/A" : Convert.ToString(ComponantMonitor.DRLatestFullbkpTime));
                   // Logger.InfoFormat("iDRLATESTFULLBKPTIME : " + ComponantMonitor.DRLatestFullbkpTime);                       

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLASTLOGINSPECTED", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLastLogInspected));
                   // Logger.InfoFormat("iPRLASTLOGINSPECTED : " + ComponantMonitor.PRLastLogInspected);   
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDRLASTLOGINSPECTED", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLastLogInspected));
                   // Logger.InfoFormat("iDRLASTLOGINSPECTED : " + ComponantMonitor.DRLastLogInspected);  

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLASTLOGCOPYNOTIFIED", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLastLogCopyNotified));
                    //Logger.InfoFormat("iPRLASTLOGCOPYNOTIFIED : " + ComponantMonitor.PRLastLogCopyNotified);  

                    Database.AddInParameter(dbCommand, Dbstring+"iDRLASTLOGCOPYNOTIFIED", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLastLogCopyNotified));
                   // Logger.InfoFormat("iDRLASTLOGCOPYNOTIFIED : " + ComponantMonitor.DRLastLogCopyNotified); 

                    Database.AddInParameter(dbCommand, Dbstring+"iPRLASTLOGCOPIED", DbType.AnsiString, Convert.ToString(ComponantMonitor.PRLastLogCopied));
                    //Logger.InfoFormat("iPRLASTLOGCOPIED : " + ComponantMonitor.PRLastLogCopied); 
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDRLASTLOGCOPIED", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLastLogCopied));
                  //  Logger.InfoFormat("iDRLASTLOGCOPIED : " + ComponantMonitor.DRLastLogCopied); 

                    Database.AddInParameter(dbCommand, Dbstring+"iDRLATLOGCOPIEDTIME", DbType.AnsiString, Convert.ToString(ComponantMonitor.DRLatLogCopiedTime));
                    //Logger.InfoFormat("iDRLATLOGCOPIEDTIME : " + ComponantMonitor.DRLatLogCopiedTime); 

                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occured while insert AddDAGRepilcationMonitor information :" + exc.InnerException.Message, exc);
            }
            return false;
        }

        ///// <summary>
        ///// Get <see cref="ExchangeService"/> From bcms_exchange_healthstatus table by Id.
        ///// </summary>
        ///// <param name="groupid">Id of the ExchangeHealth</param>
        ///// <returns>ExchangeHealth</returns>
        ///// <author>Kiran Ghadge</author>
        /////
        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupId(int groupid)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeHealth_GetByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}

        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupIdandPrDatabaseId(int groupid, int prdatabaseid)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeDAGComponantMonitor_GetByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            Database.AddInParameter(cmd, Dbstring+"iDataBaseId", DbType.Int32, prdatabaseid);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}

        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupIdandMailBoxName(int groupid,string mailboxname)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeDAGComponantMonitor_GetByGroupIdAndMailBoxName";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            Database.AddInParameter(cmd, Dbstring+"iMailBoxName", DbType.String, mailboxname);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}
        ///// <summary>
        ///// Get <see cref="ExchangeService"/> From bcms_exchange_healthstatus table.
        ///// </summary>
       
        ///// <returns>ExchangeHealth List</returns>
        ///// <author>Kiran Ghadge</author>
        /////
        ///// 
        ///// 
        
        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByGroupIdGetAll(int groupid)
        //{
        //    try
        //    {
        //        const string sp = "ExchangeDAGComponatMonitor_GetAllByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        { 
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
                    
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetAll" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }

        //}

        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetAll()
        //{
        //    try
        //    {
        //        const string sp = "ExchangeHealthStatus_GetAll";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetAll" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }

        //}

        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByDate(int groupId, string startDate, string endDate)
        //{

        //    try
        //    {
        //        const string sp = "ExchangeSCRSLog_GetByDate";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupId);
        //            Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.AnsiString, startDate);
        //            Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.AnsiString, endDate);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IOraleLogDataAccess.GetByDate(" + groupId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}
       
        #endregion        
    }
}
