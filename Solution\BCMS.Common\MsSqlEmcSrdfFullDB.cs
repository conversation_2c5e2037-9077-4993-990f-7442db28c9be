﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "MsSqlEmcSrdfFullDB", Namespace = "http://www.BCMS.com/types")]
    public class MsSqlEmcSrdfFullDB : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraobjectId
        {   
            get;
            set;
        }

        [DataMember]
        public string InstanceName
        {
            get;
            set;
        }


        [DataMember]
        public string DatabaseState
        {
            get;
            set;
        }

        [DataMember]
        public string RestrictAccessStatus
        {
            get;
            set;
        }
        [DataMember]
        public string DRDatabaseState
        {
            get;
            set;
        }

        [DataMember]
        public string DRRestrictAccessStatus
        {
            get;
            set;
        }
        #endregion Properties
    }
}
