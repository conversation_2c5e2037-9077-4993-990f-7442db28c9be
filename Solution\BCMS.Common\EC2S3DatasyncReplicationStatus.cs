﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "EC2S3DatasyncReplicationStatus", Namespace = "http://www.BCMS.com/types")]
    public class EC2S3DatasyncReplicationStatus : BaseEntity
    {

        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }      

        [DataMember]
        public string ReplicationType
        {
            get;
            set;
        }

        [DataMember]
        public string ReplicationStatus
        {
            get;
            set;
        }

        [DataMember]
        public string SourceServerPath
        {
            get;
            set;
        }

        [DataMember]
        public string DestinationS3Path
        {
            get;
            set;
        }

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string Syncstatus
        {
            get;
            set;
        }


        #endregion Properties

    }
}