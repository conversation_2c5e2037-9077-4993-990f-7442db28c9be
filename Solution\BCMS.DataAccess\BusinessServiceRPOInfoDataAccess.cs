﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class BusinessServiceRPOInfoDataAccess : BaseDataAccess
    {
        public static bool AddBusinessServiceRPOInfo(BusinessServiceRPOInfo businessServiceRpoInfo)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("BusinessServiceRPOInfo_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, businessServiceRpoInfo.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessFunctionId", DbType.Int32, businessServiceRpoInfo.BusinessFunctionId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, businessServiceRpoInfo.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentRPO", DbType.String, businessServiceRpoInfo.CurrentRPO);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
#if MSSQL
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert BusinessServiceRPOInfo information", exc);
            }
            return false;
        }

    }
}
