﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "bcms_snapmirror", Namespace = "http://www.BCMS.com/types")]
    public class SnapMirror : BaseEntity
    {
       


        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int MCPRServerId
        {
            get;
            set;
        }

        [DataMember]
        public int MCDRServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PRStorageId
        {
            get;
            set;
        }

        [DataMember]
        public string DRStorageId
        {
            get;
            set;
        }

        [DataMember]
        public int Mode
        {
            get;
            set;
        }

        [DataMember]
        public string PRVolume
        {
            get;
            set;
        }

        [DataMember]
        public string DRVolume
        {
            get;
            set;
        }

        

        #endregion

        #region Constructor

        public SnapMirror()
            : base()
        {
        }

        #endregion

    }
}