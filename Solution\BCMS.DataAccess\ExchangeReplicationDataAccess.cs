﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ExchangeReplicationDataAccess : BaseDataAccess
    {
        public static ExchangeReplication GetExchangeReplicationById(int id)
        {

            ExchangeReplication scrGroup = new ExchangeReplication();

            try
            {
                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("ExchangeReplication_GetById"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

                    using (IDataReader myReader = db.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {

                            scrGroup.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                            scrGroup.ReplicationName = Convert.IsDBNull(myReader["ReplicationId"]) ? string.Empty : Convert.ToString(myReader["ReplicationId"]);
                            scrGroup.PRServerID = Convert.IsDBNull(myReader["PRServerId"]) ? 0 : Convert.ToInt32(myReader["PRServerId"]);
                            scrGroup.DRServerID = Convert.IsDBNull(myReader["DRServerId"]) ? 0 : Convert.ToInt32(myReader["DRServerId"]);

                            scrGroup.PRExchangeInstallationFolderPath = Convert.IsDBNull(myReader["PRInstallationPath"]) ? string.Empty : Convert.ToString(myReader["PRInstallationPath"]);
                            scrGroup.DRExchangeBCMSExchangeComponentPath = Convert.IsDBNull(myReader["DRInstallationPath"]) ? string.Empty : Convert.ToString(myReader["DRInstallationPath"]);

                            scrGroup.PRExchangeNewMailboxPath = Convert.IsDBNull(myReader["PRNewMailboxPath"]) ? string.Empty : Convert.ToString(myReader["PRNewMailboxPath"]);
                            scrGroup.DRExchangeNewMailboxPath = Convert.IsDBNull(myReader["DRNewMailboxPath"]) ? string.Empty : Convert.ToString(myReader["DRNewMailboxPath"]);

                            scrGroup.PRExchangeBCMSExchangeComponentPath = Convert.IsDBNull(myReader["PRComponentPath"]) ? string.Empty : Convert.ToString(myReader["PRComponentPath"]);
                            scrGroup.DRExchangeBCMSExchangeComponentPath = Convert.IsDBNull(myReader["DRComponentPath"]) ? string.Empty : Convert.ToString(myReader["DRComponentPath"]);

                            scrGroup.ExchangeBCMSExchangeEXEName = Convert.IsDBNull(myReader["ExeFileName"]) ? string.Empty : Convert.ToString(myReader["ExeFileName"]);
                            scrGroup.ReplaylagTime = Convert.IsDBNull(myReader["ReplayLagTime"]) ? string.Empty : Convert.ToString(myReader["ReplayLagTime"]);
            

                            //scrGroup.Id = myReader.IsDBNull(0) ? 0 : Convert.ToInt32(myReader[0]);
                            //scrGroup.ReplicationName = myReader.IsDBNull(1) ? string.Empty : myReader[1].ToString();
                            //scrGroup.PRServerID = myReader.IsDBNull(2) ? 0 : Convert.ToInt32(myReader[2]);
                            //scrGroup.DRServerID = myReader.IsDBNull(3) ? 0 : Convert.ToInt32(myReader[3]);
                            //scrGroup.PRExchangeInstallationFolderPath = myReader.IsDBNull(4) ? string.Empty : myReader[4].ToString();
                            //scrGroup.DRExchangeInstallationFolderPath = myReader.IsDBNull(5) ? string.Empty : myReader[5].ToString();
                            //scrGroup.PRExchangeNewMailboxPath = myReader.IsDBNull(6) ? string.Empty : myReader[6].ToString();
                            //scrGroup.DRExchangeNewMailboxPath = myReader.IsDBNull(7) ? string.Empty : myReader[7].ToString();
                            //scrGroup.PRExchangeBCMSExchangeComponentPath = myReader.IsDBNull(8) ? string.Empty : myReader[8].ToString();
                            //scrGroup.DRExchangeBCMSExchangeComponentPath = myReader.IsDBNull(9) ? string.Empty : myReader[9].ToString();
                            //scrGroup.ExchangeBCMSExchangeEXEName = myReader.IsDBNull(10) ? string.Empty : myReader[10].ToString();
                            //scrGroup.ReplaylagTime = myReader.IsDBNull(11) ? string.Empty : myReader[11].ToString();
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Parallel Server Information", exc);
            }
            return scrGroup;
        }
        public static ExchangeReplication Update(ExchangeReplication exchangeReplication)
        {
            const string SP = "ExchangeReplication_Update";
            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                db.AddOutParameter(cmd, "@ReturnCode", DbType.Int32, 4);
                db.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, exchangeReplication.Id);
                db.AddInParameter(cmd, Dbstring+"iReplicationName", DbType.AnsiString, exchangeReplication.ReplicationName);
                db.AddInParameter(cmd, Dbstring+"iPRServerID", DbType.Int32, exchangeReplication.PRServerID);
                db.AddInParameter(cmd, Dbstring+"iDRServerID", DbType.Int32, exchangeReplication.DRServerID);
                db.AddInParameter(cmd, Dbstring+"iPRExchangeInstallationFolderPath", DbType.AnsiString, exchangeReplication.PRExchangeInstallationFolderPath);
                db.AddInParameter(cmd, Dbstring+"iDRExchangeInstallationFolderPath", DbType.AnsiString, exchangeReplication.DRExchangeInstallationFolderPath);
                db.AddInParameter(cmd, Dbstring+"iPRExchangeNewMailboxPath", DbType.AnsiString, exchangeReplication.PRExchangeNewMailboxPath);
                db.AddInParameter(cmd, Dbstring+"iDRExchangeNewMailboxPath", DbType.AnsiString, exchangeReplication.DRExchangeNewMailboxPath);
                db.AddInParameter(cmd, Dbstring+"iPRExchangeBCMSExchangeComponentPath", DbType.AnsiString, exchangeReplication.PRExchangeBCMSExchangeComponentPath);
                db.AddInParameter(cmd, Dbstring+"iDRExchangeBCMSExchangeComponentPath", DbType.AnsiString, exchangeReplication.DRExchangeBCMSExchangeComponentPath);
                db.AddInParameter(cmd, Dbstring+"iExchangeBCMSExchangeEXEName", DbType.AnsiString, exchangeReplication.ExchangeBCMSExchangeEXEName);
                db.AddInParameter(cmd, Dbstring+"iReplaylagTime", DbType.AnsiString, exchangeReplication.ReplaylagTime);


                using (IDataReader myDROperationResultReader = db.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        exchangeReplication.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        exchangeReplication = null;
                    }
                }

                return exchangeReplication;

                
            }
        }
    }
}
