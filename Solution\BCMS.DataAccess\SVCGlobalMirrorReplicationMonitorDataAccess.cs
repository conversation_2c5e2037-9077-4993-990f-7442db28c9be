﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;


namespace Bcms.DataAccess
{
    public class SVCGlobalMirrorReplicationMonitorDataAccess : BaseDataAccess
    {
        public static bool AddSVCGlobalMirrorReplicationlog(SVCGMReplicationM svcgmrepli)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SVCGMREPLICATION_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iINFRAOBJECTID", DbType.Int32, svcgmrepli.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRCONSISTENCYGROUPNAME", DbType.AnsiString, svcgmrepli.PRConsistencyGroupName);
                    Database.AddInParameter(cmd, Dbstring + "iPRRELATIONSHIPPRIMARYVALUE", DbType.AnsiString, svcgmrepli.PRRelationshipPrimaryValue);
                    Database.AddInParameter(cmd, Dbstring + "iPRMASTERVOLUMENAME", DbType.AnsiString, svcgmrepli.PRMasterVolumeName);
                    Database.AddInParameter(cmd, Dbstring + "iPRAUXILIARYVOLUMENAME", DbType.AnsiString, svcgmrepli.PRAuxiliaryVolumeName);
                    Database.AddInParameter(cmd, Dbstring + "iPRRCRELATIONSHIPSTATE", DbType.AnsiString, svcgmrepli.PRRCRelationshipState);
                    Database.AddInParameter(cmd, Dbstring + "iPRRCRELATIONSHIPPROGRESS", DbType.AnsiString, svcgmrepli.PRRCRelationshipProgress);
                    Database.AddInParameter(cmd, Dbstring + "iPRRELATIONSHIPNAME", DbType.AnsiString, svcgmrepli.PRRelationshipName);
                    Database.AddInParameter(cmd, Dbstring + "iDRCONSISTENCYGROUPNAME", DbType.AnsiString, svcgmrepli.DRConsistencyGroupName);
                    Database.AddInParameter(cmd, Dbstring + "iDRRELATIONSHIPPRIMARYVALUE", DbType.AnsiString, svcgmrepli.DRRelationshipPrimaryValue);
                    Database.AddInParameter(cmd, Dbstring + "iDRMASTERVOLUMENAME", DbType.AnsiString, svcgmrepli.DRMasterVolumeName);
                    Database.AddInParameter(cmd, Dbstring + "iDRAUXILIARYVOLUMENAME", DbType.AnsiString, svcgmrepli.DRAuxiliaryVolumeName);
                    Database.AddInParameter(cmd, Dbstring + "iDRRCRELATIONSHIPSTATE", DbType.AnsiString, svcgmrepli.DRRCRelationshipState);
                    Database.AddInParameter(cmd, Dbstring + "iDRRCRELATIONSHIPPROGRESS", DbType.AnsiString, svcgmrepli.DRRCRelationshipProgress);
                    Database.AddInParameter(cmd, Dbstring + "iDRRELATIONSHIPNAME", DbType.AnsiString, svcgmrepli.DRRelationshipName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add SVCGlobalMirrorMonitor Details", exc);
            }

        }
    }
}
