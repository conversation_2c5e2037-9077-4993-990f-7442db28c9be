﻿using System;
using System.Data;
using System.Data.Common;
using System.Threading;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using log4net;
using System.Configuration;
using Bcms.Common.Shared;
using Devart.Data.Oracle;

namespace Bcms.ExceptionHandler
{
    public static class ExceptionManager
    {
        private readonly static ILog Logger = LogManager.GetLogger(typeof(ExceptionManager));
        //Added by <PERSON><PERSON><PERSON> dated on 21-05-15
        static string FromAddress = GetSMTPAddress();
        static string SMTPFromAddress = string.IsNullOrEmpty(FromAddress) ? ConfigurationManager.AppSettings["SMTPFromAddress"] : FromAddress;

        //static string SMTPFromAddress = ConfigurationManager.AppSettings["SMTPFromAddress"];

        private static string Dbstring
        {
            get
            {
#if MSSQL
                return "@";
#else
                return "?";
#endif
            }
        }

        public static class CommonMessage
        {

            public static string GetEntitySerilizationExceptionMessage(string entityName, int entityId)
            {
                return "Error While Serializing " + entityName + " Entity Id " + entityId + ".";
            }
        }

        public static void Manage(Exception exception, string jobName, int groupId, string groupName)
        {
            if (exception.GetType() == typeof(BcmsException))
            {
                Manage((BcmsException)exception, jobName, groupId, groupName);
            }
            else if (exception.InnerException != null && exception.InnerException.GetType() == typeof(BcmsException))
            {
                Manage((BcmsException)exception.InnerException, jobName, groupId, groupName);
            }
            else
            {
                if (exception is ThreadAbortException)
                {
                    return;
                }
                var newException = new BcmsException(BcmsExceptionType.CommonUnhandled, string.Empty, exception);
                Manage(newException, jobName, groupId, groupName);
            }
        }

        public static void Manage(BcmsException bcmsException)
        {
            Manage(bcmsException, "JobNameUndefined", 0, "NotAvailable");
        }

        public static void Manage(BcmsException bcmsException, string jobName, int groupId, string groupName)
        {
            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.Low
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.High
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                 || bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {              
                WriteLog(bcmsException);
            }

            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
            || bcmsException.ExceptionPriority == BcmsExceptionPriority.High
            || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {
               
                WriteDatabase(bcmsException, jobName, groupId);
            }

            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.High
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Critical)
                 //|| bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {
                var emailManager = new EmailManager();
                //if (bcmsException.ExceptionType.ToString().StartsWith("NR"))
                //{
               // if (emailManager.IsSendMail())
               // {
                    
                
                    SendEmail(bcmsException, groupName, groupId);  // Commented by MAHESH 04.06.12
              //  }
                //}
                if (bcmsException.ExceptionType.ToString().StartsWith("WF"))
                {
                    SendEmailWorkflowAlerts(bcmsException.SystemDefinedMessage, bcmsException.ExceptionType.ToString().Replace("WF", ""));
                }

                Logger.Info("Sending trap Started :");
                SendTrap(bcmsException);
                Logger.Info("Sending trap Ended :");


                if (emailManager.IsSendSms())
                {
                    SendSMS(bcmsException, groupId);
                }
            }
            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
              || bcmsException.ExceptionPriority == BcmsExceptionPriority.Critical)
            {
                Logger.Error(string.Format("{0} : is locking for The reason {1}", groupName, bcmsException.SystemDefinedMessage));
                LockGoup(groupId);
            }
        }


        //suriya.B
        public static void Manage(BcmsException bcmsException, string jobName, int groupId, string groupName, string IPADDRESS)
        {
            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.Low
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.High
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                 || bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {
                WriteLog(bcmsException);
            }

            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
            || bcmsException.ExceptionPriority == BcmsExceptionPriority.High
            || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {

                WriteDatabase(bcmsException, jobName, groupId, IPADDRESS);
            }

            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.High
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Normal
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
                || bcmsException.ExceptionPriority == BcmsExceptionPriority.Critical
                 || bcmsException.ExceptionPriority == BcmsExceptionPriority.Information)
            {
                var emailManager = new EmailManager();
                //if (bcmsException.ExceptionType.ToString().StartsWith("NR"))
                //{
                // if (emailManager.IsSendMail())
                // {


                SendEmail(bcmsException, groupName, groupId);  // Commented by MAHESH 04.06.12
                //  }
                //}
                if (bcmsException.ExceptionType.ToString().StartsWith("WF"))
                {
                    SendEmailWorkflowAlerts(bcmsException.SystemDefinedMessage, bcmsException.ExceptionType.ToString().Replace("WF", ""));
                }

                Logger.Info("Sending trap Started :");
                SendTrap(bcmsException);
                Logger.Info("Sending trap Ended :");


                if (emailManager.IsSendSms())
                {
                    SendSMS(bcmsException, groupId);
                }
            }
            if (bcmsException.ExceptionPriority == BcmsExceptionPriority.VeryHigh
              || bcmsException.ExceptionPriority == BcmsExceptionPriority.Critical)
            {
                Logger.Error(string.Format("{0} : is locking for The reason {1}", groupName, bcmsException.SystemDefinedMessage));
                LockGoup(groupId);
            }
        }

        //Added by Vishal Shinde for SMTP Address 
        private static string GetSMTPAddress()
        {
            var SMTP = EmailManager.GetSmtpConfigration();
            string FromAddress = "";
            if (SMTP != null)
            {
                FromAddress = SMTP.Username;
            }
            return FromAddress;
        }
        //End

        private static void SendEmail(BcmsException bcmsException, string groupName)
        {
            //Added By Vishal Shinde
            if (string.IsNullOrEmpty(SMTPFromAddress))
            {
                Logger.Info("SMTP Address Not Configured");
                return;
            }

            var emailManager = new EmailManager();
            emailManager.From = SMTPFromAddress;
            emailManager.Subject = "Continuity Patrol Alert : " + groupName + " - " +
                                   bcmsException.ExceptionType.ToString().Remove(0, 2);
            emailManager.Body = "Alert !!! \r\n" + bcmsException.UserDefinedMessage + "\r\n Date : " + DateTime.Now;
            emailManager.Send();
        }

        private static void SendEmail(BcmsException bcmsException, string groupName, int groupId)
        {
            var Emailalert = new EmailAlert();
            string ReceiverName = "";
            int EmailCount = 0;
            //Added By Vishal Shinde
            if (string.IsNullOrEmpty(SMTPFromAddress))
            {
                Logger.Info("SMTP Address Not Configured");
                return;
            }

            string excptionmsg = bcmsException.ExceptionType.ToString().Contains("NR") ? bcmsException.ExceptionType.ToString().Remove(0, 2) : bcmsException.ExceptionType.ToString();
            var emailManager = new EmailManager();
            emailManager.From = SMTPFromAddress;
            emailManager.Subject = "Continuity Patrol Alert : " + groupName + " - " + excptionmsg;
            //emailManager.Subject = "Continuity Patrol Alert : " + groupName + " - " +
            //                       bcmsException.ExceptionType.ToString().Remove(0, 2);
            emailManager.Body = "Alert !!! \r\n" + bcmsException.UserDefinedMessage + "\r\n Date : " + DateTime.Now;
            emailManager.GroupId = groupId;
            //if (!string.IsNullOrEmpty(SMTPFromAddress))
            //{
            emailManager.Send();
            ReceiverName = emailManager.ReturnReceiver().Trim().TrimEnd(',');
            EmailCount = ReceiverName.Split(',').Length;         
            //}
            if (groupId != 0 && ReceiverName != string.Empty) 
            {
                Emailalert.DateAndTime = System.DateTime.Now;
                Emailalert.Description = emailManager.Body;
                Emailalert.InfraObjectName = emailManager.GroupId.ToString();
                Emailalert.UserName = ReceiverName;
                Emailalert.EmailCount = EmailCount;
                ExceptionManager.AddEmailDetails(Emailalert);

            }
        }

        //Start: Added by Neeraj for Alerts Isssue(19062012)
        public static void SendAlertsToUsers(string strAlertMessage, string strGroupName, string strAlertType)
        {
            SendEmail(strAlertMessage, strGroupName, strAlertType);
        }

        public static void SendEmail(String alertMessage, string groupName, string alertType)
        {
            //Added By Vishal Shinde
            if (string.IsNullOrEmpty(SMTPFromAddress))
            {
                Logger.Info("SMTP Address Not Configured");
                return;
            }

            var emailManager = new EmailManager();
            emailManager.From = SMTPFromAddress;
            emailManager.Subject = "Continuity Patrol Alert : " + groupName + " - " + alertType;
            emailManager.Body = "Alert !!! \r\n" + alertMessage + "\r\n Date : " + DateTime.Now;
            //if (!string.IsNullOrEmpty(SMTPFromAddress))
            //{
            emailManager.Send();
            //}
        }
        //End: 

        public static void SendEmailWorkflowAlerts(string message, string workfolwActionType)
        {
            //Added By Vishal Shinde
            if (string.IsNullOrEmpty(SMTPFromAddress))
            {
                Logger.Info("SMTP Address Not Configured");
                return;
            }

            var emailManager = new EmailManager();
            emailManager.From = SMTPFromAddress;
            emailManager.Subject = "Continuity Patrol Alert : " + workfolwActionType;
            emailManager.Body = "Alert !!! \r\n" + message + "\r\n Date : " + DateTime.Now;
            //if (!string.IsNullOrEmpty(SMTPFromAddress))
            //{
            emailManager.Send();
            //}
        }

        public static void SendEmail(string message)
        {
            //Added By Vishal Shinde
            if (string.IsNullOrEmpty(SMTPFromAddress))
            {
                Logger.Info("SMTP Address Not Configured");
                return;
            }

            var emailManager = new EmailManager();
            emailManager.From = SMTPFromAddress;
            emailManager.Subject = "Continuity Patrol Alert : CP Database backup completed";
            emailManager.Body = "Message : " + message + "\r\n Date : " + DateTime.Now;
            //if (!string.IsNullOrEmpty(SMTPFromAddress))
            //{
            emailManager.Send();
            //}
        }

        private static void SendTrap(BcmsException bcmsException)
        {
            string snmpTrapHost = ConfigurationManager.AppSettings["SNMPTrapHost"];

            Logger.Info("snmpTrapHost :" + snmpTrapHost);

            string snmpTrapPort = ConfigurationManager.AppSettings["SNMPTrapPort"];

            Logger.Info("snmpTrapPort:" + snmpTrapPort);

            if (!string.IsNullOrEmpty(snmpTrapHost) && !string.IsNullOrEmpty(snmpTrapPort))
            {
                SendSNMPTrap.SendTrapV2c(snmpTrapHost, Convert.ToInt32(snmpTrapPort), bcmsException.UserDefinedMessage, "public", "*******.*******.0");

            }
        }

        private static void LockGoup(int groupId)
        {
            UpdateGroupByReplicationStatus(groupId, InfraObjectState.Locked, 12, true);
        }

        private static void WriteDatabase(BcmsException bcmsException, string jobName, int infraObjectId)
        {


            string type = bcmsException.ExceptionType.ToString().Contains("NR") ||
                          bcmsException.ExceptionType.ToString().Contains("WF")
                ? bcmsException.ExceptionType.ToString().Remove(0, 2)
                : bcmsException.ExceptionType.ToString();


            var alert = new Alert
                              {
                                  Type = type,
                                  Severity = bcmsException.ExceptionPriority.ToString(),
                                  UserMessage = bcmsException.UserDefinedMessage,
                                  SystemMessage = bcmsException.SystemDefinedMessage,
                                  JobName = jobName,
                                  InfraObjectId = infraObjectId
                              };
            AddAlert(alert);
        }
        //suriya.B
        private static void WriteDatabase(BcmsException bcmsException, string jobName, int infraObjectId, string IPAddress)
        {


            string type = bcmsException.ExceptionType.ToString().Contains("NR") ||
                          bcmsException.ExceptionType.ToString().Contains("WF")
                ? bcmsException.ExceptionType.ToString().Remove(0, 2)
                : bcmsException.ExceptionType.ToString();


            var alert = new Alert
            {
                Type = type,
                Severity = bcmsException.ExceptionPriority.ToString(),
                UserMessage = bcmsException.UserDefinedMessage,
                SystemMessage = bcmsException.SystemDefinedMessage,
                JobName = jobName,
                InfraObjectId = infraObjectId,
                IPADDRESS = IPAddress
            };
            AddAlert(alert);
        }

        public static void RaiseAlert(string Type, string Severity, string UserMessage, string SystemMessage, string jobName, int infraObjectId)
        {
            var alert = new Alert
            {
                Type = Type,
                Severity = Severity,
                UserMessage = UserMessage,
                SystemMessage = SystemMessage,
                JobName = jobName,
                InfraObjectId = infraObjectId
            };
            AddSqlNativeAlert(alert);
            //if (Severity == "High")
            //{
            //    EmailManager.GetSmtpConfigration();
            //    SendEmail(UserMessage, groupId.ToString(), Type);
            //}
        }

        private static void WriteLog(BcmsException cpException)
        {

            string message = cpException.InnerException != null
              ? "r/n INNER EXCEPTION " + cpException.InnerException.Message + "/r/n SYSTEM MESSAGE" +
              cpException.SystemDefinedMessage + "r/n USER MESSAGE" + cpException.UserDefinedMessage : "r/n SYSTEM MESSAGE" +
              cpException.SystemDefinedMessage + "r/n USER MESSAGE" + cpException.UserDefinedMessage;

            Logger.Error(Environment.NewLine + message);
        }

        private static void SendEmail(BcmsException bcmsException)
        {
            //EmailManager emailManager = new EmailManager();
            //emailManager.From = "<EMAIL>";
            //emailManager.Subject = "Application Error : Page Name : " + pageName.Request.Url.AbsoluteUri;
            //emailManager.Body = "Message : " + ex.Message + "</br>Inner Exception : " + ex.InnerException + "</br>";
            //emailManager.To.Add("<EMAIL>");
            //emailManager.Send();
        }

        public static void SendSMS(BcmsException bcmsException, int groupId)
        {
            var smsalerts = new SmsAlert();
            string ReceiverName = "";
            int SMSCount = 0;
            var emailManager = new EmailManager();
            emailManager.Body = "Continuity Patrol Alert : " + bcmsException.ToString() + "\r\n Date : " + DateTime.Now;
            emailManager.GroupId = groupId;
            emailManager.SendSms();
            ReceiverName = emailManager.ReturnReceiver().Trim().TrimEnd(',');
            SMSCount = ReceiverName.Split(',').Length;
            if (groupId != 0)
            {
                smsalerts.DateAndTime = System.DateTime.Now;
                smsalerts.Description = emailManager.Body;
                smsalerts.InfraObjectName = groupId.ToString();
                smsalerts.UserName = ReceiverName;
                smsalerts.SMSCount = SMSCount;
                ExceptionManager.AddSmsDetails(smsalerts);
            }
        }

        #region Alert

        public static bool AddAlert(Alert alert)
        {
            try
            {


                using (DbCommand dbCommand = DataAccessBase.Database.GetStoredProcCommand("Alert_Create"))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iType", DbType.AnsiString, alert.Type);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSeverity", DbType.AnsiString, alert.Severity);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSystemMessage", DbType.AnsiString, alert.SystemMessage);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iUserMessage", DbType.AnsiString, alert.UserMessage);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iJobName", DbType.AnsiString, alert.JobName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, alert.InfraObjectId);

#if ORACLE
                    dbCommand.Parameters.Add(new OracleParameter("cur", OracleDbType.Cursor, 0, ParameterDirection.Output, true, 0, 0, String.Empty, DataRowVersion.Default, DBNull.Value));
#endif

                    int value = DataAccessBase.Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    return value< 0;
#endif
                    return value > 0;

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }

        }

        public static bool AddSqlNativeAlert(Alert alert)
        {
            try
            {

                using (DbCommand dbCommand = DataAccessBase.Database.GetStoredProcCommand("Alert_SqlNative_Create"))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iType", DbType.AnsiString, alert.Type);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSeverity", DbType.AnsiString, alert.Severity);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSystemMessage", DbType.AnsiString, alert.SystemMessage);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iUserMessage", DbType.AnsiString, alert.UserMessage);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iJobName", DbType.AnsiString, alert.JobName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, alert.InfraObjectId);
                    //db.AddInParameter(dbCommand, Dbstring+"iIsApplication", DbType.Int32, alert.IsApplication);
                    int value = DataAccessBase.Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return value< 0;
#endif
                    return value > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }
        }

        public static void GenerateNotification(string type, string severity, string userMessage, string systemMessage, string jobName, int InfraId)
        {
            var alert = new Alert
            {
                Type = type,
                Severity = severity,
                UserMessage = userMessage,
                SystemMessage = systemMessage,
                JobName = jobName,
                InfraObjectId = InfraId
            };
            AddAlert(alert);
        }

        #endregion

        #region Group

        public static bool UpdateGroupByReplicationStatus(int groupId, InfraObjectState state, int replState, bool isTrue)
        {
            try
            {
                var db = DatabaseFactory.CreateDatabase();
                DbCommand dbCommand = db.GetStoredProcCommand("Group_UpdateByReplicationStatus");
                db.AddInParameter(dbCommand, Dbstring + "iGroupId", DbType.Int32, groupId);
                db.AddInParameter(dbCommand, Dbstring + "iState", DbType.AnsiString, state.ToString());
                db.AddInParameter(dbCommand, Dbstring + "iReplStatus", DbType.Int32, replState);
                db.AddInParameter(dbCommand, Dbstring + "isTrue", DbType.Boolean, isTrue);
                int isuccess = db.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess< 0;
#endif
                return isuccess > 0;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by replication status ", exc);
            }

        }

        #endregion

        public static void AlertManage(string wfAlertMechanismType, string currentWorkflowActionName, string wfEmailFailed, string wfSmsFailed)
        {
            if (!String.IsNullOrEmpty(wfAlertMechanismType))
            {
                if (wfAlertMechanismType == "1")
                {

                }

            }
        }
        public static bool AddEmailDetails(EmailAlert Emaildt)
        {
            try
            {
                const string sp = "Add_Email_Details";
                using (DbCommand dbCommand = DataAccessBase.Database.GetStoredProcCommand(sp))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iEmailDescription", DbType.AnsiString, Emaildt.Description);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.AnsiString, Emaildt.InfraObjectName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iDateAndTime", DbType.DateTime, Emaildt.DateAndTime);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iUserName", DbType.AnsiString, Emaildt.UserName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iEmailCount", DbType.Int32, Emaildt.EmailCount);


                    int value = DataAccessBase.Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert Email details information", exc);
            }
            return false;
        }

        public static bool AddSmsDetails(SmsAlert smsdt)
        {
            try
            {
                const string sp = "Add_Sms_Details";
                using (DbCommand dbCommand = DataAccessBase.Database.GetStoredProcCommand(sp))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSmsDescription", DbType.AnsiString, smsdt.Description);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.AnsiString, smsdt.InfraObjectName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iDateAndTime", DbType.DateTime, smsdt.DateAndTime);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iUserName", DbType.AnsiString, smsdt.UserName);
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iSMSCount", DbType.Int32, smsdt.SMSCount);

                    int value = DataAccessBase.Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert sms details information", exc);
            }
            return false;
        }
    }


}
