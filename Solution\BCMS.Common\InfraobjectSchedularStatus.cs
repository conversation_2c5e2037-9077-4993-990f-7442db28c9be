﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ActionExecution", Namespace = "http://www.BCMS.com/types")]
    public class InfraobjectSchedularStatus : BaseEntity
    {
        [DataMember]
        public string InfraObjectName
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string WorkflowName
        {
            get;
            set;
        }

        [DataMember]
        public int WorkflowId
        {
            get;
            set;
        }

        [DataMember]
        public string CurrentActionName
        {
            get;
            set;
        }

        [DataMember]
        public int CurrentActionId
        {
            get;
            set;
        }

        [DataMember]
        public int ScheduleId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraschuWfID
        {
            get;
            set;
        }

        [DataMember]
        public string ScheduleType
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string Type
        {
            get;
            set;
        }

        [DataMember]
        public DateTime StartTime
        {
            get;
            set;
        }

        [DataMember]
        public DateTime EndTime
        {
            get;
            set;
        }


    }
}
