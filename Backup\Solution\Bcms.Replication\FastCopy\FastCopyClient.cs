﻿using System;
using Bcms.Replication.Base;

 

using log4net;

namespace Bcms.Replication.FastCopy
{
    public class FastCopyClient : IDisposable
    {
        private bool _isDisposed;

        public PrimaryHost PrimaryHost { get; set; }

        public DRHost DRHost { get; set; }

        public string RemoteDir { get; set; }

        public string LocalDir { get; set; }

        public string ProcessCode { get; set; }

        public int InfraObjectId { get; set; }

        public int ReplicationId { get; set; }

        public int FastCopyJobId { get; set; }

        public int FastCopyId { get; set; }

        public bool IsWindows { get; set; }


        private static readonly ILog Logger = LogManager.GetLogger(typeof(FastCopyClient));

        public FastCopyClient()
        {

        }

        public FastCopyClient(PrimaryHost primaryHost, DRHost drHost, string localDir, string remoteDir)
        {
            if (primaryHost == null)
            {
                throw new ArgumentException("Primary Host cannot be null");
            }
            if (drHost == null)
            {
                throw new ArgumentException("DR Host cannot be null");
            }
            if (string.IsNullOrEmpty(localDir))
            {
                throw new ArgumentException("Local Directory cannot be null or empty");
            }
            if (string.IsNullOrEmpty(remoteDir))
            {
                throw new ArgumentException("Remote Directory cannot be null or empty");
            }
            PrimaryHost = primaryHost;
            DRHost = drHost;
            LocalDir = localDir;
            RemoteDir = remoteDir;
            //Message = string.Empty;
        }

        public void ConnectPR()
        {
            if (PrimaryHost.Connect(false))
            {
                Logger.InfoFormat("Primary Server {0} is Connected", PrimaryHost.HostName);
            }
        }

        public void ConnectDR()
        {
            if (DRHost.Connect(false))
            {
                Logger.InfoFormat("DR Server {0} is Connected", DRHost.HostName);
            }
        }

        public string StartFastCopyReplication()
        {
            return PrimaryHost.StartFastCopy(DRHost.HostName, DRHost.UserName, DRHost.Password, LocalDir, RemoteDir, ProcessCode);
        }

        public void DisConnectPR()
        {
            PrimaryHost.Disconnect();
        }

        public void DisConnectDR()
        {
            DRHost.Disconnect();
        }

        public string StartFastCopyApplicationReplication(string dataSyncPath)
        {
            DRHost.IsWindows = IsWindows;

            return DRHost.ApplicationReplicationFastCopy("-RA", InfraObjectId, FastCopyJobId, dataSyncPath);
        }
        public string StartWindowsFastCopyApplicationReplication()
        {
            return DRHost.ApplicationReplicationFastCopy(InfraObjectId, FastCopyId);

        }

        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    PrimaryHost = null;
                    DRHost = null;
                    RemoteDir = string.Empty;
                    LocalDir = string.Empty;
                    ProcessCode = string.Empty;
                    ReplicationId = 0;
                    InfraObjectId = 0;
                    FastCopyJobId = 0;
                }
            }
            _isDisposed = true;
        }

        ~FastCopyClient()
        {
            Dispose(false);
        }

        #endregion
    }
}