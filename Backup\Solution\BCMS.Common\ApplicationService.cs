﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ApplicationService", Namespace = "http://www.BCMS.com/types")]
    public class ApplicationService : BaseEntity
    {
        #region Properties

        [DataMember]
        public int ApplicationId
        {
            get;
            set;
        }

        [DataMember]
        public string ServiceName
        {
            get;
            set;
        }

        [DataMember]
        public bool Status
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public ApplicationService()
            : base()
        {
        }

        #endregion
    }
}