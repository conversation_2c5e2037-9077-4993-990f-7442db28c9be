﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="3.5" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4F12B107-29B7-4E7A-98A2-E4219142261F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Bcms.Common</RootNamespace>
    <AssemblyName>Bcms.Common</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionExecution.cs" />
    <Compile Include="Alert.cs" />
    <Compile Include="AlertReceiver.cs" />
    <Compile Include="Application.cs" />
    <Compile Include="ApplicationDetail.cs" />
    <Compile Include="ApplicationGroup.cs" />
    <Compile Include="ApplicationGroupHealth.cs" />
    <Compile Include="ApplicationService.cs" />
    <Compile Include="Base\BaseEntity.cs" />
    <Compile Include="BPAutomation.cs" />
    <Compile Include="BusinessFunction.cs" />
    <Compile Include="BusinessService.cs" />
    <Compile Include="BusinessServiceRPOInfo.cs" />
    <Compile Include="DatabaseBackupInfo.cs" />
    <Compile Include="DatabaseBackupOperation.cs" />
    <Compile Include="DatabaseBase.cs" />
    <Compile Include="DatabaseDB2.cs" />
    <Compile Include="DatabaseExchange.cs" />
    <Compile Include="DatabaseNodes.cs" />
    <Compile Include="DatabaseOracle.cs" />
    <Compile Include="DatabaseOracleRac.cs" />
    <Compile Include="DatabaseSql.cs" />
    <Compile Include="DatabaseType.cs" />
    <Compile Include="DatabaseVersion.cs" />
    <Compile Include="DataGuard.cs" />
    <Compile Include="DROperation.cs" />
    <Compile Include="DROperationResult.cs" />
    <Compile Include="EmcReplication .cs" />
    <Compile Include="EMCSRDF.cs" />
    <Compile Include="ExchangeHealthStatus.cs" />
    <Compile Include="ExchangeReplication.cs" />
    <Compile Include="ExchangeSCRStatus.cs" />
    <Compile Include="ExchangeService.cs" />
    <Compile Include="FastCopy.cs" />
    <Compile Include="FastCopyJob.cs" />
    <Compile Include="GlobalMirror.cs" />
    <Compile Include="GlobalMirrorLuns.cs" />
    <Compile Include="GlobalMirrorMonitor.cs" />
    <Compile Include="Group.cs" />
    <Compile Include="GroupDatabaseNodes.cs" />
    <Compile Include="GroupLuns.cs" />
    <Compile Include="GroupWorkflow.cs" />
    <Compile Include="HADR.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="HADRReplication.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Heatmap.cs" />
    <Compile Include="HitachiURDeviceMonitoring.cs" />
    <Compile Include="HitachiUrLuns.cs" />
    <Compile Include="HitachiURMonitoring.cs" />
    <Compile Include="HitachiUrReplication.cs" />
    <Compile Include="HmcInfo.cs" />
    <Compile Include="DataLag.cs" />
    <Compile Include="ImpactAnalysis.cs" />
    <Compile Include="InfraObject.cs" />
    <Compile Include="LogVolume.cs" />
    <Compile Include="MonitorInfo.cs" />
    <Compile Include="MonitorService.cs" />
    <Compile Include="MountPoint.cs" />
    <Compile Include="MsSqlNative.cs" />
    <Compile Include="NetworkIP.cs" />
    <Compile Include="DataGuardMonitor.cs" />
    <Compile Include="Nodes.cs" />
    <Compile Include="ParallelDROperation.cs" />
    <Compile Include="ParallelGroupWorkflow.cs" />
    <Compile Include="ParallelServer.cs" />
    <Compile Include="ParallelWorkflowActionResult.cs" />
    <Compile Include="ReplicationBase.cs" />
    <Compile Include="SCR.cs" />
    <Compile Include="Server.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServiceAvailability.cs" />
    <Compile Include="SeverConnection.cs" />
    <Compile Include="Shared\Enum.cs" />
    <Compile Include="Shared\UIConstants.cs" />
    <Compile Include="Shared\ValidationConstants.cs" />
    <Compile Include="SMSConfiguration.cs" />
    <Compile Include="SmtpConfiguration.cs" />
    <Compile Include="SnapMirror.cs" />
    <Compile Include="SnapMirrorReplication.cs" />
    <Compile Include="SqlLog.cs" />
    <Compile Include="SqlLogNative.cs" />
    <Compile Include="SqlNative.cs" />
    <Compile Include="SqlnativeHealth.cs" />
    <Compile Include="SqlNativeHealthParamete.cs" />
    <Compile Include="SqlnativeServices.cs" />
    <Compile Include="SqlServer2000.cs" />
    <Compile Include="SqlServer2000Log.cs" />
    <Compile Include="SqlServer2000Service.cs" />
    <Compile Include="ThreadTaskInfo.cs" />
    <Compile Include="VmWare.cs" />
    <Compile Include="Workflow.cs" />
    <Compile Include="WorkflowAction.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>