﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.Common;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class TPCRMonitorDataAccess : BaseDataAccess
    {
        /// <Created> Mridul - CP FGB 4.0v - 13-05-2016 for OracleDB</Created>
        public static bool AddTPCRMonitorLogs(TPCRMonitor oMonitor)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("TPCRMONITORLOGS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iINFRAOBJECTID", DbType.Int32, oMonitor.InfraobjectId);

                    Database.AddInParameter(cmd, Dbstring+"iGROUPNAME", DbType.AnsiString, oMonitor.Name);
                    Database.AddInParameter(cmd, Dbstring+"iTYPE", DbType.AnsiString, oMonitor.Type);
                    Database.AddInParameter(cmd, Dbstring+"iSTATE", DbType.AnsiString, oMonitor.STATE);
                    Database.AddInParameter(cmd, Dbstring+"iSTATUS", DbType.AnsiString, oMonitor.STATUS);
                    Database.AddInParameter(cmd, Dbstring+"iLOCATIONS", DbType.AnsiString, oMonitor.LOCATIONS);
                    Database.AddInParameter(cmd, Dbstring+"iCOPYSETS", DbType.AnsiString, oMonitor.COPYSETS);
                    Database.AddInParameter(cmd, Dbstring+"iCOPYING", DbType.AnsiString, oMonitor.COPYING);
                    Database.AddInParameter(cmd, Dbstring+"iRECOVERABLE", DbType.AnsiString, oMonitor.RECOVERABLE);
                    Database.AddInParameter(cmd, Dbstring+"iACTIVEHOST", DbType.AnsiString, oMonitor.ACTIVEHOST);
                    Database.AddInParameter(cmd, Dbstring+"iERRORCOUNT", DbType.AnsiString, oMonitor.ERRORCOUNT);
                    Database.AddInParameter(cmd, Dbstring+"iDESCRIPTION", DbType.AnsiString, oMonitor.DESCRIPTION);
                    //
                    Database.AddInParameter(cmd, Dbstring+"iPAIRNAME", DbType.AnsiString, oMonitor.PAIRNAME);
                    Database.AddInParameter(cmd, Dbstring+"iERROR", DbType.AnsiString, oMonitor.ERROR);
                    Database.AddInParameter(cmd, Dbstring+"iCOPYTYPE", DbType.AnsiString, oMonitor.COPYTYPE);
                    Database.AddInParameter(cmd, Dbstring+"iPROGRESS", DbType.AnsiString, oMonitor.PROGRESS);
                    Database.AddInParameter(cmd, Dbstring+"iERRORVOLUMES", DbType.AnsiString, oMonitor.ERRORVOLUMES);
                    Database.AddInParameter(cmd, Dbstring+"iRECOVERABLEPAIRS", DbType.AnsiString, oMonitor.RECOVERABLEPAIRS);
                    Database.AddInParameter(cmd, Dbstring+"iCOPYINGPAIRS", DbType.AnsiString, oMonitor.COPYINGPAIRS);
                    Database.AddInParameter(cmd, Dbstring+"iTOTALPAIRS", DbType.AnsiString, oMonitor.TOTALPAIRS);
                    Database.AddInParameter(cmd, Dbstring+"iRECOVERYTIME", DbType.AnsiString, oMonitor.RECOVERYTIME);

                    Database.AddInParameter(cmd, Dbstring+"iCreateDate", DbType.DateTime, oMonitor.CreateDate);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }

    }
}
