using Bcms.Common;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.FastCopy;
//using Bcms.Replication.RoboCopy;
using Bcms.Replication.Shared;
using Jscape.Ssh;
using log4net;
using PGlobalMirror;
using PSSHConn;
using Rebex.TerminalEmulation;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

namespace Bcms.Replication.Base
{
    public sealed class DRPSSHHost : HostPSSH
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(DRHost));
        private readonly object _lockObject = new object();
        private int _maxattempt = 1;
        private VirtualTerminal terminal;
       // private static Dictionary<int, RoboCopyOutData> rcDictionary = new Dictionary<int, RoboCopyOutData>();

        private readonly object _roboLock = new object();

        public DRPSSHHost(string hostName, string userName, string password, int port, List<PSSHConn.SubAuthentication> SubAuthlist)
        {
            HostName = hostName;
            UserName = userName;
            Password = password;
            Port = port;
            SubAuthenticationlist = SubAuthlist;
            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public DRPSSHHost(string hostName, string userName, int port, string privatekey, string privateKeyPasspharase, List<PSSHConn.SubAuthentication> SubAuthlist)
        {
            HostName = hostName;
            UserName = userName;
            Password = "";
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
            Port = port;

            SubAuthenticationlist = SubAuthlist;
            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public DRPSSHHost(string hostName, string userName, int port, string privatekey, string privateKeyPasspharase)
        {
            HostName = hostName;
            UserName = userName;
            Password = "";
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
            Port = port;
        }

        public DRPSSHHost(string hostName, string userName, string password, int port)
        {
            HostName = hostName;
            UserName = userName;
            Password = password;
            Port = port;
        }

        internal void ProcessSubAuthentication()
        {
            int SubAuthenticationType = 0;
            string command = string.Empty;
            string LoginCommand = string.Empty;
            string output = string.Empty;
            string shellPrompt = "\\$|#|>";
            foreach (var SubAuthentication in SubAuthenticationlist)
            {
                SubAuthenticationType = SubAuthentication.SubAuthenticationType;


                switch (SubAuthentication.SubAuthenticationType)
                {
                    case (int)PSSHConn.SubstituteAuthentication.SudoSu:

                        Logger.Info("SUDO SU SubAuthentication Type serverIP: " + HostName);

                        command = SubAuthentication.SubPath + "sudo su - " +
                                       SubAuthentication.SubUser;
                        Logger.Info("serverIP: " + HostName + " command: " + command);
                        if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                        {
                            output = DRSession.SendWait(command, ":", false, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " + output);
                            DRSession.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                        }
                        else
                        {
                            output = DRSession.SendWait(command, shellPrompt, true, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " + output);
                        }
                        Logger.Info("SUDO SU SubAuthentication Type serverIP: " + HostName + " Completed");
                        break;
                    case (int)PSSHConn.SubstituteAuthentication.Su:

                        Logger.Info("SU SubAuthentication Type serverIP: " + HostName);

                        command = SubAuthentication.SubPath + "su - " + SubAuthentication.SubUser;
                        Logger.Info("serverIP: " + HostName + " command: " + command);

                        if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                        {
                            output = DRSession.SendWait(command, ":", false, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " + output);

                            DRSession.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                        }
                        else
                        {
                            output = DRSession.SendWait(command, shellPrompt, true, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " + output);
                        }
                        Logger.Info("SU SubAuthentication Type serverIP: " + HostName + " Completed");
                        break;
                    case (int)PSSHConn.SubstituteAuthentication.Sudo:
                        LoginCommand = SubAuthentication.SubPath + "sudo ";
                        break;

                    case (int)PSSHConn.SubstituteAuthentication.Privrun:
                        LoginCommand = SubAuthentication.SubPath + "privrun ";
                        break;

                    case (int)PSSHConn.SubstituteAuthentication.Asu:
                        Logger.Info("ASU SubAuthentication Type serverIP: " + HostName);

                        command = SubAuthentication.SubPath + "asu " + SubAuthentication.SubUser;
                        Logger.Info("serverIP: " + HostName + " command: " + command);

                        if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                        {
                            output = DRSession.SendWait(command, ":", false, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " +
                                           output);
                            DRSession.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                        }
                        else
                        {
                            output = DRSession.SendWait(command, shellPrompt, true, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " +
                                           output);

                        }
                        Logger.Info("ASU SubAuthentication Type serverIP: " + HostName + " Completed");
                        break;

                    case (int)PSSHConn.SubstituteAuthentication.Other:
                        Logger.Info("ASU SubAuthentication Type serverIP: " + HostName);

                        command = SubAuthentication.SubPath + " " + SubAuthentication.SubUser;
                        Logger.Info("serverIP: " + HostName + " command: " + command);

                        if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                        {
                            output = DRSession.SendWait(command, ":", false, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " +
                                           output);
                            DRSession.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                        }
                        else
                        {
                            output = DRSession.SendWait(command, shellPrompt, true, 120000);
                            Logger.Info("serverIP: " + HostName + " command: " + command + " output: " + output);
                        }
                        Logger.Info("ASU SubAuthentication Type serverIP: " + HostName + " Completed");
                        break;
                }
            }

        }

        public dynamic DRSession { get; set; }

        internal override bool Connect(bool isConnectDatabase)
        {
            bool isConnected = false;
            string strOutdb = string.Empty;
            SSHServerInfo objSSHServer = null;
            string shellPrompt = "\\$|#|>";

            try
            {
                lock (_lockObject)
                {
                    objSSHServer = new SSHServerInfo(HostName, UserName, Password, !string.IsNullOrEmpty(PrivateKey), PrivateKey, PrivateKeyPasspharase, Port, SubAuthenticationlist);
                    DRSession = PSSH.CreateSSHSession(objSSHServer);

                    //LogHelper.LogHeaderAndMessage("DR SERVER STATUS: ", string.Format(" DR Server {0} is connected", HostName));
                    if (isConnectDatabase)
                    {
                        if (!string.IsNullOrEmpty(OracleHomePath))
                        {
                            LogHelper.LogMessage("SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") OracleHomePath: " + OracleHomePath + " is connecting...");
                            if (DrODGInfo != null)

                                isConnected = _ConnectToDatabase(DRSession, DrODGInfo, DatabaseName, OracleHomePath);
                            else
                                isConnected = _ConnectToDatabase(DRSession, DatabaseName, OracleHomePath);

                            //return ConnectToDatabase(PrimarySession, DatabaseName, OracleHomePath);
                            return isConnected;
                        }
                        else
                        {
                            LogHelper.LogMessage(" SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") is connecting...");
                            if (DrODGInfo != null)
                                isConnected = _ConnectToDatabase(DRSession, DrODGInfo, DatabaseName);
                            else
                                isConnected = _ConnectToDatabase(DRSession, DatabaseName);




                            // isConnected= ConnectToDatabase(PrimarySession, DatabaseName);
                            //return ConnectToDatabase(PrimarySession, DatabaseName);
                            return isConnected;
                        }
                        throw new BcmsException(BcmsExceptionType.DatabaseConnectionFailed, "Primary Database (" + DatabaseName + ") connection is failed");
                    }
                    //if (isConnectDatabase)
                    //{
                    //    if (!string.IsNullOrEmpty(OracleHomePath))
                    //    {
                    //        LogHelper.LogMessage("DR SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") OracleHomePath: " + OracleHomePath + " is connecting...");
                    //        //return ConnectToDatabase(DRSession, DatabaseName, OracleHomePath);
                    //        isConnected=ConnectToDatabase(DRSession, DatabaseName, OracleHomePath);
                    //        return isConnected;
                    //    }
                    //    else
                    //    {
                    //        LogHelper.LogMessage("DR SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") is connecting...");
                    //        //return ConnectToDatabase(DRSession, DatabaseName);
                    //        isConnected = ConnectToDatabase(DRSession, DatabaseName);
                    //        return isConnected;
                    //    }
                    //    throw new BcmsException(BcmsExceptionType.DatabaseConnectionFailed, " DR Database (" + DatabaseName + ") connection is failed");

                    //}
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("DR SERVER AUTHENTICATION ERROR ", sshAuthentication);
                throw new BcmsException(BcmsExceptionType.DRAuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("DR SERVER CONNECTION SSH ERROR ", sshException);

                if (_maxattempt == 1)
                {
                    LogHelper.LogMessage("RETRY DR SERVER CONNECTION: " + HostName);

                    _maxattempt++;

                    Connect(isConnectDatabase);
                }
                else
                {
                    throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                        "SshException occurred while connecting DR host - " + HostName, sshException);
                }
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR SERVER CONNECTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while connecting DR host - " + HostName, exc);
            }

            return true;
        }

        public string ApplicationDeleteFiles(int infraobjectId, int fastcopyJobId, string dataSyncPath)
        {
            Logger.Info("Application File Deletion started (client): ");
            Logger.Info("Application File Deletion Jobs InfraObjectId :" + infraobjectId);
            Logger.Info("Application File Deletion Jobs fastcopyJobId :" + fastcopyJobId);



            if (IsWindows)
            {
                string letter = Path.GetPathRoot(dataSyncPath).Remove(2);

                var commands1 = new List<string>
                    {
                        "cd " + dataSyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraobjectId, fastcopyJobId, "-DEL")
                    };

                foreach (string command in commands1)
                {
                    Logger.Info("Application Deletion : Command (" + command + ") is executed");

                    RunSshCommandsWithNoWait(DRSession, command);
                }

                Logger.Info("Application Replication Deletion Ended (client): ");
                return string.Empty;

            }
            else
            {

                String shellPrompt = "\\$|>|>";

                var commands = new List<string>
            {
                 "cd " + dataSyncPath,
                string.Format("nohup java -jar DataSync.jar -DEL {0} {1} &",infraobjectId,fastcopyJobId)          
            };
                foreach (var command in commands)
                {
                    Logger.Info("Application File Deletion Command :" + command);
                    RunSshCommandsWithNoWait(DRSession, command);
                }

                Thread.Sleep(10000);

                Logger.Info("Application File Deletion Ended (client): ");
                return string.Empty;

            }

            //var commands = new List<string>
            //{
            //     "cd " + dataSyncPath,
            //    string.Format("nohup java -jar DataSync.jar -DEL {0} {1} &",infraobjectId,fastcopyJobId)          
            //};
            //foreach (var command in commands)
            //{
            //    Logger.Info("Application File Deletion Command :" + command);
            //    RunSshCommandsWithNoWait(DRSession, command);
            //}

            //Thread.Sleep(10000);

            //Logger.Info("Application File Deletion Ended (client): ");
            //return string.Empty;
        }

        public string CheckApplicationFilesChanges(int infraobjectId, int fastcopyJobId, string dataSyncPath)
        {
            Logger.Info("Application File Changes started (client): ");
            Logger.Info("Application File Changes Jobs InfraObjectId: " + infraobjectId);
            Logger.Info("Application File Changes Jobs fastcopyJobId: " + fastcopyJobId);
            String shellPrompt = "\\$|>|>";

            var commands = new List<string>
            {
                 "cd " + dataSyncPath,
                string.Format("nohup java -jar DataSync.jar -FA {0} {1} &",infraobjectId,fastcopyJobId)          
            };
            foreach (var command in commands)
            {
                Logger.Info("Application File Changes Command :" + command);
                RunSshCommandsWithNoWait(DRSession, command);
            }

            Thread.Sleep(10000);

            Logger.Info("Application File Changes Completed (client): ");
            return string.Empty;
        }

        //internal override bool ConnectWithDatabaseName(string databaseName)
        //{
        //    try
        //    {
        //        DRSession = new SshSession(HostName, UserName, Password) { LicenseKey = Constants.QueryConstants.LicenseKey };
        //        DRSession.SetShellPrompt("\\$|#|>|:", true);
        //        DRSession.Connect();

        //        return ConnectToDatabase(DRSession, databaseName);
        //    }
        //    catch (SshAuthenticationException sshAuthentication)
        //    {
        //        throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
        //    }
        //    catch (SshException sshException)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting DR host - " + HostName, sshException);
        //    }
        //    catch (BcmsException)
        //    {
        //        throw;
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonUnhandled,
        //                                "Exception occured while connecting DR host - " + HostName, exc);
        //    }
        //}

        internal override void Disconnect()
        {
            if (DRSession != null)
            {
                LogHelper.LogMessage("DR SERVER CONNECTION DISCONNECT");
                try
                {
                    PSSH.DisconnectAndRemoveSSHSession(DRSession);
                }
                catch (Exception ex)
                {
                    LogHelper.LogMessage($"Error disconnecting DR SSH session for {HostName}: {ex.Message}");
                }
            }
            else
            {
                LogHelper.LogMessage($"DR SSH session was null for {HostName}, no cleanup needed");
            }
        }

        internal bool IsDatabaseRunning()
        {
            if (IsWindows)
            {
                return IsWinDatabaseRunning();
            }

            return OracleDB.DBRunning(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString, DatabaseName);
        }

        internal bool IsListnerRunning()
        {
            if (IsWindows)
            {
                return true;
            }

            return OracleDB.IsListnerRunning(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString);
        }

        internal override bool VerifyDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("DR DATABASE MODE COMMAND: ",
                    Constants.QueryConstants.DatabaseModeAndRole);

                string drRole =
                    RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.DatabaseModeAndRole,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("DR DATABASE MODE OUTPUT: ", drRole);
                LogHelper.LogHeaderAndMessage("DR DATABASE MODE OUTPUT: ", drRole);
                if (drRole.Contains("standby") && drRole.Contains("mounted"))
                    return true;
                else if (drRole.ToLower().Contains("mounted"))
                    return true;
                else if (drRole.ToLower().Contains("read only with apply standby"))
                    return true;
                else if (drRole.ToLower().Contains("read only with apply"))
                    return true;
                else if (drRole.ToLower().Contains("read only"))
                    return true;
                else
                    return false;
                //return drRole.Contains("standby") && drRole.Contains("mounted");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.DRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while verify DR database mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while verify DR database mode", exc);
            }
        }

        private bool VerifyWindowsDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsDatabaseModeAndRole);

                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDatabaseModeAndRole)
                    )
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string mode = myDatabaseReader[1].ToString();
                            string role = myDatabaseReader[2].ToString();

                            if (mode == "MOUNTED" && role == "PHYSICAL STANDBY")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE OUTPUT: ", exc);
                return false;
            }

            return false;
        }

        internal override bool VerifyUserStatus()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("DR USER STATUS COMMAND: ", Constants.QueryConstants.ActiveUserCount);

                string usercount =
                    RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.ActiveUserCount,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("DR DATABASE USER STATUS: ", usercount);

                if (usercount.Contains("count(*)") && usercount.Contains("-") && usercount.Contains("sql>"))
                {
                    usercount = usercount.Replace("\r\n", "");
                    usercount = usercount.Replace("\t", "");
                    usercount = usercount.Replace("count(*)", "");
                    usercount = usercount.Replace("-", "");
                    usercount = usercount.Replace("sql>", "").Trim();

                    if (Convert.ToInt32(usercount) > 0)
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound,
                        usercount + "Active user (" + usercount + ") found logged in status in DR Server(" + HostName + ")");
                    }

                    return true;
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.DRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while DR get Active user count", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get DR Active user count", exc);
            }

            return false;
        }

        internal override string GetMaxSequenceNumber()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGGetMaxSequenceNumber();
                //}

                LogHelper.LogHeaderAndMessage("DR DATBASE MAX SEQUENCE COMMAND: ",
                    Constants.QueryConstants.MaxSequenceNo);

                string output = RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.MaxSequenceNo,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("DR DATABASE MAX SEQUENCE OUTPUT: ", output);

                return output;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.DRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while get DR Max Sequence No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get DR Max Sequence No", exc);
            }
        }


        internal override string GetMaxPRnDRSequenceNumber()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGGetMaxSequenceNumber();
                //}

                LogHelper.LogHeaderAndMessage("DR DATBASE RESETLOG COMMAND: ",
                    Constants.QueryConstants._RESETLOG);
                int index = 1;
                string output = RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants._RESETLOG,
                    Constants.QueryConstants.GreaterThan);

                output = output.Replace(Constants.QueryConstants._RESETLOG, " ").Trim();

                if (string.IsNullOrEmpty(output) || output.Contains("no rows selected"))
                {
                    return "NA";
                }

                int strstr = output.IndexOf("-");
                //int strindex=output .IndexOf("\n",strstr);
                string resultstring = output.Substring(strstr);
                if (strstr != -1)
                {
                    resultstring = output.Substring(strstr);
                }
                else
                {
                    resultstring = output.Substring(strstr);
                }

                string[] resultarray = Regex.Split(resultstring, "\r\n");
                if (string.IsNullOrEmpty(resultarray[index].ToString().Trim()))
                {
                    return "NA";
                }

                output = resultarray[index].Trim();
                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE RESETLOG Changes Value is: ", resultarray[index].Trim());

                if (!string.IsNullOrEmpty(output))
                {
                    string Log_sequence = Constants.QueryConstants._Log_sequences + "'" + output + "'" + " GROUP BY THREAD#;";
                    output = RunDatabaseCommandsWithWait(DRSession, Log_sequence,
                     Constants.QueryConstants.GreaterThan);

                    LogHelper.LogHeaderAndMessage("GetMaxDRSequenceNumber SEQUENCE OUTPUT: ", output);
                    return output;
                }


                return "NA";
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("GetMaxDRSequenceNumber NUMBER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.DRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage(" GetMaxDRSequenceNumber SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while get DR Max Sequence No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get DR Max Sequence No", exc);
            }
        }

        internal bool ConnectServer()
        {
            return IsWindows ? SSHHelper.Ping(HostName) : SSHHelper.Connect(HostName, UserName, Password);
        }

        internal override bool Exit()
        {
            try
            {
                ExitPromt(DRSession);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal override bool MoveFiles(string sourcePath, string fileName, string targetPath)
        {
            string moveResult = MoveFiles(DRSession, sourcePath, fileName, targetPath);

            return moveResult == null || !moveResult.Contains("cannot move");
        }

        internal override void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt)
        {
            MoveFiles(DRSession, sourcePath, targetPath, true);
        }

        internal bool CancelRecoverMode()
        {
            //if (IsWindows)
            //{
            //    return WinDGSwitchDatabaseToPrimaryMode();
            //}

            string switchresult =
                RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.CancelRecoverMode,
                    Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CancelRecoverMode);

            LogHelper.LogHeaderAndMessage("CANCEL RECOVER MODE OUTPUT:", switchresult);

            return switchresult.Contains("database altered");
        }

        internal bool SwitchDatabaseToPrimaryMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE  : ",
                    Constants.QueryConstants.SwitchToPrimaryMode);

                string switchresult =
                    RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.SwitchToPrimaryMode,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE TO PRIMARY MODE OUTPUT:", switchresult);

                return switchresult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE SSH EXCEPTION ERROR",
                    sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Switch database standby to primary mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while Switch database standby to primary mode", exc);
            }
        }

        internal bool StartPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("START NEW PRIMARY DATABASE COMMAND : ",
                    Constants.QueryConstants.StartPrimaryDatabase);

                string startDbResult = RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.StartPrimaryDatabase,
                    Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("START NEW PRIMARY DATABASE OUTPUT:", startDbResult);

                return startDbResult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while start new Primary Database", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while  start new Primary Database", exc);
            }
        }

        internal bool SwitchPrimaryLogFile()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("SWITCH PRIMARY LOGFILE COMMAND : ",
                    Constants.QueryConstants.PrimarySwitchtoLogfile);

                string switchPrimaryResult =
                    RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.PrimarySwitchtoLogfile,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH PRIMARY LOGFILE OUTPUT:", switchPrimaryResult);

                return switchPrimaryResult.Contains("system altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOG FILE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOGFILE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while switch primary log file", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOGFILE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while switch primary log file", exc);
            }
        }

        internal string VerifyRecoveryMode()
        {
            return RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.DataGuardInRecovery,
                    Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string VerifyArachiveLogGap()
        {
            return RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.VerifyArchiveGap, Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string ArachiveLogGap()
        {
            return RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.ArchiveGap, Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal bool WinVerifyRecoveryMode()
        {
            try
            {
                string output = string.Empty;

                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinDataGuardInRecovery))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            output += myDatabaseReader[0] + " ";
                        }
                    }
                }
                return output.ToLower().Contains("manage");
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR ARCHIVE LOG GAP  EXCEPTION: ", exc);
                return false;
            }
        }

        internal bool WinVerifyArachiveLogGap()
        {
            try
            {

                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinVerifyArchiveGap))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            string output = myDatabaseReader[0].ToString();
                            LogHelper.LogHeaderAndMessage("DR ARCHIVE LOG GAP  STATUS O/P ", output);
                            return output.ToLower().Contains("no rows selected");
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR ARCHIVE LOG GAP  EXCEPTION : ", exc);

                return false;
            }

            return true;
        }

        internal string ReplicateArachiveLogFastcopy(int groupId)
        {
            string result =
                RunSshCommandsWithWait(DRSession, "cd C:\\FastCopy1", Constants.QueryConstants.GreaterThan).ToLower();
            if (result.Contains("c:\\fastcopy"))
            {
                return
                    RunSshCommandsWithWait(DRSession, "FastCopy.exe " + groupId, Constants.QueryConstants.GreaterThan).
                        ToLower();
            }
            return null;
        }

        internal string FastcopyReplicationFile(string fastcopypath, int actionId) // for -R
        {
            Logger.InfoFormat(fastcopypath + "\\DataSync.exe -R " + actionId);
            // string result = RunSshCommandsWithWait(DRSession, fastcopypath + "\\DataSync.exe -R " + actionId, Constants.QueryConstants.GreaterThan).ToLower();
            RunSshCommandsWithNoWait(DRSession, fastcopypath + "\\DataSync.exe -R " + actionId);
            string result = string.Empty;
            return result;
        }

        internal string FastcopyReplicationFileByAction(string command)
        {
            Logger.InfoFormat(command);
            string result = string.Empty;
            //RunSshCommandsWithWait(DRSession, fastcopypath + "\\DataSync.exe -R " + actionId,
            //    Constants.QueryConstants.GreaterThan).ToLower();
            result = RunSshCommandsWithTimeOut(DRSession, command, 1800000);

            return result;
        }

        internal string FastcopyReplicationFolder(string fastcopypath, int actionId)
        {
            Logger.InfoFormat(fastcopypath + "\\FastCopy.exe -RD " + actionId);
            string result =
                RunSshCommandsWithWait(DRSession, fastcopypath + "\\FastCopy.exe -RD " + actionId,
                    Constants.QueryConstants.GreaterThan).ToLower();
            return result;
        }

        internal string CheckDRDataGuard()
        {
            string result;

            string prdgResult = RunDatabaseCommandsWithWait(DRSession, Constants.QueryConstants.DRDataGuardStatus,
                Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.DRDataGuardStatus);

            LogHelper.LogHeaderAndMessage("COMMAND Output: ", prdgResult);

            if (prdgResult.Contains("wait_for_log"))
            {
                result = "Wait for Log";
            }
            else if (prdgResult.Contains("wait_for_gap"))
            {
                result = "Wait for Gap";
            }
            else if (prdgResult.Contains("applying_log"))
            {
                result = "Applying log";
            }
            else if (prdgResult.Contains("closing"))
            {
                result = "Closing";
            }
            else if (prdgResult.Contains("arch"))
            {
                result = "Arch";
            }
            else if (prdgResult.Contains("connected"))
            {
                result = "Connected";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        internal string DynamicDRDBName()
        {
            var discoverDBcmd = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.discoveryqueryondr,
                                                    Constants.QueryConstants.GreaterThan);

            if (discoverDBcmd.ToLower().Contains("db_unique_name"))
            {
                //   string output = discoverDBcmd;
                int index = discoverDBcmd.ToLower().IndexOf("db_unique_name");
                string output = discoverDBcmd.Substring(index);
                string output1 = output.Replace("\r\n", ",");
                LogHelper.LogHeaderAndMessage("COMMAND output1: ", output1.ToString());

                string[] result = output1.Split(',');
                //  string dbname = result[4];
                string dbname = result[2];
                LogHelper.LogHeaderAndMessage("dbname Output: ", dbname.ToString());
                dbname = dbname.Replace("\r", "");
                dbname = dbname.Replace("\n", "");
                return dbname;
            }
            return "N/A";
        }

        internal string CheckDRDataGuardDynamic(string DBName)
        {
            string result = "N/A";

            if (DBName != "N/A")
            {
                string DGStatuscmd = Constants.QueryConstants.dataguardstatus + "('" + DBName + "','" + DBName.ToUpper() + "') and TARGET='STANDBY';";
                var prdgResult = RunSshCommandsWithWait(DRSession, DGStatuscmd, Constants.QueryConstants.GreaterThan).ToLower();

                if (prdgResult.Contains("valid"))
                {
                    // result = "Running";
                    result = "Wait for Log";
                }
                else if (prdgResult.Contains("deferred"))
                {
                    result = "Deferred";
                }
                else if (prdgResult.Contains("inactive"))
                {
                    result = "Inactive";
                }
                // dr ---
                else if (prdgResult.Contains("wait_for_gap"))
                {
                    result = "Wait for Gap";
                }
                else if (prdgResult.Contains("applying_log"))
                {
                    result = "Applying log";
                }
                else if (prdgResult.Contains("closing"))
                {
                    result = "Closing";
                }
                else if (prdgResult.Contains("arch"))
                {
                    result = "Arch";
                }
                else if (prdgResult.Contains("connected"))
                {
                    result = "Connected";
                }
                else if (prdgResult.Contains("error"))
                {
                    result = "Error";
                }
                else
                {
                    result = "N/A";
                }
            }
            return result;
        }

        internal string StartFastCopy(string hostname, string username, string password, string localDir, string remoteDir)
        {
            var sb = new StringBuilder();
            var commands = new List<string>
            {
                Constants.QueryConstants.ChangeFastCopyDirectory,
                string.Format("{0},{1},{2},{3},\"{4}\",\"{5}\"",
                    Constants.QueryConstants.StartFastCopyReplication,
                    hostname,
                    username,
                    password,
                    localDir,
                    remoteDir)
            };

            foreach (string command in commands)
            {
                sb.Append(RunSshCommands(DRSession, command));
            }
            string connectResult = sb.ToString().ToLower();
            return connectResult;
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover)
        {
            try
            {
                if (IsWindows)
                {
                    //Windows FastCopy Log Replication

                    string repicationstatus = ReplicateArachiveLogFastcopy(groupId);

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", repicationstatus);

                    return repicationstatus;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    Constants.QueryConstants.ChangeFastCopyDirectory,
                    iswithover
                        ? string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-SO",
                            groupId, replicationId)
                        : string.Format("{0} {1} {2}", Constants.QueryConstants.StartFastCopyReplication, groupId,
                            replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());

                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopyReplicateArchiveFolder(string fastCopyPath, int workflowActionId, int replicationId, string sequence, string logfile)
        {
            try
            {
                if (IsWindows)
                {
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}", fastCopyPath),
                    string.Format(" nohup {0} -WDB {1} {2} {3} {4} &", Constants.QueryConstants.StartFastCopyReplication,
                        workflowActionId, replicationId, sequence, logfile)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());

                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate ARCHIVE files ", exc.Message);
                return string.Empty;
            }
        }

        internal string StartFastCopy_OLD(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName, string path)
        {
            try
            {

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)
                {
                    var commands = new List<string>
                {                   
                    "cd " + path,
                    iswithover
                        ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                            "-DB", groupId, replicationId, logsequence, logsequenceName)
                        : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                            "-SO", groupId, replicationId, logsequence, logsequenceName) // for oracle win dayasync
               
                };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());

                    return sb.ToString().ToLower();
                }
                else
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName) // for oracle win dayasync               
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName, string path)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();
                if (IsWindows)
                {   // for oracle windows datasync
                    var commands = new List<string>
                    {
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName)               
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("Windows ARCHIVE LOG DATASYNC DR RESULT(in StartFastCopy - DRHost): ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for non-windows
                {
                    var commands = new List<string>
                    {
                        "rm -f nohup.out",
                        "cd " + path,
                        iswithover 
                        ? string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar -SO {0} {1} {2} {3} &", groupId, replicationId, logsequence, logsequenceName)
                        : string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar -DB {0} {1} {2} {3} &", groupId, replicationId, logsequence, logsequenceName)
                    };

                    foreach (string command in commands)
                    {
                        Logger.Info("ARCHIVE LOG DATASYNC Command(in StartFastCopy - DRHost): " + command);
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    return "";
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("ARCHIVE LOG DATASYNC EXCEPTION(in StartFastCopy - DRHost): ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName, string path, string jrepath, bool jreFlag)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;

                var sb = new StringBuilder();

                if (IsWindows)
                {   // for oracle windows datasync
                    var commands = new List<string>
                    {
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName)               
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("Windows ARCHIVE LOG DATASYNC DR RESULT(in StartFastCopy - DRHost): ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for non-windows
                {

                    if (!string.IsNullOrEmpty(jrepath))
                    {
                        datasync = "nohup " + jrepath.TrimEnd('/') + "/java ";
                    }
                    else
                    {
                        datasync = "nohup java ";
                    }

                    var commands = new List<string>
                    {
                        
                        "cd " + path,
                        "rm -f nohup.out",
                        iswithover 
                        ? string.Format("{4} -Xms512m -Xmx1024m -jar DataSync.jar -SO {0} {1} {2} {3} &", groupId, replicationId, logsequence, logsequenceName, datasync)
                        : string.Format("{4} -Xms512m -Xmx1024m -jar DataSync.jar -DB {0} {1} {2} {3} &", groupId, replicationId, logsequence, logsequenceName, datasync)
                    };

                    foreach (string command in commands)
                    {
                        Logger.Info("ARCHIVE LOG DATASYNC Command(in StartFastCopy - DRHost): " + command);
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    return "";
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("ARCHIVE LOG DATASYNC EXCEPTION(in StartFastCopy - DRHost): ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName, string path, string sudoUser)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName) // for oracle win dayasync
               
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else
                {
                    var commands = new List<string>
                    {                        
                        "sudo su - " + sudoUser,
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName) // for oracle win dayasync               
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName, string path, string sudoUser, string jrepath)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName) // for oracle win datasync
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else
                {
                    if (!string.IsNullOrEmpty(jrepath))
                    {
                        datasync = jrepath.TrimEnd('/') + "/" + datasync;
                    }

                    var commands = new List<string>
                    {                        
                        "sudo su - " + sudoUser,
                        "cd " + path,
                        iswithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", groupId, replicationId, logsequence, logsequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", groupId, replicationId, logsequence, logsequenceName) // for oracle datasync               
                    };

                    int i = 1;
                    foreach (string command in commands)
                    {
                        LogHelper.LogMessage(" Command(" + i + "): " + command);
                        i++;
                        sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartSybaseFastCopy(int groupId, int replicationId, bool iswithover, string path)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    "cd " + path,
                    iswithover
                        ? string.Format("{0} {1} {2} {3}", datasync,
                            "-SOSYDB", groupId, replicationId)
                        : string.Format("{0} {1} {2} {3}", datasync,
                            "-SYDB", groupId, replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                //LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());
                LogHelper.LogMessage("Sybase Job Replication Output is: \r\n" + sb.ToString().ToLower());
                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartSybaseFastCopyForRebex(int groupId, int replicationId, bool iswithover, string path)
        {
            try
            {
                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    "cd " + path,
                    iswithover
                        ? string.Format("{0} {1} {2} {3}", datasync,
                            "-SOSYDB", groupId, replicationId)
                        : string.Format("{0} {1} {2} {3}", datasync,
                            "-SYDB", groupId, replicationId)
                };

                foreach (string command in commands)
                {
                    Logger.Info("Application Replication : Command (" + command + ") is executed");
                    sb.Append(RunSshCommandsWithNoWaitRebex(terminal, command));
                }

                LogHelper.LogMessage("Sybase Job Replication Output is: \r\n" + sb.ToString().ToLower());
                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION  :", exc.Message);
                return exc.Message;
            }
        }

        internal string WFSybaseReplicationForRebex(string option, int workflowActionId, int replicationId, string datasyncPath)
        {
            try
            {

                string datasync = Constants.QueryConstants.FastCopyExe;
                var sb = new StringBuilder();
                var commands = new List<string>
                {
                   "cd " + datasyncPath,
                        string.Format("{0} {1} {2} {3}", datasync,option, workflowActionId,replicationId) 
                };

                foreach (string command in commands)
                {
                    Logger.Info("Application Replication : Command (" + command + ") is executed");
                    sb.Append(RunSshCommandsWithNoWaitRebex(terminal, command));
                }
                LogHelper.LogMessage("Sybase Job Replication Output is: \r\n" + sb.ToString().ToLower());
                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        public string WFSybaseReplicationDataSyncBCPTableForRebex(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Sybase Replication DataSync Started (client): ");
                LogHelper.LogMessage("Sybase Replication DataSync workflowactionid: " + workflowactionid);

                String shellPrompt = "\\$|>|>";
                var sb = new StringBuilder();

                string output = string.Empty;

                string datasync = Constants.QueryConstants.FastCopyExe;
                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,                        
                         string.Format("{0} {1} {2}", datasync,option, workflowactionid)                         
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Sybase Replication : Command (" + command + ") is executed");
                    //sb.Append(RunSshCommandsWithNoWaitRebex(terminal, command));
                    sb.Append(RunSshCommandsWithNoWait(DRSession, command));
                }

                LogHelper.LogMessage("Sybase Replication DataSync Output: " + output);
                LogHelper.LogMessage("Sybase Replication DataSync Completed (client): ");
                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Sybase DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        internal string StartFastCopyReverseReplicateArchiveFolder(string fastCopyPath, int groupId, int replicationId)
        {
            if (IsWindows)
            {
            }

            //Linux FastCopy Log Replication
            var sb = new StringBuilder();

            var commands = new List<string>
            {
                string.Format("cd {0}", fastCopyPath),
                string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-SO", groupId,
                    replicationId)
            };

            foreach (string command in commands)
            {
                sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1200000));
            }
            LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
            return sb.ToString().ToLower();
        }

        internal string StartFastCopyReplicateRedoFiles(int workflowActionId, int replicationid, string fastCopyPath)
        {
            try
            {
                if (IsWindows)
                {
                    return string.Empty;
                }

                string javaLocation = "java";//string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";
                var commands = new List<string>
                {
                    "cd " + fastCopyPath,
                     "rm -f nohup.out",
                     string.Format("nohup " + javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {0} {1} {2} &", "-WF",
                    workflowActionId, replicationid)
                };

                foreach (string command in commands)
                {
                    RunSshCommandsWithNoWait(DRSession, command);
                }
                return "";
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);
                return string.Empty;
            }
        }

        internal string StartFastCopyReplicateRedoFiles(int PRServerId, int DRServerId, int replicationid, string fastCopyPath)
        {
            try
            {
                if (IsWindows)
                {
                    return string.Empty;
                }

                string javaLocation = "java";//string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";
                var commands = new List<string>
                {
                   
                     "rm -f nohup.out",
                                      "cd " + fastCopyPath,
                                       string.Format("nohup "+javaLocation+" -Xms512m -Xmx1024m -jar DataSync.jar {0} {1} {2} {3} &", "-WF",
                        PRServerId, DRServerId, replicationid)
                };

                foreach (string command in commands)
                {
                    RunSshCommandsWithNoWait(DRSession, command);
                }


                var sb = new StringBuilder();

                //var commands = new List<string>
                //{
                //    string.Format("cd {0}", fastCopyPath),
                //    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-WF",
                //        workflowActionId, replicationid)
                //};

                //foreach (string command in commands)
                //{
                //    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                //}
                LogHelper.LogHeaderAndMessage("REPLICATE REDO FILES RESULT :", sb.ToString());

                return sb.ToString().ToLower();



            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);

                return string.Empty;
            }
        }

        internal bool ApplyArchiveLogs()
        {
            if (string.IsNullOrEmpty(PrivateKey))
            {
                if (string.IsNullOrEmpty(OracleHomePath))
                {
                    return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString, DatabaseName, IsWindows);
                }
                else
                {
                    return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString, OracleHomePath, DatabaseName, IsWindows);
                }
            }
            else
            {
                if (string.IsNullOrEmpty(OracleHomePath))
                {
                    return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, PrivateKey, "", Port, GetSubAuthList(SubAuthenticationlist)), SudoString, DatabaseName, IsWindows);
                }
                else
                {
                    return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, PrivateKey, "", Port, GetSubAuthList(SubAuthenticationlist)), SudoString, OracleHomePath, DatabaseName, IsWindows);
                }
            }
        }

        internal string CheckDRDataGuardWindows()
        {
            string result;

            string prdgResult = WinDGGetDRDataGuardStatus().ToLower();

            if (prdgResult.Contains("wait_for_log"))
            {
                result = "Wait for Log";
            }
            else if (prdgResult.Contains("wait_for_gap"))
            {
                result = "Wait for Gap";
            }
            else if (prdgResult.Contains("applying_log"))
            {
                result = "Applying log";
            }
            else if (prdgResult.Contains("closing"))
            {
                result = "Closing";
            }
            else if (prdgResult.Contains("arch"))
            {
                result = "Arch";
            }
            else if (prdgResult.Contains("connected"))
            {
                result = "Connected";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        private string WinDGGetDRDataGuardStatus()
        {
            try
            {
                string output = string.Empty;

                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDRDataGuardStatus))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();
                        }
                    }
                }

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATAGUARD STATUS: ", exc);

                return string.Empty;
            }
        }

        private bool IsWinDatabaseRunning()
        {
            try
            {
                using (var db = new Devart.Data.Oracle.OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB RUNNING EXCEPTION : ", exc);

                return false;
            }
        }

        private string WinDGGetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsMaxSequenceNo);


                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsMaxSequenceNo))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string squenceno = myDatabaseReader[0].ToString();
                            LogHelper.LogHeaderAndMessage("DR MAX SEQUENCE NO OUTPUT: ", squenceno);
                            return squenceno;
                        }
                    }
                }

                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR MAX SEQUENCE NO OUTPUT: ", exc);
                throw;
            }
        }

        private bool WinDGSwitchDatabaseToPrimaryMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsSwitchToPrimaryMode);
                using (var db = new Devart.Data.Oracle.OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsSwitchToPrimaryMode);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH STANDBY DATABASE TO PRIMARY EXCEPTION: ", exc);

                throw;
            }
        }

        private bool WinDGStartPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsStartPrimaryDatabase);
                using (var db = new Devart.Data.Oracle.OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsStartPrimaryDatabase);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START PRIMARY DATABASE EXCEPTION: ", exc);
                throw;
            }
        }

        private bool WinDGSwitchPrimaryLogFile()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsPrimarySwitchtoLogfile);

                using (var db = new Devart.Data.Oracle.OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsPrimarySwitchtoLogfile);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOG FILE EXCEPTION: ", exc);
                throw;
            }
        }

        internal string GetDRMaxLogSequnce()
        {
            try
            {
                string sequenceno = string.Empty;
                Microsoft.Practices.EnterpriseLibrary.Data.Database db =
                    Bcms.Replication.Base.CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinDRLatestLogSeqno))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            sequenceno = myDatabaseReader[0].ToString();
                        }
                    }
                }
                return sequenceno;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET DR LATEST LOG SEQUENCE EXCEPTION: ", exc);
                throw;
            }
        }

        public string ApplicationReplicationFastCopy(int applicationId, int fastcopyId)
        {
            string command = string.Format("F:\\DSExePath\\DataSync.exe {0} {1}", applicationId, fastcopyId);
            Logger.Info("Application Replication FastCopy started (client): " + command);
            RunSshCommandsWithNoWait(DRSession, command);
            return string.Empty;
        }

        public string ApplicationReplicationFastCopy(string option, int infraObjectId, int fastcopyJobId, string datasyncPath)
        {
            return ApplicationReplicationFastCopy(option, infraObjectId, fastcopyJobId, datasyncPath, string.Empty);
        }

        public string ApplicationReplicationDataSync(string option, int infraObjectId, int replicationId, string datasyncPath)
        {
            return ApplicationReplicationDataSync(option, infraObjectId, replicationId, datasyncPath, string.Empty);
        }

        public string ApplicationReplicationFastCopy(string option, int infraObjectId, int fastcopyJobId, string datasyncPath, string jrePath)
        {
            try
            {
                if (IsWindows)
                {
                    Logger.Info("Application Replication FastCopy started (client): ");
                    Logger.Info("Application Replication Jobs applicationId: " + infraObjectId);
                    Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraObjectId, fastcopyJobId, option)
                    };

                    foreach (string command in commands1)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    Logger.Info("Application Replication FastCopy Completed (client): ");
                    return string.Empty;
                }

                Logger.Info("Application Replication FastCopy started (client): ");
                Logger.Info("Application Replication Jobs applicationId: " + infraObjectId);
                Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                String shellPrompt = "\\$|>|>";

                string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                var commands = new List<string>
                {                   
                    "rm -f nohup.out",
                    "cd " + datasyncPath,
                    string.Format("nohup " + javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, fastcopyJobId, option)
                };

                foreach (string command in commands)
                {
                    Logger.Info("Application Replication - InfraobjectID: " + infraObjectId + " : Command (" + command + ") is executed");
                    RunSshCommandsWithNoWait(DRSession, command);
                }

                Logger.Info("Application Replication FastCopy Completed (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);
                return string.Empty;
            }
        }

        public string ApplicationReplicationFastCopyOLD(string option, int infraObjectId, int fastcopyJobId, string datasyncPath, string jrePath, string sudouser)
        {
            try
            {
                if (IsWindows)
                {
                    Logger.Info("Application Replication FastCopy started (client): ");
                    Logger.Info("Application Replication Jobs applicationId: " + infraObjectId);
                    Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraObjectId, fastcopyJobId, option),
                        "exit"
                    };

                    foreach (string command in commands1)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    Logger.Info("Application Replication FastCopy Completed (client): ");
                    return string.Empty;
                }

                Logger.Info("Application Replication FastCopy started (client): ");
                Logger.Info("Application Replication Jobs applicationId: " + infraObjectId);
                Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                String shellPrompt = "\\$|>|>";
                string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                var commands = new List<string>
                {
                    !string.IsNullOrEmpty(sudouser) ? "sudo su - " + sudouser : "",
                    "cd " + datasyncPath,
                    string.Format("nohup " + javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, fastcopyJobId, option),
                    "exit"
                };

                foreach (string command in commands)
                {
                    RunSshCommandsWithNoWait(DRSession, command);
                }

                Logger.Info("Application Replication FastCopy Completed (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while replicating files ", exc.Message);
                return string.Empty;
            }
        }
        //

        //public DataSyncOutData _ApplicationDeleteFiles(int infraobjectId, int fastcopyJobId, string dataSyncPath)
        //{
        //    Logger.Info("Application File Deletion Job InfraObjectId: " + infraobjectId);
        //    Logger.Info("Application File Deletion Job fastcopyJobId: " + fastcopyJobId);
        //    String shellPrompt = "\\$|>|>";

        //    DataSyncOutData dsOutData = null;

        //    if (IsWindows)
        //    {
        //        Logger.Info("Application File Deletion started (Windows): ");

        //        string letter = Path.GetPathRoot(dataSyncPath).Remove(2);

        //        var commands1 = new List<string>
        //            {
        //                "cd " + dataSyncPath,
        //                letter,
        //                string.Format("DataSync.exe {2} {0} {1}", infraobjectId, fastcopyJobId, "-DEL")
        //            };

        //        dsOutData = new DataSyncOutData();
        //        dsOutData.DataSyncJobId = fastcopyJobId;
        //        dsOutData.RSyncJobCommand = commands1[2];
        //        dsOutData._JobSession = DRSession;
        //        dsOutData.JobCompleted = false;

        //        foreach (string command in commands1)
        //        {
        //            Logger.Info("Application File Deletion Command (" + command + ") is executed");
        //            RunSshCommandsWithNoWait(DRSession, command);
        //        }
        //    }
        //    else
        //    {
        //        Logger.Info("Application File Deletion started (Non-Windows): ");

        //        var commands = new List<string>
        //        {
        //            "cd " + dataSyncPath,
        //            string.Format("nohup java -jar DataSync.jar -DEL {0} {1} &", infraobjectId, fastcopyJobId)
        //        };

        //        dsOutData = new DataSyncOutData();
        //        dsOutData.DataSyncJobId = fastcopyJobId;
        //        dsOutData.RSyncJobCommand = commands[1];
        //        dsOutData._JobSession = DRSession;
        //        dsOutData.JobCompleted = false;

        //        foreach (var command in commands)
        //        {
        //            Logger.Info("Application File Deletion Command: " + command);
        //            RunSshCommandsWithNoWait(DRSession, command);
        //        }
        //    }

        //    Thread.Sleep(10000);

        //    Logger.Info("Application File Deletion Completed");
        //    return dsOutData;
        //}


        //public DataSyncOutData _ApplicationReplicationFastCopy(string option, int infraObjectId, int fastcopyJobId, string datasyncPath, string jrePath, string sudouser)
        //{
        //    bool result = false;
        //    DataSyncOutData dsOutData = null;
        //    try
        //    {
        //        if (IsWindows)
        //        {
        //            Logger.Info("Application Replication FastCopy started (client - Windows) ");
        //            Logger.Info("Application Replication Jobs InfraobjectId: " + infraObjectId);
        //            Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

        //            string letter = Path.GetPathRoot(datasyncPath).Remove(2);

        //            var commands1 = new List<string>
        //            {
        //                sudouser,
        //                "cd " + datasyncPath,
        //                //letter,
        //                string.Format("DataSync.exe {2} {0} {1}", infraObjectId, fastcopyJobId, option),
        //                "exit"
        //            };

        //            dsOutData = new DataSyncOutData();
        //            dsOutData.DataSyncJobId = fastcopyJobId;
        //            dsOutData.RSyncJobCommand = commands1[2];
        //            //dsOutData.JobSession = DRSession;
        //            dsOutData._JobSession = DRSession;
        //           dsOutData.JobCompleted = false;


        //            foreach (string command in commands1)
        //            {
        //                Logger.Info("Application Replication : Command (" + command + ") is executed");
        //                RunSshCommandsWithNoWait(DRSession, command);
        //            }

        //            Logger.Info("Application Replication FastCopy Completed (client): ");
        //            result = true;
        //        }
        //        else
        //        {
        //            Logger.Info("Application Replication FastCopy started (client - NonWindows) ");
        //            Logger.Info("Application Replication Jobs InfraobjectId: " + infraObjectId);
        //            Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

        //            //String shellPrompt = "\\$|>|>";
        //            string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

        //            var commands = new List<string>
        //            {
        //                sudouser,
        //                "cd " + datasyncPath,
        //                string.Format("nohup " + javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, fastcopyJobId, option),
        //                "exit"
        //            };

        //            dsOutData = new DataSyncOutData();
        //            dsOutData.DataSyncJobId = fastcopyJobId;
        //            dsOutData.RSyncJobCommand = commands[1];
        //           // dsOutData.JobSession = DRSession;
        //            dsOutData._JobSession = DRSession;
        //            dsOutData.JobCompleted = false;

        //            foreach (string command in commands)
        //            {
        //                Logger.Info("Application Replication : Command (" + command + ") is executed");
        //                RunSshCommandsWithNoWait(DRSession, command);
        //            }

        //            Logger.Info("Application Replication FastCopy Completed (client): ");
        //            result = true;
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        LogHelper.LogHeaderAndErrorMessage("Exception occurred while Application Replication FastCopy and Exception: ", exc.Message);
        //    }
        //    finally
        //    {

        //    }
        //    return dsOutData;
        //}
        
        public bool ApplicationReplicationFastCopy(string option, int infraObjectId, int fastcopyJobId, string datasyncPath, string jrePath, string sudouser)
        {
            bool result = false;
            try
            {
                if (IsWindows)
                {
                    Logger.Info("Application Replication FastCopy started (client - Windows) ");
                    Logger.Info("Application Replication Jobs InfraobjectId: " + infraObjectId);
                    Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                    {
                        sudouser,
                        "cd " + datasyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraObjectId, fastcopyJobId, option),
                        "exit"
                    };

                    foreach (string command in commands1)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    Logger.Info("Application Replication FastCopy Completed (client): ");
                    result = true;
                }
                else
                {
                    Logger.Info("Application Replication FastCopy started (client - NonWindows) ");
                    Logger.Info("Application Replication Jobs InfraobjectId: " + infraObjectId);
                    Logger.Info("Application Replication Jobs fastcopyJobId: " + fastcopyJobId);

                    //String shellPrompt = "\\$|>|>";
                    string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                    var commands = new List<string>
                    {
                        sudouser,
                        "cd " + datasyncPath,
                        string.Format("nohup " + javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, fastcopyJobId, option),
                        "exit"
                    };

                    foreach (string command in commands)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    Logger.Info("Application Replication FastCopy Completed (client): ");
                    result = true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Application Replication FastCopy and Exception: ", exc.Message);
            }
            finally
            {

            }
            return result;
        }

        public string ODSReplication(string option, int infraObjectId, int fastcopyJobId, string datasyncPath, string jrePath)
        {
            try
            {
                if (IsWindows)
                {
                    Logger.Info("ODS Replication started (client): ");
                    Logger.Info("ODS Replication Jobs InfraobjectId: " + infraObjectId);
                    Logger.Info("ODS Replication Jobs fastcopyJobId: " + fastcopyJobId);

                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraObjectId, fastcopyJobId, option)
                    };

                    foreach (string command in commands1)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithTimeOut(DRSession, command, 1800000);
                    }

                    Logger.Info("ODS Replication Completed (client): ");
                    return string.Empty;
                }

                Logger.Info("ODS Replication started (client): ");
                Logger.Info("ODS Replication Jobs applicationId: " + infraObjectId);
                Logger.Info("ODS Replication Jobs fastcopyJobId: " + fastcopyJobId);

                String shellPrompt = "\\$|>|>";

                string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                var commands = new List<string>
                {
                    "cd " + datasyncPath,
                    string.Format(javaLocation + " -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, fastcopyJobId, option)
                };

                foreach (string command in commands)
                {
                    Logger.Info("ODS Replication - InfraobjectID: " + infraObjectId + " : Command (" + command + ") is executed");
                    RunSshCommandsWithTimeOut(DRSession, command, 1800000);
                }

                Logger.Info("ODS Replication Completed (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicating ODS files ", exc.Message);
                return string.Empty;
            }
        }

        public bool ReplicateTraceOrControlFile(string option, int workflowActionId, string datasyncPath, string sourceFile, string targetFile)
        {
            bool result = false;
            try
            {
                Logger.Info("Execute " + option + " job started for WorkflowActionId: " + workflowActionId);

                string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                var commands1 = new List<string>
                {
                    "cd " + datasyncPath,
                    letter,
                    string.Format("DataSync.exe {0} {1} {2} {3}", option, workflowActionId, sourceFile, targetFile)
                };

                foreach (string command in commands1)
                {
                    RunSshCommandsWithTimeOut(DRSession, command, 180000);
                }
                result = true;

                Logger.Info(option + " job Completed for WorkflowActionId: " + workflowActionId);
                return result;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred in ReplicateTraceOrControlFile for option: " + option, exc.Message);
                return result;
            }
        }

        public string ApplicationReplicationDataSync(string option, int infraObjectId, int replicationId, string datasyncPath, string jrePath)
        {
            try
            {
                if (IsWindows)
                {
                    Logger.Info("Application Replication DataSync started (client): ");
                    Logger.Info("Application Replication Jobs InfraobjectId: " + infraObjectId);
                    Logger.Info("Application Replication Jobs ReplicationId: " + replicationId);

                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        letter,
                        string.Format("DataSync.exe {2} {0} {1}", infraObjectId, replicationId, option)
                    };

                    foreach (string command in commands1)
                    {
                        Logger.Info("Application Replication : Command (" + command + ") is executed");
                        RunSshCommandsWithNoWait(DRSession, command);
                    }

                    Logger.Info("Application Replication FastCopy Completed (client): ");
                    return string.Empty;
                }

                Logger.Info("Application Replication DataSync started (client): ");
                Logger.Info("Application Replication Jobs InfraobjectId :" + infraObjectId);
                Logger.Info("Application Replication Jobs ReplicationId :" + replicationId);

                String shellPrompt = "\\$|>|>";

                string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                var commands = new List<string>
                {                   
                    "rm -f nohup.out",
                    "cd " + datasyncPath,
                    string.Format("nohup "+javaLocation+" -Xms512m -Xmx1024m -jar DataSync.jar {2} {0} {1} &", infraObjectId, replicationId, option)
                };

                foreach (string command in commands)
                {
                    RunSshCommandsWithNoWait(DRSession, command);
                }

                Logger.Info("Application Replication FastCopy Completed (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);
                return string.Empty;
            }
        }

        //internal RoboCopyOutData StartRoboCopy(string hostname, string username, string password, string sourceDir, string destinationDir, int serverPort, string rcOptionsAndFilters, int roboCopyJobId, string infraobjectName, int robocopyChkPrsTime, int infraobjectId, string sourceIpAddress, string destiIpAddress)
        //{
        //    string retString = string.Empty;
        //    RoboCopyOutData roboresult = null;
        //    var sb = new StringBuilder();
        //    bool ret = false;
        //    string outFile = string.Empty;

        //    lock (_roboLock)
        //    {
        //        roboresult = new RoboCopyOutData();
        //        outFile = "RobocopyResult_" + roboCopyJobId.ToString() + "_" + DateTime.UtcNow.Ticks.ToString() + ".txt";

        //        Logger.Info("Robocopy outFile: " + outFile + " RobocopyjobId: " + roboCopyJobId.ToString());

        //        roboresult.RoboCopyJobId = roboCopyJobId;
        //        roboresult.OutFileName = outFile;
        //        roboresult.jobCompleted = false;
        //        roboresult.OutResultValue = "";

        //        //rcDictionary.Add(roboresult.RoboCopyJobId, roboresult);

        //        var commands = new List<string>
        //        {
        //            Constants.QueryConstants.ChangeRoboCopyDirectory,
        //            //string.Format("{0} {1} {2} /np /ndl /nfl /R:0 /W:0 {3}", Constants.QueryConstants.RoboCopyExe, sourceDir, destinationDir, rcOptionsAndFilters)
        //            string.Format("{0} {1} {2} /np /ndl /nfl /R:0 /W:0 {3} > {4}", Constants.QueryConstants.RoboCopyExe, sourceDir, destinationDir, rcOptionsAndFilters, outFile)
        //        };

        //        RoboCopySSH.RoboCopyServer robocopyServer = new RoboCopySSH.RoboCopyServer(hostname, username, password, null, serverPort, null);

        //        ret = RoboCopySSH.SendNoWaitSshCommands(commands, robocopyServer);
        //    }

        //    return roboresult;
        //}

        //private void UpdateOutputReplicationLog(string rcOutData, string infraobjectName, int infraobjectId, string sourceIpAddress, string destiIpAddress, int roboCopyJobId)
        //{
        //    string[] strResult = rcOutData.Replace("\r", "").Replace("\t", "").Split(new Char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
        //    string strLine = string.Empty;
        //    string excDir = string.Empty;
        //    string excFiles = string.Empty;
        //    int iAssumption = 0;
        //    int secSpeed = 0;

        //    RoboCopyMonitor robocopyMonitor = new RoboCopyMonitor();

        //    robocopyMonitor.RoboCopyJobId = roboCopyJobId;
        //    robocopyMonitor.InfraObjectId = infraobjectId;
        //    robocopyMonitor.SourceIP = sourceIpAddress;
        //    robocopyMonitor.DestinationIP = destiIpAddress;
        //    robocopyMonitor.SpeedBytesPerSeconds = string.Empty;
        //    robocopyMonitor.SpeedMBPerMinute = string.Empty;

        //    Logger.Info("Start of UpdateOutputReplicationLog method");

        //    try
        //    {

        //        for (int i = 0; i < strResult.Count(); i++)
        //        {
        //            if (!string.IsNullOrEmpty(strResult[i].Trim()))
        //            {
        //                if (strResult[i].ToLower().Trim().Contains("started :"))
        //                {
        //                    robocopyMonitor.RepStartTime = strResult[i].Trim().Substring(10).Trim();
        //                }

        //                if (strResult[i].ToLower().Trim().Contains("source :"))
        //                {
        //                    robocopyMonitor.SourcePath = strResult[i].Trim().Substring(9).Trim();
        //                }

        //                if (strResult[i].ToLower().Trim().Contains("dest :"))
        //                {
        //                    robocopyMonitor.DestinationPath = strResult[i].Trim().Substring(7).Trim();
        //                }

        //                if (strResult[i].ToLower().Trim().Contains("exc files :"))
        //                {
        //                    //robocopyMonitor.SelectedOptions = strResult[i].Trim().Substring(10).Trim();
        //                    excFiles = "/XF " + strResult[i].Trim().Substring(12).Trim();
        //                    int j = i + 1;
        //                    while (!string.IsNullOrEmpty(strResult[j].Trim()) && (!strResult[j].ToLower().Trim().Contains("exc dirs :") || !strResult[j].ToLower().Trim().Contains("options :")))
        //                    {
        //                        excFiles += " " + strResult[j].Trim();
        //                        j++;
        //                    }
        //                }

        //                if (strResult[i].ToLower().Trim().Contains("exc dirs :"))
        //                {
        //                    //robocopyMonitor.SelectedOptions = strResult[i].Trim().Substring(10).Trim();
        //                    excDir = "/XD " + strResult[i].Trim().Substring(11).Trim();
        //                    int j = i + 1;
        //                    while (!string.IsNullOrEmpty(strResult[j].Trim()) && !strResult[j].ToLower().Trim().Contains("options :"))
        //                    {
        //                        excDir += " " + strResult[j].Trim();
        //                        j++;
        //                    }
        //                }


        //                if (strResult[i].ToLower().Trim().Contains("options :"))
        //                {
        //                    robocopyMonitor.SelectedOptions = strResult[i].Trim().Substring(10).Trim();
        //                }

        //                if (strResult[i].ToLower().Trim().Contains("ended :"))
        //                {
        //                    robocopyMonitor.RepEndTime = strResult[i].Trim().Substring(8).Trim();
        //                }

        //                if (strResult[i].Trim().ToLower().Contains("total") && strResult[i].Trim().ToLower().Contains("copied") && strResult[i].Trim().ToLower().Contains("skipped") && strResult[i].Trim().ToLower().Contains("mismatch") && strResult[i].Trim().ToLower().Contains("failed") && strResult[i].Trim().ToLower().Contains("extras"))
        //                {
        //                    iAssumption = i;
        //                }

        //                if (iAssumption != 0 && i > iAssumption && strResult[i].ToLower().Trim().Contains("dirs :"))
        //                {
        //                    string[] strDirs = strResult[i].Trim().Substring(6).Trim().Split(new Char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

        //                    robocopyMonitor.TotalDirCount = Convert.ToInt32(strDirs[0].Trim());
        //                    robocopyMonitor.TotalDirCopiedCount = Convert.ToInt32(strDirs[1].Trim());
        //                    robocopyMonitor.TotalSkippedDirCount = Convert.ToInt32(strDirs[2].Trim());
        //                    robocopyMonitor.TotalMisMatchedDirCount = Convert.ToInt32(strDirs[3].Trim());
        //                    robocopyMonitor.TotalFailedDirCount = Convert.ToInt32(strDirs[4].Trim());
        //                    robocopyMonitor.TotalExtrasDirCount = Convert.ToInt32(strDirs[5].Trim());
        //                }

        //                if (iAssumption != 0 && i > iAssumption && strResult[i].ToLower().Trim().Contains("files :"))
        //                {
        //                    string[] strFiles = strResult[i].Trim().Substring(7).Trim().Split(new Char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

        //                    robocopyMonitor.TotalFilesCount = Convert.ToInt32(strFiles[0].Trim());
        //                    robocopyMonitor.TotalFilesCopiedCount = Convert.ToInt32(strFiles[1].Trim());
        //                    robocopyMonitor.TotalSkippedFilesCount = Convert.ToInt32(strFiles[2].Trim());
        //                    robocopyMonitor.TotalMisMatchedFilesCount = Convert.ToInt32(strFiles[3].Trim());
        //                    robocopyMonitor.TotalFailedFilesCount = Convert.ToInt32(strFiles[4].Trim());
        //                    robocopyMonitor.TotalExtrasFilesCount = Convert.ToInt32(strFiles[5].Trim());
        //                }

        //                if (iAssumption != 0 && i > iAssumption && strResult[i].ToLower().Trim().Contains("bytes :"))
        //                {
        //                    string[] strBytes = strResult[i].Trim().Substring(7).Trim().Split(new Char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

        //                    int bytesArrCount = strBytes.Count();
        //                    int nextCount = 0;
        //                    string strKBytes = "k";
        //                    string strMBytes = "m";
        //                    string strGBytes = "g";

        //                    if (strBytes[1].Trim().Equals(strMBytes) || strBytes[1].Trim().Equals(strGBytes) || strBytes[1].Trim().Equals(strKBytes))
        //                    {
        //                        robocopyMonitor.TotalBytesCount = strBytes[0].Trim() + " " + strBytes[1].Trim();
        //                        nextCount += 1;
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalBytesCount = strBytes[0].Trim();
        //                    }

        //                    if (strBytes[2 + nextCount].Trim().Equals(strMBytes) || strBytes[2 + nextCount].Trim().Equals(strGBytes) || strBytes[2 + nextCount].Trim().Equals(strKBytes))
        //                    {
        //                        robocopyMonitor.TotalBytesCopiedCount = strBytes[1 + nextCount].Trim() + " " + strBytes[2 + nextCount].Trim();
        //                        nextCount += 1;
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalBytesCopiedCount = strBytes[1 + nextCount].Trim();
        //                    }

        //                    if (strBytes[3 + nextCount].Trim().Equals(strMBytes) || strBytes[3 + nextCount].Trim().Equals(strGBytes) || strBytes[3 + nextCount].Trim().Equals(strKBytes))
        //                    {
        //                        robocopyMonitor.TotalSkippedBytesCount = strBytes[2 + nextCount].Trim() + " " + strBytes[3 + nextCount].Trim();
        //                        nextCount += 1;
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalSkippedBytesCount = strBytes[2 + nextCount].Trim();
        //                    }

        //                    if (strBytes[4 + nextCount].Trim().Equals(strMBytes) || strBytes[4 + nextCount].Trim().Equals(strGBytes) || strBytes[4 + nextCount].Trim().Equals(strKBytes))
        //                    {
        //                        robocopyMonitor.TotalMisMatchedBytesCount = strBytes[3 + nextCount].Trim() + " " + strBytes[4 + nextCount].Trim();
        //                        nextCount += 1;
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalMisMatchedBytesCount = strBytes[3 + nextCount].Trim();
        //                    }

        //                    if (strBytes[5 + nextCount].Trim().Equals(strMBytes) || strBytes[5 + nextCount].Trim().Equals(strGBytes) || strBytes[5 + nextCount].Trim().Equals(strKBytes))
        //                    {
        //                        robocopyMonitor.TotalFailedBytesCount = strBytes[4 + nextCount].Trim() + " " + strBytes[5 + nextCount].Trim();
        //                        nextCount += 1;
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalFailedBytesCount = strBytes[4 + nextCount].Trim();
        //                    }

        //                    if (bytesArrCount > (6 + nextCount) && (strBytes[6 + nextCount].Trim().Equals(strMBytes) || strBytes[6 + nextCount].Trim().Equals(strGBytes) || strBytes[6 + nextCount].Trim().Equals(strKBytes)))
        //                    {
        //                        robocopyMonitor.TotalExtrasBytesCount = strBytes[5 + nextCount].Trim() + " " + strBytes[6 + nextCount].Trim();
        //                    }
        //                    else
        //                    {
        //                        robocopyMonitor.TotalExtrasBytesCount = strBytes[5 + nextCount].Trim();
        //                    }
        //                }

        //                if (iAssumption != 0 && i > iAssumption && strResult[i].ToLower().Trim().Contains("times :"))
        //                {
        //                    string[] strTimes = strResult[i].Trim().Substring(7).Trim().Split(new Char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

        //                    robocopyMonitor.TotalTimesCount = strTimes[0].Trim();
        //                    robocopyMonitor.TotalTimesCopiedCount = strTimes[1].Trim();
        //                    robocopyMonitor.TotalSkippedTimesCount = "";
        //                    robocopyMonitor.TotalMisMatchedTimesCount = "";
        //                    robocopyMonitor.TotalFailedTimesCount = strTimes[2].Trim();
        //                    robocopyMonitor.TotalExtrasTimesCount = strTimes[3].Trim();
        //                }

        //                if (secSpeed == 0)
        //                {
        //                    if (strResult[i].Trim().ToLower().Contains("speed :"))
        //                    {
        //                        robocopyMonitor.SpeedBytesPerSeconds = strResult[i].Trim().Substring(8).Trim();
        //                        secSpeed = i;
        //                    }
        //                }
        //                else
        //                {
        //                    if (strResult[i].Trim().ToLower().Contains("speed :"))
        //                    {
        //                        robocopyMonitor.SpeedMBPerMinute = strResult[i].Trim().Substring(8).Trim();
        //                    }
        //                }
        //            }
        //        }

        //        if (!string.IsNullOrEmpty(excDir))
        //        {
        //            robocopyMonitor.SelectedOptions += " " + excDir;
        //        }
        //        if (!string.IsNullOrEmpty(excFiles))
        //        {
        //            robocopyMonitor.SelectedOptions += " " + excFiles;
        //        }

        //        RoboCopyJobDataAccess.AddRoboCopyMonitor(robocopyMonitor);

        //        Logger.Info("UpdateOutputReplicationLog method completed");
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat(infraobjectName + " : Exception occurred in UpdateOutputReplicationLog, and ERROR MESSAGE: " + exc.Message);
        //    }
        //    finally
        //    {
        //        //bool outDel = DeleteRoboCopyOutFile(rcOutData.OutFileName);
        //    }
        //}

        //internal string CheckRoboCopyOutResult(string hostname, string username, string password, int serverPort, string outFile, int roboCopyJobId)
        //{
        //    var sb = new StringBuilder();
        //    var sbProcess = new StringBuilder();
        //    string retString = string.Empty;

        //    //Process[] ipByName = Process.GetProcessesByName("robocopy", hostname);
        //    RoboCopySSH.RoboCopyServer robocopyServer = new RoboCopySSH.RoboCopyServer(hostname, username, password, null, serverPort, null);

        //    var commands = new List<string>
        //    {
        //        Constants.QueryConstants.ChangeRoboCopyDirectory,
        //        //string.Format("{0} {1} {2} /np /ndl /nfl /R:0 /W:0 {3}", Constants.QueryConstants.RoboCopyExe, sourceDir, destinationDir, rcOptionsAndFilters)
        //        string.Format("type {0}", outFile)
        //    };

        //    sb.Append(RoboCopySSH.GetRoboCopyResultFromFile(commands, robocopyServer));
        //    retString = sb.ToString();

        //    Logger.Info("In CheckRoboCopyOutResult, retString: " + retString);
        //    return retString;
        //}

        //internal bool CheckRobocopyJob(string rcOutData, string infraobjectName)
        //{
        //    bool jobDone = false;

        //    string[] strResult = rcOutData.Replace("\r", "").Replace("\t", "").Split(new Char[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);

        //    try
        //    {
        //        for (int i = 0; i < strResult.Count(); i++)
        //        {
        //            if (!string.IsNullOrEmpty(strResult[i].Trim()))
        //            {
        //                if (strResult[i].Trim().ToLower().Contains("total") && strResult[i].Trim().ToLower().Contains("copied") && strResult[i].Trim().ToLower().Contains("skipped") && strResult[i].Trim().ToLower().Contains("mismatch") && strResult[i].Trim().ToLower().Contains("failed") && strResult[i].Trim().ToLower().Contains("extras"))
        //                {
        //                    jobDone = true;
        //                    break;
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat(infraobjectName + " : Exception occurred in CheckRobocopyJob, and ERROR MESSAGE: " + exc.Message);
        //    }

        //    return jobDone;
        //}

        //internal bool DeleteRoboCopyResultFile(string hostname, string username, string password, int serverPort, string outFile)
        //{
        //    bool retValue = false;

        //    RoboCopySSH.RoboCopyServer robocopyServer = new RoboCopySSH.RoboCopyServer(hostname, username, password, null, serverPort, null);
        //    var commandProcess = new List<string>
        //        {
        //            Constants.QueryConstants.ChangeRoboCopyDirectory,
        //            string.Format("del {0}", outFile)
        //        };

        //    retValue = RoboCopySSH.DeletionRoboCopyResultFile(commandProcess, robocopyServer);

        //    return retValue;
        //}

    }
}
