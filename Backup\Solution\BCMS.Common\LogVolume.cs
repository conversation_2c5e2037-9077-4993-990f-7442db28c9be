﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "LogVolume", Namespace = "http://www.BCMS.com/types")]

    public class LogVolume : BaseEntity
    {
        #region Properties

        [DataMember]
        public string TotalLogVolume
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public LogVolume()
            : base()
        {
        }

        #endregion
    }
}
