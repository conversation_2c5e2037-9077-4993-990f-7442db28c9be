﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAActionTrendDataDataAccess : BaseDataAccess
    {
        // for update  IsAnalyze Status in ParallelGroupWorkflow table
        public static void ExecuteActionDBMigration()
        {

            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("Biaactiontrenddatapermonth"))
                {
                    int isuccess = Database.ExecuteNonQuery(cmd);
                }
            }

            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while BIA DB migration for Action in ExecuteActionDBMigration", exc);

            }


        }
    }
}
