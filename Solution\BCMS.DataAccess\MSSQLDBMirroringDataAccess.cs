﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
namespace Bcms.DataAccess
{
    public class MSSQLDBMirroringDataAccess : BaseDataAccess
    {

        public static bool AddMSSQLDBMonitorStatus(SqlDBMirroring _SqlMirroring, int infraobject)
        {
            const string sp = "MSSqlDBMirrorMoniStatus_Create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraobject);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseName", DbType.AnsiString, _SqlMirroring.PRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseName", DbType.AnsiString, _SqlMirroring.DRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iPRoprmode", DbType.AnsiString, _SqlMirroring.PROpreationMode);
                    Database.AddInParameter(cmd, Dbstring + "iDRoprmode", DbType.AnsiString, _SqlMirroring.DROpreationMode);
                    Database.AddInParameter(cmd, Dbstring + "iPRRoleofDB", DbType.AnsiString, _SqlMirroring.PRDBRole);
                    Database.AddInParameter(cmd, Dbstring + "iDRRoleofDB", DbType.AnsiString, _SqlMirroring.DRDBRole);
                    Database.AddInParameter(cmd, Dbstring + "iPRMirroringState", DbType.AnsiString, _SqlMirroring.PRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iDRMirroringState", DbType.AnsiString, _SqlMirroring.DRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogGenerateRate", DbType.AnsiString, _SqlMirroring.PRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogGenerateRate", DbType.AnsiString, _SqlMirroring.DRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnsentLog", DbType.AnsiString, _SqlMirroring.PRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnsentLog", DbType.AnsiString, _SqlMirroring.DRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogSentRate", DbType.AnsiString, _SqlMirroring.PRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSentRate", DbType.AnsiString, _SqlMirroring.DRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnrestoredQLog", DbType.AnsiString, _SqlMirroring.PRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnrestoredQLog", DbType.AnsiString, _SqlMirroring.DRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogRecoveryRate", DbType.AnsiString, _SqlMirroring.PRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogRecoveryRate", DbType.AnsiString, _SqlMirroring.DRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionDelay", DbType.AnsiString, _SqlMirroring.PRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionDelay", DbType.AnsiString, _SqlMirroring.DRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionPerSecond", DbType.AnsiString, _SqlMirroring.PRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionPerSecond", DbType.AnsiString, _SqlMirroring.DRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, _SqlMirroring.DataLag);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    //return returnCode <= 0;
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif

#if MSSQL

                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                    if (returnCode > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlNativeLogDetails entry ", exc);
            }
            return false;
        }

        public static bool AddMSSQLDBMonitorLogs(SqlDBMirroring _SqlMirroring, int infraobject)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("MSSqlDBMirrorMoniLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraobject);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseName", DbType.AnsiString, _SqlMirroring.PRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseName", DbType.AnsiString, _SqlMirroring.DRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iPRoprmode", DbType.AnsiString, _SqlMirroring.PROpreationMode);
                    Database.AddInParameter(cmd, Dbstring + "iDRoprmode", DbType.AnsiString, _SqlMirroring.DROpreationMode);
                    Database.AddInParameter(cmd, Dbstring + "iPRRoleofDB", DbType.AnsiString, _SqlMirroring.PRDBRole);
                    Database.AddInParameter(cmd, Dbstring + "iDRRoleofDB", DbType.AnsiString, _SqlMirroring.DRDBRole);
                    Database.AddInParameter(cmd, Dbstring + "iPRMirroringState", DbType.AnsiString, _SqlMirroring.PRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iDRMirroringState", DbType.AnsiString, _SqlMirroring.DRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogGenerateRate", DbType.AnsiString, _SqlMirroring.PRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogGenerateRate", DbType.AnsiString, _SqlMirroring.DRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnsentLog", DbType.AnsiString, _SqlMirroring.PRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnsentLog", DbType.AnsiString, _SqlMirroring.DRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogSentRate", DbType.AnsiString, _SqlMirroring.PRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSentRate", DbType.AnsiString, _SqlMirroring.DRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnrestoredQLog", DbType.AnsiString, _SqlMirroring.PRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnrestoredQLog", DbType.AnsiString, _SqlMirroring.DRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogRecoveryRate", DbType.AnsiString, _SqlMirroring.PRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogRecoveryRate", DbType.AnsiString, _SqlMirroring.DRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionDelay", DbType.AnsiString, _SqlMirroring.PRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionDelay", DbType.AnsiString, _SqlMirroring.DRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionPerSecond", DbType.AnsiString, _SqlMirroring.PRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionPerSecond", DbType.AnsiString, _SqlMirroring.DRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, _SqlMirroring.DataLag);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
#if MSSQL

                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlNativeLogDetails entry ", exc);
            }
            return false;
        }
    }
}
