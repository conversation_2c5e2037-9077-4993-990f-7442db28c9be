﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;


namespace Bcms.DataAccess
{
    public class SVCControllerDataAccess : BaseDataAccess
    {
        public static bool AddSVCControllerMonitorLog(SVCcontrollerMonitor svccontrolmonitorlog)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SvcContrormonitor_log_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "IPRDISKCONTROLLERID", DbType.AnsiString, svccontrolmonitorlog.PRdiskControllerId);
                    Database.AddInParameter(cmd, Dbstring + "IPRDISKCONTROLLERNAME", DbType.AnsiString, svccontrolmonitorlog.PRdiskControllerName);
                    Database.AddInParameter(cmd, Dbstring + "IPRMDISKSDEGRADESTATUS", DbType.AnsiString, svccontrolmonitorlog.PRmdisksDegradeStatus);
                    Database.AddInParameter(cmd, Dbstring + "IPRCONTROLLERPRODUCTID", DbType.AnsiString, svccontrolmonitorlog.PRcontrollerProductId);
                    Database.AddInParameter(cmd, Dbstring + "IPRSTORAGEPOOLSTATUS", DbType.AnsiString, svccontrolmonitorlog.PRstoragePoolStatus);
                    Database.AddInParameter(cmd, Dbstring + "IPRCLUSTEREDSYSTEMNODEIDSTATUS", DbType.AnsiString, svccontrolmonitorlog.PRclusteredSystemNodeIdStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRDISKCONTROLLERID", DbType.AnsiString, svccontrolmonitorlog.DRdiskControllerId);
                    Database.AddInParameter(cmd, Dbstring + "IDRDISKCONTROLLERNAME", DbType.AnsiString, svccontrolmonitorlog.DRdiskControllerName);
                    Database.AddInParameter(cmd, Dbstring + "IDRMDISKSDEGRADESTATUS", DbType.AnsiString, svccontrolmonitorlog.DRmdisksDegradeStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRCONTROLLERPRODUCTID", DbType.AnsiString, svccontrolmonitorlog.DRcontrollerProductId);
                    Database.AddInParameter(cmd, Dbstring + "IDRSTORAGEPOOLSTATUS", DbType.AnsiString, svccontrolmonitorlog.DRstoragePoolStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRCLUSTEREDSYSTEMNODEIDSTATUS", DbType.AnsiString, svccontrolmonitorlog.DRclusteredSystemNodeIdStatus);
                    Database.AddInParameter(cmd, Dbstring + "iINFRAOBJECTID", DbType.Int32, svccontrolmonitorlog.InfraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add SVCcontrollerMonitor Details", exc);
            }
        }

    }
}
