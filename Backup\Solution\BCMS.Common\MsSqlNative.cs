﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bcms.Common.Base;
using System.Runtime.Serialization;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "mssqlnative", Namespace = "http://www.BCMS.com/types")]

    public class MsSqlNative:BaseEntity
    {
        
         #region Properties

            [DataMember]
            
           public int GroupId
            {
                 get;
                 set;
            }
            [DataMember]
            public string BackupFolderName
            {
                get;
                set;
            }

            [DataMember]
            public string BackupFolderSharedName
            {
                get;
                set;
            }

            [DataMember]
            public string CopyRestoreName
            {
                get;
                set;
            }

            [DataMember]
            public string CopyRestoreSharedName
            {
                get;
                set;
            }

            [DataMember]
            public int BackupInterval
            {
                get;
                set;
            }

            [DataMember]
            public int CopyInterval
            {
                get;
                set;
            }

            [DataMember]
            public int RestoreInterval
            {
                get;
                set;
            }          

            #endregion


      #region Constructor

            public MsSqlNative()
                : base()
            {
            }

            #endregion


    }
}
