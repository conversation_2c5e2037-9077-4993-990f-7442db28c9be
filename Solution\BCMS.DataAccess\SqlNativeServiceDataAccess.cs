﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

using Bcms.Common;

namespace Bcms.DataAccess
{
    public class SqlNativeServiceDataAccess:BaseDataAccess
    {
        public static string GetservicebyId(string server, string servicename, int groupId)
        {
            string serviceName = string.Empty;
            try
            {
               using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLNATIVESERVICES_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iServer", DbType.AnsiString, server);
                    Database.AddInParameter(dbCommand, Dbstring+"iServiceName", DbType.AnsiString, servicename);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySqlserviceReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlserviceReader.Read())
                        {
                            serviceName = mySqlserviceReader[4].ToString();
                        }
                    }
                }
                return serviceName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature Services GetbyId for group Id(" + groupId + ")", exc);

            }
        }


        public static bool AddSqlNativeServices(SqlnativeServices sqlnativeServices)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVESERVICES_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlnativeServices.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iServer", DbType.AnsiString, sqlnativeServices.Server);
                    Database.AddInParameter(cmd, Dbstring+"iServiceName", DbType.AnsiString, sqlnativeServices.Service_Name);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, sqlnativeServices.Status);
                    Database.AddInParameter(cmd, Dbstring+"iStartMode", DbType.AnsiString, sqlnativeServices.Start_Mode);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqlnativeServices.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, sqlnativeServices.CreatorId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogNative_Create entry ", exc);
            }
        }

        public static bool UpdateSqlNativeServices(SqlnativeServices sqlnativeServices)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVESERVICES_UPDATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, sqlnativeServices.Id);
                    Database.AddInParameter(cmd, Dbstring+"iInfraobjectId", DbType.Int32, sqlnativeServices.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iServer", DbType.AnsiString, sqlnativeServices.Server);
                    Database.AddInParameter(cmd, Dbstring+"iServiceName", DbType.AnsiString, sqlnativeServices.Service_Name);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, sqlnativeServices.Status);
                    Database.AddInParameter(cmd, Dbstring+"iStartMode", DbType.AnsiString, sqlnativeServices.Start_Mode);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqlnativeServices.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, sqlnativeServices.UpdatorId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogNative_Create entry ", exc);
            }
        }

    }
}
