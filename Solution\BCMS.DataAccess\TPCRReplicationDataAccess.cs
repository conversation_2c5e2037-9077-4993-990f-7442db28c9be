﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class TPCRReplicationDataAccess : BaseDataAccess
    {
        public static TPCR_Replication GetTPCRReplicationById(int id)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("TPRCREPLI_GETBYREPLICATIONID"))
                {
                    var TprcRep = new TPCR_Replication();

                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            TprcRep.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["ID"]);
                            TprcRep.ReplicationId = Convert.IsDBNull(reader["REPLICATIONID"]) ? 0 : Convert.ToInt32(reader["REPLICATIONID"]);
                            TprcRep.GroupName = Convert.IsDBNull(reader["GROUPNAME"]) ? string.Empty : Convert.ToString(reader["GROUPNAME"]);
                            TprcRep.BatchFilePath = Convert.IsDBNull(reader["BATCHFILEPATH"]) ? string.Empty : Convert.ToString(reader["BATCHFILEPATH"]);
                            TprcRep.BatchFileName = Convert.IsDBNull(reader["BATCHFILENAME"]) ? string.Empty : Convert.ToString(reader["BATCHFILENAME"]);
                        }
                        return TprcRep;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get TPCR Replication By Id", exc);
            }
        }
    }
}
