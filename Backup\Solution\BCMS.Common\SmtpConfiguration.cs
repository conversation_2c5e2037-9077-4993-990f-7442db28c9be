﻿using System.Runtime.Serialization;
using Bcms.Common.Base;
 

namespace Bcms.Common
{
    public class SmtpConfiguration : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Smtphost
        {
            get;
            set;
        }

        [DataMember]
        public string Username
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }
        [DataMember]
        public bool EnableSsl
        {
            get;
            set;
        }


        [DataMember]
        public bool IsBodyHtml
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public SmtpConfiguration()
            : base()
        {
        }

        #endregion
    }
}