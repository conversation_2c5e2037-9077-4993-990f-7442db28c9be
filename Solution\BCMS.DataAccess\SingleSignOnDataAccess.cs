﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.Common.Base;
using Bcms.Helper;

namespace Bcms.DataAccess
{
    public class SingleSignOnDataAccess : BaseDataAccess
    {
        #region Constructors


        #endregion

        #region Methods

        public static SSOConfiguration GetBySSOTypeId(int issotypeid)
        {
            SSOConfiguration ssoconfig = new SSOConfiguration();

            try
            {
                const string sp = "SINGLESIGNON_GETBYSSOTYPEID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iSSOTypeid", DbType.AnsiString, issotypeid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ssoconfig.SSOTypeId = Convert.IsDBNull(reader["SSOTypeId"]) ? 0 : Convert.ToInt32(reader["SSOTypeId"]);
                            ssoconfig.HostName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
                            ssoconfig.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"]);
                            ssoconfig.IpAddress = Convert.IsDBNull(reader["IpAddress"]) ? string.Empty : Convert.ToString(reader["IpAddress"]);
                            ssoconfig.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
                            ssoconfig.ExcecutionPath = Convert.IsDBNull(reader["ExecutionPath"]) ? string.Empty : Convert.ToString(reader["ExecutionPath"]);
                            ssoconfig.KeyLocation = Convert.IsDBNull(reader["KeyLocation"]) ? string.Empty : Convert.ToString(reader["KeyLocation"]);
                        }

                    }
                    return ssoconfig;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while GetBySSOTypeId - " + issotypeid, exc);
            }
        }

        public static SSOConfiguration GetBySSOTypeIdProfileId(int issotypeid,int Profileid)
        {
            SSOConfiguration ssoconfig = new SSOConfiguration();

            try
            {
                const string sp = "SINGLESIGNON_GETBYSSOTYPEPID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iSSOTypeid", DbType.Int32, issotypeid);
                    Database.AddInParameter(cmd, Dbstring + "iProfileid", DbType.Int32, Profileid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ssoconfig.SSOTypeId = Convert.IsDBNull(reader["SSOTypeId"]) ? 0 : Convert.ToInt32(reader["SSOTypeId"]);
                            ssoconfig.ProfileName = Convert.IsDBNull(reader["profilename"]) ? string.Empty : Convert.ToString(reader["profilename"]);
                            ssoconfig.CredentialFilePath = Convert.IsDBNull(reader["CREDFILEPATH"]) ? string.Empty : Convert.ToString(reader["CREDFILEPATH"]);
                            ssoconfig.ConnectionTimeout = Convert.IsDBNull(reader["CONNECTIONTIMEOUT"]) ? 0 : Convert.ToInt32(reader["CONNECTIONTIMEOUT"]);
                            ssoconfig.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);

                            // added for arcos password generation
                            ssoconfig.ARCOSOnlineUrl = Convert.IsDBNull(reader["ARCOSOnlineUrl"]) ? string.Empty : Convert.ToString(reader["ARCOSOnlineUrl"]);
                            ssoconfig.ARCOSWebAPIURL = Convert.IsDBNull(reader["ARCOSWebAPIURL"]) ? string.Empty : Convert.ToString(reader["ARCOSWebAPIURL"]);
                            ssoconfig.HostName = Convert.IsDBNull(reader["HostName"]) ? string.Empty : Convert.ToString(reader["HostName"]);
                            ssoconfig.IpAddress = Convert.IsDBNull(reader["IPADDRESS"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(reader["IPADDRESS"]));
                            ssoconfig.SharedKey = Convert.IsDBNull(reader["SharedKey"]) ? string.Empty : Convert.ToString(reader["SharedKey"]);
                            ssoconfig.ServiceType = Convert.IsDBNull(reader["ServiceType"]) ? string.Empty : Convert.ToString(reader["ServiceType"]);
                            ssoconfig.DBInstance = Convert.IsDBNull(reader["DBInstance"]) ? string.Empty : Convert.ToString(reader["DBInstance"]);
                           

                        }

                    }
                    return ssoconfig;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while GetBySSOTypeId - " + issotypeid, exc);
            }
        }

        #endregion
    }
}
