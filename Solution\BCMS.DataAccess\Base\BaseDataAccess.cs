﻿using System;
using System.Data;
using System.Diagnostics;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using System.Configuration;
using Bcms.ExceptionHandler;

#if ORACLE
//comment by <PERSON> 27/08/2014
//using Oracle.DataAccess.Client;
using Devart.Data.Oracle;
#endif

namespace Bcms.DataAccess.Base
{
    public abstract class BaseDataAccess : MarshalByRefObject, IDisposable
    {
        #region Instance Variables

        private bool _isDisposed;

        private static Database _db;

        private static string _name;


        #endregion

        protected static string Dbstring
        {
            get
            {
#if MSSQL
                return "@";
#else
                return "?";
#endif
            }
        }

        public static Database Database
        {
            get
            {
                if (_db == null)
                {
                   // string type = typeof(Database).ToString();

                    string encrypt = ConfigurationManager.ConnectionStrings["CPConnectionString"].ConnectionString;

                    _db = CustomDatabaseFactory.CreateDatabase(CryptographyHelper.Md5Decrypt(encrypt));
                }

                return _db;
            }
        }

        public static String DbRoleName
        {
            get
            {
                if (_name == null)
                {
                    _name = ConfigurationManager.AppSettings["DBRoleName"];
                }

                return _name;
            }
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                }
            }

            _isDisposed = true;
        }

        ~BaseDataAccess()
        {
            Dispose(false);
        }

#if ORACLE


        [DebuggerStepThrough]
        protected static OracleParameter BuildRefCursorParameter(String name)
        {
            return new OracleParameter(name, OracleDbType.Cursor, 0, ParameterDirection.Output, true, 0, 0, String.Empty, DataRowVersion.Default, DBNull.Value);
            //comment by Martin 27/08/2014
            //return new OracleParameter(name, OracleDbType.RefCursor, 0, ParameterDirection.Output, true, 0, 0, String.Empty, DataRowVersion.Default, DBNull.Value);
        }

#endif

    }

    public static class CustomDatabaseFactory
    {

        private static DbProviderFactory _dbProviderFactory;

        public static DbProviderFactory CustomDBFactory
        {
            get
            {
#if ORACLE
                return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("Devart.Data.Oracle"));
                //comment by Martin 27/08/2014
                //return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("Oracle.DataAccess.Client"));

#elif MSSQL

                return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("System.Data.SqlClient"));
#else
                return _dbProviderFactory ?? (_dbProviderFactory = DbProviderFactories.GetFactory("MySql.Data.MySqlClient"));
#endif
            }
        }

        public static Database CreateDatabase(string connectionString)
        {
            return new GenericDatabase(connectionString, CustomDBFactory);
        }
    }
}

