﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PMainframe;

namespace Bcms.Core.Client
{
    public class MimixClient : IDisposable
    {
        
        #region Variables 
        private static readonly ILog Logger = LogManager.GetLogger(typeof(MimixClient));
        private Server _server;
        private const int MaxAlertCount = 3;
        #endregion 

        public MimixClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

        public InfraObject CurrentInfraObject { get; set; }

        public void MonitorMimixComponent()
        {
           try
           {  
               if (CurrentServer != null)
               {
                   CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                   CurrentServer.InfraObjectId = CurrentInfraObject.Id;
                   var InfraObjicetInfo = InfraObjectDataAccess.GetInfraObjectById(CurrentInfraObject.Id);
                   if (InfraObjicetInfo.PRReplicationId != 0)
                   {
                       var tn3270 = new TN3270();
                       var replication = ReplicationBaseDataAccess.GetReplicationById(InfraObjicetInfo.PRReplicationId);

                        #region MIMIXManager

                                               var appMimixmgr = new ApplicationMimix.MIMIXManager();                       
                      
                                               if (replication != null)
                                               {
                                                  var _serverPR = new MainframeServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, CurrentServer.PRPort);
                                                  //var _serverDR = new MainframeServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, CurrentServer.DRPort);
                                                   string script = "";
                                                    var mimixMgrworkflow = WorkflowDataAccess.GetWorkflowById(Convert.ToInt32(replication.mimixReplication.MIMIXManager));
                                                    if(mimixMgrworkflow!=null)
                                                    {
                                                        if(!string.IsNullOrEmpty(mimixMgrworkflow.Xml))
                                                        {
                                                            var workflowActionMgr = new WorkflowAction();
                                                            string[] workflowActions = GetWorkFlowActionFromXml(mimixMgrworkflow.Xml);
                                                            if (workflowActions.Length > 1)
                                                            {
                                                                workflowActionMgr = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32((workflowActions[1].Split('@'))[0]));
                                                            }
                                                            else
                                                            {
                                                                Logger.InfoFormat(" workflow attached to Replication Comopnant " + replication.Name + " has no action added :", mimixMgrworkflow.Name);  
                                                            }
                                                            script = workflowActionMgr.Command;                                   
                                                        }
                                                    }

                                                    var _mimixMgrStatus = tn3270.FetchMimixManagerStatus(_serverPR, script);
                                                    if (_mimixMgrStatus != null)
                                                    {
                                                        if (_mimixMgrStatus.Count > 0) 
                                                        {
                                                            foreach (var item in _mimixMgrStatus) 
                                                            {
                                                              appMimixmgr.SystemDefinition =  Convert.ToString(item.SystemDefinition);
                                                              appMimixmgr.Type = Convert.ToString(item.Type);
                                                              appMimixmgr.System = Convert.ToString(item.System);
                                                              appMimixmgr.Journal = Convert.ToString(item.Journal);
                                                              appMimixmgr.ClusterServices = Convert.ToString(item.ClusterServices);
                                                              appMimixmgr.InfraobjectId = CurrentInfraObject.Id;      
                               
                                                              var isSuccess = MimixMonitorDataAccess.AddMimixManagerMonitorStatus(appMimixmgr);

                                                              RaiseMimixMgrAlert(appMimixmgr, CurrentServer);
                                                            }
                                                        }
                                                        Logger.InfoFormat("Add Mimix Manager Monitor Status details :", CurrentInfraObject.Name);  
                                                    }                           
                                               }
                                               
                        #endregion MIMIXManager

                        #region MimixAlerts

                        var appMimixAlerts = new ApplicationMimix.MIMIXAlerts();                       
                      
                                               if (replication != null)
                                               {
                                                   var _serverPR = new MainframeServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, CurrentServer.PRPort);
                                                   //var _serverDR = new MainframeServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, CurrentServer.DRPort);
                                                   string script = "";
                                                    var mimixAlertworkflow = WorkflowDataAccess.GetWorkflowById(Convert.ToInt32(replication.mimixReplication.MIMIXAlerts));
                                                    if (mimixAlertworkflow != null)
                                                    {
                                                        if (!string.IsNullOrEmpty(mimixAlertworkflow.Xml))
                                                        {
                                                            var workflowActionAlert = new WorkflowAction();
                                                            string[] workflowActions = GetWorkFlowActionFromXml(mimixAlertworkflow.Xml);
                                                            if (workflowActions.Length > 1)
                                                            {
                                                                workflowActionAlert = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32((workflowActions[1].Split('@'))[0]));
                                                            }
                                                            else
                                                            {
                                                                Logger.InfoFormat(" workflow attached to Replication Comopnant " + replication.Name + " has no action added :", mimixAlertworkflow.Name);
                                                            }
                                                            script = workflowActionAlert.Command;     
                                                        }
                                                    }

                                                    var _mimixAlertStatus = tn3270.FetchMimixAlerts(_serverPR, script);
                                                    if (_mimixAlertStatus != null)
                                                    {
                                                        appMimixAlerts.AuditCount =  Convert.ToString(_mimixAlertStatus.AuditCount);
                                                        appMimixAlerts.RecoveryCount = Convert.ToString(_mimixAlertStatus.RecoveryCount);
                                                        appMimixAlerts.NotificationCount = Convert.ToString(_mimixAlertStatus.NotificationCount);
                                                                                               
                                                        appMimixAlerts.InfraobjectId = CurrentInfraObject.Id;

                                                        var isSuccess = MimixMonitorDataAccess.AddMimiXAlertsMonitorStatus(appMimixAlerts);
                                   
                                                        Logger.InfoFormat("Add Mimix Alert Monitor Status details :", CurrentInfraObject.Name);

                                                        RaiseMimixAlertAlert(appMimixAlerts, CurrentServer);

                                                    }                           
                                               }


                        #endregion MimixAlerts

                        #region MimixHealth

                                               var appMimixHealth = new ApplicationMimix.MIMIXHealth();                       
                      
                                               if (replication != null)
                                               {
                                                   var _serverPR = new MainframeServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, CurrentServer.PRPort);
                                                   //var _serverDR = new MainframeServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, CurrentServer.DRPort);
                                                   string script = "";
                                                    var mimixHealthworkflow = WorkflowDataAccess.GetWorkflowById(Convert.ToInt32(replication.mimixReplication.MIMIXHealth));
                                                    if (mimixHealthworkflow != null)
                                                    {

                                                        if (!string.IsNullOrEmpty(mimixHealthworkflow.Xml))
                                                        {
                                                            var workflowActionHealth = new WorkflowAction();
                                                            string[] workflowActions = GetWorkFlowActionFromXml(mimixHealthworkflow.Xml);
                                                            if (workflowActions.Length > 1)
                                                            {
                                                                workflowActionHealth = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32((workflowActions[1].Split('@'))[0]));
                                                            }
                                                            else
                                                            {
                                                                Logger.InfoFormat(" workflow attached to Replication Comopnant " + replication.Name + " has no action added :", mimixHealthworkflow.Name);
                                                            }
                                                            script = workflowActionHealth.Command;
                                                        }
                                                    }

                                                    var _mimixHealthStatus = tn3270.FetchMimixHealthStatus(_serverPR, script, replication.mimixReplication.DataGroup);
                                                    if (_mimixHealthStatus != null)
                                                    {
                                                        if (_mimixHealthStatus.Count > 0) 
                                                        {
                                                            foreach (var item in _mimixHealthStatus) 
                                                            {
                                                              appMimixHealth.DataGroup =  Convert.ToString(item.DataGroup);

                                                              appMimixHealth.SourceSystem = Convert.ToString(item.SourceSystem);
                                                              appMimixHealth.SourceMgr = Convert.ToString(item.SourceMgr);
                                                              appMimixHealth.SourceDB = Convert.ToString(item.SourceDB);
                                                              appMimixHealth.SourceObj = Convert.ToString(item.SourceObj); 
                                      
                                                              appMimixHealth.TargetSystem = Convert.ToString(item.TargetSystem);
                                                              appMimixHealth.TargetMgr = Convert.ToString(item.TargetMgr);
                                                              appMimixHealth.TargetDB = Convert.ToString(item.TargetDB);
                                                              appMimixHealth.TargetObj = Convert.ToString(item.TargetObj);

                                                              appMimixHealth.ErrorDB = Convert.ToString(item.ErrorDB);
                                                              appMimixHealth.ErrorObj = Convert.ToString(item.ErrorObj);
                            
                                                              appMimixHealth.InfraobjectId = CurrentInfraObject.Id;

                                                              var isSuccess = MimixMonitorDataAccess.AddMimixHealthMonitorStatus(appMimixHealth);

                                                              RaiseMimixHealthAlert(appMimixHealth, CurrentServer);
                                                            }
                                                        }
                                                        Logger.InfoFormat("Add Mimix Health Status details :", CurrentInfraObject.Name);  
                                                    }                             
                                               }



                        #endregion MimixHealth

                        #region MimixAvailability

                                               var appMimixAvl = new ApplicationMimix.MIMIXAvilability();

                                               if (replication != null)
                                               {
                                                   var _serverPR = new MainframeServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, CurrentServer.PRPort);
                                                   //var _serverDR = new MainframeServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, CurrentServer.DRPort);
                                                   string script = "";
                                                   var mimixAvalworkflow = WorkflowDataAccess.GetWorkflowById(Convert.ToInt32(replication.mimixReplication.MIMIXAvilability));
                                                   if (mimixAvalworkflow != null)
                                                   {
                                                       if (!string.IsNullOrEmpty(mimixAvalworkflow.Xml))
                                                       {
                                                           var workflowActionAvailability = new WorkflowAction();
                                                           string[] workflowActions = GetWorkFlowActionFromXml(mimixAvalworkflow.Xml);
                                                           if (workflowActions.Length > 1)
                                                           {
                                                               workflowActionAvailability = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32((workflowActions[1].Split('@'))[0]));
                                                           }
                                                           else
                                                           {
                                                               Logger.InfoFormat(" workflow attached to Replication Comopnant " + replication.Name + " has no action added :", mimixAvalworkflow.Name);
                                                           }
                                                           script = workflowActionAvailability.Command;
                                                       }
                                                   }

                                                   var appMimixAvllist = tn3270.FetchMimixAvailability(_serverPR, script);
                                                   if (appMimixAvllist != null)
                                                   {
                                                       if (appMimixAvllist.Count > 0)
                                                       {
                                                           foreach (var item in appMimixAvllist)
                                                           {
                                                               appMimixAvl.Activity = Convert.ToString(item.Activity);
                                                               appMimixAvl.Status = Convert.ToString(item.Status);                                      
                                                               appMimixAvl.InfraobjectId = CurrentInfraObject.Id;

                                                               var isSuccess = MimixMonitorDataAccess.AddMimixAvailabilityMonitorStatus(appMimixAvl);

                                                               RaiseMimixAvailabilityAlert(appMimixAvl, CurrentServer);
                                                           }
                                                       }
                                                       Logger.InfoFormat("Add Mimix Availability Monitor Status details :", CurrentInfraObject.Name);
                                                   }
                                               }


                        #endregion MimixAvailability

                        #region MimixDatalag

                                               var appMimixDatalag = new ApplicationMimix.MIMIXDatalag();

                                               if (replication != null)
                                               {
                                                   var _serverPR = new MainframeServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, CurrentServer.PRPort);
                                                   //var _serverDR = new MainframeServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, CurrentServer.DRPort);
                                                   string script = "";
                                                   var mimixDatalagworkflow = WorkflowDataAccess.GetWorkflowById(Convert.ToInt32(replication.mimixReplication.MIMIXDatalag));
                                                   if (mimixDatalagworkflow != null)
                                                   {
                                                       var workflowActionDatalag = new WorkflowAction();
                                                       if (!string.IsNullOrEmpty(mimixDatalagworkflow.Xml))
                                                       {
                                                           string[] workflowActions = GetWorkFlowActionFromXml(mimixDatalagworkflow.Xml);
                                                           if (workflowActions.Length > 1)
                                                           {
                                                               workflowActionDatalag = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32((workflowActions[1].Split('@'))[0]));
                                                           }
                                                           else
                                                           {
                                                               Logger.InfoFormat(" workflow attached to Replication Comopnant " + replication.Name + " has no action added :", mimixDatalagworkflow.Name);
                                                           }
                                                           script = workflowActionDatalag.Command;
                                                       }
                                                   }

                                                   var _datalag = tn3270.FetchMimixDatalag2(_serverPR, script);
                                                   if (_datalag != null)
                                                   {
                                                       appMimixDatalag.DataGroup = Convert.ToString(_datalag.DataGroup);
                                                       appMimixDatalag.DatabaseErrors = Convert.ToString(_datalag.DatabaseErrors);
                                                       appMimixDatalag.ElapsedTime = Convert.ToString(_datalag.ElapsedTime);

                                                       appMimixDatalag.ObjectInErrorAndActive = Convert.ToString(_datalag.ObjectInErrorAndActive);
                                                       appMimixDatalag.TransferDefinition = Convert.ToString(_datalag.TransferDefinition);
                                                       appMimixDatalag.State = Convert.ToString(_datalag.State);

                                                       appMimixDatalag.Database = Convert.ToString(_datalag.Database);
                                                       appMimixDatalag.Object = Convert.ToString(_datalag.Object);


                                                       appMimixDatalag.DBSourceReceiver = Convert.ToString(_datalag.DBSourceReceiver);
                                                       appMimixDatalag.DBTargetReceiver = Convert.ToString(_datalag.DBTargetReceiver);
                                                       appMimixDatalag.DBLastReadReceiver = Convert.ToString(_datalag.DBLastReadReceiver);

                                                       appMimixDatalag.DBSourceSequence = Convert.ToString(_datalag.DBSourceSequence);
                                                       appMimixDatalag.DBTargetSequence = Convert.ToString(_datalag.DBTargetSequence);
                                                       appMimixDatalag.DBLastReadSequence = Convert.ToString(_datalag.DBLastReadSequence);

                                                       appMimixDatalag.DBSourceDateTime = Convert.ToString(_datalag.DBSourceDateTime);
                                                       appMimixDatalag.DBTargetDateTime = Convert.ToString(_datalag.DBTargetDateTime);
                                                       appMimixDatalag.DBLastReadDateTime = Convert.ToString(_datalag.DBLastReadDateTime);

                                                       appMimixDatalag.DBSourceTransPerHour = Convert.ToString(_datalag.DBSourceTransPerHour);
                                                       appMimixDatalag.DBTargetTransPerHour = Convert.ToString(_datalag.DBTargetTransPerHour);
                                                       appMimixDatalag.DBLastReadTransPerHour = Convert.ToString(_datalag.DBLastReadTransPerHour);

                                                       appMimixDatalag.DBEntrieNotRead = Convert.ToString(_datalag.DBEntrieNotRead);
                                                       appMimixDatalag.DBEstimatedTimeToRead = Convert.ToString(_datalag.DBEstimatedTimeToRead);

                                                       appMimixDatalag.ObjCurrentReceiver = Convert.ToString(_datalag.ObjCurrentReceiver);
                                                       appMimixDatalag.ObjLastReadReceiver = Convert.ToString(_datalag.ObjLastReadReceiver);

                                                       appMimixDatalag.ObjCurrentSequence = Convert.ToString(_datalag.ObjCurrentSequence);
                                                       appMimixDatalag.ObjLastReadSequence = Convert.ToString(_datalag.ObjLastReadSequence);

                                                       appMimixDatalag.ObjCurrentDateTime = Convert.ToString(_datalag.ObjCurrentDateTime);
                                                       appMimixDatalag.ObjLastReadDateTime = Convert.ToString(_datalag.ObjLastReadDateTime);

                                                       appMimixDatalag.ObjCurrentTransPerHour = Convert.ToString(_datalag.ObjCurrentTransPerHour);
                                                       appMimixDatalag.ObjLastReadTransPerHour = Convert.ToString(_datalag.ObjLastReadTransPerHour);

                                                       appMimixDatalag.ObjEntrieNotRead = Convert.ToString(_datalag.ObjEntrieNotRead);
                                                       appMimixDatalag.ObjEstimatedTimeToRead = Convert.ToString(_datalag.ObjEstimatedTimeToRead);

                                                       appMimixDatalag.CurrentDatalag = Convert.ToString(_datalag.CurrentDatalag);
                                                       appMimixDatalag.InfraobjectId = CurrentInfraObject.Id;

                                                       var isSuccess = MimixMonitorDataAccess.AddMimixDatalagMonitorStatus(appMimixDatalag);

                                                       Logger.InfoFormat("Add Mimix Datalag Monitor Status details :", CurrentInfraObject.Name);

                                                       RaiseMimixDatalagAlert(appMimixDatalag, CurrentServer);
                                                   }
                                               }



                        #endregion MimixDatalag
                   }
               }
           }

           catch (BcmsException exc)
           {
               Logger.Info("Exception:Monitor Mimix component :" + CurrentInfraObject.Name + exc.Message + exc.InnerException.Message);
               throw;
           }
       }

        //private string[] GetWorkFlowActionFromXml(string rawXml)
        //{
        //    string returnValue = string.Empty;

        //    XmlReader reader = XmlReader.Create(new StringReader(rawXml));
        //    while (reader.Read())
        //    {
        //        switch (reader.NodeType)
        //        {
        //            case XmlNodeType.Element:
        //                if (reader.HasAttributes)
        //                {
        //                    if (!reader.GetAttribute(0).Contains("divCondi"))
        //                    {
        //                        returnValue = returnValue + reader.Name + ",";
        //                        if (string.IsNullOrEmpty(reader.GetAttribute(3)) || reader.GetAttribute(3).Equals("undefined"))
        //                        {
        //                            returnValue = returnValue + reader.GetAttribute(0) + ",";
        //                        }
        //                        else
        //                        {
        //                            returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(3) + "@" + reader.GetAttribute(2) + ",";
        //                        }
        //                    }
        //                    //returnValue = returnValue + reader.GetAttribute(0) + ",";
        //                }
        //                break;
        //        }
        //    }

        //    returnValue = returnValue.TrimEnd(':').Replace("Property,", "").TrimEnd(',');
        //    return returnValue.Split(',');
        //}

        private static string[] GetWorkFlowActionFromXml(string rawXml)
        {
            string returnValue = string.Empty;

            XmlReader reader = XmlReader.Create(new StringReader(rawXml));
            while (reader.Read())
            {
                switch (reader.NodeType)
                {
                    case XmlNodeType.Element:
                        if (reader.HasAttributes)
                        {
                            if (!reader.GetAttribute(0).Contains("divCondi"))
                            {
                                if (reader.GetAttribute(0).Contains("True") || reader.GetAttribute(0).Contains("False"))
                                {
                                    returnValue = returnValue + reader.GetAttribute(0) + ",";
                                }
                                else
                                {
                                    if (reader.AttributeCount < 2)
                                    {
                                        returnValue = returnValue + reader.GetAttribute(0) + ",";
                                    }
                                    else
                                    {
                                       returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(4) + ",";
                                    }
                                }
                            }
                        }
                        break;
                }
            }
            returnValue = returnValue.TrimEnd(':').TrimEnd(',');

            return returnValue.Split(',');
        }

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }

        public void RaiseMimixMgrAlert(ApplicationMimix.MIMIXManager mimixMgr, Server server)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.MimixManagerJob;
            if ((!string.IsNullOrEmpty(mimixMgr.System)) && (mimixMgr.System.ToLower().Contains("inactive")))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.Mimix_Manager_System;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " System Definition : (" + mimixMgr.SystemDefinition + "), Status : InActive";
            }
            else
            {
                bcmsexceptiontype = BcmsExceptionType.Mimix_Manager_System_Normal;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " System Definition : (" + mimixMgr.SystemDefinition + "), Status : Active";
            }
            
            RaiseMimixAlert(status, alertType, bcmsexceptiontype, message, "MimixManagerJobStatus");
        }

        public void RaiseMimixAlertAlert(ApplicationMimix.MIMIXAlerts mimixAlert, Server server)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.MimixAlertJob;
            if ((!string.IsNullOrEmpty(mimixAlert.NotificationCount)) && (mimixAlert.NotificationCount.ToLower() != "000"))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.Mimix_Alert_System;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Alert Notification Value is (" + mimixAlert.NotificationCount + ") ";
            }
            else
            {
                bcmsexceptiontype = BcmsExceptionType.Mimix_Alert_System_Normal;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Alert Notification Value is (" + mimixAlert.NotificationCount + ") ";
            }

            RaiseMimixAlert(status, alertType, bcmsexceptiontype, message, "MimixAlertJobStatus");
        }

        public void RaiseMimixHealthAlert(ApplicationMimix.MIMIXHealth mimixHealth, Server server)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.MimixHealthJob;
            if ((!string.IsNullOrEmpty(mimixHealth.SourceMgr)) && (mimixHealth.SourceMgr.ToLower() != "a") || (!string.IsNullOrEmpty(mimixHealth.SourceDB)) && (mimixHealth.SourceDB.ToLower() != "r") || (!string.IsNullOrEmpty(mimixHealth.SourceObj)) && (mimixHealth.SourceObj.ToLower() != "a"))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.Mimix_Health_System;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Datagroup (" + mimixHealth.DataGroup + ") Source Mgr: " + mimixHealth.SourceMgr + "Source DB: " + mimixHealth.SourceDB + " Source Obj: " + mimixHealth.SourceObj;
            }
            else if ((!string.IsNullOrEmpty(mimixHealth.TargetMgr)) && (mimixHealth.TargetMgr.ToLower() != "a") || (!string.IsNullOrEmpty(mimixHealth.TargetDB)) && (mimixHealth.TargetDB.ToLower() != "a") || (!string.IsNullOrEmpty(mimixHealth.TargetObj)) && (mimixHealth.TargetObj.ToLower() != "a"))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.Mimix_Health_System;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Datagroup (" + mimixHealth.DataGroup + ") Target Mgr: " + mimixHealth.SourceMgr + " Target DB: " + mimixHealth.SourceDB + " Target Obj: " + mimixHealth.SourceObj;
            }
            else
            {
                bcmsexceptiontype = BcmsExceptionType.Mimix_Health_System_Normal;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Datagroup (" + mimixHealth.DataGroup + ") is Healthy";
            }

            RaiseMimixAlert(status, alertType, bcmsexceptiontype, message, "MimixHealthJobStatus");
        }

        public void RaiseMimixAvailabilityAlert(ApplicationMimix.MIMIXAvilability mimixAval, Server server)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.MimixAvailabilityJob;
            if ((!string.IsNullOrEmpty(mimixAval.Status)) && (!mimixAval.Status.ToLower().Contains("no action required")))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.Mimix_Availability_System;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Status :"+ mimixAval.Status ;
            }           
            else
            {
                bcmsexceptiontype = BcmsExceptionType.Mimix_Availability_System_Normal;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Status :"+ mimixAval.Status ;
            }
            RaiseMimixAlert(status, alertType, bcmsexceptiontype, message, "MimixAvailabilityJob");
        }

        public void RaiseMimixDatalagAlert(ApplicationMimix.MIMIXDatalag mimixdatalag, Server server)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.MimixdatalagJob;
            if (!string.IsNullOrEmpty(mimixdatalag.DBSourceDateTime) && !string.IsNullOrEmpty(mimixdatalag.DBTargetDateTime))
            {
                if (mimixdatalag.DBSourceDateTime.ToLower() != mimixdatalag.DBTargetDateTime.ToLower())
                {
                    status = false;
                    bcmsexceptiontype = BcmsExceptionType.Mimix_Datalag_System;
                    message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + "Source Date Time(" + mimixdatalag.DBSourceDateTime + ")" + " Target Date Time(" + mimixdatalag.DBTargetDateTime + ")";
                }
                else
                {
                    bcmsexceptiontype = BcmsExceptionType.Mimix_Datalag_System_Normal;
                    message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + "Source Date Time(" + mimixdatalag.DBSourceDateTime + ")" + " Target Date Time(" + mimixdatalag.DBTargetDateTime + ")";
                }
                RaiseMimixAlert(status, alertType, bcmsexceptiontype, message, "MimixDatalagAlert");
            }
           
        }

        private void RaiseMimixAlert(bool AlertStatus, int alertType, BcmsExceptionType bcmsexceptiontype, string message, string Alertdetails)
        {
            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (AlertStatus)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));
                    ExceptionManager.Manage(new BcmsException(bcmsexceptiontype, message), Alertdetails, CurrentInfraObject.Id, CurrentInfraObject.Name);
                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));
                    ExceptionManager.Manage(new BcmsException(bcmsexceptiontype, message), Alertdetails, CurrentInfraObject.Id, CurrentInfraObject.Name);
                }
            }
        }

        #region IDisposable Members

        public void Dispose()
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
