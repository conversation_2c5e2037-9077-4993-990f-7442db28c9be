﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SnmpSharpNet;

namespace Bcms.ExceptionHandler
{
    public class SendSNMPTrap
    {

        public static void SendTrapV1(string sRecieverIP, int iRecieverPort, string sCommunity, string sOID, string sSenderIP,
             string sAsyncType, int iGeneric)
        {
            TrapAgent agent = new TrapAgent();
            VbCollection col = new VbCollection();

            col.Add(new Oid(sOID), new OctetString("UserName: " + Environment.UserName));
            agent.SendV1Trap(new IpAddress(sRecieverIP), iRecieverPort, sCommunity, new Oid(sOID), new IpAddress(sSenderIP),
                          iGeneric, 0, 13432, col);
        }


        public static void SendTrapV2c(string sRecieverIP, int iRecieverPort, string TrapText, string sCommunity, string sOID)
        {
            TrapAgent agent = new TrapAgent();
            VbCollection col = new VbCollection();

            col.Add(new Oid(sOID), new OctetString("TrapText:" + TrapText));
            agent.SendV2Trap(new IpAddress(sRecieverIP), iRecieverPort, sCommunity, 13433, new Oid(sOID), col);
        }


        public static void SendTrapV3()
        {
            TrapAgent agent = new TrapAgent();
            VbCollection col = new VbCollection();
            col.Add(new Oid("*******.*******.0"), new OctetString("Test string"));
            col.Add(new Oid("*******.*******.0"), new Oid("*******.*******"));
            col.Add(new Oid("*******.*******.0"), new TimeTicks(2324));
            col.Add(new Oid("*******.*******.0"), new OctetString("Milan"));

            agent.SendV3Trap(new IpAddress("localhost"), 162,
                 new byte[] { 0x00, 0x08, 0x02, 0x01, 0x20, 0x12, 0x14, 0xa0, 0xb1, 0xc2 },
                 1, 500, "mysecurityname", 13434, new Oid(".*******.*******.5"), col);
        }
    }
}
