﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using BCMS.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess;
using Bcms.Replication.Base;
using Bcms.Replication.Shared;
using Bcms.Replication.SqlServerNative;
using OracleDbSwitchoverwithDG;
using PEMCSRDF;
using PGlobalMirror;
using PHPUNIX;
using PSnapMirror;
using SqlServer2000dll;
using log4net;
using Bcms.Replication.OracleDataGuard;
using DNSManagement;
using Bcms.ExceptionHandler;
using LogshippingNative;
using Bcms.Core.FastCopy;
using System.Xml;
using PVMWare;
using BCMSExchangeClass;
using PDB2HADR;
using Jscape.Ssh;
using Bcms.Replication.FastCopyOracle;

namespace Bcms.Core.Client
{
    public class BcmsWorkFlowClient : IDisposable
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(BcmsWorkFlowClient));

        #region workflow Variables

        private Server _wfServer;
        private Server _wfTargetServer;
        private GlobalMirror _wfGlobalMirror;
        private DatabaseBase _wfDatabase;
        private SSHInfo _wfsshServer;
        private SSHInfo _wfTargetsshServer;
        private SSHInfo _wfDB2Server;
        private StorageInfo _wfstorageInfo;
        private GMOperations _wfGmOperations;
        private Server _wfdscliserver;
        private DSCLIInfo _wfPRdscliInfo;
        private DSCLIInfo _wfDRdscliInfo;
        private Server _wfhmcPRInfo;
        private Server _wfhmcDRInfo;
        private bool _isAbort;
        private bool _isDisposed;
        private SSHInfo _wfsshInfo;
        private SSHInfo _wfsshInfoPr;
        private SSHInfo _wfsshInfoDr;
        private DROperations _wfdrOperations;
        private GroupLuns _wfgroupLuns;
        private SymCLI _symCli;
        private HPServer _hpServer;
        private ReplicationBase _replicationBase;
        private string _wfSudoUser = string.Empty;
        private string _wfOracleSID = string.Empty;
        private string _wfControlFilePath = string.Empty;
        private string _wfScriptFilePath = string.Empty;
        private string _wfStandbyControlFile = string.Empty;
        private string _wfReverseSessionId = string.Empty;
        private string _wfScpFile = string.Empty;
        private string _wfSession = string.Empty;
        private string _wfJobQueue = string.Empty;
        private string _wfVGName = string.Empty;
        private string _wfCommand = string.Empty;
        private string _wfTempFilePath = string.Empty;
        private string _wfVmPath = string.Empty;
        private int _wfPRorDR = 0;
        private int _wfSessionAddorRemove = 0;
        private string[] _wfMountPoints;
        private string[] _wfLundD;
        private LunInfo _wflunInfo;
        private PrimaryHost _primaryHost;
        private DRHost _drHost;
        private DRHost _drSingleHost;
        private int _wfFastCopy = 0;
        private string _wfListener = string.Empty;
        private int _wfDNSServer = 0;
        private string _wfExistingHost = string.Empty;
        private string _wfExistingIP = string.Empty;
        private string _wfNewHost = string.Empty;
        private string _wfNewIp = string.Empty;
        private string _wfDomainName = string.Empty;
        private string _wfLocalMountPoints = string.Empty;
        private string _wfRemoteMountPoints = string.Empty;
        private string _wfHostName = string.Empty;
        private int _wfIsUseSudo = 0;
        private List<string> _wfCheckOutput = new List<string>();
        private int _wfIsReturn = 0;
        private string _wfFastCopyPath = string.Empty;
        private string _wfSourceFile = string.Empty;
        private List<string> _wfSourceFolder = new List<string>();
        private List<string> _wfTargetFolder = new List<string>();
        private List<string> _wfHPMountPoint = new List<string>();
        private List<string> _wfHPFileSystem = new List<string>();
        private string _wfDiskGroup = string.Empty;
        private string _wfDiskName = string.Empty;
        private string _wfLuns = string.Empty;
        private string _wfRsynPath = string.Empty;
        private string _wfTargetFile = string.Empty;
        private string _wfMachineName = string.Empty;
        private string _wfSnapShotName = string.Empty;
        private string _wfTimeOut = string.Empty;
        private string _wfVirtualMachine = string.Empty;
        private string _wfVolume = string.Empty;
        private string _wfDestinationPath = string.Empty;
        private SnapMirror _wfsnapMirror;
        private int _wfReplicationId = 0;
        private string _wfTargetMachine = string.Empty;
        private string _wfOriginalText = string.Empty;
        private string _wfNewText = string.Empty;
        private string _wfRouterConfiguration = string.Empty;
        private string _wfInterfacePassword = string.Empty;
        private string _wfDeviceGroup = string.Empty;
        private string _wfFileSystem = string.Empty;
        private int _wfWaitTime = 0;
        private string _wfApplicationName = string.Empty;
        private string _wfMapFile = string.Empty;
        private string _wfTask = string.Empty;
        private int _wfTargetDatabase = 0;
        private DatabaseBase _wfTargetServerDatabase;
        private NetAppInstance _wfappInstance;
        private Server _server;
        private DataGuardClient _client;
        private DataGuardClient _prclient;
        private DataGuardClient _drclient;

        private FastCopyOracleClient _fastCopyOracle;

        private DROperationResult _drOperationResult;
        private DNSServer _dnsServer;
        private int _workflowActionId;
        private ParallelWorkflowActionResult _parallelWorkflowActionResult;
        private DROperation _currentworkflowDRoperation;
        private ParallelDROperation _currentParallelDRoperation;
        private ActionExecution _currentactionExecution;
        private WorkflowActionType _workflowActionType = WorkflowActionType.None;
        private ConditionalOperationType _conditionalAction = ConditionalOperationType.None;
        private ConditionalOperationType _conditionalParallelAction = ConditionalOperationType.None;
        private DROperation _drOperation;
        private DatabaseBase _database;
        private SCRConfig _scrconfig = new SCRConfig();
        private SSHInfo _sshInfo;
        private Server _dscliserver;
        private DSCLIInfo _dscliPRInfo;
        private DSCLIInfo _dscliDRInfo;
        private Server _hmcPRInfo;
        private Server _hmcDRInfo;
        private GlobalMirror _globalMirror;
        private GlobalMirrorMonitor _globalMirrorReplication;
        private List<SqlLog> _applyLogList;
        private string _logName;
        public Dictionary<int, string> JobExecStatus = new Dictionary<int, string>()
         {
	        {0,"Not_idle_suspended"},
            {1, "Executing"},
	        {2, "Waiting"},
	        {3, "Between_retries"},
	        {4, "Idle"},
            {5, "Suspended"},
            {6, "Undescribe"},
            {7, "Completed"}
         };



        #endregion


        #region Workflow properties

        public Group CurrentGroup { get; set; }

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentGroup != null)
                    {
                        _server = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }

        public DatabaseBase CurrentDatabase
        {

            get
            {
                if (_database == null)
                {
                    if (CurrentGroup != null)
                    {
                        _database = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
                    }
                }
                return _database;
            }

            set
            {
                _database = value;
            }
        }

        public SCRConfig CurrentSCRConfig
        {
            get
            {
                if (CurrentGroup != null)
                {
                    if (_scrconfig == null)
                    {
                        _scrconfig = BuildSCRGroupEntity();
                    }
                }
                return _scrconfig;
            }
            set { _scrconfig = value; }
        }

        public GlobalMirror CurrentGlobalMirror
        {
            get
            {
                if (_globalMirror == null)
                {
                    if (CurrentGroup != null)
                    {
                        _globalMirror = GlobalMirrorDataAccess.GetGlobalMirrorById(CurrentGroup.PRReplicationId);
                    }
                }

                return _globalMirror;
            }
            set
            {
                _globalMirror = value;

            }
        }

        public Server CurrentPRHMCInfo
        {
            get
            {
                if (_hmcPRInfo == null)
                {
                    if (CurrentGlobalMirror != null)
                    {
                        _hmcPRInfo = ServerDataAccess.GetServersById(CurrentGlobalMirror.HMCPRServerId);
                    }
                }
                return _hmcPRInfo;
            }
            set
            {
                if (value != null)
                {
                    _hmcPRInfo = value;
                }
            }

        }

        public Server CurrentDRHMCInfo
        {
            get
            {
                if (_hmcDRInfo == null)
                {
                    if (CurrentGlobalMirror != null)
                    {
                        _hmcDRInfo = ServerDataAccess.GetServersById(CurrentGlobalMirror.HMCDRServerId);
                    }
                }
                return _hmcDRInfo;
            }
            set
            {
                if (value != null)
                {
                    _hmcDRInfo = value;
                }
            }

        }

        public SSHInfo CurrentSSHInfo
        {
            get
            {
                if (_sshInfo == null)
                {
                    if (CurrentDSCLIServer != null)
                    {
                        _sshInfo = new SSHInfo(CurrentDSCLIServer.PRIPAddress, CurrentDSCLIServer.PRUserName, CurrentDSCLIServer.PRPassword);
                    }
                }

                return _sshInfo;
            }

        }

        public DSCLIInfo CurrentPRDSCLI
        {
            get
            {
                if (_dscliPRInfo == null)
                {
                    if (CurrentPRHMCInfo != null)
                    {
                        _dscliPRInfo = new DSCLIInfo(CurrentPRHMCInfo.PRIPAddress, CurrentPRHMCInfo.PRUserName, CurrentPRHMCInfo.PRPassword, @"/opt/ibm/dscli/dscli");
                    }
                }
                return _dscliPRInfo;
            }
        }

        public DSCLIInfo CurrentDRDSCLI
        {
            get
            {
                if (_dscliDRInfo == null)
                {
                    if (CurrentDRHMCInfo != null)
                    {
                        _dscliDRInfo = new DSCLIInfo(CurrentDRHMCInfo.PRIPAddress, CurrentDRHMCInfo.PRUserName, CurrentDRHMCInfo.PRPassword, @"/opt/ibm/dscli/dscli");
                    }
                }

                return _dscliDRInfo;
            }
        }

        public GlobalMirrorMonitor CurrentGlobalMirrorMonitor
        {
            get
            {
                if (_globalMirrorReplication == null)
                {
                    if (CurrentGlobalMirror != null)
                    {
                        _globalMirrorReplication = GlobalMirrorMonitorDataAccess.GetCurrentStatus(CurrentGlobalMirror.PRStorageImageId);
                    }
                }
                return _globalMirrorReplication;
            }

            set
            {
                _globalMirrorReplication = value;
            }
        }

        public Server CurrentDSCLIServer
        {
            get
            {
                if (_dscliserver == null)
                {
                    if (CurrentGlobalMirror != null)
                    {
                        _dscliserver = ServerDataAccess.GetServersById(CurrentGlobalMirror.DSCLIServerId);
                    }
                }
                return _dscliserver;
            }
            set
            {
                if (value != null)
                {
                    _dscliserver = value;
                }
            }
        }

        //public int CurrentWorkFlowActionId
        //{
        //    get { return _workflowActionId; }

        //    set { _workflowActionId = value; }
        //}

        public string CurrentWorkflowActionName { get; set; }

        public int CurrentWorkflowActionId { get; set; }

        public string PRSqenceNo { get; set; }

        public string DRSqenceNo { get; set; }

        public DROperationType CurrentDROperationType { get; set; }

        public List<SqlLog> ApplyLogList
        {
            get { return _applyLogList ?? (_applyLogList = new List<SqlLog>()); }

            set { _applyLogList = value; }
        }

        public string CurrentLogFileName
        {
            get { return _logName; }
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    throw new ArgumentException("Log file name may not be empty");
                }
                _logName = value;
            }
        }

        public bool IsWindows
        {
            get
            {
                if (WFServer != null)
                {
                    return WFServer.PROSType.Contains("Windows");
                }
                return false;
            }
        }

        public bool IsKilled { get; set; }

        public bool IsGenerate { get; set; }

        public DataGuardClient DRClient
        {
            get { return _drclient ?? (_drclient = new DataGuardClient(DRSingleHost, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.DROracleSID, IsWindows)); }


            set { _drclient = value; }
        }

        public DNSServer CurrentDNSServer
        {
            get
            {
                if (_dnsServer == null)
                {
                    if (_wfDNSServer != 0)
                    {
                        Server server = ServerDataAccess.GetServersById(_wfDNSServer);

                        if (server != null)
                        {
                            _dnsServer = new DNSServer(server.PRIPAddress, server.PRUserName, server.PRPassword);
                        }
                    }
                }
                return _dnsServer;

            }
        }

        public DataGuardClient PrimaryClient
        {
            get { return _prclient ?? (_prclient = new DataGuardClient(PrimaryHost, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, IsWindows)); }
            set { _prclient = value; }

        }

        public DataGuardClient Client
        {
            get
            {
                return _client ?? (_client = new DataGuardClient(PrimaryHost, DRHost, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, WFDatabase.DatabaseOracle.DROracleSID, IsWindows));
            }

            set { _client = value; }
        }

        public FastCopyOracleClient FastCopyOracleClient
        {
            get { return _fastCopyOracle ?? (_fastCopyOracle = new FastCopyOracleClient(PrimaryHost, DRHost)); }

            set { _fastCopyOracle = value; }
        }

        public DRHost DRHost
        {
            get
            {
                if (_drHost == null)
                {
                    if (WFTargetServer != null)
                    {
                        _drHost = new DRHost(WFTargetServer.PRIPAddress, WFTargetServer.PRUserName, WFTargetServer.PRPassword);
                    }

                }
                return _drHost;
            }
        }

        public DRHost DRSingleHost
        {
            get
            {
                if (_drSingleHost == null)
                {
                    if (WFServer != null)
                    {
                        // _drSingleHost = new DRHost(WFServer.DRIPAddress, WFServer.DRUserName, WFServer.DRPassword);
                        _drSingleHost = new DRHost(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                    }

                }
                return _drSingleHost;
            }
        }

        public PrimaryHost PrimaryHost
        {
            get
            {
                if (_primaryHost == null)
                {
                    if (WFServer != null)
                    {
                        _primaryHost = new PrimaryHost(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                    }

                }
                return _primaryHost;
            }
        }

        public NetAppInstance WFNetAppInstance
        {
            get
            {
                if (_wfappInstance == null)
                {
                    if (WFSnapMirror != null)
                    {
                        Server server = _wfPRorDR == 1
                                            ? ServerDataAccess.GetServersById(WFSnapMirror.MCPRServerId)
                                            : ServerDataAccess.GetServersById(WFSnapMirror.MCDRServerId);

                        if (server != null)
                        {
                            _wfappInstance = new NetAppInstance(server.PRIPAddress, server.PRUserName, server.PRPassword);
                        }

                    }
                }
                return _wfappInstance;
            }
        }

        public SnapMirror WFSnapMirror
        {
            get
            {
                if (_wfsnapMirror == null)
                {
                    if (_wfReplicationId != 0)
                    {
                        _wfsnapMirror = SnapMirrorDataAccess.GetSnapMirrorById(_wfReplicationId);
                    }
                }

                return _wfsnapMirror;
            }
            set
            {
                _wfsnapMirror = value;

            }
        }

        public string WFVolume
        {
            get
            {
                if (string.IsNullOrEmpty(_wfVolume))
                {
                    if (WFSnapMirror != null)
                    {
                        _wfVolume = _wfPRorDR == 1 ? WFSnapMirror.PRVolume : WFSnapMirror.DRVolume;
                    }
                }

                return _wfVolume;
            }
        }

        public StorageInfo WFStorageInfo
        {
            get
            {
                if (_wfstorageInfo == null)
                {
                    if (WFGlobalMirror != null)
                    {
                        _wfstorageInfo = new StorageInfo(WFGlobalMirror.PRStorageImageId, WFGlobalMirror.DRStorageImageId);
                    }
                }
                return _wfstorageInfo;
            }

            set
            {
                _wfstorageInfo = value;
            }

        }

        public GlobalMirror WFGlobalMirror
        {
            get
            {
                return _wfGlobalMirror ?? (_wfGlobalMirror = new GlobalMirror());
            }
            set
            {
                _wfGlobalMirror = value;

            }
        }

        public Server WFServer
        {
            get { return _wfServer ?? (_wfServer = new Server()); }

            set
            {
                if (value != null)
                {
                    _wfServer = value;
                }
                else
                {
                    Logger.ErrorFormat("Argument Null exception occure while fetching wf server values");
                }
            }

        }

        public Server WFTargetServer
        {
            get { return _wfTargetServer ?? (_wfTargetServer = new Server()); }

            set
            {
                if (value != null)
                {
                    _wfTargetServer = value;
                }
                else
                {
                    Logger.ErrorFormat("Argument Null exception occure while fetching wf Target server values");
                }
            }

        }

        public SSHInfo WFSSHServer
        {
            get
            {
                if (_wfsshServer == null)
                {
                    if (WFServer != null)
                    {
                        _wfsshServer = new SSHInfo(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                    }
                }
                return _wfsshServer;
            }

            set
            {
                _wfsshServer = value;
            }


        }

        public SSHInfo WFTARGETSSHServer
        {
            get
            {
                if (_wfTargetsshServer == null)
                {
                    if (WFTargetServer != null)
                    {
                        _wfTargetsshServer = new SSHInfo(WFTargetServer.PRIPAddress, WFTargetServer.PRUserName, WFTargetServer.PRPassword);
                    }
                }
                return _wfTargetsshServer;
            }

            set
            {
                _wfTargetsshServer = value;
            }


        }

        public SSHInfo WFDB2Server
        {
            get
            {
                if (_wfDB2Server == null)
                {
                    if (WFTargetServer != null)
                    {
                        _wfTargetsshServer = new SSHInfo(WFTargetServer.PRIPAddress, WFTargetServer.PRUserName, WFTargetServer.PRPassword);
                    }
                }
                return _wfTargetsshServer;
            }

            set
            {
                _wfTargetsshServer = value;
            }


        }

        public DatabaseBase WFDatabase
        {
            get { return _wfDatabase ?? (_wfDatabase = new DatabaseBase()); }

            set
            {
                if (value != null)
                {
                    _wfDatabase = value;
                }
                else
                {
                    Logger.ErrorFormat("Argument Null exception occure while fetching wf Database values");
                }
            }

        }

        public string WFSudoUser
        {
            get { return _wfSudoUser; }

            set { _wfSudoUser = value; }
        }

        public string WFOracleSID
        {
            get { return _wfOracleSID; }

            set { _wfOracleSID = value; }
        }

        public string WFControlFilePath
        {
            get { return _wfControlFilePath; }

            set { _wfControlFilePath = value; }
        }

        public string WFVGName
        {
            get { return _wfVGName; }

            set { _wfVGName = value; }
        }

        public string WFScriptFilePath
        {
            get { return _wfScriptFilePath; }

            set { _wfScriptFilePath = value; }
        }

        public string WFStandbyControlFile
        {
            get { return _wfStandbyControlFile; }

            set { _wfStandbyControlFile = value; }
        }

        public GMOperations WFGMOperation
        {
            get
            {
                if (_wfGmOperations == null)
                {
                    if (WFGlobalMirror != null && WFPRDSCLI != null && WFDRDSCLI != null && WFSSHInfo != null && CurrentGroup.Name != string.Empty)
                    {
                        _wfGmOperations = new GMOperations(WFStorageInfo, WFPRDSCLI, WFDRDSCLI, WFSSHInfo, CurrentGroup.Name);
                    }
                }
                return _wfGmOperations;
            }
        }

        public Server WFPRHMCInfo
        {
            get
            {
                if (_wfhmcPRInfo == null)
                {
                    if (WFGlobalMirror != null)
                    {
                        _wfhmcPRInfo = ServerDataAccess.GetServersById(WFGlobalMirror.HMCPRServerId);
                    }
                }
                return _wfhmcPRInfo;
            }
            set
            {
                if (value != null)
                {
                    _wfhmcPRInfo = value;
                }
            }

        }

        public Server WFDRHMCInfo
        {
            get
            {
                if (_wfhmcDRInfo == null)
                {
                    if (WFGlobalMirror != null)
                    {
                        _wfhmcDRInfo = ServerDataAccess.GetServersById(WFGlobalMirror.HMCDRServerId);
                    }
                }
                return _wfhmcDRInfo;
            }
            set
            {
                if (value != null)
                {
                    _wfhmcDRInfo = value;
                }
            }

        }

        public SSHInfo WFSSHInfo
        {
            get
            {
                if (_wfsshInfo == null)
                {
                    if (WFDSCLIServer != null)
                    {
                        _wfsshInfo = new SSHInfo(WFDSCLIServer.PRIPAddress, WFDSCLIServer.PRUserName, WFDSCLIServer.PRPassword);
                    }
                }

                return _wfsshInfo;
            }

        }

        public SymCLI WFSymCLi
        {
            get
            {
                if (_symCli == null)
                {
                    if (WFServer != null)
                    {
                        _symCli = new SymCLI(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                    }
                }

                return _symCli;
            }

        }

        public HPServer WFHPServer
        {
            get
            {
                if (_hpServer == null)
                {
                    if (WFServer != null)
                    {
                        _hpServer = new HPServer(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword, WFServer.PRSudoUser);
                    }
                }

                return _hpServer;
            }
        }

        public DSCLIInfo WFPRDSCLI
        {
            get
            {
                if (_wfPRdscliInfo == null)
                {
                    if (WFPRHMCInfo != null)
                    {
                        _wfPRdscliInfo = new DSCLIInfo(WFPRHMCInfo.PRIPAddress, WFPRHMCInfo.PRUserName, WFPRHMCInfo.PRPassword, @"/opt/ibm/dscli/dscli");
                    }
                }
                return _wfPRdscliInfo;
            }
        }

        public DSCLIInfo WFDRDSCLI
        {
            get
            {
                if (_wfDRdscliInfo == null)
                {
                    if (WFDRHMCInfo != null)
                    {
                        _wfDRdscliInfo = new DSCLIInfo(WFDRHMCInfo.PRIPAddress, WFDRHMCInfo.PRUserName, WFDRHMCInfo.PRPassword, @"/opt/ibm/dscli/dscli");
                    }
                }

                return _wfDRdscliInfo;
            }
        }

        public Server WFDSCLIServer
        {
            get
            {
                if (_wfdscliserver == null)
                {
                    if (WFGlobalMirror != null)
                    {
                        _wfdscliserver = ServerDataAccess.GetServersById(WFGlobalMirror.DSCLIServerId);
                    }
                }
                return _wfdscliserver;
            }
            set
            {
                if (value != null)
                {
                    _wfdscliserver = value;
                }
            }
        }

        public LunInfo WFLunInfo
        {
            get
            {
                if (_wflunInfo == null)
                {
                    if (CurrentGroup != null)
                    {

                        var lunsList = GlobalMirrorLunsDataAccess.GetGlobalMirrorLunsByGlobalMirrorId(CurrentGroup.PRReplicationId);

                        if (lunsList != null)
                        {
                            var lunA = new string[lunsList.Count];
                            var lunB = new string[lunsList.Count];
                            var lunC = new string[lunsList.Count];
                            var lunD = new string[lunsList.Count];
                            var lunE = new string[lunsList.Count];
                            var lunF = new string[lunsList.Count];

                            BindLuns(lunsList, lunA, lunB, lunC, lunD, lunE, lunF);

                            if (lunA.Length > 0 && lunB.Length > 0 && lunC.Length > 0 && lunD.Length > 0)
                            {
                                _wflunInfo = new LunInfo(lunA, lunB, lunC, lunD, lunE, lunF);
                            }
                        }

                    }
                }
                return _wflunInfo;
            }
        }

        public GroupLuns WFGroupLuns
        {
            get
            {
                if (_wfgroupLuns == null)
                {
                    if (CurrentGroup != null)
                    {
                        _wfgroupLuns = GroupLunsDataAccess.GetGroupLunsByGroupId(CurrentGroup.Id);
                    }
                }
                return _wfgroupLuns;
            }
        }

        public DROperations WFDROperation
        {
            get
            {
                if (_wfdrOperations == null)
                {
                    if (WFSSHInfoPR != null && WFSSHInfoDR != null && WFGroupLuns != null && CurrentGroup.Name != string.Empty)
                    {
                        _wfdrOperations = new DROperations(WFSSHInfoPR, WFSSHInfoDR, WFGroupLuns.AGroup, WFGroupLuns.AMount, CurrentGroup.Name);

                    }
                }
                return _wfdrOperations;
            }
        }

        public SSHInfo WFSSHInfoPR
        {
            get
            {
                if (_wfsshInfoPr == null)
                {
                    if (WFServer != null)
                    {
                        _wfsshInfoPr = new SSHInfo(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                    }
                }

                return _wfsshInfoPr;
            }
        }

        public SSHInfo WFSSHInfoDR
        {
            get
            {
                if (_wfsshInfoDr == null)
                {
                    if (WFServer != null)
                    {
                        _wfsshInfoDr = new SSHInfo(WFServer.DRIPAddress, WFServer.DRUserName, WFServer.DRPassword);
                    }
                }

                return _wfsshInfoDr;
            }
        }

        public ParallelGroupWorkflow CurrentParallelGroupWorkflow { get; set; }

        public int TotalActionCount { get; set; }

        public int CompletedActionCount { get; set; }

        public ConditionalOperationType CurrentConditionalOperationType { get; set; }

        public ConditionalOperationType CurrentParallelConditionalOperationType { get; set; }

        public DROperation CurrentWorkflowDROperation
        {
            get { return _currentworkflowDRoperation ?? (_currentworkflowDRoperation = new DROperation()); }
            set { _currentworkflowDRoperation = value; }
        }

        public ParallelDROperation CurrentParallelDROperation
        {
            get { return _currentParallelDRoperation ?? (_currentParallelDRoperation = new ParallelDROperation()); }
            set { _currentParallelDRoperation = value; }
        }

        public ActionExecution CurrentActionExecution
        {
            get { return _currentactionExecution ?? (_currentactionExecution = new ActionExecution()); }
            set { _currentactionExecution = value; }
        }

        public DROperationResult CurrentDROperationResult
        {
            get { return _drOperationResult ?? (_drOperationResult = new DROperationResult()); }
            set { _drOperationResult = value; }
        }

        public ParallelWorkflowActionResult ParallelWorkflowActionResult
        {
            get { return _parallelWorkflowActionResult ?? (_parallelWorkflowActionResult = new ParallelWorkflowActionResult()); }
            set { _parallelWorkflowActionResult = value; }
        }

        public WorkflowActionType CurrentWorkflowActionType
        {
            get { return _workflowActionType; }
            set { _workflowActionType = value; }
        }

        public ConditionalOperationType CurrentConditionalAction
        {
            get { return _conditionalAction; }
            set { _conditionalAction = value; }
        }

        public ConditionalOperationType CurrentParallelConditionalAction
        {
            get { return _conditionalParallelAction; }

            set { _conditionalParallelAction = value; }
        }

        public DROperation CurrentWFDROperation
        {
            get { return _drOperation ?? (_drOperation = new DROperation()); }

            set { _drOperation = value; }
        }

        #endregion

        public BcmsWorkFlowClient(ParallelGroupWorkflow parallel)
        {
            CurrentParallelGroupWorkflow = parallel;

            CurrentGroup = GroupDataAccess.GetGroupById(parallel.GroupId);
        }

        public BcmsWorkFlowClient(Group group)
        {
            CurrentGroup = group;
        }

        public BcmsWorkFlowClient()
        {

        }

        public DateTime CurrentActionStartTime { get; set; }

        public DateTime CurrentActionEndTime { get; set; }

        public DateTime CurrentWorkflowStartTime { get; set; }

        public ParallelDROperation CurrentParallelDroperation
        {
            get
            {
                return _currentParallelDRoperation ??
                       (_currentParallelDRoperation = ParallelDROperationDataAccess.GetByParallelLastId());
            }
            set
            {
                _currentParallelDRoperation = value;
            }
        }




        #region Public Method

        public void VerifyWorkFlowAction(WorkflowActionType workflowActionType, ref StringBuilder sp)
        {
            var str = new[] { "Not Exist" };
            try
            {
                switch (workflowActionType)
                {
                    case WorkflowActionType.PauseGlobalMirror:
                        sp.Append(Environment.NewLine
                         + Environment.NewLine
                         + "PAUSE GLOBAL MIRROR "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "PRLSSID:" + WFGlobalMirror.PRLSSID
                         + Environment.NewLine
                         + "SESSION ID :" + WFGlobalMirror.SessionId
                         + Environment.NewLine
                         + "nPRDR ID : 1");
                        break;


                    case WorkflowActionType.ResumeGlobalMirror:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "RESUME GLOBAL MIRROR "
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "PRLSSID:" + WFGlobalMirror.PRLSSID
                        + Environment.NewLine
                        + "SESSION ID :" + WFGlobalMirror.SessionId
                        + Environment.NewLine
                        + "nPRDR ID : 1"
                        );

                        break;
                    case WorkflowActionType.PerformNormalReplication:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                          + "DATA SYNCHRONIZATION "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PRSTORAGEIMAGEID:" + WFStorageInfo.PRStorageImageId
                          + Environment.NewLine
                          + "DRSTORAGEIMAGEID:" + WFStorageInfo.DRStorageImageId
                          + Environment.NewLine
                          + "HMCDR:" + WFDRDSCLI.DSCLIHost
                          + Environment.NewLine
                          + "DSCLI:" + WFSSHInfo.SSHHost
                          + Environment.NewLine
                          + "PRLSSID:" + WFGlobalMirror.PRLSSID
                          + Environment.NewLine
                          + "SESSION ID :" + WFGlobalMirror.SessionId
                          );

                        break;
                    case WorkflowActionType.PausePPRC:

                        break;
                    case WorkflowActionType.ResumePPRC:

                        break;
                    case WorkflowActionType.FailBackPPRC:

                        break;
                    case WorkflowActionType.MKFlashBC:

                        break;
                    case WorkflowActionType.MKFlashBD:

                        break;
                    case WorkflowActionType.RMFlashBC:

                        break;
                    case WorkflowActionType.RMFlashBD:

                        break;
                    case WorkflowActionType.FailOverPPRC:

                        break;
                    case WorkflowActionType.ApplyIncrementalLogs:
                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "APPLY LOGS"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                       + Environment.NewLine
                       + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                       + Environment.NewLine
                       + "DATABASE NAME:" + CurrentDatabase.DatabaseOracle.PROracleSID
                       + Environment.NewLine
                       + "SUDO USER :" + WFServer.DRSudoUser
                       );
                        break;

                    case WorkflowActionType.MountVG:
                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "MOUNT VG"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "PRSTORAGEIMAGEID:" + WFStorageInfo.PRStorageImageId
                         + Environment.NewLine
                         + "DRSTORAGEIMAGEID:" + WFStorageInfo.DRStorageImageId
                         + Environment.NewLine
                         + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                         + Environment.NewLine
                         + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                         + Environment.NewLine
                         + "VGNAME:" + WFGroupLuns.AGroup
                         + Environment.NewLine
                         + "MOUNT POINT :" + WFGroupLuns.AMount
                         + Environment.NewLine
                         + "LOG INFO : " + string.Join(", ", WFLunInfo.LunD)
                         + Environment.NewLine
                         + "VGMOUNT : 2"
                         );

                        break;

                    case WorkflowActionType.UnMountVG:
                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "UNMOUNT VG"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "VG NAME:" + WFDROperation.VGName
                       + Environment.NewLine
                       + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                       + Environment.NewLine
                       + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                       + Environment.NewLine
                       + "MOUNT POINT:" + WFDROperation.MountPoint

                       );

                        break;

                    case WorkflowActionType.ShutDB:

                        sp.Append(
                           Environment.NewLine
                           + Environment.NewLine
                           + "SHUT DOWN DB "
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Sudo User :" + WFServer.PRSudoUser
                           + Environment.NewLine
                           + "Oracle SID :" + WFDatabase.DatabaseSql.PRDatabaseSID
                           );


                        break;

                    case WorkflowActionType.SwitchOver:

                        sp.Append(
                           Environment.NewLine
                           + Environment.NewLine
                           + "SWITCH OVER :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR STORAGE IMAGE ID :" + WFGMOperation.PRStorageImageId
                           + Environment.NewLine
                           + "DR STORAGE IMAGE ID :" + WFGMOperation.DRStorageImageId
                           + Environment.NewLine
                           + "PR DSCLI HOST :" + WFGMOperation.PRDSCLIHost
                           + Environment.NewLine
                           + "DR DSCLI HOST :" + WFGMOperation.DRDSCLIHost
                           + Environment.NewLine
                           + "SSH HOST :" + WFGMOperation.SSHHost
                           + Environment.NewLine
                           + "nLSS ID :" + WFGlobalMirror.PRLSSID
                           + Environment.NewLine
                           + "SESSION ID :" + WFGlobalMirror.SessionId
                           + Environment.NewLine
                           + "NEW SESSION ID :" + _wfReverseSessionId
                           + Environment.NewLine
                          + "LunA :" + string.Join(", ", WFLunInfo.LunA ?? str)
                          + Environment.NewLine
                          + "LunAB :" + string.Join(", ", WFLunInfo.LunAB ?? str)
                          + Environment.NewLine
                          + "LunAE:" + string.Join(", ", WFLunInfo.LunAE ?? str)
                          + Environment.NewLine
                          + "LunAF :" + string.Join(", ", WFLunInfo.LunAF ?? str)
                          + Environment.NewLine
                          + "LunB :" + string.Join(", ", WFLunInfo.LunB ?? str)
                          + Environment.NewLine
                          + "LunBA :" + string.Join(", ", WFLunInfo.LunBA ?? str)
                          + Environment.NewLine
                          + "LunBC :" + string.Join(", ", WFLunInfo.LunBC ?? str)
                          + Environment.NewLine
                          + "LunBD:" + string.Join(", ", WFLunInfo.LunBD ?? str)
                          + Environment.NewLine
                          + "LunBF :" + string.Join(", ", WFLunInfo.LunBF ?? str)
                          + Environment.NewLine
                          + "LunC :" + string.Join(", ", WFLunInfo.LunC ?? str)
                          + Environment.NewLine
                          + "LunD :" + string.Join(", ", WFLunInfo.LunD ?? str)
                          + Environment.NewLine
                          + "LunE :" + string.Join(", ", WFLunInfo.LunE ?? str)
                          + Environment.NewLine
                          + "LunF :" + string.Join(", ", WFLunInfo.LunF ?? str)
                          );

                        break;
                    case WorkflowActionType.SwitchBack:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                          + "SWITCH BACK :"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR STORAGE IMAGE ID :" + WFGMOperation.PRStorageImageId
                          + Environment.NewLine
                          + "DR STORAGE IMAGE ID :" + WFGMOperation.DRStorageImageId
                          + Environment.NewLine
                          + "PR DSCLI HOST :" + WFGMOperation.PRDSCLIHost
                          + Environment.NewLine
                          + "DR DSCLI HOST :" + WFGMOperation.DRDSCLIHost
                          + Environment.NewLine
                          + "SSH HOST :" + WFGMOperation.SSHHost
                          + Environment.NewLine
                          + "nLSS ID :" + WFGlobalMirror.PRLSSID
                          + Environment.NewLine
                          + "SESSION ID :" + WFGlobalMirror.SessionId
                          + Environment.NewLine
                          + "NEW SESSION ID :" + _wfReverseSessionId
                          + Environment.NewLine
                          + "LunA :" + string.Join(", ", WFLunInfo.LunA ?? str)
                          + Environment.NewLine
                          + "LunAB :" + string.Join(", ", WFLunInfo.LunAB ?? str)
                          + Environment.NewLine
                          + "LunAE:" + string.Join(", ", WFLunInfo.LunAE ?? str)
                          + Environment.NewLine
                          + "LunAF :" + string.Join(", ", WFLunInfo.LunAF ?? str)
                          + Environment.NewLine
                          + "LunB :" + string.Join(", ", WFLunInfo.LunB ?? str)
                          + Environment.NewLine
                          + "LunBA :" + string.Join(", ", WFLunInfo.LunBA ?? str)
                          + Environment.NewLine
                          + "LunBC :" + string.Join(", ", WFLunInfo.LunBC ?? str)
                          + Environment.NewLine
                          + "LunBD:" + string.Join(", ", WFLunInfo.LunBD ?? str)
                          + Environment.NewLine
                          + "LunBF :" + string.Join(", ", WFLunInfo.LunBF ?? str)
                          + Environment.NewLine
                          + "LunC :" + string.Join(", ", WFLunInfo.LunC ?? str)
                          + Environment.NewLine
                          + "LunD :" + string.Join(", ", WFLunInfo.LunD ?? str)
                          + Environment.NewLine
                          + "LunE :" + string.Join(", ", WFLunInfo.LunE ?? str)
                          + Environment.NewLine
                          + "LunF :" + string.Join(", ", WFLunInfo.LunF ?? str)
                          );

                        break;

                    case WorkflowActionType.WinDGCheckUserStatus:
                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {

                                string usercount = SwitchoverwithDG.GetActiveUserCount(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword);

                                if (Convert.ToInt32(usercount) > 0)
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAActiveUserFound, usercount + "Active user (" + usercount + ") found logged in status in Primary Server (" + _wfServer.PRIPAddress + ")");
                                }
                                Logger.DebugFormat("VERIFY USER STATUS : Primary Database ({0}) user status verified successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get Active user count", exc);
                            }

                        }


                        break;

                    case WorkflowActionType.DGConnector:

                        break;
                    case WorkflowActionType.DGVerifyDBModeAndRole:

                        break;
                    case WorkflowActionType.DGCheckUserStatus:

                        break;
                    case WorkflowActionType.DGJobStatus:

                        break;
                    case WorkflowActionType.DGVerifyMaxSequenceNumber:

                        break;
                    case WorkflowActionType.DGSwitchPrimaryToStandBy:

                        break;
                    case WorkflowActionType.DGSwitchStandByToPrimary:

                        break;
                    case WorkflowActionType.DGShutDownPrimary:

                        break;
                    case WorkflowActionType.DGMountStandBy:

                        break;
                    case WorkflowActionType.DGStartPrimary:

                        break;
                    case WorkflowActionType.DGSwitchLogFile:

                        break;
                    case WorkflowActionType.DGRecoverStandBy:

                        break;
                    case WorkflowActionType.PowerOnMachine:

                        break;
                    case WorkflowActionType.PowerOffMachine:

                        break;
                    case WorkflowActionType.ExecuteOSCommand:

                        sp.Append(
                           Environment.NewLine
                           + Environment.NewLine
                           + Environment.NewLine
                           + "EXECUTE OSCOMMAND :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "SSHSERVER :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "COMMAND :" + _wfCommand
                           );

                        break;

                    case WorkflowActionType.MakeGMIR:

                        break;
                    case WorkflowActionType.RemoveGMIR:

                        break;
                    case WorkflowActionType.MakeSession:

                        break;
                    case WorkflowActionType.ChangeSession:

                        break;
                    case WorkflowActionType.MountVGS:

                        sp.Append(
                           Environment.NewLine
                           + Environment.NewLine
                           + "MOUNT VGS "
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Storage PR Info :" + WFStorageInfo.PRStorageImageId + "Storage DR Info :" + WFStorageInfo.DRStorageImageId
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "VG Name :" + WFVGName
                           + Environment.NewLine
                           + "MOUNT POINTS :" + string.Join(", ", _wfMountPoints)
                           + "LUNS D :" + string.Join(", ", _wfLundD)
                           );

                        break;
                    case WorkflowActionType.UnmountVGS:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "UNMOUNT VGS "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "VG Name :" + WFVGName
                         + Environment.NewLine
                         + "Mount Point :" + string.Join(", ", _wfMountPoints)
                         );

                        break;
                    case WorkflowActionType.ExecuteDBCommand:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "EXCEUTE DB COMMAND"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseSql.PRDatabaseSID
                       + Environment.NewLine
                       + "COMMAND :" + _wfCommand
                        );

                        break;
                    case WorkflowActionType.ExecuteStorageCommand:

                        break;
                    case WorkflowActionType.StartDatabaseNoMount:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                          + "START DATABASE UNMOUNT"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User :" + WFServer.PRSudoUser
                          + Environment.NewLine
                          + "Oracle SID :" + WFDatabase.DatabaseSql.PRDatabaseSID
                          );

                        break;
                    case WorkflowActionType.StartDatabaseMount:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "START DB MOUNT"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseSql.PRDatabaseSID
                         );

                        break;
                    case WorkflowActionType.OpenDatabase:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "OPEN DATABASE"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseSql.PRDatabaseSID
                         );

                        break;
                    case WorkflowActionType.AlterDatabaseOpen:

                        sp.Append(
                      Environment.NewLine
                      + Environment.NewLine
                      + "ALTER DATABASE OPEN"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Server IP :" + WFSSHServer.SSHHost
                      + Environment.NewLine
                      + "Sudo User :" + WFServer.PRSudoUser
                      + Environment.NewLine
                      + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                      );

                        break;


                    case WorkflowActionType.AlterDatabaseMount:

                        sp.Append(
                     Environment.NewLine
                     + Environment.NewLine
                     + "ALTER DATABASE MOUNT"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFSSHServer.SSHHost
                     + Environment.NewLine
                     + "Sudo User :" + WFServer.PRSudoUser
                     + Environment.NewLine
                     + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                     );

                        break;


                    case WorkflowActionType.BackUpControlFile:

                        sp.Append(
                            Environment.NewLine
                          + Environment.NewLine
                          + "BACKUP CONTROL FILE "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User :" + WFServer.PRSudoUser
                          + Environment.NewLine
                          + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          + "Control file path :" + WFControlFilePath
                          );

                        break;
                    case WorkflowActionType.CreateStandbyControlFile:

                        sp.Append(
                          Environment.NewLine
                        + Environment.NewLine
                        + "CREATE STANDBY CONTROL FILE "
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        + Environment.NewLine
                        + "Standby Control file path :" + WFStandbyControlFile
                        );

                        break;

                    case WorkflowActionType.MSSqlGenerateLog:

                        break;

                    case WorkflowActionType.MSSqlGenerateLastLog://copied from olderversion to new version
                        break;
                    case WorkflowActionType.MSSqlRestoreLastLog:

                        break;
                    case WorkflowActionType.MSSqlRestoreLog:

                        break;
                    //case WorkflowActionType.MSSqlKillProcess:

                    //    break;
                    case WorkflowActionType.MSSqlDBOption:
                        break;
                    case WorkflowActionType.MSSqlRestoreDBWithRecovery:
                        break;

                    case WorkflowActionType.SCP:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                        + "SCP REPLICATION"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Source Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Standby Control file path :" + WFStandbyControlFile
                        );

                        break;
                    case WorkflowActionType.RestoreStandbyControlFile:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                      + "RESTORE STANDBY CONTROL FILE"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Source Server IP :" + WFSSHServer.SSHHost
                      + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        + Environment.NewLine
                        + "Standby Control file path :" + WFStandbyControlFile
                      );

                        break;
                    case WorkflowActionType.RecoverStandbyDatabaseFile:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                       + "RECOVER STANDBY DATABASE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Source Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         + "Standby Control file path :" + WFStandbyControlFile
                       );


                        break;
                    case WorkflowActionType.RecoverDatabase:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "RECOVER DATABASE "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                         );

                        break;
                    case WorkflowActionType.CreateControlFileByScript:

                        sp.Append(
                    Environment.NewLine
                    + Environment.NewLine
                    + "CREATE CONTROL FILE BY SCRIPT "
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFSSHServer.SSHHost
                    + Environment.NewLine
                    + "Sudo User :" + WFServer.PRSudoUser
                    + Environment.NewLine
                    + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                    + Environment.NewLine
                    + "Script file path :" + WFScriptFilePath
                    );

                        break;
                    case WorkflowActionType.CreateControlFileScript:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "CREATE CONTROL FILE SCRIPT "
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Control file path :" + WFControlFilePath
                        + Environment.NewLine
                        + "Script file path :" + WFScriptFilePath
                        );

                        break;

                    case WorkflowActionType.ISDBRunning:


                        break;
                    case WorkflowActionType.ISDBDown:

                        break;

                    case WorkflowActionType.AlterSystemLog:
                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "ALTER SYSTEM LOG"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        break;
                    case WorkflowActionType.CreateTempFile:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "CREATE TEMP FILE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                       + "Control File Path :" + _wfControlFilePath
                       + Environment.NewLine
                       + "Temp File path:" + _wfTempFilePath
                       );

                        break;
                    case WorkflowActionType.CreateTempFileTableSpace:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "CREATE TEMP FILE TABLE SPACE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                      + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       + Environment.NewLine
                       + "Temp File path:" + _wfTempFilePath
                       );

                        break;
                    case WorkflowActionType.IsCheckPointCountOne:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "IS CHECK POINT COUNT ONE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        break;


                    case WorkflowActionType.StartDBStandBy:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "START DB STANDBY"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                      + "Sudo User :" + WFServer.PRSudoUser
                      + Environment.NewLine
                      + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        break;

                    case WorkflowActionType.StartDBReadWrite:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "START DB R/W "
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        break;
                    case WorkflowActionType.ReplicateStandByControlFile:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "REPLICATION STANDBY CONTORL"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                        + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                         );
                        break;


                    case WorkflowActionType.SwitchShutPrimaryDB:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "SWITCH SHUT PRIMARY DB"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                       + "Sudo User:" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        break;
                    case WorkflowActionType.SetJobQueProcess:
                        break;
                    case WorkflowActionType.CheckJobQueProcess:
                        break;
                    case WorkflowActionType.DeleteArchiveLogs:

                        break;
                    case WorkflowActionType.IsFileSystemMounted:
                        sp.Append(
                      Environment.NewLine
                      + Environment.NewLine
                      + "IS MOUNT VG"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "WFSSHServer:" + WFSSHServer.SSHHost
                      + Environment.NewLine
                      + "MOUNT POINT :" + WFGroupLuns.AMount
                      + Environment.NewLine
                      );

                        break;
                    case WorkflowActionType.ExecuteRMANCommand:



                        break;
                    case WorkflowActionType.IsCGFormed:
                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "IS CG FORMED"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "DSCLI:" + CurrentSSHInfo.SSHHost

                        + Environment.NewLine
                        + "HMCSERVER:" + CurrentPRDSCLI.DSCLIHost

                        + Environment.NewLine
                        + "STORAGE IMAGE ID:" + CurrentGlobalMirror.PRStorageImageId


                        + Environment.NewLine
                        + "MOUNT POINT :" + WFGroupLuns.AMount
                        + Environment.NewLine
                        );


                        break;
                    case WorkflowActionType.VerifyLogSequence:

                        break;

                    case WorkflowActionType.ActiveDatabaseReadWrite:


                        sp.Append(
                      Environment.NewLine
                      + Environment.NewLine
                      + "ACTIVATE DATABASE R/W "
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Server IP :" + WFSSHServer.SSHHost
                      + Environment.NewLine
                     + "Sudo User :" + WFServer.PRSudoUser
                     + Environment.NewLine
                     + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                      );


                        break;

                    case WorkflowActionType.StartDatabase:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                          + "START DATABASE "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                          );

                        break;

                    case WorkflowActionType.ImportVG:

                        break;

                    case WorkflowActionType.ReplicateStandByCtrFile:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "REPLICATE STANDBY CONTROL FILE "
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                      + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                      + Environment.NewLine
                      + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        break;

                    case WorkflowActionType.ReplicateTraceFile:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "REPLICATE TRACE FILE "
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        break;

                    case WorkflowActionType.StartListner:
                        break;
                    case WorkflowActionType.StopListner:
                        break;
                    case WorkflowActionType.ReStartListner:
                        break;
                    case WorkflowActionType.CheckGroupSyncStatus:
                        break;
                    case WorkflowActionType.ChangeDNS:

                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "CHANGE DNS"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "DNS Server IP :" + CurrentDNSServer.ServerIP
                       + Environment.NewLine
                       + "Domain Name :" + _wfDomainName
                       + Environment.NewLine
                       + "Exist Host Name:" + _wfExistingHost
                       + Environment.NewLine
                       + "Exis Host IP:" + _wfExistingIP
                       + Environment.NewLine
                       + "New Host Name:" + _wfNewHost
                       + Environment.NewLine
                       + "New Host IP:" + _wfNewIp
                       );

                        break;


                    case WorkflowActionType.CheckNoLoggingOperation:

                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                        + "CHECKNOLOGGINGOPERATION"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Source Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "PR Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Database Name:" + WFDatabase.DatabaseSql.PRDatabaseSID
                        );


                        break;


                    case WorkflowActionType.StartCopyJobDr:

                        break;
                    case WorkflowActionType.StartRestoreJobDr:

                        break;
                    case WorkflowActionType.KillTransactionProcess:

                        break;
                    case WorkflowActionType.GenerateLastLog:

                        break;
                    case WorkflowActionType.DisableSecondaryDbJob:

                        break;
                    case WorkflowActionType.MakeSingleUser:

                        break;

                    case WorkflowActionType.RestoreLogShipping:

                        break;
                    case WorkflowActionType.BackUpPrimaryDatabase:

                        break;
                    case WorkflowActionType.RestoreSecondaryDatabase:


                        break;
                    case WorkflowActionType.RestoreLogShippingPrimary:

                        break;
                    case WorkflowActionType.AddSecondaryLog:

                        break;
                    case WorkflowActionType.RestoreLogshipSecondary:

                        break;
                    case WorkflowActionType.UpdateSecondaryServerCopyJob:

                        break;
                    case WorkflowActionType.UpdateSecondaryServerRestoreJob:

                        break;

                    case WorkflowActionType.MonitorLogShipping:

                        //SQLServerMonitor.DataAccess.Monitoring(WFServer.PRIPAddress, WFServer.PRUserName,
                        //                                       WFServer.PRPassword,
                        //                                       WFTargetServer.PRIPAddress,
                        //                                       WFTargetServer.PRUserName,
                        //                                       WFTargetServer.PRPassword, WFDatabase.DatabaseSql.PRDatabaseSID);

                        break;

                    //case WorkflowActionType.MsSqlSwitchOver:

                    //   SQLServerMonitor.DataAccess.SwitchOver(WFServer.PRIPAddress, WFServer.PRUserName,
                    //                                          WFServer.PRPassword,
                    //                                          WFDatabase.DatabaseSql.PRDatabaseSID,
                    //                                          WFTargetServer.PRIPAddress,
                    //                                          WFTargetServer.PRUserName,
                    //                                          WFTargetServer.PRPassword, "PRShareFolder", "DRShareFolder");
                    //   break;



                    case WorkflowActionType.RunBackJob:

                        break;
                    case WorkflowActionType.RunCopyJob:

                        break;
                    case WorkflowActionType.RunRestoreJob:

                        break;

                    case WorkflowActionType.AddDNS:
                        sp.Append(
                       Environment.NewLine
                       + Environment.NewLine
                       + "ADD DNS"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "DNS Server IP :" + CurrentDNSServer.ServerIP
                       + Environment.NewLine
                       + "Domain Name :" + _wfDomainName
                       + Environment.NewLine
                       + "Host Name:" + _wfNewHost
                       + Environment.NewLine
                       + "Host IP:" + _wfNewIp
                       );

                        break;
                    case WorkflowActionType.DeleteDNS:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "DELETE DNS"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "DNS Server IP :" + CurrentDNSServer.ServerIP
                        + Environment.NewLine
                        + "Domain Name :" + _wfDomainName
                        + Environment.NewLine
                        + "Host Name:" + _wfNewHost
                        + Environment.NewLine
                        + "Host IP:" + _wfNewIp
                        );

                        break;
                    case WorkflowActionType.StartOracleListner:

                        sp.Append(
                        Environment.NewLine
                        + Environment.NewLine
                        + "START ORACLE LISTENER"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Sudo User:" + WFServer.PRSudoUser
                        );



                        break;
                    case WorkflowActionType.StopOracleListner:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "STOP ORACLE LISTENER"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User:" + WFServer.PRSudoUser
                         );


                        break;


                    case WorkflowActionType.PreShutRedoCtrlScript:

                        sp.Append(
                         Environment.NewLine
                         + Environment.NewLine
                         + "GENERATE COPY REDO FILE SCRIPT"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User:" + WFServer.PRSudoUser
                          + Environment.NewLine
                         + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                         );

                        break;
                    case WorkflowActionType.CopyRedoCtrFile:


                        sp.Append(
                          Environment.NewLine
                          + Environment.NewLine
                          + "COPY REDO CONTROL FILE SCRIPT"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User:" + WFServer.PRSudoUser
                           + Environment.NewLine
                          + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                          );


                        break;

                    case WorkflowActionType.Status:

                        break;

                    case WorkflowActionType.CheckStorageMailboxPath:

                        break;
                    case WorkflowActionType.SetSCRPrerequisite:

                        break;
                    case WorkflowActionType.EnableSCR:

                        break;
                    case WorkflowActionType.ExecuteSudoCommand:

                        sp.Append(
                          Environment.NewLine
                          + "EXECUTE SUDO COMMAND"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User:" + WFServer.PRSudoUser
                           + Environment.NewLine
                          + "Command" + _wfCommand
                          );

                        break;
                    case WorkflowActionType.UnMountNFSVolume:

                        sp.Append(
                         Environment.NewLine
                         + "UNMOUNT NFS VOLUME"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Mount Point :" + _wfMountPoints[0]
                         );


                        break;
                    case WorkflowActionType.MountNFSVolume:

                        sp.Append(
                        Environment.NewLine
                        + "MOUNT NFS VOLUME"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Host Name :" + _wfHostName
                        + Environment.NewLine
                        + "Local Mount Points :" + _wfLocalMountPoints
                        + Environment.NewLine
                        + "Remote Mount Points :" + _wfRemoteMountPoints
                        );

                        break;
                    case WorkflowActionType.CheckFileExist:


                        break;
                    case WorkflowActionType.CheckDBStandBy:


                        break;
                    case WorkflowActionType.CheckDBReadWrite:


                        break;
                    case WorkflowActionType.ExecuteCheckOSCommand:

                        sp.Append(
                            Environment.NewLine
                            + "EXECUTE CHECK OS COMMAND"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "Server IP :" + WFSSHServer.SSHHost
                            + Environment.NewLine
                            + "Sudo User:" + WFServer.PRSudoUser
                            + Environment.NewLine
                            + "Command" + _wfCommand
                            + Environment.NewLine
                            + "CHECK OUTPUT" + string.Join(", ", _wfCheckOutput == null ? str : _wfCheckOutput.ToArray())
                            );

                        break;

                    case WorkflowActionType.FastCopyReplicateFile:

                        sp.Append(
                           Environment.NewLine
                           + "FASTCOPY REPLICATE FILE"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Sudo User:" + WFTargetServer.PRSudoUser
                           + Environment.NewLine
                           + "FastCopyPath" + _wfCommand
                            + Environment.NewLine
                           + "Source File" + _wfSourceFile
                           + Environment.NewLine
                           + "Target Folder" + _wfTargetFolder[0]
                           );

                        break;

                    case WorkflowActionType.FastCopySyncFolders:

                        sp.Append(
                           Environment.NewLine
                           + "FASTCOPY SYNC FOLDER"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Sudo User:" + WFTargetServer.PRSudoUser
                           + Environment.NewLine
                           + "FastCopyPath" + _wfCommand
                            + Environment.NewLine
                            + "Source Folder :" + string.Join(", ", _wfSourceFolder == null ? str : _wfSourceFolder.ToArray())
                            + Environment.NewLine
                           + "Target Folder " + string.Join(", ", _wfTargetFolder == null ? str : _wfTargetFolder.ToArray())
                           );

                        break;

                }

            }

            catch (BcmsException exc)
            {
                // LoggerDrill.ErrorFormat("Exception occured while executing WorkFlowAction: " + workflowActionType + " ExceptionDetails: " + exc.Message);
                sp.Append("Exception occured while executing WorkFlowAction: " + workflowActionType + " ExceptionDetails: " + exc.Message);
                //UpdateDROperationResultStatus("Error");

                //while (!WaitForConditionalAction())
                //{
                //    Thread.Sleep(5000);
                //}
                //PerformConditonalAction();
            }
            catch (Exception exc)
            {
                // LoggerDrill.ErrorFormat("Exception occured while executing WorkFlowAction: " + workflowActionType + " ExceptionDetails: " + exc.Message);
                sp.Append("Exception occured while executing WorkFlowAction: " + workflowActionType + " ExceptionDetails: " + exc.Message);
                //UpdateDROperationResultStatus("Error");

                //while (!WaitForConditionalAction())
                //{
                //    Thread.Sleep(5000);

                //}
                //PerformConditonalAction();
            }
        }

        public void PerformVerifyWorkflow(Workflow workflow, Group group)
        {
            var sr = new StreamWriter("C:\\Bcms\\WorkFlowExecutionLog.txt");
            var sp = new StringBuilder();
            CurrentGroup = group;
            sp.Append("***********************************LOG CREATED AT: " + DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss") + "*****************************"
                      + Environment.NewLine
                      + "GROUP NAME: " + group.Name
                      + Environment.NewLine
                      + "WORKFLOW NAME: " + workflow.Name
                    );

            GetLastDROperation(group.Id);
            string[] workflowActions = GetWorkFlowActionFromXml(workflow.Xml);

            foreach (var action in workflowActions)
            {
                _workflowActionId = Convert.ToInt32(action);
                var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(_workflowActionId);
                if (workflowAction != null)
                {
                    BindWorkflowActionProperties(workflowAction);
                    _drOperationResult = null;
                    CurrentWorkflowActionName = workflowAction.Name;
                    CurrentWorkflowActionType = (WorkflowActionType)workflowAction.ActionType;
                    CurrentActionStartTime = DateTime.Now;
                    //Added by neeraj
                    VerifyWorkFlowAction(CurrentWorkflowActionType, ref sp);
                }
            }

            sr.Write(sp.ToString());
            sr.Close();

            //ShowPDFReport();
        }

        public void PerformCustom()
        {
            if (CurrentGroup != null && CurrentServer != null)
            {
                var workflow = WorkflowDataAccess.GetWorkflowByGroupIdAndStatus(CurrentGroup.Id);

                if (workflow != null)
                {
                    PerformWorkflow(workflow, DROperationType.Customized);
                }
            }
        }

        public void PerformSwitchOver()
        {
            if (CurrentGroup != null && CurrentServer != null)
            {
                CurrentWFDROperation = null;

                CurrentDROperationType = DROperationType.SwitchOver;

                var workflow = WorkflowDataAccess.GetWorkflowByGroupIdAndStatus(CurrentGroup.Id);

                if (workflow != null && workflow.Id > 0)
                {
                    PerformWorkflow(workflow, DROperationType.SwitchOver);
                }
            }

        }

        public void PerformCustomAction()
        {
            Logger.InfoFormat("Custom Action is Started");

            CurrentWorkflowDROperation = DROperationDataAccess.GetDROperationById(CurrentGroup.DROperationId);

            _drOperationResult = null;

            GetPendingAction();

            var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(CurrentDROperationResult.ActionId);

            if (workflowAction != null)
            {
                Logger.InfoFormat("Binding Action");

                BindWorkflowActionProperties(workflowAction);

                CurrentWorkflowActionName = workflowAction.Name;

                CurrentWorkflowActionType = (WorkflowActionType)workflowAction.ActionType;

                CurrentActionStartTime = DateTime.Now;

                CurrentActionEndTime = DateTime.Now;

                UpdateDROperationResultStatus("Running");

                PerformAction(CurrentWorkflowActionType, false);
            }
        }

        public void PerformSwitchBack()
        {
            if (CurrentGroup != null && CurrentServer != null)
            {
                CurrentWFDROperation = null;

                CurrentDROperationType = DROperationType.SwitchBack;

                var workflow = WorkflowDataAccess.GetWorkflowByGroupIdAndStatus(CurrentGroup.Id);

                if (workflow != null && workflow.Id > 0)
                {

                    PerformWorkflow(workflow, DROperationType.SwitchBack);

                }
            }

        }

        public void PerformParallelWorkflow()
        {
            Logger.InfoFormat("{0} : Perform Parallel workflow", CurrentParallelGroupWorkflow.WorkflowName);


            if (CurrentGroup != null)
            {
                var workflow = WorkflowDataAccess.GetWorkflowById(CurrentParallelGroupWorkflow.WorkflowId);

                if (workflow != null)
                {
                    PerformParallelWorkflowActions(workflow);

                    var groupWorkflow =
                        GroupWorkflowDataAccess.GetByGroupIdAndWorkflowId(CurrentParallelGroupWorkflow.GroupId,
                                                                          CurrentParallelGroupWorkflow.WorkflowId);

                    if (groupWorkflow != null)
                    {
                        int status = 0;

                        switch (groupWorkflow.ActionType)
                        {
                            case (int)DROperationType.SwitchOver:

                                status = (int)GroupWorkflowOperation.SwitchOverCompleted;

                                break;
                            case (int)DROperationType.SwitchBack:

                                status = (int)GroupWorkflowOperation.SwitchBackCompleted;

                                break;
                            case (int)DROperationType.FailBack:
                                status = (int)GroupWorkflowOperation.FailBackCompleted;

                                break;
                            case (int)DROperationType.FailOver:

                                status = (int)GroupWorkflowOperation.FailOverCompleted;

                                break;
                            case (int)DROperationType.Customized:

                                status = (int)GroupWorkflowOperation.CustomCompleted;

                                break;
                        }
                        GroupDataAccess.UpdateWorkflowOperation(CurrentParallelGroupWorkflow.GroupId, status, 0);
                    }
                }
            }
        }

        private void PerformParallelWorkflowActions(Workflow workflow)
        {
            try
            {
                Logger.InfoFormat("{0} : Perform Parallel workflow Action", CurrentParallelGroupWorkflow.WorkflowName);

                CompletedActionCount = 0;

                TotalActionCount = 0;

                string[] workflowActions = GetWorkFlowActionFromXml(workflow.Xml);

                TotalActionCount = workflowActions.Length;

                foreach (var action in workflowActions)
                {
                    _workflowActionId = Convert.ToInt32(action);

                    var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(_workflowActionId);

                    if (workflowAction != null)
                    {
                        if (!_isAbort)
                        {
                            UpdateParallelConditonalOperation(ConditionalOperationType.None);

                            CurrentParallelConditionalOperationType = ConditionalOperationType.None;

                            BindWorkflowActionProperties(workflowAction);

                            _parallelWorkflowActionResult = null;

                            CurrentWorkflowActionName = workflowAction.Name;

                            CurrentWorkflowActionId = workflowAction.Id;

                            CurrentWorkflowActionType = (WorkflowActionType)workflowAction.ActionType;

                            CurrentActionStartTime = DateTime.Now;

                            CurrentActionEndTime = DateTime.Now;

                            UpdateOperationStatus("Running", true);

                            CompletedActionCount++;

                            PerformAction(CurrentWorkflowActionType, true);

                            if (CurrentParallelDroperation.ActionMode == ActionMode.Manual)
                            {
                                if (CurrentParallelConditionalOperationType != ConditionalOperationType.Next)
                                {
                                    if (action != workflowActions[workflowActions.Length - 1])
                                    {
                                        while (!WaitForParallelConditionalAction())
                                        {
                                            Thread.Sleep(10000);
                                        }
                                    }
                                }

                            }
                        }
                        else
                        {
                            UpdateParallelWorkflowActionResultStatus("Abort");

                            UpdateParallelGroupWorkflow("Abort");

                            break;
                        }
                    }
                }
                UpdateParallelGroupWorkflow("Completed");
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("ERROR Executing workflow :" + exc.Message + " inner exception" + exc.InnerException.Message);

                throw;
            }

        }

        private void UpdateProgressBarStatus()
        {
            string progress = string.Format("{0}/{1}", CompletedActionCount, TotalActionCount);

            ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);

        }


        #endregion





        #region MSSqlNative

        public void DisablebackupJob()
        {
            string constr1 = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.PRPassword);
            var result = LogShippingNative.ExecProcUpdateJob(constr1, "LSBackup_" + WFDatabase.DatabaseSql.PRDatabaseSID, 0);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} Job Disable successfully ", CurrentGroup.Name, "LSBackup_" + WFDatabase.DatabaseSql.PRDatabaseSID);

        }

        public void DisableCopyJob()
        {
            string seccon1 = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var result = LogShippingNative.ExecProcUpdateJob(seccon1, "LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID, 0);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} Job Disable successfully ", CurrentGroup.Name, "LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID);
        }

        public void DisableRestoreJob()
        {
            string seccon1 = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var result = LogShippingNative.ExecProcUpdateJob(seccon1, "LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID, 0);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} Job Disable successfully ", CurrentGroup.Name, "LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID);
        }

        public string RunJob()
        {
            var result = "";
            string seccon1 = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            result = LogShippingNative.ExecProcStartJob(seccon1, "LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID);

            result = result + " " + LogShippingNative.ExecProcStartJob(seccon1, "LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID);
            return result;
        }

        public void CheckReplication()
        {
            var result = "";
            Dictionary<string, string> logser = new Dictionary<string, string>();
            logser = LogDataAccess.LogDetailstotable(WFServer.PRIPAddress, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFDatabase.DatabaseSql.DRDatabaseSID, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.DRUserName);
            var lastBackupFile = logser["last_backup_file"].Split('_');
            var lastRestoredFile = logser["last_restored_file"].Split('_');
            while (lastBackupFile[1] != lastRestoredFile[1])
            //while (!logser["last_backup_file"].Equals(logser["last_restored_file"]))
            {
                result = RunJob();
                //if (!result.Contains("Failure"))
                //    Logger.DebugFormat("{0} : {1} Run Job successfully ", CurrentGroup.Name, "LSRestore" + "LSCopy");
                //logser = LogDataAccess.LogDetailstotable(WFServer.PRIPAddress, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.PRDatabaseSID, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.DRUserName);
                if (result.Contains("Failure"))
                {
                    throw new BcmsException(BcmsExceptionType.MSSqlVerifyLogSequenceFailed);
                }

                logser = LogDataAccess.LogDetailstotable(WFServer.PRIPAddress, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.PRDatabaseSID, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.DRUserName);

                //obj = SqlServerNative.LogDetailsFunction(WFSqlServer.PRIPAddress, WFSqlDatabase.PRUserName, WFSqlDatabase.PRPassword, WFSqlDatabase.PRDatabaseName, WFSqlServer.DRIPAddress, WFSqlDatabase.DRPassword, WFSqlDatabase.DRUserName);
                //Logger.DebugFormat("{0} : backup file {1}  restore file {2}", CurrentGroup.Name, obj.last_backup_file, obj.last_restored_file);
                Thread.Sleep(3000);
            }
            Logger.DebugFormat("{0} : {1} Run Job successfully ", CurrentGroup.Name, "LSRestore" + "LSCopy");
        }

        public void deletefile()
        {
            var result = LogDataAccess.DeleteFile(WFDatabase.DatabaseSql.DRNetworkSharedPath + WFDatabase.DatabaseSql.PRDatabaseSID + "LastBackUp.trn", WFDatabase.DatabaseSql.PRBackupRestorePath + "ROLLBACK_UNDO_" + WFDatabase.DatabaseSql.PRDatabaseSID + ".BAK");
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} deleted file ", CurrentGroup.Name, "LastBackUp.trn");
        }

        public void killprocessprimary()
        {
            string constr = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword);
            var result = LogDataAccess.RunUSP_KILLPROCESS(constr, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} kill process primary successfully done ", CurrentGroup.Name, WFServer.PRName);
        }

        public void migrateLoginprimary()
        {
            SqlServerNative.MigrateLoginPR_SA(WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFServer.PRIPAddress, WFDatabase.DatabaseSql.DRNetworkSharedPath, "EXEC[dbo].[sp_help_revlogin]");
        }

        public void migrateLoginsecondary()
        {
            //SqlServerNative.MigrateLoginDR_SA(WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRNetworkSharedPath, "EXEC[dbo].[sp_help_revlogin]", "Restore");
            SqlServerNative.MigrateLoginDR_SA(WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRNetworkSharedPath);
        }

        public void lastbackuplog()
        {
            var filename = "LastLogBackUp" + CurrentGroup.Id.ToString() + DateTime.Now.ToString("yyyymmddhhmmss") + ".trn";
            var lastlog = SqlLogNativeDataAccess.CreateFileName(CurrentGroup.Id, filename);
            if (lastlog)
                Logger.DebugFormat("{0} : {1} last backup file name log written to DB ", CurrentGroup.Name, WFServer.PRName);
            var sqlstr = LogDataAccess.LastBackUpLog(WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.DRNetworkSharedPath, WFDatabase.DatabaseSql.PRBackupRestorePath, filename);
            string constr1 = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "msdb", WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRPassword);
            var result = LogDataAccess.ExecuteQuerry(constr1, sqlstr);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} last backup log successfully done ", CurrentGroup.Name, WFServer.PRName);
        }

        public void killprocesssecondary()
        {
            string seccon = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var result = LogDataAccess.RunUSP_KILLPROCESS(seccon, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} kill process secondary successfully done ", CurrentGroup.Name, WFServer.DRName);
        }

        public void setoptionDB()
        {
            string seccon = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var result = LogDataAccess.RunStandbyStorProc(seccon, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} setoptionDB successfully done ", CurrentGroup.Name, WFServer.DRName);
        }

        public void RestoreLastLog()
        {
            var filename = "";
            bool ressbool = false;
            filename = SqlLogNativeDataAccess.GetfileNamebyId(CurrentGroup.Id);
            string seccon = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var sqlstr = LogDataAccess.RestoreLastLog(WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.DRNetworkSharedPath, filename);
            var result = LogDataAccess.ExecuteQuerry(seccon, sqlstr);
            Logger.DebugFormat("{0} : {1} Restore Last Log successfully done ", CurrentGroup.Name, WFServer.DRName);
        }

        public void SetLastFileFlag()
        {
            bool ressbool = false;
            var filename = "";
            filename = SqlLogNativeDataAccess.GetfileNamebyId(CurrentGroup.Id);
            ressbool = SqlLogNativeDataAccess.UpdateFileName(filename);
            if (ressbool)
                Logger.DebugFormat("{0} : {1} Last Log written to database ", CurrentGroup.Name, WFServer.DRName);
        }

        public void RestorewithRecovery()
        {
            string seccon = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            var sqlstr = LogDataAccess.RestorewithRecovery(WFDatabase.DatabaseSql.PRDatabaseSID);
            var result = LogDataAccess.ExecuteQuerry(seccon, sqlstr);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} Restore with Recovery successfully done ", CurrentGroup.Name, WFServer.DRName);
        }

        public void RemovelogshippingPRDR()
        {
            string con = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword);
            var result = LogShippingNative.ExecuteRemovePrimLog(con, WFDatabase.DatabaseSql.PRDatabaseSID, WFServer.DRIPAddress, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} Remove logshipping PRDR successfully done ", CurrentGroup.Name, WFServer.PRName);
        }

        public void removelogshipPR()
        {
            string con = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRPassword);
            var result = LogShippingNative.ExecuteRemoveLSPri(con, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} remove logshiping PR ", CurrentGroup.Name, WFServer.PRName);
        }

        public string removelogshipDR()
        {
            string con = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword); ;
            var result = LogShippingNative.ExecuteRemoveLSSEC(con, WFDatabase.DatabaseSql.PRDatabaseSID);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} remove logshiping DR ", CurrentGroup.Name, WFServer.DRName);
            return result;
        }

        public void SwitchPrimaryServer()
        {
            var result = SqlLogNativeDataAccess.SwitchPrimarySecondary(CurrentGroup.Id);
            if (result)
                Logger.DebugFormat("{0} : {1} Switch Primary Server ", CurrentGroup.Name, WFServer.PRName);
        }

        public void enablelogshipPR()
        {
            string conpr = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRPassword);
            var result = LogShippingNative.ExcuteProcLogshipPrim(conpr, WFDatabase.DatabaseSql.PRDatabaseSID, WFServer.DRIPAddress, WFServer.PRIPAddress, WFDatabase.DatabaseSql.PRBackupRestorePath, "LSBackup_" + WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRNetworkSharedPath, WFServer.PRIPAddress);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} enable logshiping PR ", CurrentGroup.Name, WFServer.PRName);
        }

        public void enablelogshipDR()
        {
            string condr = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            string[] para = new string[7];
            para[0] = WFServer.PRIPAddress;
            para[1] = WFDatabase.DatabaseSql.PRDatabaseSID;
            para[2] = WFDatabase.DatabaseSql.PRNetworkSharedPath;
            para[3] = WFDatabase.DatabaseSql.PRBackupRestorePath;
            para[4] = "LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID;
            para[5] = "LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID;
            para[6] = WFServer.DRIPAddress;
            var result = LogShippingNative.ExcuteProcLogshipSeco(condr, para);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} enable logshiping DR ", CurrentGroup.Name, WFServer.DRName);
        }

        public void updatebackupjob()
        {
            string conpr = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRPassword);
            string conn = LogDataAccess.Connectionstring(WFServer.PRIPAddress, "msdb", WFDatabase.DatabaseSql.PRDatabaseSID, WFDatabase.DatabaseSql.PRPassword);
            string prsql = LogShippingNative.Primaryidsqlstring(WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> prdataid = LogDataAccess.ExecuteReader(conn, prsql);
            var sql = LogShippingNative.Jobnamesqlstring("LSBackup_" + WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> prjobid = LogDataAccess.ExecuteReader(conn, sql);
            var CommandLine = @"C:\Program Files\Microsoft SQL Server\100\Tools\Binn\sqllogship.exe -Backup " + prdataid["primary_id"] + " -server " + WFServer.PRIPAddress + "";
            var result = LogShippingNative.Executesp_UpdateDRServer(conpr, prjobid["job_id"], prjobid["step_name"], CommandLine);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} update backup job ", CurrentGroup.Name, "LSBackup_" + WFDatabase.DatabaseSql.PRDatabaseSID);
        }

        public void updatecopyjob()
        {
            string condr = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            string conn = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            string sql1 = LogShippingNative.Serveridsqlstring(WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> drdataid = LogDataAccess.ExecuteReader(conn, sql1);
            var sql = LogShippingNative.Jobnamesqlstring("LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> drjobid = LogDataAccess.ExecuteReader(conn, sql);
            var CommandLine = @"C:\Program Files\Microsoft SQL Server\100\Tools\Binn\sqllogship.exe -Copy " + drdataid["secondary_id"] + " -server " + WFServer.DRIPAddress + "";
            var result = LogShippingNative.Executesp_UpdateDRServer(condr, drjobid["job_id"], drjobid["step_name"], CommandLine);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} update copy job ", CurrentGroup.Name, "LSCopy_" + WFDatabase.DatabaseSql.PRDatabaseSID);
        }

        public void updaterestorejob()
        {
            string condr = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "Master", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            string conn = LogDataAccess.Connectionstring(WFServer.DRIPAddress, "msdb", WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
            string sql1 = LogShippingNative.Serveridsqlstring(WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> drdataid = LogDataAccess.ExecuteReader(conn, sql1);
            var sql = LogShippingNative.Jobnamesqlstring("LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID);
            Dictionary<string, string> drjobid2 = LogDataAccess.ExecuteReader(conn, sql);
            var CommandLine = @"C:\Program Files\Microsoft SQL Server\100\Tools\Binn\sqllogship.exe -Restore " + drdataid["secondary_id"] + " -server " + WFServer.DRIPAddress + "";
            var result = LogShippingNative.Executesp_UpdateDRServer(condr, drjobid2["job_id"], drjobid2["step_name"], CommandLine);
            if (!result.Contains("Failure"))
                Logger.DebugFormat("{0} : {1} update restore job ", CurrentGroup.Name, "LSRestore_" + WFDatabase.DatabaseSql.PRDatabaseSID);
        }
        public void MonitorSqlServerNative()
        {
            Logger.Debug("Sql Native service executing MonitorSqlServerNative");
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            //_wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);

            if (_wfServer != null && _wfDatabase != null)
            {
                Dictionary<string, string> sqllogdetails = LogDataAccess.LogDetailstotable(WFServer.PRIPAddress, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFDatabase.DatabaseSql.DRDatabaseSID, WFServer.DRIPAddress, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.DRUserName);
                if (sqllogdetails.Count != 0)
                {
                    SqlLogNative sqllog = new SqlLogNative();
                    sqllog.GroupId = CurrentGroup.Id;
                    sqllog.PRServer = WFServer.PRName;
                    sqllog.DRServer = WFServer.DRName;
                    sqllog.DatabaseName = WFDatabase.DatabaseSql.PRDatabaseSID;
                    sqllog.LastGenLog = sqllogdetails.ContainsKey("last_backup_file") ? sqllogdetails["last_backup_file"] : "";
                    sqllog.LastApplyLog = sqllogdetails.ContainsKey("last_restored_file") ? sqllogdetails["last_restored_file"] : "";
                    sqllog.LastCopiedLog = sqllogdetails.ContainsKey("last_copied_file") ? sqllogdetails["last_copied_file"] : "";
                    sqllog.LSNlastbackupLog = sqllogdetails.ContainsKey("Backup LastLSN") ? sqllogdetails["Backup LastLSN"] : "";
                    sqllog.LSNlastrestoredLog = sqllogdetails.ContainsKey("RestoreLastLSN") ? sqllogdetails["RestoreLastLSN"] : "";
                    sqllog.LSNLastCopiedLog = sqllogdetails.ContainsKey("Copy LastLSN") ? sqllogdetails["Copy LastLSN"] : "";
                    sqllog.LastLogGenTime = sqllogdetails.ContainsKey("last_backup_date") ? sqllogdetails["last_backup_date"] : "";
                    sqllog.LastLogApplTime = sqllogdetails.ContainsKey("last_restored_date") ? sqllogdetails["last_restored_date"] : "";
                    sqllog.LastLogCopyTime = sqllogdetails.ContainsKey("last_copied_date") ? sqllogdetails["last_copied_date"] : "";
                    sqllog.DataLag = sqllogdetails.ContainsKey("DataLag") ? sqllogdetails["DataLag"] : "";
                    sqllog.IsActive = 1;
                    sqllog.CreatorId = 1;
                    var addsqllog = SqlLogNativeDataAccess.AddSqlNativeLogDetails(sqllog);
                    var addstatus = SqlLogNativeDataAccess.AddSqlNativeLogStatusDetails(sqllog);
                    if (addsqllog && addstatus)
                        Logger.Debug("Sql Native service Completed MonitorSqlServerNative");
                }
            }

        }

        public void MSSqlNativeservices()
        {
            Logger.Debug("Sql Native service executing MSSqlNativeservices");
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            //_wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            if (_wfServer != null && _wfDatabase != null)
            {
                string[] str1 = { "MSSQLSERVER", "SQLSERVERAGENT", "MSSQLServerOLAPService", "MsDtsServer100", "ReportServer", "SQLWriter", "MSSQLServerADHelper100", "MSSQLFDLauncher", "SQLBrowser", "MpsSvc" };
                string[] servers = { _wfServer.PRName, _wfServer.DRName };

                string[] servicenameINT = { "SQL Server (MSSQLSERVER)", "SQL Server Agent (MSSQLSERVER)", "SQL Server Analysis Services (MSSQLSERVER)", "SQL Server Integration Services 10.0", "SQL Server Reporting Services (MSSQLSERVER)", "SQL Server VSS Writer", "SQL Active Directory Helper Service", "SQL Full-text Filter Daemon Launcher (MSSQLSERVER)", "SQL Server Browser", "Windows Firewall" };
                foreach (string server in servers)
                {
                    int i = 0;
                    var servicename = "";
                    var status = "";
                    var Mode = "";
                    var username = _wfServer.PRName.Equals(server) ? _wfServer.PRUserName : _wfServer.DRUserName;
                    var password = _wfServer.PRName.Equals(server) ? _wfServer.PRPassword : _wfServer.DRPassword;
                    foreach (string str in str1)
                    {
                        var result = LogDataAccess.WindowServices(server, username, password, str);

                        if (result != null)
                        {
                            var value = result.Split(';');
                            servicename = value[0];
                            status = value[1];
                            Mode = value[2];
                        }
                        else
                        {
                            servicename = servicenameINT[i];
                            status = "NA";
                            Mode = "NA";
                        }

                        SqlnativeServices sqlservice = new SqlnativeServices();
                        sqlservice.GroupId = CurrentGroup.Id;
                        sqlservice.Server = server;//servername
                        sqlservice.Service_Name = servicename;
                        sqlservice.Status = status;
                        sqlservice.Start_Mode = Mode;
                        sqlservice.IsActive = 1;
                        sqlservice.CreatorId = 1;
                        SqlNativeServiceDataAccess.AddSqlNativeServices(sqlservice);
                        i++;
                    }

                }
            }
        }

        public void MSSqlHealthservices()
        {
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            //_wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            if (_wfServer != null && _wfDatabase != null)
            {
                var conpr = LogDataAccess.Connectionstring(_wfServer.PRIPAddress, "Master", _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword);
                var condr = LogDataAccess.Connectionstring(_wfServer.DRIPAddress, "Master", _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword);
                var conmsdbpr = LogDataAccess.Connectionstring(_wfServer.PRIPAddress, "msdb", _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword);
                var conmsdbdr = LogDataAccess.Connectionstring(_wfServer.DRIPAddress, "msdb", _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword);
                SqlnativeHealth mshealth = new SqlnativeHealth();
                var sql = "";
                Dictionary<string, string> healthpr;
                Dictionary<string, Dictionary<string, string>> result = new Dictionary<string, Dictionary<string, string>>();
                sql = LogDataAccess.DBHealthStatus(_wfDatabase.DatabaseSql.DRDatabaseSID);
                mshealth.GroupId = CurrentGroup.Id;
                healthpr = LogDataAccess.ExecuteReader(conpr, sql);
                if (healthpr.Count != 0)
                {
                    mshealth.MSSQL_Database_State_PR = healthpr.ContainsKey("state_desc") ? healthpr["state_desc"] : "";
                    mshealth.MSSQL_Database_Recovery_Model_PR = healthpr.ContainsKey("recovery_model_desc") ? healthpr["recovery_model_desc"] : "";
                    mshealth.Database_Restrict_Access_Status_PR = healthpr.ContainsKey("user_access_desc") ? healthpr["user_access_desc"] : "";
                }

                sql = LogDataAccess.DBHealthStatus(_wfDatabase.DatabaseSql.DRDatabaseSID);
                healthpr = LogDataAccess.ExecuteReader(condr, sql);
                if (healthpr.Count != 0)
                {
                    mshealth.MSSQL_Database_State_DR = healthpr.ContainsKey("state_desc") ? healthpr["state_desc"] : "";
                    mshealth.MSSQL_Database_Recovery_Model_DR = healthpr.ContainsKey("recovery_model_desc") ? healthpr["recovery_model_desc"] : "";
                    mshealth.Database_Restrict_Access_Status_DR = healthpr.ContainsKey("user_access_desc") ? healthpr["user_access_desc"] : "";
                }
                healthpr = LogDataAccess.jobstatus(conmsdbpr, "LSBackup_" + _wfDatabase.DatabaseSql.DRDatabaseSID);
                if (healthpr.Count != 0)
                {
                    if (healthpr.ContainsKey("enabled"))
                        mshealth.Backup_Job_Status = Convert.ToInt32(healthpr["enabled"]) == 1 ? "enabled" : "disabled";
                    if (healthpr.ContainsKey("current_execution_status"))
                        mshealth.Backup_Job_Execution_Status = JobExecStatus[Convert.ToInt32(healthpr["current_execution_status"])];
                }
                healthpr = LogDataAccess.jobstatus(conmsdbdr, "LSCopy_" + _wfDatabase.DatabaseSql.DRDatabaseSID);
                if (healthpr.Count != 0)
                {
                    if (healthpr.ContainsKey("enabled"))
                        mshealth.Copy_Job_Status = Convert.ToInt32(healthpr["enabled"]) == 1 ? "enabled" : "disabled";
                    if (healthpr.ContainsKey("current_execution_status"))
                        mshealth.Copy_Job_Execution_Status = JobExecStatus[Convert.ToInt32(healthpr["current_execution_status"])];
                }
                healthpr = LogDataAccess.jobstatus(conmsdbdr, "LSRestore_" + _wfDatabase.DatabaseSql.DRDatabaseSID);
                if (healthpr.Count != 0)
                {
                    if (healthpr.ContainsKey("enabled"))
                        mshealth.Restore_Job_Status = Convert.ToInt32(healthpr["enabled"]) == 1 ? "enabled" : "disabled";
                    if (healthpr.ContainsKey("current_execution_status"))
                        mshealth.Restore_Job_Execution_Status = JobExecStatus[Convert.ToInt32(healthpr["current_execution_status"])];
                }
                result = LogDataAccess.log_shipping_Enable_Status(conpr);
                if (healthpr.Count != 0)
                {
                    if (result.ContainsKey(_wfDatabase.DatabaseSql.PRDatabaseSID + _wfServer.PRName))
                        healthpr = result[_wfDatabase.DatabaseSql.PRDatabaseSID + _wfServer.PRName];
                    if (healthpr.ContainsKey("is_primary"))
                        mshealth.Transaction_Log_Shipping_status_PR = healthpr["is_primary"] == "True" ? "enabled" : "disabled";
                }

                result = LogDataAccess.log_shipping_Enable_Status(condr);
                if (result.Count != 0)
                {
                    if (result.ContainsKey(_wfDatabase.DatabaseSql.DRDatabaseSID + _wfServer.DRName))
                        healthpr = result[_wfDatabase.DatabaseSql.DRDatabaseSID + _wfServer.DRName];
                    if (healthpr.ContainsKey("is_primary"))
                        mshealth.Transaction_Log_Shipping_status_DR = healthpr["is_primary"] == "True" ? "enabled" : "disabled";
                }

                mshealth.IsActive = 1;
                mshealth.CreatorId = 1;
                SqlNativeHealthDataAccess.AddSqlNativeHealthLogs(mshealth);
                SqlNativeHealthDataAccess.AddSqlNativeHealthStatus(mshealth);

            }
        }

        #endregion




        private void BindLuns(IList<GlobalMirrorLuns> lunsList, string[] lunA, string[] lunB, string[] lunC, string[] lunD, string[] lunE, string[] lunF)
        {
            for (var i = 0; i < lunsList.Count; i++)
            {

                lunA[i] = lunsList[i].AVolume;

                Logger.Debug("LUN A [" + i + "] : " + lunA[i]);

                lunB[i] = lunsList[i].BVolume;

                Logger.Debug("LUN B [" + i + "] : " + lunB[i]);

                lunC[i] = lunsList[i].CVolume;

                Logger.Debug("LUN C [" + i + "] : " + lunC[i]);

                lunD[i] = lunsList[i].DVolume;

                Logger.Debug("LUN D [" + i + "] : " + lunD[i]);

                lunE[i] = lunsList[i].EVolume;

                Logger.Debug("LUN E [" + i + "] : " + lunE[i]);

                lunF[i] = lunsList[i].FVolume;

                Logger.Debug("LUN F [" + i + "] : " + lunF[i]);

                Logger.DebugFormat(Environment.NewLine);

            }
        }


        private void PerformAction(WorkflowActionType workflowActionType, bool isParallel)
        {
            try
            {
                ClearConsoleOutput();

                switch (workflowActionType)
                {
                    case WorkflowActionType.PauseGlobalMirror:
                        Logger.InfoFormat(
                           Environment.NewLine
                           + "PAUSE GLOBAL MIRROR "
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRLSSID:" + WFGlobalMirror.PRLSSID
                           + Environment.NewLine
                           + "SESSION ID :" + WFGlobalMirror.SessionId
                           );

                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.PausingGM, true);

                        bool isPauseGM = WFGMOperation.PauseGM(Convert.ToInt32(WFGlobalMirror.PRLSSID), WFGlobalMirror.SessionId, 1);

                        if (isPauseGM)
                        {

                            BindConsoleOutput(OperationType.GMOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);

                            //PauseGlobalMirrorFailed();
                        }


                        break;




                    case WorkflowActionType.ResumeGlobalMirror:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "RESUME GLOBAL MIRROR "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "PRLSSID:" + WFGlobalMirror.PRLSSID
                         + Environment.NewLine
                         + "SESSION ID :" + WFGlobalMirror.SessionId
                         );

                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.ResumingGM, true);

                        bool isResumeGM = WFGMOperation.ResumeGM(Convert.ToInt32(WFGlobalMirror.PRLSSID), WFGlobalMirror.SessionId, 1);

                        if (isResumeGM)
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.ResumeGlobalMirrorFailed);
                        }


                        break;
                    case WorkflowActionType.PerformNormalReflicatin:


                        Logger.InfoFormat(
                          Environment.NewLine
                          + "DATA SYNCHRONIZATION "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PRSTORAGEIMAGEID:" + WFStorageInfo.PRStorageImageId
                          + Environment.NewLine
                          + "DRSTORAGEIMAGEID:" + WFStorageInfo.DRStorageImageId
                          + Environment.NewLine
                          + "HMCDR:" + WFDRDSCLI.DSCLIHost
                          + Environment.NewLine
                          + "DSCLI:" + WFSSHInfo.SSHHost
                          + Environment.NewLine
                          + "PRLSSID:" + WFGlobalMirror.PRLSSID
                          + Environment.NewLine
                          + "SESSION ID :" + WFGlobalMirror.SessionId
                          );

                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.PerformingDataSynchronization, true);

                        NormalReplicationResult dataSynchronization = WFGMOperation.PerformNormalReplication(WFLunInfo, Convert.ToInt32(WFGlobalMirror.PRLSSID), WFGlobalMirror.SessionId);

                        if (dataSynchronization.Result)
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.NRReplicationFailure);
                        }

                        break;
                    case WorkflowActionType.PausePPRC:

                        //  WFGMOperation.PausePPRC(WFGlobalMirror.LSSIDRange, 1);

                        break;
                    case WorkflowActionType.ResumePPRC:

                        break;
                    case WorkflowActionType.FailBackPPRC:

                        break;
                    case WorkflowActionType.MKFlashBC:

                        break;
                    case WorkflowActionType.MKFlashBD:

                        break;
                    case WorkflowActionType.RMFlashBC:

                        break;
                    case WorkflowActionType.RMFlashBD:

                        break;
                    case WorkflowActionType.FailOverPPRC:

                        break;
                    case WorkflowActionType.ApplyIncrementalLogs:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "APPLY LOGS"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                        + Environment.NewLine
                        + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                        + Environment.NewLine
                        + "DATABASE NAME:" + CurrentDatabase.DatabaseOracle.PROracleSID
                        + Environment.NewLine
                        + "SUDO USER :" + WFServer.DRSudoUser
                        );


                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.ApplyingIncrementalLogs, true);
                        bool isApply = WFDROperation.ApplyLogs(CurrentDatabase.DatabaseOracle.PROracleSID, WFServer.DRSudoUser);

                        if (isApply)
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.ApplyLogFailed);
                        }

                        break;

                    case WorkflowActionType.DisableBackupJobPrimary: //abhay
                        DisablebackupJob();
                        break;



                    case WorkflowActionType.MountVG:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "MOUNT VG"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "PRSTORAGEIMAGEID:" + WFStorageInfo.PRStorageImageId
                         + Environment.NewLine
                         + "DRSTORAGEIMAGEID:" + WFStorageInfo.DRStorageImageId
                         + Environment.NewLine
                         + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                         + Environment.NewLine
                         + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                         + Environment.NewLine
                         + "VGNAME:" + WFGroupLuns.AGroup
                         + Environment.NewLine
                         + "MOUNT POINT :" + WFGroupLuns.AMount
                         + Environment.NewLine
                         + "LOG INFO : " + WFLunInfo.LunD
                         );

                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.MountingVolume, true);

                        bool isMount = WFDROperation.MountVG(WFStorageInfo, WFLunInfo.LunD, 2);

                        if (isMount)
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.NRMountVGFailed);
                        }

                        break;

                    case WorkflowActionType.UnMountVG:


                        Logger.InfoFormat(
                        Environment.NewLine
                        + "UNMOUNT VG"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "WFSSHInfoPR:" + WFSSHInfoPR.SSHHost
                        + Environment.NewLine
                        + "WFSSHInfoDR:" + WFSSHInfoDR.SSHHost
                        + Environment.NewLine
                        );

                        UpdateGroupStatus(GroupState.Replicating, (int)ReplicationState.UnMountingVolume, true);

                        bool isUnMount = WFDROperation.UnmountVG();

                        if (isUnMount)
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.DROperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.NRUnMountVGFailed);
                        }


                        break;

                    case WorkflowActionType.ShutDB:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "SHUT DOWN DB "
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "Server IP :" + WFSSHServer.SSHHost
                            + Environment.NewLine
                            + "Sudo User :" + WFServer.PRSudoUser
                            + Environment.NewLine
                            + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                            );

                        bool shutDB = OracleDB.ShutDownDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (shutDB)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFShutDownDataBaseFailed);
                        }

                        break;

                    case WorkflowActionType.SwitchOver:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "SWITCH OVER :"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PR STORAGE IMAGE ID :" + WFGMOperation.PRStorageImageId
                            + Environment.NewLine
                            + "DR STORAGE IMAGE ID :" + WFGMOperation.DRStorageImageId
                            + Environment.NewLine
                            + "PR DSCLI HOST :" + WFGMOperation.PRDSCLIHost
                            + Environment.NewLine
                            + "DR DSCLI HOST :" + WFGMOperation.DRDSCLIHost
                             + Environment.NewLine
                            + "SSH HOST :" + WFGMOperation.SSHHost
                            );


                        bool isSwitchOver = WFGMOperation.PerformSwitchOver(WFLunInfo, Convert.ToInt32(WFGlobalMirror.PRLSSID), "01", "02");

                        if (isSwitchOver)
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStorageSwitchOverFailed);
                        }

                        break;
                    case WorkflowActionType.SwitchBack:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "SWITCH BACK :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR STORAGE IMAGE ID :" + WFGMOperation.PRStorageImageId
                           + Environment.NewLine
                           + "DR STORAGE IMAGE ID :" + WFGMOperation.DRStorageImageId
                           + Environment.NewLine
                           + "PR DSCLI HOST :" + WFGMOperation.PRDSCLIHost
                           + Environment.NewLine
                           + "DR DSCLI HOST :" + WFGMOperation.DRDSCLIHost
                            + Environment.NewLine
                           + "SSH HOST :" + WFGMOperation.SSHHost
                           );


                        bool isSwitchBack = WFGMOperation.PerformSwitchBack(WFLunInfo, Convert.ToInt32(WFGlobalMirror.PRLSSID), "02", "01");


                        if (isSwitchBack)
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.GMOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStorageSwitchBackFailed);
                        }


                        break;
                    //case WorkflowActionType.PerformNormalReflicatin:

                    //    break;
                    case WorkflowActionType.DGConnector:

                        Client.Connect();

                        break;
                    case WorkflowActionType.DGVerifyDBModeAndRole:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "DG VERIFY DBMODE AND ROLE :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR Host IP :" + Client.PrimaryHost.HostName
                           + Environment.NewLine
                           + "DR Host IP  :" + Client.DRHost.HostName
                           + Environment.NewLine
                            // + "Database Name :" + Client.DatabaseName
                           + Environment.NewLine
                           );

                        Client.VerifyDatabaseMode();


                        break;

                    case WorkflowActionType.WinDGCheckUserStatus:
                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                           Environment.NewLine
                           + "WINDOWS DG CHECK USER STATUS :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR Host IP :" + _wfServer.PRIPAddress
                           + Environment.NewLine
                           + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                           + Environment.NewLine
                           );

                                string usercount = SwitchoverwithDG.GetActiveUserCount(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword);

                                if (Convert.ToInt32(usercount) > 0)
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAActiveUserFound, usercount + "Active user (" + usercount + ") found logged in status in Primary Server (" + _wfServer.PRIPAddress + ")");
                                }
                                Logger.DebugFormat("VERIFY USER STATUS : Primary Database ({0}) user status verified successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get Active user count", exc);
                            }

                        }


                        break;
                    case WorkflowActionType.DGCheckUserStatus:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "DG CHECK USER STATUS :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR Host IP :" + Client.PrimaryHost.HostName
                           + Environment.NewLine
                           + "DR Host IP  :" + Client.DRHost.HostName
                           + Environment.NewLine
                            // + "Database Name :" + Client.DatabaseName
                           + Environment.NewLine
                           );


                        Client.VerifyUserStatus();

                        break;
                    case WorkflowActionType.DGJobStatus:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                           Environment.NewLine
                           + "WINDOWS DG JOB STATUS :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                          + "PR Host IP :" + _wfServer.PRIPAddress
                           + Environment.NewLine
                           + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                           + Environment.NewLine
                           );
                                string jobcount = SwitchoverwithDG.GetProcessCount(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword);

                                if (Convert.ToInt32(jobcount) == 0)
                                {
                                    Logger.DebugFormat("VERIFY PRIMARY JOBS : Primary process verified successfully");
                                }

                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get Primary Job count ", exc);
                            }

                        }
                        else
                        {
                            Logger.InfoFormat(
                           Environment.NewLine
                           + "DG JOB STATUS :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PR Host IP :" + PrimaryClient.PrimaryHost.HostName
                           + Environment.NewLine
                                //+ "Database Name :" + PrimaryClient.DatabaseName
                           + Environment.NewLine
                           );

                            PrimaryClient.VerifyPrimaryJobs();
                        }

                        break;
                    case WorkflowActionType.DGVerifyMaxSequenceNumber:

                        Logger.InfoFormat(
                          Environment.NewLine
                          + "DG VERIFY MAX SEQUENCE NO:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + Client.PrimaryHost.HostName
                          + Environment.NewLine
                          + "DR Host IP  :" + Client.DRHost.HostName
                          + Environment.NewLine
                            //+ "Database Name :" + Client.DatabaseName
                          + Environment.NewLine
                          );

                        Client.VerifyMaxSequenceNo();

                        break;

                    case WorkflowActionType.WinDGVerifyMaxSequenceNumber:
                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                          Environment.NewLine
                          + "WINDOWS DG VERIFY MAX SEQUENCE NO:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + _wfServer.PRIPAddress
                          + Environment.NewLine
                          + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          );
                                if (SwitchoverwithDG.CheckMaxSequencCount(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword, _wfTargetServer.PRIPAddress, _wfTargetServerDatabase.DatabaseOracle.PROracleSID, _wfTargetServerDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("VERIFY MAXSEQUENCE NO : Max Sequence number is verified successfully ");

                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORALogsPending);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while verify max sequence no", exc);
                            }


                        }

                        break;
                    case WorkflowActionType.DGSwitchPrimaryToStandBy:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                         Environment.NewLine
                         + "WINDOWS DG SWITCH PRIMARY TO STANDBY:"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "PR Host IP :" + _wfServer.PRIPAddress
                         + Environment.NewLine
                         + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         );
                                if (SwitchoverwithDG.PrimaryDbSwitchToStandbyMode(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("SWITCH TO STANDBY MODE : Database({0}) Switch Database Primary to Standby mode successfully", CurrentDatabase.DatabaseOracle.PROracleSID);
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORASwitchDBToStandbyFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch Database to Standby mode", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                          Environment.NewLine
                          + "DG SWITCH PRIMARY TO STANDBY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + PrimaryClient.PrimaryHost.HostName
                          + Environment.NewLine
                                // + "Database Name :" + PrimaryClient.DatabaseName

                         // + "Database Name :" + PrimaryClient.DatabaseName
                          + Environment.NewLine
                          );

                            PrimaryClient.SwitchToStandbyMode();
                        }

                        break;
                    case WorkflowActionType.DGSwitchStandByToPrimary:



                        //DRClient.SwitchToPrimaryMode();
                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                          Environment.NewLine
                          + "WINDOWS DG SWITCHSTANDBY TO PRIMARY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "DR Host IP  :" + _wfServer.PRIPAddress
                          + Environment.NewLine
                          + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          );
                                //  if (SwitchoverwithDG.StandbyDbSwitchToPrimaryMode(_wfServer.DRIPAddress, _wfDatabase.DRDatabaseName, _wfDatabase.DRPassword))
                                if (SwitchoverwithDG.StandbyDbSwitchToPrimaryMode(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("SWITCH TO PRIMARY MODE : Database({0}) Switch Database Stanby to Primary mode successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORASwitchDBToPrimaryModeFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch Database to primary mode", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                          Environment.NewLine
                          + "DG SWITCHSTANDBY TO PRIMARY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "DR Host IP  :" + DRClient.DRHost.HostName
                          + Environment.NewLine
                                // + "Database Name :" + DRClient.DatabaseName
                          + Environment.NewLine
                          );

                            DRClient.SwitchToPrimaryMode();
                        }

                        break;
                    case WorkflowActionType.DGShutDownPrimary:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                          Environment.NewLine
                          + "WINDOWS DG SHUT DOWN PRIMARY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + _wfServer.PRIPAddress
                          + Environment.NewLine
                          + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          );

                                if (SwitchoverwithDG.ShutdownStandbyHost(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("SHUTDOWN PRIMARY : Shutdown Primary Database({0}) successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAShutdownPrimaryDBFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while shutdown primary Database", exc);
                            }

                        }
                        else
                        {
                            Logger.InfoFormat(
                          Environment.NewLine
                          + "DG SHUT DOWN PRIMARY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + PrimaryClient.PrimaryHost.HostName
                          + Environment.NewLine
                                // + "Database Name :" + PrimaryClient.DatabaseName
                          + Environment.NewLine
                          );
                            PrimaryClient.ShutdownPrimary();
                        }

                        break;
                    case WorkflowActionType.DGMountStandBy:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {

                            try
                            {
                                Logger.InfoFormat(
                          Environment.NewLine
                          + "DG MOUNT STANDBY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + _wfServer.PRIPAddress
                          + Environment.NewLine
                          + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          );
                                if (SwitchoverwithDG.MountStandbyHost(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("MOUNT STANDBY : Mount Standby Database ({0}) successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAMountStandbyFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while mounting standby Database", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                          Environment.NewLine
                          + "DG MOUNT STANDBY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + PrimaryClient.PrimaryHost.HostName
                          + Environment.NewLine
                                // + "Database Name :" + PrimaryClient.DatabaseName
                          + Environment.NewLine
                          );
                            PrimaryClient.MountStandby();
                        }

                        break;
                    case WorkflowActionType.DGStartPrimary:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                         Environment.NewLine
                         + "DG START PRIMARY"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "DR Host IP  :" + _wfServer.PRIPAddress
                         + Environment.NewLine
                         + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         );
                                //if (SwitchoverwithDG.StartPrimaryDatabase(_wfServer.DRIPAddress, _wfDatabase.DRDatabaseName, _wfDatabase.DRPassword))
                                if (SwitchoverwithDG.StartPrimaryDatabase(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("START PRIMARY DATABASE : Start Primary Database ({0}) successfully", _wfDatabase.DatabaseOracle.PROracleSID);
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAStartPrimaryDBFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while start primary database", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                         Environment.NewLine
                         + "DG START PRIMARY"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "DR Host IP  :" + DRClient.DRHost.HostName
                         + Environment.NewLine
                                //+ "Database Name :" + DRClient.DatabaseName
                         + Environment.NewLine
                         );
                            DRClient.StartPrimary();
                        }

                        break;
                    case WorkflowActionType.DGSwitchLogFile:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                         Environment.NewLine
                         + "WINDOWS DG SWITCH LOG FILE:"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "DR Host IP  :" + _wfServer.PRIPAddress
                         + Environment.NewLine
                         + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         );
                                // if (SwitchoverwithDG.PrimarySwitchtoLogfile(_wfServer.DRIPAddress, _wfDatabase.DRDatabaseName, _wfDatabase.DRPassword))
                                if (SwitchoverwithDG.PrimarySwitchtoLogfile(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("SWITCH LOGFILE : Switch Primary Log file successfully");
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORASwitchPrimaryLogFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch primary log files", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                         Environment.NewLine
                         + "DG SWITCH LOG FILE:"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "DR Host IP  :" + DRClient.DRHost.HostName
                         + Environment.NewLine
                                //+ "Database Name :" + DRClient.DatabaseName
                         + Environment.NewLine
                         );
                            DRClient.SwitchLogFile();
                        }

                        break;
                    case WorkflowActionType.DGRecoverStandBy:


                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                Logger.InfoFormat(
                          Environment.NewLine
                          + "WINDOWS DG RECOVER STANDBY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + _wfServer.PRIPAddress
                          + Environment.NewLine
                          + "Database Name :" + _wfDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          );
                                if (SwitchoverwithDG.StandbyHostRecovery(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    Logger.DebugFormat("START STANDBY RECOVERY : Start Standby Recovery successfully");
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.ORAStartStandbyRecoveryFailed);
                                }
                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while standby recovery ", exc);
                            }


                        }
                        else
                        {
                            Logger.InfoFormat(
                          Environment.NewLine
                          + "DG RECOVER STANDBY:"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "PR Host IP :" + PrimaryClient.PrimaryHost.HostName
                          + Environment.NewLine
                                // + "Database Name :" + PrimaryClient.DatabaseName
                          + Environment.NewLine
                          );
                            PrimaryClient.StartStandbyRecovery();
                        }

                        break;
                    case WorkflowActionType.PowerOnMachine:

                        PowerOnVM();

                        break;
                    case WorkflowActionType.PowerOffMachine:
                        PowerOffVM();
                        break;
                    case WorkflowActionType.ExecuteOSCommand:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "EXECUTE OSCOMMAND :"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "SSHSERVER :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "COMMAND :" + _wfCommand
                           );

                        bool isExecuteOsTask = AIXOperations.ExecuteOSTask(_wfCommand, WFSSHServer, WFServer.PRSudoUser);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isExecuteOsTask)
                        {
                            throw new BcmsException(BcmsExceptionType.WFExecuteOSCommandFailed, "Execute OS Command failed");
                        }

                        break;

                    case WorkflowActionType.MakeGMIR:

                        //   WFGMOperation.InvokeMKGMIR(_wfl

                        break;
                    case WorkflowActionType.RemoveGMIR:

                        break;
                    case WorkflowActionType.MakeSession:



                        break;
                    case WorkflowActionType.ChangeSession:

                        //  WFGMOperation.InvokeChSession();

                        break;
                    case WorkflowActionType.MountVGS:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "MOUNT VGS "
                           + Environment.NewLine
                           + "================================="
                            + Environment.NewLine
                           + "Storage PR Info :" + WFStorageInfo.PRStorageImageId + "Storage DR Info :" + WFStorageInfo.DRStorageImageId
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "VG Name :" + WFVGName
                           + Environment.NewLine
                           );

                        foreach (string mount in _wfMountPoints)
                        {
                            Logger.Debug("MOUNT POINTS :" + mount);

                        }
                        foreach (string luns in _wfLundD)
                        {
                            Logger.Debug("LUNS D :" + luns);

                        }

                        bool mountvgs = AIXOperations.MountVGS(WFStorageInfo, WFSSHServer, WFVGName, _wfMountPoints, _wfLundD);


                        if (mountvgs)
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFMountVGSFailed);
                        }
                        break;
                    case WorkflowActionType.UnmountVGS:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "UNMOUNT VGS "
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "VG Name :" + WFVGName
                           + Environment.NewLine
                           );

                        foreach (string mount in _wfMountPoints)
                        {
                            Logger.Debug("MOUNT POINTS :" + mount);
                        }

                        bool unmountvgs = AIXOperations.UnmountVGS(WFSSHServer, WFVGName, _wfMountPoints);

                        if (unmountvgs)
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFUnmountVGSFailed);
                        }

                        break;
                    case WorkflowActionType.ExecuteDBCommand:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EXCEUTE DB COMMAND"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       + Environment.NewLine
                       + "COMMAND :" + _wfCommand
                        );

                        OracleDB.ExecuteSQLCommand(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, _wfCommand);

                        BindConsoleOutput(OperationType.OracleDB, isParallel);


                        break;
                    case WorkflowActionType.ExecuteStorageCommand:
                        Logger.InfoFormat(
                            Environment.NewLine
                            + "EXECUTE STORAGE COMMAND"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "DSCLI IP :" + WFSSHInfo.SSHHost
                            + Environment.NewLine
                            + "Storage Command :" + _wfCommand
                            );

                        StorageOperations.ExecuteStorageCommand(_wfCommand, WFSSHInfo, _wfPRorDR == 1 ? WFPRDSCLI : WFDRDSCLI);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        break;
                    case WorkflowActionType.StartDatabaseNoMount:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "START DATABASE UNMOUNT"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Sudo User :" + WFServer.PRSudoUser
                           + Environment.NewLine
                           + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                           );
                        bool nomount = OracleDB.StartDatabaseNoMount(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (nomount)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartDatabaseNoMountFailed);
                        }

                        break;
                    case WorkflowActionType.StartDatabaseMount:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "START DB MOUNT"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFSSHServer.SSHHost
                    + Environment.NewLine
                   + "Sudo User :" + WFServer.PRSudoUser
                   + Environment.NewLine
                   + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                    );

                        bool statDatabaseMount = OracleDB.StartDatabaseMount(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (statDatabaseMount)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartDatabaseMountFailed);
                        }
                        break;
                    case WorkflowActionType.OpenDatabase:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "OPEN DATABASE"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Sudo User :" + WFServer.PRSudoUser
                           + Environment.NewLine
                           + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                           );

                        bool openDB = OracleDB.OpenDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (openDB)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFOpenDatabaseFailed);
                        }

                        break;
                    case WorkflowActionType.AlterDatabaseOpen:


                        Logger.InfoFormat(
                       Environment.NewLine
                       + "ALTER DATABASE OPEN"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        bool alterDb = OracleDB.AlterDatabaseOpen(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (alterDb)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFAlterDatabaseOpenFailed);
                        }

                        break;


                    case WorkflowActionType.AlterDatabaseMount:

                        Logger.InfoFormat(
                       Environment.NewLine
                       + "ALTER DATABASE MOUNT"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                       + Environment.NewLine
                       + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        bool alterDbMount = OracleDB.AlterDatabaseMount(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (alterDbMount)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFAlterDatabaseMountFailed);
                        }


                        break;


                    case WorkflowActionType.BackUpControlFile:

                        Logger.InfoFormat(
                            Environment.NewLine
                          + "BACKUP CONTROL FILE "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User :" + WFServer.PRSudoUser
                          + Environment.NewLine
                          + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                          + Environment.NewLine
                          + "Control file path :" + WFControlFilePath
                          );

                        bool isbackupControl = OracleDB.BackupControlFile(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, WFControlFilePath);

                        if (isbackupControl)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFBackUpControlFileFailed);
                        }


                        break;
                    case WorkflowActionType.CreateStandbyControlFile:

                        Logger.InfoFormat(
                           Environment.NewLine
                         + "CREATE STANDBY CONTROL FILE "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         + "Standby Control file path :" + WFStandbyControlFile
                         );

                        bool isStandbyControl = OracleDB.CreateStandByControlFile(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, WFStandbyControlFile);

                        if (isStandbyControl)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFCreateStandbyControlFileFailed);
                        }

                        break;

                    case WorkflowActionType.MSSqlGenerateLastLog_FC:
                        GenerateLog(true);
                        break;

                    case WorkflowActionType.MSSqlRestoreLog:
                        ApplyLog(false);
                        break;


                    case WorkflowActionType.MSSqlKillProcess_Prim:
                        KillProcessPR();
                        break;

                    case WorkflowActionType.MSSqlKillProcess_Sec:
                        KillProcessDR();
                        break;

                    case WorkflowActionType.MSSqlRestoreLastLog:
                        ApplyLog(true);
                        break;


                    case WorkflowActionType.MSSqlDBOption:
                        RunDBOption();
                        break;

                    case WorkflowActionType.FastCopySql2000:
                        sql2000FastCopy();
                        Thread.Sleep(20000);
                        break;

                    case WorkflowActionType.MSSqlRestoreDBWithRecovery:
                        SwitchDatabaseToPrimary();
                        break;

                    case WorkflowActionType.MigrateLoging_PR:
                        RunStoreProcedurePR("EXEC[dbo].[sp_help_revlogin]", "MigrateLogin.sql");
                        break;

                    case WorkflowActionType.MigrateLoging_DR:
                        RunScriptDr("MigrateLogin.sql");
                        break;

                    case WorkflowActionType.MigrateServer_roles_PR:
                        RunStoreProcedurePR("EXEC[dbo].[af_revLoginAttrib_2k]", "MigrateServerrole.sql");
                        break;

                    case WorkflowActionType.MigrateServer_roles_DR:
                        RunScriptDr("MigrateServerrole.sql");
                        break;


                    case WorkflowActionType.SCP:

                        Logger.InfoFormat(
                          Environment.NewLine
                        + "SCP REPLICATION"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Source Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Standby Control file path :" + WFStandbyControlFile
                        );

                        bool pscp = AIXOperations.PSCP(WFSSHServer, WFTARGETSSHServer, _wfScpFile);

                        if (pscp)
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFSCPFailed);
                        }

                        break;
                    case WorkflowActionType.RestoreStandbyControlFile:

                        Logger.InfoFormat(
                         Environment.NewLine
                       + "RESTORE STANDBY CONTROL FILE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Source Server IP :" + WFSSHServer.SSHHost
                       + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                         + Environment.NewLine
                         + "Standby Control file path :" + WFStandbyControlFile
                       );

                        bool restoreStand = OracleDB.RestoreStandByControlFile(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, WFStandbyControlFile);

                        if (restoreStand)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFRestoreStandbyControlFileFailed);
                        }

                        break;
                    case WorkflowActionType.RecoverStandbyDatabaseFile:

                        Logger.InfoFormat(
                     Environment.NewLine
                   + "RECOVER STANDBY DATABASE"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "Source Server IP :" + WFSSHServer.SSHHost
                   + Environment.NewLine
                     + "Sudo User :" + WFServer.PRSudoUser
                     + Environment.NewLine
                     + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                     + Environment.NewLine
                     + "Standby Control file path :" + WFStandbyControlFile
                   );
                        bool recoverStand = OracleDB.RecoverStandByDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (recoverStand)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFRecoverStandbyDatabaseFileFailed);
                        }


                        break;
                    case WorkflowActionType.RecoverDatabase:

                        Logger.InfoFormat(
                          Environment.NewLine
                          + "RECOVER DATABASE "
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User :" + WFServer.PRSudoUser
                          + Environment.NewLine
                          + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                          );

                        bool isReverse = OracleDB.RecoverDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isReverse)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFRecoverDatabaseFailed);
                        }

                        break;
                    case WorkflowActionType.CreateControlFileByScript:

                        Logger.InfoFormat(
                      Environment.NewLine
                      + "CREATE CONTROL FILE BY SCRIPT "
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Server IP :" + WFSSHServer.SSHHost
                      + Environment.NewLine
                     + "Sudo User :" + WFServer.PRSudoUser
                     + Environment.NewLine
                     + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                     + Environment.NewLine
                      + "Script file path :" + WFScriptFilePath
                      );

                        bool isCreateCtrlSucces = OracleDB.CreateCtrlFileByScript(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, WFScriptFilePath);

                        if (isCreateCtrlSucces)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.CreateCtrlFileByScriptFailed);
                        }

                        break;
                    case WorkflowActionType.CreateControlFileScript:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "CREATE CONTROL FILE SCRIPT "
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Control file path :" + WFControlFilePath
                         + Environment.NewLine
                         + "Script file path :" + WFScriptFilePath
                         );

                        bool isCreateControl = AIXOperations.CreateControlFileScript(WFSSHServer, WFServer.PRSudoUser, WFControlFilePath, WFScriptFilePath);

                        if (isCreateControl)
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFCreateControlFileScriptFailed);
                        }

                        break;

                    case WorkflowActionType.ISDBRunning:


                        break;
                    case WorkflowActionType.ISDBDown:

                        break;

                    case WorkflowActionType.AlterSystemLog:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "ALTER SYSTEM LOG"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        bool alterSystem = OracleDB.AlterSystemArchiveLogCurrent(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (alterSystem)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFAlterSystemLogFailed);
                        }

                        break;
                    case WorkflowActionType.CreateTempFile:


                        Logger.InfoFormat(
                        Environment.NewLine
                        + "CREATE TEMP FILE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Control File Path :" + _wfControlFilePath
                        + Environment.NewLine
                        + "Temp File path:" + _wfTempFilePath
                        );

                        bool createTemp = AIXOperations.CreateTempFile(WFSSHServer, WFServer.PRSudoUser, _wfControlFilePath, _wfTempFilePath);

                        if (createTemp)
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.AIXOperations, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFCreateTempFileFailed);
                        }

                        break;
                    case WorkflowActionType.CreateTempFileTableSpace:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "CREATE TEMP FILE TABLE SPACE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                       + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        + Environment.NewLine
                        + "Temp File path:" + _wfTempFilePath
                        );

                        bool createTempSpace = OracleDB.CreateTempTableSpaceFile(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, _wfTempFilePath);

                        if (createTempSpace)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                            throw new BcmsException(BcmsExceptionType.WFCreateTempFileTableSpaceFailed);
                        }

                        break;
                    case WorkflowActionType.IsCheckPointCountOne:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "IS CHECK POINT COUNT ONE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                        );
                        int checkPointcount = OracleDB.GetCheckPointCount(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (checkPointcount <= 1)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFIsCheckPointCountOneFailed);
                        }

                        break;


                    case WorkflowActionType.StartDBStandBy:

                        Logger.InfoFormat(
                    Environment.NewLine
                    + "START DB STANDBY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFSSHServer.SSHHost
                    + Environment.NewLine
                   + "Sudo User :" + WFServer.PRSudoUser
                   + Environment.NewLine
                   + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                    );


                        bool isStartStand = OracleDB.StartDatabaseStandBy(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isStartStand)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartDBStandByFailed);
                        }

                        break;

                    case WorkflowActionType.StartDBReadWrite:


                        Logger.InfoFormat(
                    Environment.NewLine
                    + "START DB R/W "
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFSSHServer.SSHHost
                    + Environment.NewLine
                   + "Sudo User :" + WFServer.PRSudoUser
                   + Environment.NewLine
                   + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                    );

                        bool isStartRead = OracleDB.StartDatabaseReadWrite(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);
                        if (isStartRead)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartDBReadWriteFailed);
                        }

                        break;
                    case WorkflowActionType.ReplicateStandByControlFile:


                        Logger.InfoFormat(
                 Environment.NewLine
                 + "REPLICATION STANDBY CONTORL"
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "Server IP :" + WFSSHServer.SSHHost
                 + Environment.NewLine
                + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                + Environment.NewLine
                + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                 );
                        bool isReplicate = OracleDB.ReplicateStandByCtrlFile(WFSSHServer, WFTARGETSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isReplicate)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFReplicateStandByControlFileFailed);
                        }
                        break;


                    case WorkflowActionType.SwitchShutPrimaryDB:

                        Logger.InfoFormat(
                 Environment.NewLine
                 + "SWITCH SHUT PRIMARY DB"
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "Server IP :" + WFSSHServer.SSHHost
                 + Environment.NewLine
                + "Sudo User:" + WFServer.PRSudoUser
                + Environment.NewLine
                + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                 );



                        bool isSwitchShut = OracleDB.SwitchShutPrimaryDB(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isSwitchShut)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFSwitchShutPrimaryDBFailed,
                                                    "Switch shut Primary db process failed");
                        }

                        break;
                    case WorkflowActionType.SetJobQueProcess:
                        break;
                    case WorkflowActionType.CheckJobQueProcess:
                        break;
                    case WorkflowActionType.DeleteArchiveLogs:
                        break;
                    case WorkflowActionType.IsFileSystemMounted:

                        Logger.InfoFormat(
                       Environment.NewLine
                       + "IS MOUNT VG"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "WFSSHServer:" + WFSSHServer.SSHHost
                       + Environment.NewLine
                       + "MOUNT POINT :" + WFGroupLuns.AMount
                       + Environment.NewLine
                       );


                        //bool isMounted = AIXOperations.IsFSMounted(WFSSHServer, _wfMountPoint);

                        //if (!isMounted)
                        //{
                        //    throw new BcmsException(BcmsExceptionType.NRMountVGFailed, "Mount VG Failed");

                        //}

                        break;
                    case WorkflowActionType.ExecuteRMANCommand:



                        break;
                    case WorkflowActionType.IsCGFormed:

                        Logger.InfoFormat(
                      Environment.NewLine
                      + "IS CG FORMED"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "DSCLI:" + CurrentSSHInfo.SSHHost

                      + Environment.NewLine
                      + "HMCSERVER:" + CurrentPRDSCLI.DSCLIHost

                      + Environment.NewLine
                      + "STORAGE IMAGE ID:" + CurrentGlobalMirror.PRStorageImageId


                      + Environment.NewLine
                      + "MOUNT POINT :" + WFGroupLuns.AMount
                      + Environment.NewLine
                      );

                        //CheckCGTime();


                        break;
                    case WorkflowActionType.VerifyLogSequence:

                        //CheckPairGroups();

                        break;

                    case WorkflowActionType.ActiveDatabaseReadWrite:


                        Logger.InfoFormat(
                   Environment.NewLine
                   + "ACTIVATE DATABASE R/W "
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "Server IP :" + WFSSHServer.SSHHost
                   + Environment.NewLine
                  + "Sudo User :" + WFServer.PRSudoUser
                  + Environment.NewLine
                  + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                   );

                        bool isActive = OracleDB.ActivateStByDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isActive)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFActivateStByDatabaseFailed,
                                                    "Activate Standby Database Failed");
                        }

                        break;

                    case WorkflowActionType.StartDatabase:

                        Logger.InfoFormat(
                  Environment.NewLine
                  + "START DATABASE "
                  + Environment.NewLine
                  + "================================="
                  + Environment.NewLine
                  + "Server IP :" + WFSSHServer.SSHHost
                  + Environment.NewLine
                 + "Sudo User :" + WFServer.PRSudoUser
                 + Environment.NewLine
                 + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                  );


                        bool isStartDB = OracleDB.StartDatabase(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isStartDB)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartDatabaseFailed, "Start Database Failed");
                        }

                        break;

                    case WorkflowActionType.ImportVG:

                        break;

                    case WorkflowActionType.ReplicateStandByCtrFile:

                        Logger.InfoFormat(
                  Environment.NewLine
                  + "REPLICATE STANDBY CONTROL FILE "
                  + Environment.NewLine
                  + "================================="
                  + Environment.NewLine
                  + "Server IP :" + WFSSHServer.SSHHost
                  + Environment.NewLine
                 + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                 + Environment.NewLine
                 + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                  );
                        bool isReplicateCtrl = OracleDB.ReplicateStandByCtrlFile(WFSSHServer, WFTARGETSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isReplicateCtrl)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFReplicateStandByCtrlFileFailed,
                                                    "Replicate stand by control file Failed");
                        }
                        break;

                    case WorkflowActionType.ReplicateTraceFile:

                        Logger.InfoFormat(
                 Environment.NewLine
                 + "REPLICATE TRACE FILE "
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "Server IP :" + WFSSHServer.SSHHost
                 + Environment.NewLine
                + "Target Server IP:" + WFTARGETSSHServer.SSHHost
                + Environment.NewLine
                + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                 );
                        bool isReplicateTrace = OracleDB.ReplicateTraceFile(WFSSHServer, WFTARGETSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isReplicateTrace)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFReplicateTraceFileFailed,
                                                    "Replicate Trace file Failed");
                        }


                        break;

                    case WorkflowActionType.StartListner:
                        break;
                    case WorkflowActionType.StopListner:
                        break;
                    case WorkflowActionType.ReStartListner:
                        break;
                    case WorkflowActionType.CheckGroupSyncStatus:
                        break;
                    case WorkflowActionType.ChangeDNS:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "CHANGE DNS"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "DNS Server IP :" + CurrentDNSServer.ServerIP
                        + Environment.NewLine
                        + "Domain Name :" + _wfDomainName
                        + Environment.NewLine
                        + "Exist Host Name:" + _wfExistingHost
                        + Environment.NewLine
                        + "Exis Host IP:" + _wfExistingIP
                        + Environment.NewLine
                        + "New Host Name:" + _wfNewHost
                        + Environment.NewLine
                        + "New Host IP:" + _wfNewIp
                        );

                        bool isChangeDNS = DNSManager.ChangeDNSRecord(CurrentDNSServer, _wfDomainName, _wfExistingHost, _wfExistingIP, _wfNewHost, _wfNewIp);

                        if (!isChangeDNS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDNSChangeFailed, "DNS change process failed");

                        }

                        break;


                    case WorkflowActionType.CheckNoLoggingOperation:

                        Logger.InfoFormat(
                          Environment.NewLine
                        + "CHECKNOLOGGINGOPERATION"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Source Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "PR Sudo User :" + WFServer.PRSudoUser
                        + Environment.NewLine
                        + "Database Name:" + WFDatabase.DatabaseOracle.PROracleSID
                        );

                        var nologgingOpResult = OracleDB.CheckNoLoggingOperation(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (nologgingOpResult.Result)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.NologinoperaionPerformed, nologgingOpResult.Alert);
                        }


                        break;


                    case WorkflowActionType.DisableCopyJobDr:
                        DisableCopyJob();
                        break;
                    case WorkflowActionType.DisableRestoreJobDr:
                        DisableRestoreJob();
                        break;
                    case WorkflowActionType.StartCopyJobDr:

                        break;
                    case WorkflowActionType.MSSqlVerifyLogSequence:
                        CheckReplication();
                        break;

                    case WorkflowActionType.NativeDeleteLastLog:
                        // result = deletefile();
                        break;

                    case WorkflowActionType.UpdateSecondaryServerBackUpJob:
                        updatebackupjob();
                        break;

                    case WorkflowActionType.GenerateLastLog:

                        break;
                    case WorkflowActionType.DisableSecondaryDbJob:

                        break;
                    case WorkflowActionType.MakeSingleUser:

                        break;


                    case WorkflowActionType.RestoreLogShipping:

                        break;

                    case WorkflowActionType.BackUpPrimaryDatabase:

                        // Group DRShareFolder


                        //string sqlstr = SQLServerMonitor.DataAccess.BackUpPrimaryQuery(WFDatabase.DatabaseSql.PRDatabaseSID, txtSharefolderDR.Text);
                        //string constr = SQLServerMonitor.DataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFServer.PRUserName, WFServer.PRPassword);
                        //string result = SQLServerMonitor.DataAccess.ExecuteQuerry(constr, sqlstr).ToLower();

                        //if (result.Contains("failure"))
                        //{
                        //    // Throw exception
                        //}




                        break;
                    case WorkflowActionType.RestoreSecondaryDatabase:

                        // string sqlstr = SQLServerMonitor.DataAccess.RestoreStandbyQuery(WFDatabase.DatabaseSql.PRDatabaseSID, txtSharefolderDR.Text);
                        //string constr1 = SQLServerMonitor.DataAccess.Connectionstring(WFServer.PRIPAddress, "Master", WFServer.PRUserName, WFServer.PRPassword);
                        //string result1 = SQLServerMonitor.DataAccess.ExecuteQuerry(constr1, sqlstr1).ToLower();

                        //if (result1.Contains("failure"))
                        //{
                        //    // Throw exception
                        //}


                        break;
                    case WorkflowActionType.RestoreLogShippingPrimary:
                        enablelogshipPR();
                        break;
                    case WorkflowActionType.AddSecondaryLog:

                        break;
                    case WorkflowActionType.RestoreLogshipSecondary:
                        enablelogshipDR();
                        break;
                    case WorkflowActionType.UpdateSecondaryServerCopyJob:
                        updatecopyjob();
                        break;
                    case WorkflowActionType.UpdateSecondaryServerRestoreJob:
                        updaterestorejob();
                        break;

                    case WorkflowActionType.MonitorLogShipping:
                        //SQLServerMonitor.DataAccess.Monitoring(WFServer.PRIPAddress, WFServer.PRUserName,
                        //                                       WFServer.PRPassword,
                        //                                       WFTargetServer.PRIPAddress,
                        //                                       WFTargetServer.PRUserName,
                        //                                       WFTargetServer.PRPassword, WFDatabase.DatabaseSql.PRDatabaseSID);

                        break;

                    case WorkflowActionType.MSSqlGenerateLastLog:
                        lastbackuplog();
                        break;

                    case WorkflowActionType.MSSqlKillProcessDR:
                        killprocesssecondary();
                        break;

                    case WorkflowActionType.MSSqlKillProcessPR:
                        killprocessprimary();
                        break;

                    case WorkflowActionType.NativeSetdbOption:
                        setoptionDB();
                        break;

                    case WorkflowActionType.RestoreLastLogNative:
                        RestoreLastLog();
                        break;

                    case WorkflowActionType.MigrateLogingPR:
                        migrateLoginprimary();
                        break;

                    case WorkflowActionType.MigrateLogingDR:
                        migrateLoginsecondary();
                        break;

                    case WorkflowActionType.NativeRemovePRDRLS:
                        RemovelogshippingPRDR();
                        break;
                    case WorkflowActionType.NativeRemoveLSPri:
                        removelogshipPR();
                        break;

                    case WorkflowActionType.NativeRemoveLSSEC:
                        var result = removelogshipDR();
                        break;

                    case WorkflowActionType.RestoreSecondaryWithRecovery:
                        RestorewithRecovery();
                        break;

                    case WorkflowActionType.RunBackJob:

                        break;
                    case WorkflowActionType.RunCopyJob:

                        break;
                    case WorkflowActionType.RunRestoreJob:

                        break;
                    case WorkflowActionType.NativeChangeRolePri_Sec:

                        SwitchPrimaryServer();
                        break;

                    case WorkflowActionType.NativeSetFileNameFlag:
                        SetLastFileFlag();
                        break;


                    case WorkflowActionType.AddDNS:
                        Logger.InfoFormat(
                        Environment.NewLine
                        + "ADD DNS"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "DNS Server IP :" + CurrentDNSServer.ServerIP
                        + Environment.NewLine
                        + "Domain Name :" + _wfDomainName
                        + Environment.NewLine
                        + "Host Name:" + _wfNewHost
                        + Environment.NewLine
                        + "Host IP:" + _wfNewIp
                        );

                        bool isAddDNS = DNSManager.AddDNSRecord(CurrentDNSServer, _wfDomainName, _wfNewHost, _wfNewIp);

                        if (!isAddDNS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDNSAddFailed, "DNS Add process failed");
                        }

                        break;
                    case WorkflowActionType.DeleteDNS:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "DELETE DNS"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "DNS Server IP :" + CurrentDNSServer.ServerIP
                         + Environment.NewLine
                         + "Domain Name :" + _wfDomainName
                         + Environment.NewLine
                         + "Host Name:" + _wfNewHost
                         + Environment.NewLine
                         + "Host IP:" + _wfNewIp
                         );


                        bool isDeleteDNS = DNSManager.DeleteDNSRecord(CurrentDNSServer, _wfDomainName, _wfNewHost, _wfNewIp);

                        if (!isDeleteDNS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDNSDeleteFailed, "DNS Delete process failed");

                        }
                        break;
                    case WorkflowActionType.StartOracleListner:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "START ORACLE LISTENER"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User:" + WFServer.PRSudoUser
                         );

                        bool isStartListener = OracleDB.StartListner(WFSSHServer, WFServer.PRSudoUser);

                        if (isStartListener)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStartOracleListnerFailed,
                                                    "Start Oracle Listener Failed");
                        }

                        break;
                    case WorkflowActionType.StopOracleListner:


                        Logger.InfoFormat(
                         Environment.NewLine
                         + "STOP ORACLE LISTENER"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User:" + WFServer.PRSudoUser
                         );


                        bool isStopListener = OracleDB.StopListner(WFSSHServer, WFServer.PRSudoUser);

                        if (isStopListener)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {

                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFStopOracleListnerFailed,
                                                    "Stop Oracle Listener Failed");
                        }

                        break;


                    case WorkflowActionType.PreShutRedoCtrlScript:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "GENERATE COPY REDO FILE SCRIPT"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Sudo User:" + WFServer.PRSudoUser
                         + Environment.NewLine
                         + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                         );

                        bool isCopyRedo = OracleDB.GenerateCopyRedoCtrlFileScript(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isCopyRedo)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFPreShutRedoCtrlScriptFailed, "Generate Copy Redo Control File Script process failed");
                        }

                        break;

                    case WorkflowActionType.CopyRedoCtrFile:

                        Logger.InfoFormat(
                          Environment.NewLine
                          + "COPY REDO CONTROL FILE SCRIPT"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Sudo User:" + WFServer.PRSudoUser
                           + Environment.NewLine
                          + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                          );

                        bool isRedoCtrl = OracleDB.ExecuteCopyRedoCtrlScript(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (isRedoCtrl)
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);
                        }
                        else
                        {
                            BindConsoleOutput(OperationType.OracleDB, isParallel);

                            throw new BcmsException(BcmsExceptionType.WFCopyRedoCtrFileFailed, "Copy Redo Ctrl File process Failed");
                        }
                        break;

                    case WorkflowActionType.StartRestoreJobDr:

                        break;
                    case WorkflowActionType.KillTransactionProcess:

                        break;
                    case WorkflowActionType.CheckTargetDBFileStatus:
                        Logger.InfoFormat(
                           Environment.NewLine
                           + "CHECK TARGET DB FILE STATUS"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        bool isCheckTargetDBFileStatus = ExchangeFunction.CheckTargetDBFileStatus(CurrentSCRConfig);


                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isCheckTargetDBFileStatus)
                        {
                            throw new BcmsException(BcmsExceptionType.CheckTargetDBFileStatusFailed, "Check Target DB File Status process failed");
                        }
                        break;
                    case WorkflowActionType.AllowFileRestoreToMailboxDB:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "ALLOW FILE RESTORE TO MAILBOX DB"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        bool isAllowFileRestoreToMailboxDB = ExchangeFunction.AllowFileRestoreToMailboxDB(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isAllowFileRestoreToMailboxDB)
                        {
                            throw new BcmsException(BcmsExceptionType.AllowFileRestoreToMailboxDBFailed, "Allow File Restore To Mailbox DB process failed");
                        }

                        break;
                    case WorkflowActionType.MountDatabase:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "MOUNT DATABASE"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        bool isMountDatabase = ExchangeFunction.MountDatabase(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isMountDatabase)
                        {
                            throw new BcmsException(BcmsExceptionType.MountDatabaseFailed, "Mount Database process failed");
                        }

                        break;

                    case WorkflowActionType.GetMaiboxListCountbeforeswitch:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "GET MAIBOX LISTCOUNT BEFORE SWITCH"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );


                        var isGetMaiboxListCountbeforeswitch = ExchangeFunction.GetMailboxListCountbeforeswitch(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (isGetMaiboxListCountbeforeswitch == null || isGetMaiboxListCountbeforeswitch == 0)
                        {
                            throw new BcmsException(BcmsExceptionType.GetMaiboxListCountbeforeswitchFailed, "Get Maibox ListCount before switch process failed");
                        }

                        break;

                    case WorkflowActionType.MoveMailboxConfiguration:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "MOVE MAILBOX CONFIGURATION"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        bool isMoveMailboxConfiguration = ExchangeFunction.MoveMailboxConfiguration(CurrentSCRConfig);



                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isMoveMailboxConfiguration)
                        {
                            throw new BcmsException(BcmsExceptionType.MoveMailboxConfigurationFailed, "Move Mailbox Configuration process failed");
                        }

                        break;

                    case WorkflowActionType.GetMaiboxListCount:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "GET MAIBOX LISTCOUNT"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        var isGetMaiboxListCount = ExchangeFunction.GetMailboxListCount(CurrentSCRConfig);
                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (isGetMaiboxListCount == 0)
                        {
                            throw new BcmsException(BcmsExceptionType.GetMaiboxListCountFailed, "Get Maibox ListCount process failed");
                        }

                        break;
                    case WorkflowActionType.EnableSCR:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "ENABLE SCR"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );


                        var isEnableSCR = ExchangeFunction.EnableSCR(CurrentSCRConfig);
                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isEnableSCR)
                        {
                            throw new BcmsException(BcmsExceptionType.EnableSCRFailed, "Enable SCR process failed");
                        }

                        else
                        {
                            Logger.Info("After EnableSCR GroupID :" + CurrentGroup.Id);
                            //SwapPRDRServer(CurrentGroup.Id);

                        }
                        break;

                    case WorkflowActionType.IsMachineRunning:


                        break;
                    case WorkflowActionType.OnRegisterVirtualMachine:

                        break;
                    case WorkflowActionType.ReplicateSnapShot:

                        break;
                    case WorkflowActionType.ReplicateSCP:

                        break;
                    case WorkflowActionType.ReplicateSnapShotFile:

                        break;
                    case WorkflowActionType.ReplicateVMConfigFile:

                        break;
                    case WorkflowActionType.SnapMirror_Update:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "SNAPMIRROR UPDATE"
                        + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "NetAppInstance Host Name :" + WFNetAppInstance.Host
                          + Environment.NewLine
                          + "DR StorageId :" + WFSnapMirror.DRStorageId
                          + Environment.NewLine
                          + "DR Volume :" + WFSnapMirror.DRVolume
                          + Environment.NewLine
                          + "PR StorageId :" + WFSnapMirror.PRStorageId
                          + Environment.NewLine
                          + "PR Volume :" + WFSnapMirror.PRVolume
                          );
                        //   bool isSnapMirrorUpdate = PSnapMirror.SnapMirrorReplication.SnapMirrorUpdate(WFNetAppInstance, WFVolume);
                        bool isSnapMirrorUpdate = _wfPRorDR == 2 ?
                          PSnapMirror.SnapMirrorReplication.SnapMirrorUpdateVolumes(WFNetAppInstance, WFSnapMirror.DRStorageId, WFSnapMirror.DRVolume, WFSnapMirror.PRStorageId, WFSnapMirror.PRVolume) :

                          PSnapMirror.SnapMirrorReplication.SnapMirrorUpdateVolumes(WFNetAppInstance, WFSnapMirror.PRStorageId, WFSnapMirror.PRVolume, WFSnapMirror.DRStorageId, WFSnapMirror.DRVolume);


                        if (!isSnapMirrorUpdate)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorUpdateFailed, "SnapMirror Update process failed");
                        }

                        break;
                    case WorkflowActionType.SnapMirror_Break:
                        Logger.InfoFormat(
                         Environment.NewLine
                         + "SNAPMIRROR BREAK"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "NetAppInstance Host Name :" + WFNetAppInstance.Host
                         + Environment.NewLine
                         + "Volume :" + WFVolume
                         );
                        bool isSnapMirrorBreak = PSnapMirror.SnapMirrorReplication.SnapMirrorBreak(WFNetAppInstance, WFVolume);

                        if (!isSnapMirrorBreak)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorBreakFailed, "SnapMirror Break process failed");
                        }

                        break;
                    case WorkflowActionType.SnapMirror_Vloume_Online:

                        Logger.InfoFormat(
                        Environment.NewLine
                         + "SNAPMIRROR VOLUME ONLINE"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "NetAppInstance Host Name :" + WFNetAppInstance.Host
                         + Environment.NewLine
                         + "Volume :" + WFVolume
                         );
                        bool isSnapMirrorVolumeOnline = PSnapMirror.SnapMirrorReplication.SnapMirrorVolumeOnline(WFNetAppInstance, WFVolume);

                        if (!isSnapMirrorVolumeOnline)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorVolumeOnlineFailed, "SnapMirror Volume Online process failed");
                        }

                        break;
                    case WorkflowActionType.SnapMirror_Vloume_Restrict:
                        Logger.InfoFormat(
                        Environment.NewLine
                        + "SNAPMIRROR VOLUME RESTRICT"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "NetAppInstance Host Name :" + WFNetAppInstance.Host
                        + Environment.NewLine
                        + "Volume :" + WFVolume
                        );
                        bool isSnapMirrorVolumeRestrict = PSnapMirror.SnapMirrorReplication.SnapMirrorVolumeRestrict(WFNetAppInstance, WFVolume);
                        if (!isSnapMirrorVolumeRestrict)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorVolumeRestrictFailed, "SnapMirror Volume Restrict process failed");
                        }

                        break;

                    case WorkflowActionType.SnapMirror_Resync:
                        Logger.InfoFormat(
                        Environment.NewLine
                        + "SNAPMIRROR RESYNC"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "NetAppInstance Host Name :" + WFNetAppInstance.Host
                        + Environment.NewLine
                        + "DR StorageId :" + WFSnapMirror.DRStorageId
                        + Environment.NewLine
                        + "DR Volume :" + WFSnapMirror.DRVolume
                        + Environment.NewLine
                        + "PR StorageId :" + WFSnapMirror.PRStorageId
                        + Environment.NewLine
                        + "PR Volume :" + WFSnapMirror.PRVolume
                        );

                        bool isSnapmirrorResync = _wfPRorDR == 2 ?
                            PSnapMirror.SnapMirrorReplication.SnapMirrorResync(WFNetAppInstance, WFSnapMirror.DRStorageId, WFSnapMirror.DRVolume, WFSnapMirror.PRStorageId, WFSnapMirror.PRVolume) :

                            PSnapMirror.SnapMirrorReplication.SnapMirrorResync(WFNetAppInstance, WFSnapMirror.PRStorageId, WFSnapMirror.PRVolume, WFSnapMirror.DRStorageId, WFSnapMirror.DRVolume);

                        if (!isSnapmirrorResync)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorResyncFailed, "SnapMirror Resync process failed");
                        }


                        break;
                    case WorkflowActionType.ApplyASMLog:


                        break;
                    case WorkflowActionType.ReplicateVirtualMachine:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "REPLICATE VIRTUAL MACHINE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "PR Server IP :" + WFServer.PRIPAddress
                        + Environment.NewLine
                        + "PR Server UserName :" + WFServer.PRUserName
                        + Environment.NewLine
                        + "DR Server IP :" + WFServer.DRIPAddress //  Property in Final Copy WFServer.DRIPAddress and Property In Parallel copy  WFTargetServer.PRIPAddress
                        + Environment.NewLine
                        + "DR Server UserName :" + WFSnapMirror.PRStorageId //  Property in Final Copy WFSnapMirror.PRStorageId and Property In Parallel copy  WFTargetServer.PRUserName
                        + Environment.NewLine
                        + "Source Virtual Machine :" + _wfVirtualMachine  //  Property in Final Copy _wfVirtualMachine and Property In Parallel copy  _wfMachineName
                         + Environment.NewLine
                        + "Target Virtual Machine :" + _wfTargetMachine

                        );
                        // here passing parameters into method is need to check

                        var _esxisrc = new ESXIServer(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);
                        var _esxitarget = new ESXIServer(WFServer.DRIPAddress, WFServer.DRUserName, WFServer.DRPassword);

                        var _vmrepl = new VMReplication(_esxisrc, _wfVirtualMachine, _esxitarget, _wfTargetMachine);
                        var vrstatus = _vmrepl.Invoke();
                        if (!vrstatus.Result)
                        {
                            throw new BcmsException(BcmsExceptionType.SnapMirrorResyncFailed, vrstatus.Details);
                        }
                        break;


                        break;
                    //case WorkflowActionType.FileReplaceText:

                    //               Logger.InfoFormat(
                    //                  Environment.NewLine
                    //                  + "FILE REPLACE TEXT"
                    //                  + Environment.NewLine
                    //                  + "================================="
                    //                  + Environment.NewLine
                    //                  + "SSH Server Host :" + WFSSHServer.SSHHost
                    //                  + Environment.NewLine
                    //                  + "OS Type :" + CurrentServer.PROSType
                    //                  + Environment.NewLine
                    //                  + "Replace File Name :" + _wfScpFile
                    //                  + Environment.NewLine
                    //                  + "Original Text :" + _wfOriginalText
                    //                  + Environment.NewLine
                    //                  + "New Text :" + _wfNewText
                    //               );

                    //               if(CurrentServer.PROSType=="Linux" || CurrentServer.PROSType=="AIX")
                    //               {
                    //                 var replaceText= AIXOperations.ReplaceFileText(WFSSHServer, _wfScpFile, _wfOriginalText, _wfNewText);
                    //                 if(!replaceText)
                    //                 {
                    //                     throw new BcmsException(BcmsExceptionType.FileReplaceTextAIXorLinuxFailed, "FileReplaceText Linux/AIX process failed");
                    //                 }
                    //               }
                    //               if (CurrentServer.PROSType.Contains("Windows"))
                    //               {
                    //                   var replaceText = WindowsOS.ReplaceFileText(WFSSHServer, _wfScpFile, _wfOriginalText, _wfNewText);
                    //               if (!replaceText)
                    //               {
                    //                   throw new BcmsException(BcmsExceptionType.FileReplaceTextWindkowFailed, "FileReplaceText Windows process failed");
                    //               }
                    //              }

                    //           break;
                    // case WorkflowActionType. ExecuteCheckStroageCommand:
                    //        Logger.InfoFormat(
                    //           Environment.NewLine
                    //           + "EXECUTE CHECK STORAGE COMMAND"
                    //           + Environment.NewLine
                    //           + "================================="
                    //           + Environment.NewLine
                    //           + "DSCLI IP :" + WFSSHInfo.SSHHost
                    //           + Environment.NewLine
                    //           + "Storage Command :" + _wfCommand
                    //           + Environment.NewLine
                    //           + "Check Output :" + _wfCheckOutput[0]
                    //           );                        
                    //               StorageOperations.ExecuteCheckStorageCommand(_wfCommand, WFSSHInfo, _wfPRorDR == 1 ? WFPRDSCLI : WFDRDSCLI,_wfCheckOutput[0]);
                    //               BindConsoleOutput(OperationType.StorageOperations, isParallel);
                    //               break;
                    //case WorkflowActionType.ExecuteSudoCommand:

                    //               Logger.InfoFormat(
                    //                Environment.NewLine
                    //                + "EXECUTE SUDO COMMAND"
                    //                + Environment.NewLine
                    //                + "================================="
                    //                + Environment.NewLine
                    //                + "Server IP :" + WFSSHServer.SSHHost
                    //                + Environment.NewLine
                    //                + "Sudo User:" + WFServer.PRSudoUser
                    //                + Environment.NewLine
                    //                + "Command" + _wfCommand
                    //                );


                    //               AIXOperations.ExecuteOSCommand(_wfCommand, WFSSHServer, WFServer.PRSudoUser);

                    //               BindConsoleOutput(OperationType.AIXOperations, isParallel);


                    //               break;
                    case WorkflowActionType.UnMountNFSVolume:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "UNMOUNT NFS VOLUME"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSSHServer.SSHHost
                         + Environment.NewLine
                         + "Mount Point :" + _wfMountPoints[0]
                         );

                        bool isUnmountNfs = AIXOperations.UnmountNFSVolume(WFSSHServer, _wfMountPoints[0]);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isUnmountNfs)
                        {
                            throw new BcmsException(BcmsExceptionType.WFUnMountNFSVolumeFailed, "Unmount NFS Volume process is failed for mount point " + _wfMountPoints[0].ToString());
                        }

                        break;
                    case WorkflowActionType.MountNFSVolume:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "MOUNT NFS VOLUME"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Host Name :" + _wfHostName
                        + Environment.NewLine
                        + "Local Mount Points :" + _wfLocalMountPoints
                        + Environment.NewLine
                        + "Remote Mount Points :" + _wfRemoteMountPoints
                        );

                        bool isMountNFS = AIXOperations.MountNFSVolume(WFSSHServer, _wfHostName, _wfLocalMountPoints, _wfRemoteMountPoints);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isMountNFS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFMountNFSVolumeFailed, "Mount NFS Volume process is failed for mount point " + _wfLocalMountPoints);
                        }


                        break;
                    case WorkflowActionType.CheckFileExist:


                        break;
                    case WorkflowActionType.CheckDBStandBy:


                        break;
                    case WorkflowActionType.CheckDBReadWrite:


                        break;
                    case WorkflowActionType.ExecuteCheckOSCommand:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "EXECUTE CHECK OS COMMAND"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "Server IP :" + WFSSHServer.SSHHost
                            + Environment.NewLine
                            + "Sudo User:" + WFServer.PRSudoUser
                            + Environment.NewLine
                            + "Command" + _wfCommand
                            + Environment.NewLine
                            + "CHECK OUTPUT :" + string.Join(" ,", _wfCheckOutput != null ? _wfCheckOutput.ToArray() : new[] { " " })
                        );

                        bool isCheckOScommand = _wfIsUseSudo == 1 ? AIXOperations.ExecuteCheckOSCommand(_wfCommand, _wfCheckOutput, WFSSHServer, WFServer.PRSudoUser) : AIXOperations.ExecuteCheckOSCommand(_wfCommand, _wfCheckOutput, WFSSHServer);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isCheckOScommand)
                        {
                            throw new BcmsException(BcmsExceptionType.WFExecuteCheckOSCommandFailed, "Execute Check OS Command is failed. Command : " + _wfCommand);
                        }

                        break;
                    case WorkflowActionType.FastCopyReplicateFile:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "FASTCOPY REPLICATE FILE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Sudo User:" + WFTargetServer.PRSudoUser
                        + Environment.NewLine
                        + "FastCopyPath" + _wfFastCopyPath
                        + Environment.NewLine
                        + "Source File" + _wfSourceFile
                           + Environment.NewLine
                            + "Target Folder" + string.Join(" ,", _wfTargetFolder != null ? _wfTargetFolder.ToArray() : new[] { " " })
                        );


                        FastCopyResult fastcopyResult = AIXOperations.FastCopyReplicateFile(_wfFastCopyPath, _wfSourceFile, _wfTargetFolder[0], WFSSHServer, WFTARGETSSHServer, WFTargetServer.PRSudoUser);


                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (fastcopyResult.Result)
                        {


                        }
                        else
                        {
                            throw new BcmsException(BcmsExceptionType.WFFastCopyReplicateFileFailed, "FastCopy Replication File process is failed , Reason " + fastcopyResult.Details);
                        }

                        break;

                    case WorkflowActionType.FastCopySyncFolders:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "FASTCOPY SYNC FOLDER"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "Server IP :" + WFSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                           + Environment.NewLine
                           + "Target Sudo User:" + WFTargetServer.PRSudoUser
                           + Environment.NewLine
                           + "FastCopyPath" + _wfFastCopyPath
                            + Environment.NewLine
                            + "Source Folder" + string.Join(" ,", _wfSourceFolder != null ? _wfSourceFolder.ToArray() : new string[] { " " })
                            + Environment.NewLine
                            + "Target Folder" + string.Join(" ,", _wfTargetFolder != null ? _wfTargetFolder.ToArray() : new string[] { " " })
                           );
                        FastCopyResult fastcopySynResult = AIXOperations.FastCopySyncFolders(_wfFastCopyPath, _wfSourceFolder, _wfTargetFolder, WFSSHServer, WFTARGETSSHServer, WFTargetServer.PRSudoUser);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (fastcopySynResult.Result)
                        {

                        }
                        else
                        {
                            throw new BcmsException(BcmsExceptionType.WFFastCopySyncFoldersFailed, "FastCopy Sync folder process is failed , Reason " + fastcopySynResult.Details);
                        }
                        break;

                    case WorkflowActionType.ChangeRedoArchivePermission:
                        break;
                    case WorkflowActionType.RevertRedoArchivePermission:
                        break;
                    case WorkflowActionType.FastCopySyncArchiveFolder:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "FASTCOPY SYNC ARCHIVE FOLDER"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                        + Environment.NewLine
                        + "Target Sudo User:" + WFTargetServer.PRSudoUser
                        + Environment.NewLine
                        + "FastCopyPath" + _wfFastCopyPath
                        + Environment.NewLine
                        + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                       );

                        bool isSyncRachive = AIXOperations.FastCopyReplicateArchiveFolder(_wfFastCopyPath, WFSSHServer, WFTARGETSSHServer, WFTargetServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isSyncRachive)
                        {
                            throw new BcmsException(BcmsExceptionType.WFFastCopySyncArchiveFolderFailed, "FastCopy  Sync Archive folder process is failed ");
                        }


                        break;

                    case WorkflowActionType.FastCopySyncRedoArchiveFolder:

                        Logger.InfoFormat(
                          Environment.NewLine
                          + "FASTCOPY SYNC REDO ARCHIVE FOLDER"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSSHServer.SSHHost
                          + Environment.NewLine
                          + "Target Server IP :" + WFTARGETSSHServer.SSHHost
                          + Environment.NewLine
                          + "Target Sudo User:" + WFTargetServer.PRSudoUser
                          + Environment.NewLine
                          + "FastCopyPath" + _wfFastCopyPath
                          + Environment.NewLine
                          + "Oracle SID" + WFDatabase.DatabaseOracle.PROracleSID
                      );

                        bool isSyncRedoRachive = AIXOperations.FastCopyReplicateRedoArchiveEVO(_wfFastCopyPath, WFSSHServer, WFTARGETSSHServer, WFTargetServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID);

                        if (!isSyncRedoRachive)
                        {
                            throw new BcmsException(BcmsExceptionType.WFFastCopySyncRedoArchiveFailed, "FastCopy  Sync Redo Archive folder process is failed ");
                        }

                        break;

                    //Exchange Workflow Case

                    case WorkflowActionType.CheckStorageMailboxPath:
                        Logger.InfoFormat(
                        Environment.NewLine
                        + "CHECK STORAGE MAILBOX PATH"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                        + Environment.NewLine
                        + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                        + Environment.NewLine
                        + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                        + Environment.NewLine
                        + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                        + Environment.NewLine
                        + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                        + Environment.NewLine
                        + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                        + Environment.NewLine
                        + "PR STORAGEMAILBOX PATH" + CurrentSCRConfig.prNewStorageMailboxPath
                        + Environment.NewLine
                        + "DR STORAGEMAILBOX PATH" + CurrentSCRConfig.drNewStorageMailboxPath
                      );

                        bool isMailBox = ExchangeFunction.CheckStorageMailboxPath(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.OracleDB, isParallel);

                        if (!isMailBox)
                        {
                            throw new BcmsException(BcmsExceptionType.CheckStorageMailboxPathFailed, "Check storage mailbox path process failed");
                        }
                        break;


                    case WorkflowActionType.CompareNewSGMailboxPath:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "COMPARE NEW SG MAILBOX PATH"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "PRServer Name :" + CurrentSCRConfig.PRServerName
                    + Environment.NewLine
                    + "DRServer Name :" + CurrentSCRConfig.DRServerName
                    + Environment.NewLine
                    + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                    + Environment.NewLine
                    + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                    + Environment.NewLine
                    + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                    + Environment.NewLine
                    + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                    + Environment.NewLine
                    + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                    + Environment.NewLine
                    + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                     + Environment.NewLine
                    + "PR STORAGEMAILBOX PATH" + CurrentSCRConfig.prNewStorageMailboxPath
                     + Environment.NewLine
                    + "DR STORAGEMAILBOX PATH" + CurrentSCRConfig.drNewStorageMailboxPath
                    );

                        bool iscompareSMGMailBox = ExchangeFunction.CompareNewSGMailboxPath(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!iscompareSMGMailBox)
                        {
                            throw new BcmsException(BcmsExceptionType.CompareNewSGMailboxPathFailed, "Compare new SG mailbox path process failed");
                        }
                        break;

                    case WorkflowActionType.SetSCRPrerequisite:
                        Logger.InfoFormat(
                Environment.NewLine
                + "SET SCR PREREQUESTSITE"
                + Environment.NewLine
                + "================================="
                + Environment.NewLine
                + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                + Environment.NewLine
                + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                + Environment.NewLine
                + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                + Environment.NewLine
                + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                + Environment.NewLine
                + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                + Environment.NewLine
                + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                );

                        bool isSetSCRPrerequisite = ExchangeFunction.SetSCRPrerequisite(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isSetSCRPrerequisite)
                        {
                            throw new BcmsException(BcmsExceptionType.SetSCRPrerequisiteFailed, "Set SCR Prerequestsite process failed");
                        }
                        break;

                    case WorkflowActionType.UpdateTargetStorageGroup:
                        Logger.InfoFormat(
                             Environment.NewLine
                             + "UPDATE TARGET STORAGE GROUP"
                             + Environment.NewLine
                             + "================================="
                             + Environment.NewLine
                             + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                             + Environment.NewLine
                             + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                             + Environment.NewLine
                             + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                             + Environment.NewLine
                             + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                             + Environment.NewLine
                             + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                             + Environment.NewLine
                             + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                    );

                        bool isUpdateTargetStorageGroup = ExchangeFunction.UpdateTargetStorageGroup(CurrentSCRConfig);


                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isUpdateTargetStorageGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.UpdateTargetStorageGroupFailed, "Update Target Storage Group process failed");
                        }
                        break;

                    case WorkflowActionType.ResumeSCR:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "RESUME SCR"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                            + Environment.NewLine
                            + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                            + Environment.NewLine
                            + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                            + Environment.NewLine
                            + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                            + Environment.NewLine
                            + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                            + Environment.NewLine
                            + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                     );
                        bool isResumeSCR = ExchangeFunction.ResumeSCR(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isResumeSCR)
                        {
                            throw new BcmsException(BcmsExceptionType.ResumeSCRFailed, "Resume SCR process failed");
                        }

                        break;
                    case WorkflowActionType.ChecktargetDBStatus:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "CHECK TARGET DB STATUS"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                            + Environment.NewLine
                            + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                            + Environment.NewLine
                            + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                            + Environment.NewLine
                            + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                            + Environment.NewLine
                            + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                            + Environment.NewLine
                            + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                     );

                        bool isChecktargetDbStatus = ExchangeFunction.ChecktargetDBStatus(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isChecktargetDbStatus)
                        {
                            throw new BcmsException(BcmsExceptionType.CheckTargetDBFileStatusFailed, "Check Target DB Status process failed");
                        }

                        break;
                    case WorkflowActionType.CreateStorageGroup:
                        Logger.InfoFormat(
                            Environment.NewLine
                            + "CREATE STORAGE GROUP"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                            + Environment.NewLine
                            + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                            + Environment.NewLine
                            + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                            + Environment.NewLine
                            + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                            + Environment.NewLine
                            + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                            + Environment.NewLine
                            + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                       );

                        bool isCreateStorageGroup = ExchangeFunction.CreateStorageGroup(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isCreateStorageGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.CreateStorageGroupFailed, "Create Storage Group process failed");
                        }
                        break;
                    case WorkflowActionType.DismountMailboxdatabase:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "DISMOUNT MAILBOX DATABASE"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                            + Environment.NewLine
                            + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                            + Environment.NewLine
                            + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                            + Environment.NewLine
                            + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                            + Environment.NewLine
                            + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                            + Environment.NewLine
                            + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                      );

                        bool isDismountMailboxdatabase = ExchangeFunction.DismountMailboxdatabase(CurrentSCRConfig);



                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isDismountMailboxdatabase)
                        {
                            throw new BcmsException(BcmsExceptionType.DismountMailboxdatabaseFailed, "Dismount Mailbox Database process failed");
                        }

                        break;

                    case WorkflowActionType.SCRStatusWithCopyQueueLength:
                        Logger.InfoFormat(
                            Environment.NewLine
                            + "SCR STATUS WITH COPYQUEUELENGTH"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                            + Environment.NewLine
                            + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                            + Environment.NewLine
                            + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                            + Environment.NewLine
                            + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                            + Environment.NewLine
                            + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                            + Environment.NewLine
                            + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                            );

                        bool isSCRStatusWithCopyQueueLength = ExchangeFunction.SCRStatusWithCopyQueueLength(_scrconfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isSCRStatusWithCopyQueueLength)
                        {
                            throw new BcmsException(BcmsExceptionType.SCRStatusWithCopyQueueLengthFailed, "SCR Status With CopyQueueLength process failed");
                        }

                        break;

                    case WorkflowActionType.DisableSCRRestorelogs:

                        Logger.InfoFormat(
                           Environment.NewLine
                           + "DISABLE SCR RESTORE LOGS"
                           + Environment.NewLine
                           + "================================="
                           + Environment.NewLine
                           + "PRServer IP :" + CurrentSCRConfig.PRServerIP
                           + Environment.NewLine
                           + "DRServer IP :" + CurrentSCRConfig.DRServerIP
                           + Environment.NewLine
                           + "PR STORAGEGROUP NAME" + CurrentSCRConfig.PRStorageGroupName
                           + Environment.NewLine
                           + "DR STORAGEGROUP NAME" + CurrentSCRConfig.DRStorageGroupName
                           + Environment.NewLine
                           + "PR MAILBOX DB NAME" + CurrentSCRConfig.PRStorageMailboxDBName
                           + Environment.NewLine
                           + "DR MAILBOX DB NAME" + CurrentSCRConfig.DRStorageMailboxDBName
                           );

                        bool isDisableSCRRestorelogs = ExchangeFunction.DisableSCRRestorelogs(CurrentSCRConfig);

                        BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        if (!isDisableSCRRestorelogs)
                        {
                            throw new BcmsException(BcmsExceptionType.DisableSCRRestorelogsFailed, "Disable SCR restore logs process failed");
                        }

                        break;


                    // surendra addedd this stmt
                    case WorkflowActionType.ExecuteCheckSqlCommand:
                        Logger.InfoFormat(
                     Environment.NewLine
                     + "EXCEUTE CHECK DB COMMAND"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFSSHServer.SSHHost
                     + Environment.NewLine
                    + "Sudo User :" + WFServer.PRSudoUser
                    + Environment.NewLine
                    + "Oracle SID :" + WFDatabase.DatabaseOracle.PROracleSID
                    + Environment.NewLine
                    + "COMMAND :" + _wfCommand
                     );

                        OracleDB.ExecuteCheckSQLCommand(WFSSHServer, WFServer.PRSudoUser, WFDatabase.DatabaseOracle.PROracleSID, _wfCommand, _wfCheckOutput[0]);
                        BindConsoleOutput(OperationType.OracleDB, isParallel);

                        break;
                    // end
                    // spell  check in this case WorkflowActionType. ExecuteCheckStroageCommand: new // case WorkflowActionType.ExecuteCheckStorageCommand: old

                    case WorkflowActionType.WriteToRouter:
                        break;
                    case WorkflowActionType.ApplicationStart:

                        Logger.InfoFormat(
                       Environment.NewLine
                        + "APPLICATION START"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Host Name :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "User Name :" + WFSSHServer.SSHUser
                        + Environment.NewLine
                        + "Application Name :" + _wfApplicationName
                        );


                        //bool startApp= CDASApp.StartCDASApp(WFSSHServer, _wfApplicationName);

                        String shellPrompt1 = "\\$|>|#";
                        String strOut1 = string.Empty;

                        // creates new SshShession instance providing hostname, username and password arguments
                        SshSession session1 = new SshSession(WFSSHServer.SSHHost, WFSSHServer.SSHUser, WFSSHServer.SSHPass);
                        Logger.Info("Starting CDAS App....");
                        try
                        {
                            // set expected shell prompt from SSH server
                            session1.SetShellPrompt(shellPrompt1, true);
                            session1.LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                            Logger.Info("Connecting Server :" + WFSSHServer.SSHHost);
                            session1.Connect(300000);

                            Logger.Info("Connected Successfully :" + WFSSHServer.SSHHost);

                            Logger.Info("Waiting for Startup Prompt :" + WFSSHServer.SSHHost);
                            strOut1 = session1.SendWait(_wfApplicationName, "Startup", false, 1200000);
                            Logger.Info(strOut1);

                            strOut1 = session1.SendWait("cdasrpt", "Live", false, 1200000);
                            Logger.Info(strOut1);

                            strOut1 = session1.SendWait("Live", "cdasrpthome>", true, 1200000);
                            Logger.Info(strOut1);

                            Thread.Sleep(20000);
                            strOut1 = session1.SendWait("ps -ef | grep tcmonitor | grep cdasrpt", shellPrompt1, true, 1200000);
                            Logger.Info(strOut1);
                            session1.Disconnect();
                            strOut1 = strOut1.Replace("grep tcmonitor", "");
                            if (!strOut1.Contains("tcmonitor"))
                            {
                                throw new BcmsException(BcmsExceptionType.WFStartApplicationFailed, "Start Application " + _wfApplicationName + " is failed . ");

                            }
                        }
                        catch (SshException SshEx)
                        {
                            session1.Disconnect();
                            Logger.ErrorFormat(SshEx.Message + SshEx.InnerException.Message);
                            throw new BcmsException(BcmsExceptionType.WFStartApplicationFailed, "Start Application " + _wfApplicationName + " is failed . detailed message " + SshEx.Message);

                        }
                        catch (Exception exc)
                        {
                            session1.Disconnect();
                            Logger.Info(exc.Message + exc.InnerException.Message);
                            throw new BcmsException(BcmsExceptionType.WFStartApplicationFailed, "Start Application " + _wfApplicationName + " is failed . detailed message " + exc.Message);
                        }
                        //BindConsoleOutput(OperationType.StorageOperations, isParallel);

                        //if (!startApp)
                        //{
                        //    throw new BcmsException(BcmsExceptionType.WFStartApplicationFailed, "Start Application " + _wfApplicationName + " is failed");
                        //}



                        break;
                    case WorkflowActionType.ApplicationStop:

                        Logger.InfoFormat(
                       Environment.NewLine
                        + "APPLICATION STOP"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Host Name :" + WFSSHServer.SSHHost
                        + Environment.NewLine
                        + "User Name :" + WFSSHServer.SSHUser
                        + Environment.NewLine
                        + "Application Name :" + _wfApplicationName
                        );


                        // bool stopApp=CDASApp.ShutCDASApp(WFSSHServer, _wfApplicationName);

                        String shellPrompt = "\\$|>|#";
                        String strOut = string.Empty;

                        // creates new SshShession instance providing hostname, username and password arguments
                        SshSession session = new SshSession(WFSSHServer.SSHHost, WFSSHServer.SSHUser, WFSSHServer.SSHPass);
                        Logger.Info("Shutting CDAS App....");
                        try
                        {
                            // set expected shell prompt from SSH server
                            session.SetShellPrompt(shellPrompt, true);
                            session.LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                            Logger.Info("Connecting Server :" + WFSSHServer.SSHHost);
                            session.Connect(300000);

                            Logger.Info("Connected Successfully :" + WFSSHServer.SSHHost);

                            strOut = session.SendWait(_wfApplicationName, "Shutdown", false, 1200000);
                            Logger.Info(strOut);
                            strOut = session.SendWait("cdasrpt", "Live", false, 1200000);
                            Logger.Info(strOut);
                            strOut = session.SendWait("Live", "cdasrpthome>", true, 1200000);
                            Logger.Info(strOut);
                            Thread.Sleep(20000);
                            strOut = session.SendWait("ps -ef | grep tcmonitor | grep cdasrpt", shellPrompt, true, 1200000);
                            Logger.Info(strOut);
                            session.Disconnect();
                            strOut = strOut.Replace("grep tcmonitor", "");
                            if (strOut.Contains("tcmonitor"))
                                throw new BcmsException(BcmsExceptionType.WFStopApplicationFailed, "Stop Application " + _wfApplicationName + " is failed");

                        }
                        catch (SshException SshEx)
                        {
                            session.Disconnect();
                            Logger.Error(SshEx.Message + SshEx.InnerException.Message);

                            throw new BcmsException(BcmsExceptionType.WFStopApplicationFailed, "Stop Application " + _wfApplicationName + " is failed. Error Messge :" + SshEx.Message);

                        }
                        catch (Exception exc)
                        {
                            session.Disconnect();
                            Logger.Error(exc.Message + exc.InnerException.Message);
                            throw new BcmsException(BcmsExceptionType.WFStopApplicationFailed, "Stop Application " + _wfApplicationName + " is failed. Error Messge :" + exc.Message);
                        }

                        break;
                    case WorkflowActionType.ApplicationMonitor:

                        break;
                    case WorkflowActionType.IsApplicationUp:

                        break;
                    case WorkflowActionType.IsApplicationDown:

                        break;

                    case WorkflowActionType.EMC_ISDGRESENT:

                        Logger.InfoFormat(
                         Environment.NewLine
                         + "EMC IS PRESENT"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSymCLi.Host
                         + Environment.NewLine
                         + "Device Group :" + _wfDeviceGroup
                         );


                        bool isDeviceGroupPresent = PEMCSRDF.SRDFProcess.IsDeviceGroupPresent(WFSymCLi, _wfDeviceGroup);

                        if (!isDeviceGroupPresent)
                        {
                            throw new BcmsException(BcmsExceptionType.WFIsDeviceGroupPresentFailed, "Device Group" + _wfDeviceGroup + "present check failed");
                        }


                        break;
                    case WorkflowActionType.EMC_DISABLEDEVICEGROUP:



                        Logger.InfoFormat(
                         Environment.NewLine
                         + "EMC DISABLE DEVICEGROUP"
                         + Environment.NewLine
                         + "================================="
                         + Environment.NewLine
                         + "Server IP :" + WFSymCLi.Host
                         + Environment.NewLine
                         + "Device Group :" + _wfDeviceGroup
                         );

                        bool DisableDeviceGroup = PEMCSRDF.SRDFProcess.DisableDeviceGroup(WFSymCLi, _wfDeviceGroup);

                        if (!DisableDeviceGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDisableDeviceGroupFailed, "Device Group" + _wfDeviceGroup + " disabled failed");
                        }


                        break;
                    case WorkflowActionType.EMC_ENABLEDEVICEGROUP:


                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC ENABLE DEVICEGROUP"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool enableDeviceGroup = PEMCSRDF.SRDFProcess.EnableDeviceGroup(WFSymCLi, _wfDeviceGroup);

                        if (!enableDeviceGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.WFEnableDeviceGroupFailed, "Device Group" + _wfDeviceGroup + " enabled failed");
                        }


                        break;
                    case WorkflowActionType.EMC_SETACPDEVICEMODE:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC SET ACP DEVICE MODE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool ACpDeviceModeON = PEMCSRDF.SRDFProcess.ACpDeviceModeON(WFSymCLi, _wfDeviceGroup);

                        if (!ACpDeviceModeON)
                        {
                            throw new BcmsException(BcmsExceptionType.WFACpDeviceModeONFailed, "Device Group" + _wfDeviceGroup + " set acp device mode failed");
                        }


                        break;
                    case WorkflowActionType.EMC_FAILOVERDEVICEGROUP:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC FAILOVER DEVICE GROUP"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool FailoverDeviceGroup = PEMCSRDF.SRDFProcess.FailoverDeviceGroup(WFSymCLi, _wfDeviceGroup);

                        if (!FailoverDeviceGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.WFFailoverDeviceGroupFailed, "Device Group" + _wfDeviceGroup + " failover failed");
                        }


                        break;
                    case WorkflowActionType.EMC_SWAPPERSONALITY:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC SWAP PERSONALITY"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool DeviceGroupSwapPersonality = PEMCSRDF.SRDFProcess.DeviceGroupSwapPersonality(WFSymCLi, _wfDeviceGroup);

                        if (!DeviceGroupSwapPersonality)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDeviceGroupSwapPersonalityFailed, "Device Group" + _wfDeviceGroup + " swap personality failed");
                        }


                        break;
                    case WorkflowActionType.EMC_RESUMEDEVICEGROUP:


                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC RESUME DEVICEGROUP"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool ResumeDeviceGroup = PEMCSRDF.SRDFProcess.ResumeDeviceGroup(WFSymCLi, _wfDeviceGroup);

                        if (!ResumeDeviceGroup)
                        {
                            throw new BcmsException(BcmsExceptionType.WFResumeDeviceGroupFailed, "Device Group" + _wfDeviceGroup + " resume failed");
                        }


                        break;
                    case WorkflowActionType.EMC_SETASYNCMODE:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EMC SET SYNC MODE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool DeviceGroupSetAsyncMode = PEMCSRDF.SRDFProcess.DeviceGroupSetAsyncMode(WFSymCLi, _wfDeviceGroup);

                        if (!DeviceGroupSetAsyncMode)
                        {
                            throw new BcmsException(BcmsExceptionType.WFDeviceGroupSetAsyncModeFailed, "Device Group" + _wfDeviceGroup + " set sync mode failed");
                        }


                        break;

                    case WorkflowActionType.HPUX_MOUNT:

                        Logger.InfoFormat(
                   Environment.NewLine
                   + "HPUX MOUNT"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "Server IP :" + WFHPServer.Host
                   + Environment.NewLine
                   + "User Name :" + WFHPServer.User
                   + Environment.NewLine
                   + "File system :" + _wfFileSystem
                   + Environment.NewLine
                   + "Mount Point :" + _wfMountPoints[0]
                   + Environment.NewLine
                   );

                        bool mount1 = HPUnixOS.Mount(WFHPServer, _wfFileSystem, _wfMountPoints[0]);

                        //BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!mount1)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXMountFailed, "HPUX Mount is(" + _wfMountPoints[0] + ") Failed");
                        }

                        break;
                    case WorkflowActionType.HPUX_UNMOUNT:


                        Logger.InfoFormat(
                   Environment.NewLine
                   + "HPUX UNMOUNT"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "Server IP :" + WFHPServer.Host
                   + Environment.NewLine
                   + "User Name :" + WFHPServer.User
                   + Environment.NewLine
                   + "Mount Point :" + _wfMountPoints[0]
                   + Environment.NewLine
                   );

                        bool unmount = HPUnixOS.UnMount(WFHPServer, _wfMountPoints[0]);

                        //BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!unmount)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXUnmountFailed, "HPUX Unmount is(" + _wfMountPoints[0] + ") Failed");
                        }


                        break;
                    case WorkflowActionType.HPUX_VGCHANGE_ACTIVE:


                        Logger.InfoFormat(
                   Environment.NewLine
                   + "HPUX_VG CHANGE ACTIVEE"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "Server IP :" + WFHPServer.Host
                   + Environment.NewLine
                   + "User Name :" + WFHPServer.User
                   + Environment.NewLine
                   + "VG Name :" + _wfVGName
                   + Environment.NewLine
                   );
                        bool isVgActive1 = HPUnixOS.ActivateVG(WFHPServer, _wfVGName);

                        // BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isVgActive1)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXActivateVGFailed, "Activate VG is" + _wfVGName + " failed");
                        }

                        break;
                    case WorkflowActionType.HPUX_VGCHANGE_DEACTIVE:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "HPUX_VG CHANGE DEACTIVEE"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFHPServer.Host
                        + Environment.NewLine
                        + "User Name :" + WFHPServer.User
                        + Environment.NewLine
                        + "VG Name :" + _wfVGName
                        + Environment.NewLine
                        );

                        bool isVgDeActive = HPUnixOS.DeActivateVG(WFHPServer, _wfVGName);

                        //BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isVgDeActive)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXDeactivateVGFailed, "Deactive VG is" + _wfVGName + " failed");
                        }

                        break;

                    case WorkflowActionType.ExecuteCheckSymcliCommand:

                        Logger.InfoFormat(
                        Environment.NewLine
                        + "EXECUTE CHECK SYMCLI COMMAND"
                        + Environment.NewLine
                        + "================================="
                        + Environment.NewLine
                        + "Server IP :" + WFSymCLi.Host
                        + Environment.NewLine
                        + "Device Group :" + _wfDeviceGroup
                        );

                        bool executeCheckSymcliCommand = SRDFProcess.ExecuteCheckSymCLICommand(WFSymCLi, _wfCommand, _wfCheckOutput[0].ToString());

                        if (!executeCheckSymcliCommand)
                        {
                            throw new BcmsException(BcmsExceptionType.WFExecuteCheckSymcliCommandFailed, "Execute check symcli command (" + _wfCommand + ") failed");
                        }

                        break;
                    case WorkflowActionType.ExecuteCheckDB2Command:

                        break;

                    case WorkflowActionType.Wait:

                        break;


                    case WorkflowActionType.HPUX_IsVGACTIVE:

                        Logger.InfoFormat(
                    Environment.NewLine
                    + "HPUX_IS VG ACTIVEE"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFHPServer.Host
                    + Environment.NewLine
                    + "User Name :" + WFHPServer.User
                    + Environment.NewLine
                    + "VG Name :" + _wfVGName
                    + Environment.NewLine
                    );

                        bool isVgActive = HPUnixOS.IsVGActive(WFHPServer, _wfVGName);

                        // BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isVgActive)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXIsVGActivateFailed, "Is VG Active is" + _wfVGName + " failed");
                        }
                        break;
                    case WorkflowActionType.HPUX_EXPORTVGTOMAPFILE:


                        Logger.InfoFormat(
                     Environment.NewLine
                     + "HPUX_EXPORT VG TO MAPFILE"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFHPServer.Host
                     + Environment.NewLine
                     + "User Name :" + WFHPServer.User
                     + Environment.NewLine
                     + "VG Name :" + _wfVGName
                      + Environment.NewLine
                     + "Map File :" + _wfMapFile
                     + Environment.NewLine
                     );
                        bool isExportVG = HPUnixOS.ExportVGToMapFile(WFHPServer, _wfVGName, _wfMapFile);

                        //BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isExportVG)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXExportVGMapFileFailed, "Export VG Map File " + _wfMapFile + " failed");
                        }

                        break;
                    case WorkflowActionType.HPUX_IMPORTVGTOMAPFILE:

                        Logger.InfoFormat(
                     Environment.NewLine
                     + "HPUX_IMPORT VG TO MAPFILE"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFHPServer.Host
                     + Environment.NewLine
                     + "User Name :" + WFHPServer.User
                     + Environment.NewLine
                     + "VG Name :" + _wfVGName
                      + Environment.NewLine
                     + "Map File :" + _wfMapFile
                     + Environment.NewLine
                     );

                        bool isImportVG = HPUnixOS.ImportVGToMapFile(WFHPServer, _wfVGName, _wfMapFile);

                        // BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isImportVG)
                        {
                            throw new BcmsException(BcmsExceptionType.WFHPUXImportVGMapFileFailed, "ImportVG Map File " + _wfMapFile + " failed");
                        }

                        break;
                    case WorkflowActionType.ExecuteSSH:


                        Logger.InfoFormat(
                       Environment.NewLine
                       + "EXECUTE SSH"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "Server IP :" + WFSymCLi.Host
                       + Environment.NewLine
                       + "Device Group :" + _wfDeviceGroup
                       );

                        bool isTaskExecute = AIXOperations.TaskExecute(_wfCommand, WFSSHServer, WFServer.PRSudoUser);

                        BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!isTaskExecute)
                        {
                            throw new BcmsException(BcmsExceptionType.WFExecuteSSHFailed, "Execute ssh command (" + _wfCommand + ") failed");
                        }

                        break;

                    case WorkflowActionType.HP_MOUNTVGS:

                        Logger.InfoFormat(
                      Environment.NewLine
                      + "HP_MOUNT VGS"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Server IP :" + WFHPServer.Host
                      + Environment.NewLine
                      + "File System :" + string.Join(",", _wfHPFileSystem.ToArray())
                      + Environment.NewLine
                       + "Mount Point:" + string.Join(",", _wfHPMountPoint.ToArray())
                      + Environment.NewLine
                      + "VG Name :" + _wfVGName
                      );

                        bool mountVGS = HPUnixOS.MountVG(WFHPServer, _wfHPFileSystem, _wfHPMountPoint, _wfVGName);

                        //BindConsoleOutput(OperationType.AIXOperations, isParallel);

                        if (!mountVGS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFMountVGFailed, "Mount Vg(" + _wfVGName + ") failed");
                        }

                        break;

                    case WorkflowActionType.HP_UNMOUNTVGS:


                        Logger.InfoFormat(
                    Environment.NewLine
                    + "HP_UNMOUNT VGS"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFHPServer.Host
                    + Environment.NewLine
                     + "Mount Point:" + string.Join(",", _wfHPMountPoint.ToArray())
                    + Environment.NewLine
                    + "VG Name :" + _wfVGName
                    );

                        bool unmountVGS = HPUnixOS.UnMountVG(WFHPServer, _wfHPMountPoint, _wfVGName);

                        if (!unmountVGS)
                        {
                            throw new BcmsException(BcmsExceptionType.WFUnmountVGFailed, "Mount Vg(" + _wfVGName + ") failed");
                        }



                        break;

                    case WorkflowActionType.EMC_SWITCHOVERDG:

                        Logger.InfoFormat(
                      Environment.NewLine
                      + "EMC SWITCHOVER DG"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "Server IP :" + WFSymCLi.Host
                      + Environment.NewLine
                      + "Device Group :" + _wfDeviceGroup
                      );

                        bool switchOverDG = SRDFProcess.SwitchOverDG(WFSymCLi, _wfDeviceGroup);

                        if (!switchOverDG)
                        {
                            throw new BcmsException(BcmsExceptionType.WFSwitchOverDGFailed, "SwitchOver DG Group(" + _wfDeviceGroup + ") failed");
                        }

                        break;

                    case WorkflowActionType.EMC_SWITCHBACKDG:

                        Logger.InfoFormat(
                    Environment.NewLine
                    + "EMC SWITCHBACK DG"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "Server IP :" + WFSymCLi.Host
                    + Environment.NewLine
                    + "Device Group :" + _wfDeviceGroup
                    );

                        bool switchBackDg = SRDFProcess.SwitchBackDG(WFSymCLi, _wfDeviceGroup);

                        if (!switchBackDg)
                        {
                            throw new BcmsException(BcmsExceptionType.WFSwitchBackDGFailed, "SwitchBack DG Group(" + _wfDeviceGroup + ") failed");
                        }


                        break;

                    case WorkflowActionType.EMC_ISDGCONSISTENT:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "EMC IS DG CONSISTENT"
                            + Environment.NewLine
                            + "================================="
                            + Environment.NewLine
                            + "Server IP :" + WFSymCLi.Host
                            + Environment.NewLine
                            + "Device Group :" + _wfDeviceGroup
                            );

                        bool isConsistent = SRDFProcess.IsDeviceGroupConsistent(WFSymCLi, _wfDeviceGroup);

                        if (!isConsistent)
                        {
                            throw new BcmsException(BcmsExceptionType.WFIsDgConsistentFailed, " Is DG consistent is (" + _wfDeviceGroup + ") failed");
                        }


                        break;

                    case WorkflowActionType.HP_MOUNTVGPARALLEL:

                        Logger.InfoFormat(
                     Environment.NewLine
                     + "HP_MOUNT VG PARALLEL"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFHPServer.Host
                     + Environment.NewLine
                     + "File System :" + string.Join(",", _wfHPFileSystem.ToArray())
                     + Environment.NewLine
                      + "Mount Point:" + string.Join(",", _wfHPMountPoint.ToArray())
                     + Environment.NewLine
                     + "VG Name :" + _wfVGName
                     );

                        bool isMountParallel = HPUnixOS.MountVGParallel(WFHPServer, _wfHPFileSystem, _wfHPMountPoint, _wfVGName);


                        if (!isMountParallel)
                        {
                            throw new BcmsException(BcmsExceptionType.WFMountVGParallelFailed, "Mount VG Paralell is (" + _wfVGName + ") failed");
                        }



                        break;
                    case WorkflowActionType.HP_UNMOUNTVGPARALLEL:

                        Logger.InfoFormat(
                     Environment.NewLine
                     + "HP_UNMOUNT VG PARALLEL"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "Server IP :" + WFHPServer.Host
                     + Environment.NewLine
                     + "Mount Point:" + string.Join(",", _wfHPMountPoint.ToArray())
                     + Environment.NewLine
                     + "VG Name :" + _wfVGName
                     );

                        bool isUnMountParallel = HPUnixOS.UnMountVGParallel(WFHPServer, _wfHPMountPoint, _wfVGName);

                        if (!isUnMountParallel)
                        {
                            throw new BcmsException(BcmsExceptionType.WFUnmountVGParallelFailed, "Un Mount VG Paralell is (" + _wfVGName + ") failed");
                        }

                        break;
                    case WorkflowActionType.EMC_CHECKDGTRACKSZERO:

                        Logger.InfoFormat(
                          Environment.NewLine
                          + "EMC CHECK DG TRACKS ZERO"
                          + Environment.NewLine
                          + "================================="
                          + Environment.NewLine
                          + "Server IP :" + WFSymCLi.Host
                          + Environment.NewLine
                          + "Device Group :" + _wfDeviceGroup
                          );


                        bool isTrackZero = SRDFProcess.CheckDeviceGroupTracksZero(WFSymCLi, _wfDeviceGroup);

                        if (!isTrackZero)
                        {
                            throw new BcmsException(BcmsExceptionType.WFCheckDGTrackZeroFailed, "Check DG Track Zero (" + _wfDeviceGroup + ")is  failed");
                        }

                        break;

                    default:

                        Logger.ErrorFormat("Action type " + workflowActionType.ToString() + "is not implemented");

                        break;
                    case WorkflowActionType.Start_HADRPrimary:

                        Logger.InfoFormat(
                    Environment.NewLine
                    + "START HADR PRIMARY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );

                        var _start_HADRPrimary = DB2Process.StartHADRAsPrimary(
                                new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                              WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!_start_HADRPrimary)
                        {
                            throw new BcmsException(BcmsExceptionType.StartHADRPrimaryFailed, "Start HADR Primary process failed");
                        }


                        break;


                    // added by kiran for windows oracle
                    case WorkflowActionType.WinDGVerifyDBModeAndRole:
                        if (CurrentServer.PROSType.ToLower().Contains("windows"))
                        {
                            try
                            {
                                if (SwitchoverwithDG.GetPrDatabaseRole(_wfServer.PRIPAddress, _wfDatabase.DatabaseOracle.PROracleSID, _wfDatabase.DatabaseOracle.PRPassword))
                                {
                                    LogHelper.LogHeaderAndMessage("PRIMARY DATABSE MODE STATUS :", string.Format("Primary Database {0} in Read Write mode", _wfDatabase.DatabaseOracle.PROracleSID));
                                    if (SwitchoverwithDG.GetDrDatabaseRole(_wfTargetServer.PRIPAddress, _wfTargetServerDatabase.DatabaseOracle.PROracleSID, _wfTargetServerDatabase.DatabaseOracle.PRPassword))
                                    {

                                        LogHelper.LogHeaderAndMessage("DR DATABSE MODE STATUS :", string.Format("DR Database {0} mounted and standby mode", _wfTargetServerDatabase.DatabaseOracle.DROracleSID));
                                    }
                                    else
                                    {
                                        throw new BcmsException(BcmsExceptionType.WFORADRMountStandbyFailed);
                                    }
                                }
                                else
                                {
                                    throw new BcmsException(BcmsExceptionType.WFORAPrimaryReadWriteModeFailed);
                                }

                            }
                            catch (BcmsException)
                            {
                                throw;
                            }
                            catch (Exception exc)
                            {
                                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Unhandled Exception occured while verify Database Mode", exc);
                            }


                        }


                        break;
                    //HADR SERVER ------------------START---------------------------------------------------------------------------------
                    case WorkflowActionType.Stop_HADR:

                        Logger.InfoFormat(
               Environment.NewLine
               + "STOP HADR"
               + Environment.NewLine
               + "================================="
               + Environment.NewLine
               + "IP ADDRESS :" + WFServer.PRIPAddress
               + Environment.NewLine
               + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
               );
                        var stop_HADR = DB2Process.StopHADR(
                             new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                           WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!stop_HADR)
                        {
                            throw new BcmsException(BcmsExceptionType.Stop_HADRFailed, "Start HADR Primary process failed");
                        }
                        break;

                    case WorkflowActionType.IsHADRAActive:
                        Logger.InfoFormat(
                       Environment.NewLine
                       + "IS HADRA ACTIVE"
                       + Environment.NewLine
                       + "================================="
                       + Environment.NewLine
                       + "IP ADDRESS :" + WFServer.PRIPAddress
                       + Environment.NewLine
                       + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                       );
                        var isHADRAActive = DB2Process.IsHADRActive(
                             new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                           WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isHADRAActive)
                        {
                            throw new BcmsException(BcmsExceptionType.IsHADRAActiveFailed, "Start HADR Primary process failed");
                        }
                        break;

                    case WorkflowActionType.DB2_IsDBUp:

                        Logger.InfoFormat(
                      Environment.NewLine
                      + "DB2 IS DBUP"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "IP ADDRESS :" + WFServer.PRIPAddress
                      + Environment.NewLine
                      + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                      );
                        var isDB2IsDBUp = DB2Process.IsDatabaseActive(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isDB2IsDBUp)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2IsDBUpFailed, "DB2IsDBUp process failed");
                        }
                        break;

                    case WorkflowActionType.DB2_SwitchOverDB:
                        Logger.InfoFormat(
                      Environment.NewLine
                      + "DB2 SWITCHOVER DB"
                      + Environment.NewLine
                      + "================================="
                      + Environment.NewLine
                      + "IP ADDRESS :" + WFServer.PRIPAddress
                      + Environment.NewLine
                      + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                      );
                        var isDB2SwitchOverDB = DB2Process.SwitchOver(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID, new DB2Server(WFServer.DRIPAddress, WFServer.DRUserName, WFServer.DRPassword,
                                          WFServer.DRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isDB2SwitchOverDB)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2SwitchOverDBFailed, "DB2_SwitchOverDB process failed");
                        }

                        break;

                    case WorkflowActionType.DB2_SwitchBackDB:
                        Logger.InfoFormat(
                     Environment.NewLine
                     + "DB2 SWITCHBACK DB"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "IP ADDRESS :" + WFServer.PRIPAddress
                     + Environment.NewLine
                     + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                     );
                        var isDB2SwitchBackDB =
                        DB2Process.SwitchBack(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID, new DB2Server(WFServer.DRIPAddress, WFServer.DRUserName, WFServer.DRPassword,
                                          WFServer.DRSudoUser), WFDatabase.DatabaseSql.DRDatabaseSID);

                        if (!isDB2SwitchBackDB)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2SwitchBackDBFailed, "DB2 SwitchBack DB process failed");
                        }

                        break;

                    case WorkflowActionType.DB2_ActivateDB:
                        Logger.InfoFormat(
                     Environment.NewLine
                     + "DB2 ACTIVATE DB"
                     + Environment.NewLine
                     + "================================="
                     + Environment.NewLine
                     + "IP ADDRESS :" + WFServer.PRIPAddress
                     + Environment.NewLine
                     + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                     );
                        var isDB2ActivateDB =
                        DB2Process.ActivateDatabase(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isDB2ActivateDB)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2ActivateDBFailed, "DB2 Activate DB process failed");
                        }

                        break;

                    case WorkflowActionType.DB2_DeActivateDBStart:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "DB2 DEACTIVATE DB START"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );

                        var isDB2_DeActivateDBStart =
                        DB2Process.DeactivateDatabaseStart(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isDB2_DeActivateDBStart)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2DeActivateDBStartFailed, "DB2 DeActivate DBStart process failed");
                        }
                        break;

                    case WorkflowActionType.IsHADRRolePrimary:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "IS HADR ROLE PRIMARY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );
                        var isHADRRolePrimary =
                        DB2Process.IsHADRRolePrimary(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isHADRRolePrimary)
                        {
                            throw new BcmsException(BcmsExceptionType.IsHADRRolePrimaryFailed, "IsHADRRole Primary process failed");
                        }
                        break;

                    case WorkflowActionType.IsHADRRoleStandby:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "IS HADR ROLE PRIMARY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );
                        var isHADRRoleStandby =
                        DB2Process.IsHADRRoleStandby(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isHADRRoleStandby)
                        {
                            throw new BcmsException(BcmsExceptionType.IsHADRRoleStandbyFailed, "IsHADRRole Standby process failed");
                        }

                        break;

                    case WorkflowActionType.DB2_IsDatabasePrimary:
                        Logger.InfoFormat(
                    Environment.NewLine
                    + "DB2 ISDATABASE PRIMARY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );
                        var isDB2_IsDatabasePrimary =
                        DB2Process.IsDatabaseActive(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isDB2_IsDatabasePrimary)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2IsDatabasePrimaryFailed, "DB2 IsDatabase Primary process failed");
                        }
                        break;

                    case WorkflowActionType.DB2_IsDatabaseStandby:
                        Logger.InfoFormat(
                   Environment.NewLine
                   + "DB2 ISDATABASE STANDBY"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "IP ADDRESS :" + WFServer.PRIPAddress
                   + Environment.NewLine
                   + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                   );
                        var isDB2_IsDatabaseStandby =
                        DB2Process.IsDatabaseStandby(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isDB2_IsDatabaseStandby)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2IsDatabaseStandbyFailed, "DB2 IsDatabase Standby process failed");
                        }
                        break;
                    case WorkflowActionType.IsHADRStatePEER:
                        Logger.InfoFormat(
                   Environment.NewLine
                   + "ISHADR STATE PEER"
                   + Environment.NewLine
                   + "================================="
                   + Environment.NewLine
                   + "IP ADDRESS :" + WFServer.PRIPAddress
                   + Environment.NewLine
                   + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                   );
                        var isHADRStatePEER =
                        DB2Process.IsHADRStatePEER(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isHADRStatePEER)
                        {
                            throw new BcmsException(BcmsExceptionType.IsHADRStatePEERFailed, "HADR State PEER process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_QuiesceDatabase:
                        Logger.InfoFormat(
                  Environment.NewLine
                  + "DB2 QUIESCE DATABASE"
                  + Environment.NewLine
                  + "================================="
                  + Environment.NewLine
                  + "IP ADDRESS :" + WFServer.PRIPAddress
                  + Environment.NewLine
                  + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                  );
                        var isQuiesceDatabase =
                        DB2Process.QuiesceDatabase(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isQuiesceDatabase)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2QuiesceDatabaseFailed, "DB2 Quiesce Database process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_UnQuiesceDatabase:
                        Logger.InfoFormat(
                  Environment.NewLine
                  + "DB2 UNQUIESCE DATABASE"
                  + Environment.NewLine
                  + "================================="
                  + Environment.NewLine
                  + "IP ADDRESS :" + WFServer.PRIPAddress
                  + Environment.NewLine
                  + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                  );
                        var isUnQuiesceDatabase =
                        DB2Process.UnquiesceDatabase(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isUnQuiesceDatabase)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2UnQuiesceDatabaseFailed, "DB2 UnQuiesce Database process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_IsDatabaseQuiesced:
                        Logger.InfoFormat(
                  Environment.NewLine
                  + "DB2 IS DATABASE QUIESCED"
                  + Environment.NewLine
                  + "================================="
                  + Environment.NewLine
                  + "IP ADDRESS :" + WFServer.PRIPAddress
                  + Environment.NewLine
                  + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                  );
                        var isDatabaseQuiesced =
                        DB2Process.ISDatabaseQuiesced(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isDatabaseQuiesced)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2IsDatabaseQuiescedFailed, "DB2 DatabaseQuiesced process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_Terminate:
                        Logger.InfoFormat(
                 Environment.NewLine
                 + "DB2 TERMINATE"
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "IP ADDRESS :" + WFServer.PRIPAddress
                 + Environment.NewLine
                 + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                 );
                        var isTerminate =
                        DB2Process.DB2Terminate(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isTerminate)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2IsDatabaseQuiescedFailed, "Terminate process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_Start:
                        Logger.InfoFormat(
                 Environment.NewLine
                 + "DB2 START"
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "IP ADDRESS :" + WFServer.PRIPAddress
                 + Environment.NewLine
                 + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                 );
                        var isStart =
                        DB2Process.DB2Start(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);

                        if (!isStart)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2StartFailed, "DB2 Start process failed");
                        }

                        break;
                    case WorkflowActionType.TakeOverHADR:
                        Logger.InfoFormat(
                 Environment.NewLine
                 + "TAKE OVER HADR"
                 + Environment.NewLine
                 + "================================="
                 + Environment.NewLine
                 + "IP ADDRESS :" + WFServer.PRIPAddress
                 + Environment.NewLine
                 + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                 );
                        var isTakeOverHadr =
                        DB2Process.TakeOverHADR(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isTakeOverHadr)
                        {
                            throw new BcmsException(BcmsExceptionType.TakeOverHADRFailed, "TakeOver HADR process failed");
                        }
                        break;
                    case WorkflowActionType.DB2_DeActivateDBTerminate:
                        Logger.InfoFormat(
                Environment.NewLine
                + "DEACTIVATE DB TERMINATE"
                + Environment.NewLine
                + "================================="
                + Environment.NewLine
                + "IP ADDRESS :" + WFServer.PRIPAddress
                + Environment.NewLine
                + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                );
                        var isDeActivateDBTerminate =
                        DB2Process.DeactivateDatabaseTerminate(
                            new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                          WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!isDeActivateDBTerminate)
                        {
                            throw new BcmsException(BcmsExceptionType.DB2DeActivateDBTerminateFailed, "DB2 DeActivate DB Terminate process failed");
                        }
                        break;

                    case WorkflowActionType.Start_HADRStandBy:

                        Logger.InfoFormat(
                    Environment.NewLine
                    + "START HADR STANDBY"
                    + Environment.NewLine
                    + "================================="
                    + Environment.NewLine
                    + "IP ADDRESS :" + WFServer.PRIPAddress
                    + Environment.NewLine
                    + "DATABASE NAME :" + WFDatabase.DatabaseSql.PRDatabaseSID
                    );

                        var startHADRStandBy = DB2Process.StartHADRAsStandby(
                                new DB2Server(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword,
                                              WFServer.PRSudoUser), WFDatabase.DatabaseSql.PRDatabaseSID);
                        if (!startHADRStandBy)
                        {
                            throw new BcmsException(BcmsExceptionType.StartHADRStandByFailed, "Start HADR StandBy process failed");
                        }
                        break;

                    case WorkflowActionType.ReplicateFastCopyFileWin:

                        break;
                    case WorkflowActionType.ReplicateFastCopyFilePosix:

                        Logger.InfoFormat(
                            Environment.NewLine
                            + "REPLICATE REDO FILES"
                            + Environment.NewLine
                            + "================================="
                             + Environment.NewLine
                             + "PR Server IP :" + FastCopyOracleClient.PrimaryHost.HostName
                             + Environment.NewLine
                            + "PR User :" + FastCopyOracleClient.PrimaryHost.UserName
                             + "DR Server IP :" + FastCopyOracleClient.DRHost.HostName
                             + Environment.NewLine
                            + "DR User :" + FastCopyOracleClient.DRHost.UserName
                             );


                        FastCopyOracleClient.FastCopyPath = _wfFastCopyPath;
                        FastCopyOracleClient.WorkflowActionId = CurrentWorkflowActionId;

                        bool isReplicate1 = FastCopyOracleClient.FatCopyRedofilesReplication();

                        if (!isReplicate1)
                        {
                            throw new BcmsException(BcmsExceptionType.ReplicateFastCopyRedofileFailed, "FastCopy (Redo)Files replication failiure ");
                        }

                        break;
                    case WorkflowActionType.CancelRecoverMode:

                        break;
                    case WorkflowActionType.GenerateRedoCtrlBKPScriptWin:

                        break;
                    case WorkflowActionType.SwitchShutPRDBWin:

                        break;
                    case WorkflowActionType.ExecuteRedoCtrlBKPWin:

                        break;
                    case WorkflowActionType.StartDBReadWriteWin:

                        break;
                    case WorkflowActionType.StartDBStandByWin:

                        break;
                    case WorkflowActionType.ReplicateFastCopyFoldersWin:

                        break;
                    case WorkflowActionType.ReplicateFastCopyFoldersPosix:


                        Logger.InfoFormat(
                           Environment.NewLine
                           + "REPLICATE ARCHIVE FOLDER"
                           + Environment.NewLine
                           + "================================="
                            + Environment.NewLine
                            + "PR Server IP :" + FastCopyOracleClient.PrimaryHost.HostName
                            + Environment.NewLine
                           + "PR User :" + FastCopyOracleClient.PrimaryHost.UserName
                            + "DR Server IP :" + FastCopyOracleClient.DRHost.HostName
                            + Environment.NewLine
                           + "DR User :" + FastCopyOracleClient.DRHost.UserName
                            );


                        FastCopyOracleClient.FastCopyPath = _wfFastCopyPath;
                        FastCopyOracleClient.GroupId = 22;
                        FastCopyOracleClient.ReplicationId = 54;

                        bool isReplicate2 = FastCopyOracleClient.FastCopyArchiveFileReplication();

                        if (!isReplicate2)
                        {
                            throw new BcmsException(BcmsExceptionType.ReplicateFastCopyArchiveFolderFailed, "FastCopy achive folder replication failiure ");
                        }

                        break;

                    case WorkflowActionType.ReveseReplicateFastCopyFoldersPosix:


                        Logger.InfoFormat(
                           Environment.NewLine
                           + "REPLICATE REVERSE ARCHIVE FOLDER"
                           + Environment.NewLine
                           + "================================="
                            + Environment.NewLine
                            + "PR Server IP :" + FastCopyOracleClient.PrimaryHost.HostName
                            + Environment.NewLine
                           + "PR User :" + FastCopyOracleClient.PrimaryHost.UserName
                            + "DR Server IP :" + FastCopyOracleClient.DRHost.HostName
                            + Environment.NewLine
                           + "DR User :" + FastCopyOracleClient.DRHost.UserName
                            );


                        FastCopyOracleClient.FastCopyPath = _wfFastCopyPath;
                        FastCopyOracleClient.GroupId = 22;
                        FastCopyOracleClient.ReplicationId = 54;

                        bool isReplicate3 = FastCopyOracleClient.FastCopyReverseArchiveFileReplication();

                        if (!isReplicate3)
                        {
                            throw new BcmsException(BcmsExceptionType.ReplicateFastCopyArchiveFolderFailed, "FastCopy reverse achive folder replication failiure ");
                        }

                        break;
                }

                if (isParallel)
                {
                    UpdateOperationStatus("Success", false);
                    UpdateProgressBarStatus();
                }
                else
                {
                    UpdateDROperationResultStatus("Success");
                }
            }

            catch (BcmsException exc)
            {
                Logger.ErrorFormat("Exception occured while executing DROperation workflow '{0}' Error Message : {1} ", CurrentWorkflowActionName
                    + Environment.NewLine,
                    exc.Message
                    );

                WaitForCallBackAction(isParallel, exc.Message);
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("Exception occured while executing DROperation workflow '{0}' Error Message : {1}", CurrentWorkflowActionName
                 + Environment.NewLine,
                 exc.Message
                 );

                WaitForCallBackAction(isParallel, exc.Message);

            }
        }


        #region DRoperation

        private void UpdateDROperationResultStatus(string status)
        {
            CurrentDROperationResult.ActionName = CurrentWorkflowActionName;
            CurrentDROperationResult.StartTime = CurrentActionStartTime;
            CurrentDROperationResult.EndTime = DateTime.Now;
            CurrentDROperationResult.ElapsedTime = DateTime.Now.Subtract(CurrentActionStartTime).ToString();
            CurrentDROperationResult.Status = status;
            CurrentDROperationResult.DROperationId = CurrentWorkflowDROperation.Id;

            CurrentDROperationResult = DROperationResultDataAccess.UpdateDROperationResult(CurrentDROperationResult);
        }

        private void AddDROperationResultStatus(string status)
        {
            CurrentDROperationResult.ActionName = CurrentWorkflowActionName;
            CurrentDROperationResult.StartTime = CurrentActionStartTime;
            CurrentDROperationResult.EndTime = DateTime.Now;
            CurrentDROperationResult.ElapsedTime = CurrentActionEndTime.Subtract(CurrentActionStartTime).ToString();
            CurrentDROperationResult.Status = status;
            CurrentDROperationResult.DROperationId = CurrentWorkflowDROperation.Id;
            CurrentDROperationResult.ActionId = CurrentWorkflowActionId;
            CurrentDROperationResult.Message = string.Empty;
            CurrentDROperationResult = DROperationResultDataAccess.AddDROperationResult(CurrentDROperationResult);
        }


        private void BindConsoleOutput(OperationType operation, bool isParallel)
        {
            switch (operation)
            {
                case OperationType.AIXOperations:

                    Logger.InfoFormat("Console Output {0}{1} {2}", Environment.NewLine, "----------------------------", AIXOperations.ConsoleOutput ?? string.Empty);

                    if (isParallel)
                    {
                        ParallelWorkflowActionResult.Message = AIXOperations.ConsoleOutput ?? string.Empty;

                        CurrentParallelGroupWorkflow.Message = AIXOperations.ConsoleOutput ?? string.Empty;
                    }
                    else
                    {
                        CurrentDROperationResult.Message = AIXOperations.ConsoleOutput ?? string.Empty;
                    }


                    break;

                case OperationType.GMOperations:

                    Logger.InfoFormat("Console Output {0}{1} {2}", Environment.NewLine, "----------------------------", WFGMOperation.ConsoleOutput ?? string.Empty);
                    if (isParallel)
                    {
                        ParallelWorkflowActionResult.Message = WFGMOperation.ConsoleOutput ?? string.Empty;

                        CurrentParallelGroupWorkflow.Message = WFGMOperation.ConsoleOutput ?? string.Empty;
                    }
                    else
                    {
                        CurrentDROperationResult.Message = WFGMOperation.ConsoleOutput ?? string.Empty;
                    }


                    break;

                case OperationType.OracleDB:

                    Logger.InfoFormat("Console Output {0}{1} {2}", Environment.NewLine, "----------------------------", OracleDB.ConsoleOutput ?? string.Empty);
                    if (isParallel)
                    {
                        ParallelWorkflowActionResult.Message = OracleDB.ConsoleOutput ?? string.Empty;

                        CurrentParallelGroupWorkflow.Message = OracleDB.ConsoleOutput ?? string.Empty;
                    }
                    else
                    {
                        CurrentDROperationResult.Message = OracleDB.ConsoleOutput ?? string.Empty;
                    }


                    break;

                case OperationType.DROperations:

                    Logger.InfoFormat("Console Output {0}{1} {2}", Environment.NewLine, "----------------------------", DROperations.ConsoleOutput ?? string.Empty);
                    if (isParallel)
                    {
                        ParallelWorkflowActionResult.Message = DROperations.ConsoleOutput ?? string.Empty;

                        CurrentParallelGroupWorkflow.Message = DROperations.ConsoleOutput ?? string.Empty;
                    }
                    else
                    {
                        CurrentDROperationResult.Message = DROperations.ConsoleOutput ?? string.Empty;
                    }


                    break;

                case OperationType.StorageOperations:

                    Logger.InfoFormat("Console Output {0}{1} {2}", Environment.NewLine, "----------------------------", StorageOperations.ConsoleOutput ?? string.Empty);
                    if (isParallel)
                    {
                        ParallelWorkflowActionResult.Message = StorageOperations.ConsoleOutput ?? string.Empty;

                        CurrentParallelGroupWorkflow.Message = StorageOperations.ConsoleOutput ?? string.Empty;

                    }
                    else
                    {
                        CurrentDROperationResult.Message = StorageOperations.ConsoleOutput ?? string.Empty;
                    }


                    break;
            }
        }

        private void WaitForCallBackAction(bool isParallel, string message)
        {

            if (isParallel)
            {
                if (ParallelWorkflowActionResult.Message == string.Empty)
                {
                    ParallelWorkflowActionResult.Message = message;

                    CurrentParallelGroupWorkflow.Message = message;
                }
                UpdateOperationStatus("Error", false);

                while (!WaitForParallelConditionalAction())
                {
                    Thread.Sleep(10000);
                }

                PerformParallelConditonalAction();

                UpdateParallelConditonalOperation(ConditionalOperationType.None);
            }
            else
            {
                if (CurrentDROperationResult.Message == string.Empty)
                {
                    CurrentDROperationResult.Message = message;
                }

                UpdateDROperationResultStatus("Error");

                while (!WaitForConditionalAction())
                {
                    Thread.Sleep(10000);
                }
                PerformConditonalAction();

                UpdateDROperationConditonalOperation(ConditionalOperationType.None);
            }
        }

        private void UpdateParallelConditonalOperation(ConditionalOperationType conditionalActionType)
        {
            ParallelGroupWorkflowDataAccess.UpdateByConditionOperation(CurrentParallelGroupWorkflow.Id, (int)conditionalActionType);
        }

        private void UpdateOperationStatus(string status, bool isNew)
        {
            if (isNew)
            {
                AddParallelWorkflowActionResultStatus(status);

                UpdateParallelConditonalOperation(ConditionalOperationType.None);
            }
            else
            {
                UpdateParallelWorkflowActionResultStatus(status);
            }


            UpdateParallelGroupWorkflow(status);
        }

        private void ClearConsoleOutput()
        {
            AIXOperations.ConsoleOutput = string.Empty;

            DROperations.ConsoleOutput = string.Empty;

            OracleDB.ConsoleOutput = string.Empty;

            StorageOperations.ConsoleOutput = string.Empty;
        }

        private void PerformParallelConditonalAction()
        {

            if (CurrentParallelConditionalAction != ConditionalOperationType.None)
            {
                switch (CurrentParallelConditionalAction)
                {
                    case ConditionalOperationType.Skip:

                        UpdateOperationStatus("Skip", false);

                        Logger.DebugFormat("{0} is skipped by user", CurrentWorkflowActionName);

                        CurrentParallelConditionalOperationType = ConditionalOperationType.Skip;

                        UpdateProgressBarStatus();

                        break;

                    case ConditionalOperationType.Retry:

                        UpdateOperationStatus("Retry", false);

                        Logger.DebugFormat("{0} is retry by user", CurrentWorkflowActionName);

                        UpdateParallelConditonalOperation(ConditionalOperationType.None);

                        PerformAction(CurrentWorkflowActionType, true);

                        CurrentParallelConditionalOperationType = ConditionalOperationType.Retry;

                        break;

                    case ConditionalOperationType.Abort:

                        _isAbort = true;

                        Logger.DebugFormat("{0} is aborted by user", CurrentWorkflowActionName);

                        CurrentParallelConditionalOperationType = ConditionalOperationType.Abort;

                        UpdateProgressBarStatus();

                        break;

                    case ConditionalOperationType.Next:

                        CurrentParallelConditionalOperationType = ConditionalOperationType.Next;

                        UpdateProgressBarStatus();

                        break;
                }
            }

        }

        private void PerformConditonalAction()
        {
            if (CurrentConditionalAction != ConditionalOperationType.None)
            {
                switch (CurrentConditionalAction)
                {
                    case ConditionalOperationType.Skip:

                        UpdateDROperationResultStatus("Skip");

                        Logger.DebugFormat("{0} is skipped by user", CurrentWorkflowActionName);

                        CurrentConditionalOperationType = ConditionalOperationType.Skip;

                        break;

                    case ConditionalOperationType.Retry:

                        UpdateDROperationResultStatus("Retry");

                        Logger.DebugFormat("{0} is retry by user", CurrentWorkflowActionName);

                        UpdateDROperationConditonalOperation(ConditionalOperationType.None);

                        PerformAction(CurrentWorkflowActionType, false);

                        CurrentConditionalOperationType = ConditionalOperationType.Retry;
                        break;

                    case ConditionalOperationType.Abort:

                        _isAbort = true;

                        Logger.DebugFormat("{0} is aborted by user", CurrentWorkflowActionName);

                        CurrentConditionalOperationType = ConditionalOperationType.Abort;

                        break;

                    case ConditionalOperationType.Next:

                        CurrentConditionalOperationType = ConditionalOperationType.Next;

                        break;
                }
            }

        }

        private bool WaitForParallelConditionalAction()
        {
            ParallelGroupWorkflow parallelGroup = ParallelGroupWorkflowDataAccess.GetById(CurrentParallelGroupWorkflow.Id);


            if (parallelGroup != null)
            {
                if (parallelGroup.ConditionalOperation != 0)
                {
                    switch (parallelGroup.ConditionalOperation)
                    {
                        case 1:
                            CurrentParallelConditionalAction = ConditionalOperationType.Skip;
                            return true;
                        case 2:
                            CurrentParallelConditionalAction = ConditionalOperationType.Retry;
                            return true;
                        case 3:
                            CurrentParallelConditionalAction = ConditionalOperationType.Abort;
                            return true;

                        case 4:
                            CurrentParallelConditionalAction = ConditionalOperationType.Next;
                            return true;
                    }
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        private bool WaitForConditionalAction()
        {
            DROperation drOperation = DROperationDataAccess.GetDROperationById(CurrentWorkflowDROperation.Id);

            if (drOperation != null)
            {
                if (drOperation.ConditionalOperation != 0)
                {
                    switch (drOperation.ConditionalOperation)
                    {
                        case 1:
                            CurrentConditionalAction = ConditionalOperationType.Skip;
                            return true;
                        case 2:
                            CurrentConditionalAction = ConditionalOperationType.Retry;
                            return true;
                        case 3:
                            CurrentConditionalAction = ConditionalOperationType.Abort;
                            return true;
                        case 4:
                            CurrentConditionalAction = ConditionalOperationType.Next;
                            return true;
                    }
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        private void PowerOnVM()
        {
            var esxi = new ESXIServer(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);

            VMWare.PowerOnMachine(WFServer.PRVirtualImagePath, esxi);
        }

        private void PowerOffVM()
        {
            var esxi = new ESXIServer(WFServer.PRIPAddress, WFServer.PRUserName, WFServer.PRPassword);

            VMWare.PowerOffMachine(WFServer.PRVirtualImagePath, esxi);

        }

        public string[] GetWorkFlowActionFromXml(string rawXml)
        {
            string returnValue = string.Empty;

            XmlReader reader = XmlReader.Create(new StringReader(rawXml));
            while (reader.Read())
            {
                switch (reader.NodeType)
                {
                    case XmlNodeType.Element:
                        if (reader.HasAttributes)
                        {
                            returnValue = returnValue + reader.Name + ",";
                            returnValue = returnValue + reader.GetAttribute(0) + ",";
                        }
                        break;
                }
            }

            returnValue = returnValue.TrimEnd(':').Replace("Property,", "").TrimEnd(',');

            return returnValue.Split(',');
        }

        private void UpdateDROperation(string status)
        {
            CurrentWorkflowDROperation.EndTime = DateTime.Now;
            CurrentWorkflowDROperation.Status = status;

            DROperationDataAccess.UpdateDROperation(CurrentWorkflowDROperation);
        }

        private void PerformWorkflow(Workflow workflow, DROperationType dROperationType)
        {

            GetLastDROperation(CurrentGroup.Id);

            string[] workflowActions = GetWorkFlowActionFromXml(workflow.Xml);

            foreach (var action in workflowActions)
            {
                _workflowActionId = Convert.ToInt32(action);

                var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(_workflowActionId);
                //var droperationresult = DROperationResultDataAccess.DROperationResult_GetByActionAndStatus(workflowAction.Name, CurrentGroup.DROperationId);

                //if( droperationresult!=null)
                //    continue;
                if (workflowAction != null)
                {
                    if (!_isAbort)
                    {

                        BindWorkflowActionProperties(workflowAction);

                        _drOperationResult = null;

                        CurrentWorkflowActionName = workflowAction.Name;

                        CurrentWorkflowActionId = workflowAction.Id;

                        CurrentWorkflowActionType = (WorkflowActionType)workflowAction.ActionType;

                        CurrentActionStartTime = DateTime.Now;

                        CurrentActionEndTime = DateTime.Now;

                        AddDROperationResultStatus("Running");

                        PerformAction(CurrentWorkflowActionType, false);

                        CurrentActionEndTime = DateTime.Now;

                        if (CurrentWorkflowDROperation.ActionMode == ActionMode.Manual)
                        {
                            if (CurrentConditionalOperationType != ConditionalOperationType.Skip)
                            {
                                if (action != workflowActions[workflowActions.Length - 1])
                                {
                                    while (!WaitForConditionalAction())
                                    {
                                        Thread.Sleep(10000);
                                    }
                                }
                            }
                        }

                        CurrentConditionalOperationType = ConditionalOperationType.None;
                    }
                    else
                    {
                        UpdateDROperationResultStatus("Abort");

                        break;
                    }

                }

            }

            UpdateDROperation("Success");

            switch (dROperationType)
            {

                case DROperationType.SwitchOver:
                    GroupDataAccess.UpdateWorkflowOperation(CurrentGroup.Id, (int)GroupWorkflowOperation.SwitchOverCompleted, CurrentWorkflowDROperation.Id);
                    break;

                case DROperationType.SwitchBack:
                    GroupDataAccess.UpdateWorkflowOperation(CurrentGroup.Id, (int)GroupWorkflowOperation.SwitchBackCompleted, CurrentWorkflowDROperation.Id);
                    break;


                case DROperationType.FailOver:
                    GroupDataAccess.UpdateWorkflowOperation(CurrentGroup.Id, (int)GroupWorkflowOperation.FailOverCompleted, CurrentWorkflowDROperation.Id);
                    break;


                case DROperationType.FailBack:
                    GroupDataAccess.UpdateWorkflowOperation(CurrentGroup.Id, (int)GroupWorkflowOperation.FailBackCompleted, CurrentWorkflowDROperation.Id);
                    break;

                case DROperationType.Customized:
                    GroupDataAccess.UpdateWorkflowOperation(CurrentGroup.Id, (int)GroupWorkflowOperation.CustomCompleted, CurrentWorkflowDROperation.Id);
                    break;
            }
        }

        private void UpdateDROperationConditonalOperation(ConditionalOperationType conditionalActionType)
        {
            bool isTrue = DROperationDataAccess.UpdateDROperationByConditional(CurrentWorkflowDROperation.Id, (int)conditionalActionType);
        }

        private void GetLastDROperation(int groupId)
        {
            CurrentWorkflowDROperation = DROperationDataAccess.GetLastDROperation(groupId);
        }

        public void BindWorkflowActionProperties(WorkflowAction workflowAction)
        {
            _wfServer = null;
            _wfDatabase = null;
            _wfsshServer = null;
            _wfsshInfo = null;
            _wfPRdscliInfo = null;
            _wfDRdscliInfo = null;
            _wfTargetsshServer = null;
            _wfTargetServer = null;
            _wfGlobalMirror = null;
            _wfScriptFilePath = string.Empty;
            _wfControlFilePath = string.Empty;
            _wfReverseSessionId = string.Empty;
            _wfhmcPRInfo = null;
            _wfhmcDRInfo = null;
            _wfScpFile = string.Empty;
            _wfTempFilePath = string.Empty;
            // _wfMountPoint = string.Empty;
            _wfPRorDR = 0;
            _wfSessionAddorRemove = 0;
            _wfSession = string.Empty;
            _wfCommand = string.Empty;
            _wfstorageInfo = null;
            _wfGmOperations = null;
            _wfMountPoints = null;
            _wfLundD = null;
            _wfdscliserver = null;
            _wflunInfo = null;
            _wfgroupLuns = null;
            _wfdrOperations = null;
            _wfsshInfoPr = null;
            _wfsshInfoDr = null;
            _wfJobQueue = string.Empty;
            _wfFastCopy = 0;
            _wfListener = string.Empty;
            _wfDNSServer = 0;
            _wfExistingHost = string.Empty;
            _wfExistingIP = string.Empty;
            _wfNewHost = string.Empty;
            _wfNewIp = string.Empty;
            _wfDomainName = string.Empty;
            _wfLocalMountPoints = string.Empty;
            _wfRemoteMountPoints = string.Empty;
            _wfHostName = string.Empty;
            _wfIsUseSudo = 0;
            _wfReplicationId = 0;
            _wfCheckOutput = null;
            _wfCheckOutput = new List<string>();
            _wfIsReturn = 0;
            _wfFastCopyPath = string.Empty;
            _wfSourceFile = string.Empty;
            _wfSourceFolder = null;
            _wfSourceFolder = new List<string>();
            _wfTargetFolder = null;
            _wfTargetFolder = new List<string>();

            _wfDiskGroup = string.Empty;
            _wfDiskName = string.Empty;
            _wfLuns = string.Empty;
            _wfRsynPath = string.Empty;
            _wfTargetFile = string.Empty;
            _wfMachineName = string.Empty;
            _wfSnapShotName = string.Empty;
            _wfTimeOut = string.Empty;
            _wfVirtualMachine = string.Empty;
            _wfVolume = string.Empty;
            _wfDestinationPath = string.Empty;
            _scrconfig = null;
            _wfappInstance = null;
            _wfTargetMachine = string.Empty;

            _wfVmPath = string.Empty;
            _wfOriginalText = string.Empty;
            _wfNewText = string.Empty;
            _wfRouterConfiguration = string.Empty;
            _wfInterfacePassword = string.Empty;
            _wfDeviceGroup = string.Empty;
            _wfFileSystem = string.Empty;
            _wfWaitTime = 0;

            _wfApplicationName = string.Empty;

            _wfMapFile = string.Empty;

            _wfTask = string.Empty;


            _wfHPFileSystem = null;
            _wfHPFileSystem = new List<string>();

            _wfHPMountPoint = null;
            _wfHPMountPoint = new List<string>();
            _hpServer = null;
            _symCli = null;
            _wfTargetServerDatabase = null;
            _fastCopyOracle = null;

            //SessionAndRemove
            //SessionName
            // 
            //Server Id

            if (workflowAction.ServerId != 0)
            {
                _wfServer = ServerDataAccess.GetServersById(workflowAction.ServerId);
            }

            //ReplicationId

            if (workflowAction.ReplicationId != 0)
            {
                _wfReplicationId = workflowAction.ReplicationId;
                _wfGlobalMirror = GlobalMirrorDataAccess.GetGlobalMirrorById(workflowAction.ReplicationId);
            }

            //DatabaseId 

            if (workflowAction.DatabaseId != 0)
            {
                _wfDatabase = DatabaseBaseDataAccess.GetDatabaseById(workflowAction.DatabaseId);
            }

            //GroupId

            if (workflowAction.GroupId != 0)
            {
                CurrentGroup = GroupDataAccess.GetGroupById(workflowAction.GroupId);
                _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
                //_wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
                _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            }




            //SessionAddRemove 

            if (workflowAction.SessionAddRemove != 0)
            {

                _wfSessionAddorRemove = workflowAction.SessionAddRemove;
            }


            //Session

            if (!string.IsNullOrEmpty(workflowAction.Session))
            {

                _wfSession = workflowAction.Session;
            }


            //Luns 
            if (!string.IsNullOrEmpty(workflowAction.Luns))
            {
                if (workflowAction.Luns.Contains(";"))
                {
                    string[] luns = workflowAction.Luns.Split(';');

                    _wfLundD = new string[luns.Length];

                    _wfLundD = luns;
                }
                else
                {
                    _wfLundD = new string[] { workflowAction.Luns };

                }
            }

            //MountPoint 

            if (!string.IsNullOrEmpty(workflowAction.MountPoint))
            {
                if (workflowAction.MountPoint.Contains(";"))
                {
                    string[] mount = workflowAction.MountPoint.Split(';');

                    _wfMountPoints = new string[mount.Length];

                    _wfMountPoints = mount;
                }
                else
                {
                    _wfMountPoints = new string[] { workflowAction.MountPoint };

                }
            }

            //VG 
            if (!string.IsNullOrEmpty(workflowAction.VG))
            {
                _wfVGName = workflowAction.VG;
            }


            //SwitchOverSession

            if (!string.IsNullOrEmpty(workflowAction.SwitchOverSession))
            {
                _wfReverseSessionId = workflowAction.SwitchOverSession;
            }

            //Command 

            if (!string.IsNullOrEmpty(workflowAction.Command))
            {
                _wfCommand = workflowAction.Command;
            }

            //PrDr 

            if (workflowAction.PrDr != 0)
            {
                _wfPRorDR = workflowAction.PrDr;
            }
            if (!string.IsNullOrEmpty(workflowAction.StandByControlFile))
            {
                _wfStandbyControlFile = workflowAction.StandByControlFile;
            }

            //StandByControlFile

            if (!string.IsNullOrEmpty(workflowAction.ControlFile))
            {
                _wfControlFilePath = workflowAction.ControlFile;

            }


            //SCP File

            if (!string.IsNullOrEmpty(workflowAction.File))
            {
                _wfScpFile = workflowAction.File;
            }


            //TargetServer

            if (workflowAction.TargetServer != 0)
            {
                _wfTargetServer = ServerDataAccess.GetServersById(workflowAction.TargetServer);
            }


            //ScriptFile 

            if (!string.IsNullOrEmpty(workflowAction.ScriptFile))
            {
                _wfScriptFilePath = workflowAction.ScriptFile;
            }

            //TempFile 

            if (!string.IsNullOrEmpty(workflowAction.TempFile))
            {
                _wfTempFilePath = workflowAction.TempFile;
            }

            //JobQueue 


            if (!string.IsNullOrEmpty(workflowAction.JobQueue))
            {
                _wfJobQueue = workflowAction.JobQueue;

            }

            //Fastcopy 

            if (workflowAction.Fastcopy != 0)
            {
                _wfFastCopy = workflowAction.Fastcopy;
            }

            //Listener

            if (!string.IsNullOrEmpty(workflowAction.Listner))
            {
                _wfListener = workflowAction.Listner;
            }

            //DNSServer


            if (workflowAction.DNSServer != 0)
            {
                _wfDNSServer = workflowAction.DNSServer;
            }

            //ExistingHost

            if (!string.IsNullOrEmpty(workflowAction.ExistingHost))
            {
                _wfExistingHost = workflowAction.ExistingHost;
            }

            //ExistingIp

            if (!string.IsNullOrEmpty(workflowAction.ExistingIp))
            {
                _wfExistingIP = workflowAction.ExistingIp;
            }

            //NewHost

            if (!string.IsNullOrEmpty(workflowAction.NewHost))
            {
                _wfNewHost = workflowAction.NewHost;
            }

            //NewIp

            if (!string.IsNullOrEmpty(workflowAction.NewIp))
            {
                _wfNewIp = workflowAction.NewIp;
            }

            //DomainName

            if (!string.IsNullOrEmpty(workflowAction.DomainName))
            {
                _wfDomainName = workflowAction.DomainName;
            }

            //LocalMountPoints

            if (!string.IsNullOrEmpty(workflowAction.LocalMountPoints))
            {
                _wfLocalMountPoints = workflowAction.LocalMountPoints;
            }

            //RemoteMountPoints


            if (!string.IsNullOrEmpty(workflowAction.RemoteMountPoints))
            {
                _wfRemoteMountPoints = workflowAction.RemoteMountPoints;
            }

            //HostName

            if (!string.IsNullOrEmpty(workflowAction.HostName))
            {
                _wfHostName = workflowAction.HostName;
            }

            //IsUseSudo (int)

            if (workflowAction.IsUseSudo != 0)
            {
                _wfIsUseSudo = workflowAction.IsUseSudo;
            }

            //CheckOutput

            if (!string.IsNullOrEmpty(workflowAction.CheckOutput))
            {
                if (workflowAction.CheckOutput.Contains(";"))
                {
                    string[] splitArray = workflowAction.CheckOutput.Split(';');

                    if (splitArray.Length > 0)
                    {
                        foreach (string output in splitArray)
                        {
                            _wfCheckOutput.Add(output);
                        }
                    }
                }
                else
                {
                    _wfCheckOutput.Add(workflowAction.CheckOutput);
                }
            }


            //IsReturn(int)

            if (workflowAction.IsReturn != 0)
            {
                _wfIsReturn = workflowAction.IsReturn;
            }


            //FastCopyPath

            if (!string.IsNullOrEmpty(workflowAction.FastCopyPath))
            {
                _wfFastCopyPath = workflowAction.FastCopyPath;
            }

            //SourceFile

            if (!string.IsNullOrEmpty(workflowAction.SourceFile))
            {
                _wfSourceFile = workflowAction.SourceFile;
            }

            //SourceFolder

            if (!string.IsNullOrEmpty(workflowAction.SourceFolder))
            {

                if (workflowAction.SourceFolder.Contains(";"))
                {
                    string[] splitArray = workflowAction.SourceFolder.Split(';');

                    if (splitArray.Length > 0)
                    {
                        foreach (string output in splitArray)
                        {
                            _wfSourceFolder.Add(output);
                        }
                    }
                }
                else
                {
                    _wfSourceFolder.Add(workflowAction.SourceFolder);
                }
            }


            //TargetFolder

            if (!string.IsNullOrEmpty(workflowAction.TargetFolder))
            {
                if (workflowAction.TargetFolder.Contains(";"))
                {
                    string[] splitArray = workflowAction.TargetFolder.Split(';');

                    if (splitArray.Length > 0)
                    {
                        foreach (string output in splitArray)
                        {
                            _wfTargetFolder.Add(output);
                        }
                    }
                }
                else
                {
                    _wfTargetFolder.Add(workflowAction.TargetFolder);
                }
            }

            //DiskGroup

            if (!string.IsNullOrEmpty(workflowAction.DiskGroup))
            {
                _wfDiskGroup = workflowAction.DiskGroup;
            }

            //DiskName

            if (!string.IsNullOrEmpty(workflowAction.DiskName))
            {
                _wfDiskName = workflowAction.DiskName;
            }


            //RsynPath

            if (!string.IsNullOrEmpty(workflowAction.RsyncPath))
            {
                _wfRsynPath = workflowAction.RsyncPath;
            }


            //TargetFile

            if (!string.IsNullOrEmpty(workflowAction.TargetFile))
            {
                _wfTargetFile = workflowAction.TargetFile;
            }

            //MachineName

            if (!string.IsNullOrEmpty(workflowAction.MachineName))
            {
                _wfMachineName = workflowAction.MachineName;
            }


            //SnapShotName

            if (!string.IsNullOrEmpty(workflowAction.SnapShotName))
            {
                _wfSnapShotName = workflowAction.SnapShotName;
            }

            //TimeOut 


            if (!string.IsNullOrEmpty(workflowAction.TimeOut))
            {
                _wfTimeOut = workflowAction.TimeOut;
            }

            //VirtualMachine

            if (!string.IsNullOrEmpty(workflowAction.VirtualMachine))
            {
                _wfVirtualMachine = workflowAction.VirtualMachine;
            }

            //DestinationPath

            if (!string.IsNullOrEmpty(workflowAction.DestinationPath))
            {
                _wfDestinationPath = workflowAction.DestinationPath;
            }

            //TargetMachine

            if (!string.IsNullOrEmpty(workflowAction.TargetMachine))
            {
                _wfTargetMachine = workflowAction.TargetMachine;
            }

            //VmPath

            if (!string.IsNullOrEmpty(workflowAction.VmPath))
            {
                _wfVmPath = workflowAction.VmPath;
            }

            //OriginalText

            if (!string.IsNullOrEmpty(workflowAction.OriginalText))
            {
                _wfOriginalText = workflowAction.OriginalText;
            }

            //NewText

            if (!string.IsNullOrEmpty(workflowAction.NewText))
            {
                _wfNewText = workflowAction.NewText;
            }

            //RouterConfiguration

            if (!string.IsNullOrEmpty(workflowAction.RouterConfiguration))
            {
                _wfRouterConfiguration = workflowAction.RouterConfiguration;
            }

            //InterfacePassword

            if (!string.IsNullOrEmpty(workflowAction.InterfacePassword))
            {
                _wfInterfacePassword = workflowAction.InterfacePassword;
            }

            //DeviceGroup

            if (!string.IsNullOrEmpty(workflowAction.DeviceGroup))
            {
                _wfDeviceGroup = workflowAction.DeviceGroup;
            }

            //FileSystem

            if (!string.IsNullOrEmpty(workflowAction.FileSystem))
            {
                _wfFileSystem = workflowAction.FileSystem;
            }

            //TargetDatabase
            if (workflowAction.TargetDatabase != 0)
            {
                _wfTargetDatabase = workflowAction.TargetDatabase;
                _wfTargetServerDatabase = DatabaseBaseDataAccess.GetDatabaseById(_wfTargetDatabase);
            }



            //WaitTime

            if (!string.Equals(workflowAction.WaitTime, "0") && !string.IsNullOrEmpty(workflowAction.WaitTime))
            {
                _wfWaitTime = Convert.ToInt32(workflowAction.WaitTime);
            }

            // ApplicationName

            if (!string.IsNullOrEmpty(workflowAction.ApplicationName))
            {
                _wfApplicationName = workflowAction.ApplicationName;
            }

            // MapFile

            if (!string.IsNullOrEmpty(workflowAction.MapFile))
            {
                _wfMapFile = workflowAction.MapFile;
            }

            // Task

            if (!string.IsNullOrEmpty(workflowAction.Task))
            {
                _wfTask = workflowAction.Task;
            }

            //FileSystemMountPoint

            if (!string.IsNullOrEmpty(workflowAction.FileSystemMountPoint))
            {
                if (workflowAction.FileSystemMountPoint.Contains(";"))
                {
                    var splitList = workflowAction.FileSystemMountPoint.Split(';').ToList();

                    if (splitList[0].Contains(":"))
                    {
                        foreach (var splitItem in splitList)
                        {
                            string[] item = splitItem.Split(':');

                            _wfHPFileSystem.Add(item[0]);
                            _wfHPMountPoint.Add(item[1]);
                        }
                    }
                    else
                    {
                        _wfHPMountPoint = splitList;
                    }
                }
            }

            //FastcopyPath

            if (!string.IsNullOrEmpty(workflowAction.Task))
            {
                _wfTask = workflowAction.Task;
            }



        }



        private void GetPendingAction()
        {
            CurrentDROperationResult = DROperationResultDataAccess.GetDROperationResultByDROperationIdAndStatus(CurrentWorkflowDROperation.Id);
        }

        #endregion


        //#region MSSql

        //private void KillProcessDR()
        //{
        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        IsKilled = Sql2000WinLog.KillProcess(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster");
        //    else
        //        IsKilled = Sql2000WinLog.KillProcess(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster");

        //    if (IsKilled)
        //    {
        //        Logger.DebugFormat("{0} : shutdown all process in {1} is successfully", CurrentGroup.Name, WFDatabase.DRName);
        //    }
        //    else
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000KillProcessDrFailed);
        //    }

        //}

        //private void KillProcessPR()
        //{

        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        IsKilled = Sql2000WinLog.KillProcess(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "PrimaryMaster");
        //    else
        //        IsKilled = Sql2000WinLog.KillProcess(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "PrimaryMaster");

        //    if (IsKilled)
        //    {
        //        Logger.DebugFormat("{0} : shutdown all process in {1} is successfully", CurrentGroup.Name, WFDatabase.DRName);
        //    }
        //    else
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000KillProcessPrFailed);
        //    }

        //}

        //private void SwitchDatabaseToPrimary()
        //{
        //    bool isrestore = false;
        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        isrestore = Sql2000WinLog.RestorewithRecovery(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster");
        //    else
        //        isrestore = Sql2000WinLog.RestorewithRecovery(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster");

        //    if (isrestore)
        //    {
        //        Logger.DebugFormat("{0} : Switch Database to Primary is successfully", CurrentGroup.Name);
        //    }
        //    else
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000RestoreWithRecoveryFailed);
        //    }
        //}

        //private void RunDBOption()
        //{
        //    bool isSuccess = false;
        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        isSuccess = Sql2000WinLog.DBOption(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster");
        //    else
        //        isSuccess = Sql2000WinLog.DBOption(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster");
        //    if (isSuccess)
        //    {
        //        Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
        //    }
        //    else
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000DBOptionFailed);
        //    }
        //}

        //public void GenerateLog(bool isLast)
        //{
        //    Logger.DebugFormat("{0} : Start Executing Job Log generation Sql server2000", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    GetTransactionLogName();
        //    GenerateLogFile(isLast);
        //}

        //public void ApplyLog(bool isLast)
        //{
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    GetApplyLogs();
        //    PerformApplyLogs(isLast);
        //}

        //private void GetApplyLogs()
        //{
        //    ApplyLogList = SqlLogDataAccess.GetApplyLogs(CurrentGroup.Id);
        //}

        //private void PerformApplyLogs(bool isLast)
        //{
        //    bool killed = false;
        //    try
        //    {
        //        if (ApplyLogList.Count > 0)
        //        {
        //            bool isApplied = false;
        //            foreach (var logs in ApplyLogList)
        //            {
        //                if ((CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart) || (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted))
        //                {
        //                    killed = Sql2000WinLog.KillProcess(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster");
        //                    isApplied = isLast ? Sql2000WinLog.RestoreLastLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, logs.LogFileName, "DRMaster") : Sql2000WinLog.RestoreLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, "DRMaster", logs.LogFileName);
        //                }
        //                else
        //                {
        //                    killed = Sql2000WinLog.KillProcess(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster");
        //                    bool asd = killed;
        //                    isApplied = isLast ? Sql2000WinLog.RestoreLastLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, logs.LogFileName, "DRMaster") : Sql2000WinLog.RestoreLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, "DRMaster", logs.LogFileName);
        //                }

        //                if (isLast)
        //                {
        //                    //  DatabaseDataAccess.UpdateDatabaseMode(CurrentDRDatabase.Id, false);
        //                }

        //                if (isApplied)
        //                {
        //                    SqlLog drlog = BuildSqlLogApplied(logs);
        //                    SqlLogDataAccess.AddSqlLogApplied(drlog);
        //                    GroupDataAccess.UpdateGroupByReplicationStatus(CurrentGroup.Id, GroupState.Active, (int)ReplicationState.CompletedSuccessfully, true);
        //                    Logger.DebugFormat("{0} : {1} is applied successfully ", CurrentGroup.Name, logs.LogFileName);
        //                }
        //                else
        //                {
        //                    Logger.DebugFormat("{0} : {1} Cannot be Apply applied successfully ", CurrentGroup.Name, logs.LogFileName);
        //                    throw new Exception();
        //                }
        //            }
        //        }

        //    }
        //    catch (BcmsException exc)
        //    {
        //        Logger.DebugFormat("{0} : Cannot Applied Log successfully {1}", CurrentGroup.Name, exc.Message);
        //        ExceptionManager.Manage(exc);
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.DebugFormat("{0} : Cannot be Applied Log successfully {1}", CurrentGroup.Name, exc.Message);
        //        if (isLast)
        //            throw new BcmsException(BcmsExceptionType.Sql2000RestoreLastLogFailed);
        //        else
        //            throw new BcmsException(BcmsExceptionType.Sql2000RestoreLogFailed);
        //    }
        //}

        //private SqlLog BuildSqlLogApplied(SqlLog logs)
        //{
        //    logs.DRSequenceNo = logs.PRSequenceNo;
        //    logs.GroupId = logs.GroupId;
        //    return logs;
        //}

        //private void GenerateLogFile(bool isLast)
        //{
        //    try
        //    {
        //        bool isGenerate = false;
        //        if ((CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart) || (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted))
        //            isGenerate = isLast ? Sql2000WinLog.LastLogGeneration(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, CurrentLogFileName, "PrimaryMaster") : Sql2000WinLog.GenerateLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, "PrimaryMaster", CurrentLogFileName);
        //        else
        //            isGenerate = isLast ? Sql2000WinLog.LastLogGeneration(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, CurrentLogFileName, "PrimaryMaster") : Sql2000WinLog.GenerateLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, "PrimaryMaster", CurrentLogFileName);

        //        if (isLast)
        //        {
        //            // DatabaseDataAccess.UpdateDatabaseMode(CurrentPRDatabase.Id, true);
        //        }

        //        if (isGenerate)
        //        {
        //            SqlLog sqlLog = BuildSqlLogGenerate();
        //            IsGenerate = SqlLogDataAccess.AddSqlLogGenerate(sqlLog);
        //            Logger.DebugFormat("{0} : {1} Generated successfully", CurrentGroup.Name, CurrentLogFileName);
        //        }
        //        else
        //        {
        //            throw new Exception();
        //        }
        //    }
        //    catch (BcmsException exc)
        //    {
        //        ExceptionManager.Manage(exc);
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000GenerateLastLogFailed);
        //    }


        //}

        //private SqlLog BuildSqlLogGenerate()
        //{
        //    var log = new SqlLog
        //    {
        //        LogFileName = CurrentLogFileName,
        //        PRSequenceNo = PRSqenceNo,
        //        LogGenerationTime = DateTime.Now,
        //        GroupId = CurrentGroup.Id
        //    };


        //    return log;
        //}

        //public void GetTransactionLogName()
        //{
        //    try
        //    {
        //        string logFileName = SqlLogDataAccess.GetMaxLogFileName(CurrentGroup.Id);

        //        if (logFileName != string.Empty)
        //        {
        //            CurrentLogFileName = GetNextLogFileName(logFileName);
        //        }
        //        else
        //        {
        //            PRSqenceNo = "1";
        //            CurrentLogFileName = string.Format("Log_{0}_1.trn", CurrentGroup.Name);
        //        }

        //    }
        //    catch (BcmsException exc)
        //    {
        //        ExceptionManager.Manage(exc, "Replication", CurrentGroup.Id, CurrentGroup.Name);
        //    }
        //    catch (Exception exc)
        //    {
        //        var bcms = new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while executing function signature SqlLogDataAccess.GetMaxLogFileName", exc);
        //        ExceptionManager.Manage(bcms, "Replication", CurrentGroup.Id, CurrentGroup.Name);
        //    }

        //}

        //public string GetNextLogFileName(string fileName)
        //{
        //    fileName = fileName.Replace(CurrentGroup.Name, "Name");

        //    string[] files = fileName.Split(new[] { '_', '.' });

        //    int id = Convert.ToInt32(files[2]) + 1;

        //    PRSqenceNo = id.ToString();

        //    return string.Format("Log_{0}_{1}.trn", CurrentGroup.Name, id);
        //}

        //public void sql2000healthMonitor()
        //{
        //    Logger.DebugFormat("{0} : Start Executing Job Sql server2000 health", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
        //        Health(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword);
        //    else
        //        Health(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword);
        //    Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 health", CurrentGroup.Name);
        //}

        //public void KillProcess()
        //{
        //    bool killed = false;
        //    Logger.DebugFormat("{0} : Start Executing Kill Process", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
        //        killed = Sql2000WinLog.KillProcess(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster");
        //    else
        //        killed = Sql2000WinLog.KillProcess(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster");
        //    if (killed)
        //        Logger.DebugFormat("{0} : Completed Executing Kill Process", CurrentGroup.Name);
        //    else
        //        Logger.DebugFormat("{0} : InCompleted Executing Kill Process", CurrentGroup.Name);
        //}

        //public void sql2000service()
        //{
        //    Logger.DebugFormat("{0} : Start Executing Job Sql server2000 Service", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    Service(_wfServer.PRId, _wfServer.PRUserName, _wfServer.PRPassword, _wfServer.PRIPAddress);
        //    Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Service for {1}", CurrentGroup.Name, _wfServer.PRIPAddress);
        //    Service(_wfServer.DRId, _wfServer.DRUserName, _wfServer.DRPassword, _wfServer.DRIPAddress);
        //    Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Service for {1}", CurrentGroup.Name, _wfServer.DRIPAddress);
        //}

        //public void sql2000healthLogMonitor()
        //{
        //    string CopiedFile = "";
        //    FileInfo file = null;
        //    Logger.DebugFormat("{0} : Start Executing Job Sql server2000 Log Generation", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    SqlLog sqllogback = SqlLogDataAccess.GetLastLogs(CurrentGroup.Id, false);
        //    SqlLog sqlrestore = SqlLogDataAccess.GetLastLogs(CurrentGroup.Id, true);
        //    SqlLog sqlcopy = SqlLogDataAccess.GetCopiedLogs(CurrentGroup.Id);
        //    if (!string.IsNullOrEmpty(sqlcopy.CopiedFile))
        //    {
        //        file = new FileInfo(sqlcopy.CopiedFile);
        //        CopiedFile = file.Name;
        //    }
        //    SqlServer2000Log sqlserverlog = new SqlServer2000Log();
        //    sqlserverlog.GroupId = CurrentGroup.Id;
        //    sqlserverlog.LastBackUpFile = sqllogback.LogFileName;
        //    sqlserverlog.LastBackUpTime = sqllogback.LogGenerationTime.ToString();

        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
        //        sqlserverlog.LastBackUpLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "PrimaryMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + sqllogback.LogFileName);
        //    else
        //        sqlserverlog.LastBackUpLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "PrimaryMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + sqllogback.LogFileName);

        //    sqlserverlog.LastRestoreFile = sqlrestore.LogFileName;
        //    sqlserverlog.LastRestoreTime = sqlrestore.LogAppliedTime.ToString();

        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
        //        sqlserverlog.LastRestoreLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + sqlrestore.LogFileName);
        //    else
        //        sqlserverlog.LastRestoreLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + sqlrestore.LogFileName);

        //    sqlserverlog.LastCopyFile = sqlcopy.CopiedFile;
        //    sqlserverlog.LastCopyTime = sqlcopy.LogCopiedTime.ToString();

        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
        //        sqlserverlog.LastCopyLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + CopiedFile);
        //    else
        //        sqlserverlog.LastCopyLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + CopiedFile);


        //    sqlserverlog.DataLag = (sqlrestore.LogAppliedTime - sqllogback.LogGenerationTime).ToString().TrimStart('-');
        //    sqlserverlog.CreatorId = 1;
        //    sqlserverlog.CreateDate = DateTime.Now;
        //    var log = SqlLogDataAccess.CreateLogMonitor(sqlserverlog);
        //    Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Log Generation", CurrentGroup.Name);
        //}

        //public void sql2000FastCopy()
        //{
        //    Logger.DebugFormat("{0} : Start Executing Fast Copy Job", CurrentGroup.Name);
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
        //    FastCopy.FastCopyJob fast = new FastCopy.FastCopyJob();
        //    try
        //    {
        //        fast.Synchronization(_wfServer, _wfDatabase, CurrentGroup);
        //        System.Threading.Thread.Sleep(10000);
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.Sql2000FastCopyFailed);
        //    }
        //}

        //public void Health(string pripAddress, string PRDBName, string DRIPAddress, string DRDBName, string PRUSer, string PRPassword, string DRUSer, string DRPassword)
        //{
        //    SqlServer2000 health = new SqlServer2000();
        //    health.GroupId = CurrentGroup.Id;
        //    health.DBVersion_PR = Sql2000WinLog.DBVersion(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBInstance_PR = Sql2000WinLog.DBServerInstance(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBState_PR = Sql2000WinLog.DatabaseState(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.Database_PR = Sql2000WinLog.DatabaseName(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBRecoveryMode_PR = Sql2000WinLog.DBRecoveryModel(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBRestrictAccess_PR = Sql2000WinLog.DBRestrictAccessStatus(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBUpdateability_PR = Sql2000WinLog.DBUpdateability(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");
        //    health.DBSize_PR = Sql2000WinLog.DBsize(pripAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster");

        //    health.DBVersion_DR = Sql2000WinLog.DBVersion(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBInstance_DR = Sql2000WinLog.DBServerInstance(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBState_DR = Sql2000WinLog.DatabaseState(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.Database_DR = Sql2000WinLog.DatabaseName(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBRecoveryMode_DR = Sql2000WinLog.DBRecoveryModel(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBRestrictAccess_DR = Sql2000WinLog.DBRestrictAccessStatus(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBUpdateability_DR = Sql2000WinLog.DBUpdateability(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.DBSize_DR = Sql2000WinLog.DBsize(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster");
        //    health.CreatorId = 1;
        //    health.CreateDate = DateTime.Now;
        //    var healthreturn = SqlLogDataAccess.CreateHealthMonitor(health);
        //}

        //private void Service(int host, string user, string password, string ipaddr)
        //{
        //    string[] services = { "MSSQLSERVER", "MSSQLServerADHelper", "SQLSERVERAGENT", "Windows Firewall/Internet Connection Sharing (ICS)" };
        //    SqlServer2000Service service = new SqlServer2000Service();
        //    string result = "";
        //    foreach (string ser in services)
        //    {
        //        service.GroupId = CurrentGroup.Id;
        //        service.Server = host;
        //        service.ServiceName = ser;
        //        result = Sql2000WinLog.WindowServices(ipaddr, user, password, ser);
        //        if ((result != "Failure") && (result != ""))
        //        {
        //            var splitresult = result.Split(';');
        //            service.ServiceName = splitresult[0];
        //            service.Status = splitresult[1];
        //            service.Start_Mode = splitresult[2];
        //            var res = SqlLogDataAccess.CreateServiceMonitor(service);
        //        }
        //    }
        //}

        //public void RunStoreProcedurePR(string Procedure, string filename)
        //{
        //    bool isSuccess = false;


        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        isSuccess = Sql2000Migration.MigrateLoginPR_SA(WFServer.DRUserName, WFServer.DRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, Procedure, @WFDatabase.DatabaseSql.DRBackupRestorePath + "\\" + filename);
        //    else
        //        isSuccess = Sql2000Migration.MigrateLoginPR_SA(WFServer.PRUserName, WFServer.PRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFServer.PRIPAddress, Procedure, @WFDatabase.DatabaseSql.PRBackupRestorePath + "\\" + filename);
        //    if (isSuccess)
        //    {
        //        Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
        //    }
        //    else
        //    {
        //        if (filename == "MigrateLogin.sql")
        //            throw new BcmsException(BcmsExceptionType.Sql2000MigrateLoginPRFailed);
        //        else
        //            throw new BcmsException(BcmsExceptionType.Sql2000MigrateServerRolePRFailed);
        //    }
        //}

        //public void RunScriptDr(string filename)
        //{
        //    bool isSuccess = false;


        //    if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
        //        isSuccess = Sql2000Migration.MigrateLoginDR_SA(WFServer.PRUserName, WFServer.PRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFServer.PRIPAddress, @WFDatabase.DatabaseSql.PRBackupRestorePath + "\\" + filename);
        //    else
        //        isSuccess = Sql2000Migration.MigrateLoginDR_SA(WFServer.DRUserName, WFServer.DRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, @WFDatabase.DatabaseSql.DRBackupRestorePath + "\\" + filename);
        //    if (isSuccess)
        //    {
        //        Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
        //    }
        //    else
        //    {
        //        if (filename == "MigrateLogin.sql")
        //            throw new BcmsException(BcmsExceptionType.Sql2000MigrateLoginDRFailed);
        //        else
        //            throw new BcmsException(BcmsExceptionType.Sql2000MigrateServerRoleDRFailed);
        //    }
        //}

        //public void SetServerStatus()
        //{
        //    _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
        //    bool prstatus = false;
        //    bool drstatus = false;
        //    prstatus = Sql2000Migration.CheckServerStatus(_wfServer.PRIPAddress, _wfServer.PRUserName, _wfServer.PRPassword);
        //    if ((CurrentServer.PRStatus != "1") && (prstatus == true))
        //    {
        //        ServerDataAccess.UpdateServerByStatus(_wfServer.PRId, ServerStatus.Up);
        //    }
        //    if ((CurrentServer.PRStatus != "0") && (prstatus == false))
        //    {
        //        ServerDataAccess.UpdateServerByStatus(_wfServer.PRId, ServerStatus.Down);
        //    }
        //    drstatus = Sql2000Migration.CheckServerStatus(_wfServer.DRIPAddress, _wfServer.DRUserName, _wfServer.DRPassword);
        //    if ((CurrentServer.DRStatus != "1") && (drstatus == true))
        //    {
        //        ServerDataAccess.UpdateServerByStatus(_wfServer.DRId, ServerStatus.Up);
        //    }
        //    if ((CurrentServer.DRStatus != "0") && (drstatus == false))
        //    {
        //        ServerDataAccess.UpdateServerByStatus(_wfServer.DRId, ServerStatus.Down);
        //    }
        //}
        //#endregion

        #region MSSql

        private void KillProcessDR()
        {
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                IsKilled = Sql2000WinLog.KillProcess(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                IsKilled = Sql2000WinLog.KillProcess(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());

            if (IsKilled)
            {
                Logger.DebugFormat("{0} : shutdown all process in {1} is successfully", CurrentGroup.Name, WFDatabase.DRName);
            }
            else
            {
                throw new BcmsException(BcmsExceptionType.Sql2000KillProcessDrFailed);
            }

        }

        private void KillProcessPR()
        {

            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                IsKilled = Sql2000WinLog.KillProcess(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "PrimaryMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            else
                IsKilled = Sql2000WinLog.KillProcess(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "PrimaryMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());

            if (IsKilled)
            {
                Logger.DebugFormat("{0} : shutdown all process in {1} is successfully", CurrentGroup.Name, WFDatabase.DRName);
            }
            else
            {
                throw new BcmsException(BcmsExceptionType.Sql2000KillProcessPrFailed);
            }

        }

        private void SwitchDatabaseToPrimary()
        {
            bool isrestore = false;
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                isrestore = Sql2000WinLog.RestorewithRecovery(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                isrestore = Sql2000WinLog.RestorewithRecovery(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());

            if (isrestore)
            {
                Logger.DebugFormat("{0} : Switch Database to Primary is successfully", CurrentGroup.Name);
            }
            else
            {
                throw new BcmsException(BcmsExceptionType.Sql2000RestoreWithRecoveryFailed);
            }
        }

        private void RunDBOption()
        {
            bool isSuccess = false;
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                isSuccess = Sql2000WinLog.DBOption(WFServer.PRIPAddress, WFDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, "DRMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                isSuccess = Sql2000WinLog.DBOption(WFServer.DRIPAddress, WFDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, "DRMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            if (isSuccess)
            {
                Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
            }
            else
            {
                throw new BcmsException(BcmsExceptionType.Sql2000DBOptionFailed);
            }
        }

        public void GenerateLog(bool isLast)
        {
            Logger.DebugFormat("{0} : Start Executing Job Log generation Sql server2000", CurrentGroup.Name);
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            GetTransactionLogName();
            GenerateLogFile(isLast);
        }

        public void ApplyLog(bool isLast)
        {
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            GetApplyLogs();
            PerformApplyLogs(isLast);
        }

        private void GetApplyLogs()
        {
            ApplyLogList = SqlLogDataAccess.GetApplyLogs(CurrentGroup.Id);
        }

        private void PerformApplyLogs(bool isLast)
        {
            bool killed = false;
            try
            {
                if (ApplyLogList.Count > 0)
                {
                    bool isApplied = false;
                    foreach (var logs in ApplyLogList)
                    {
                        if ((CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart) || (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted))
                        {
                            killed = Sql2000WinLog.KillProcess(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
                            isApplied = isLast ? Sql2000WinLog.RestoreLastLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, logs.LogFileName, "DRMaster", WFDatabase.DatabaseSql.PRAuthenticationMode.ToString()) : Sql2000WinLog.RestoreLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, "DRMaster", logs.LogFileName, _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());
                        }
                        else
                        {
                            killed = Sql2000WinLog.KillProcess(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
                            isApplied = isLast ? Sql2000WinLog.RestoreLastLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, logs.LogFileName, "DRMaster", WFDatabase.DatabaseSql.DRAuthenticationMode.ToString()) : Sql2000WinLog.RestoreLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, "DRMaster", logs.LogFileName, WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
                        }

                        if (isLast)
                        {
                            //  DatabaseDataAccess.UpdateDatabaseMode(CurrentDRDatabase.Id, false);
                        }

                        if (isApplied)
                        {
                            SqlLog drlog = BuildSqlLogApplied(logs);
                            SqlLogDataAccess.AddSqlLogApplied(drlog);
                            GroupDataAccess.UpdateGroupByReplicationStatus(CurrentGroup.Id, GroupState.Active, (int)ReplicationState.CompletedSuccessfully, true);
                            Logger.DebugFormat("{0} : {1} is applied successfully ", CurrentGroup.Name, logs.LogFileName);
                        }
                        else
                        {
                            Logger.DebugFormat("{0} : {1} Cannot be Apply applied successfully ", CurrentGroup.Name, logs.LogFileName);
                            throw new Exception();
                        }
                    }
                }

            }
            catch (BcmsException exc)
            {
                Logger.DebugFormat("{0} : Cannot Applied Log successfully {1}", CurrentGroup.Name, exc.Message);
                ExceptionManager.Manage(exc);
            }
            catch (Exception exc)
            {
                Logger.DebugFormat("{0} : Cannot be Applied Log successfully {1}", CurrentGroup.Name, exc.Message);
                if (isLast)
                    throw new BcmsException(BcmsExceptionType.Sql2000RestoreLastLogFailed);
            }
        }

        private SqlLog BuildSqlLogApplied(SqlLog logs)
        {
            logs.DRSequenceNo = logs.PRSequenceNo;
            logs.GroupId = logs.GroupId;
            return logs;
        }

        private void GenerateLogFile(bool isLast)
        {
            try
            {
                bool isGenerate = false;
                if ((CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart) || (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted))
                    isGenerate = isLast ? Sql2000WinLog.LastLogGeneration(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, CurrentLogFileName, "PrimaryMaster", _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString()) : Sql2000WinLog.GenerateLog(_wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, _wfDatabase.DatabaseSql.DRBackupRestorePath, "PrimaryMaster", CurrentLogFileName, _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString());
                else
                    isGenerate = isLast ? Sql2000WinLog.LastLogGeneration(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, CurrentLogFileName, "PrimaryMaster", _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString()) : Sql2000WinLog.GenerateLog(_wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, _wfDatabase.DatabaseSql.PRBackupRestorePath, "PrimaryMaster", CurrentLogFileName, _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());

                if (isLast)
                {
                    // DatabaseDataAccess.UpdateDatabaseMode(CurrentPRDatabase.Id, true);
                }

                if (isGenerate)
                {
                    SqlLog sqlLog = BuildSqlLogGenerate();
                    IsGenerate = SqlLogDataAccess.AddSqlLogGenerate(sqlLog);
                    Logger.DebugFormat("{0} : {1} Generated successfully", CurrentGroup.Name, CurrentLogFileName);
                }
                else
                {
                    throw new Exception();
                }
            }
            catch (BcmsException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception exc)
            {
                Logger.DebugFormat("{0} : Cannot Generated Log successfully {1}", CurrentGroup.Name, exc.Message);
                if (isLast)
                    throw new BcmsException(BcmsExceptionType.Sql2000GenerateLastLogFailed);
            }


        }

        private SqlLog BuildSqlLogGenerate()
        {
            var log = new SqlLog
            {
                LogFileName = CurrentLogFileName,
                PRSequenceNo = PRSqenceNo,
                LogGenerationTime = DateTime.Now,
                GroupId = CurrentGroup.Id
            };


            return log;
        }

        public void GetTransactionLogName()
        {
            try
            {
                string logFileName = SqlLogDataAccess.GetMaxLogFileName(CurrentGroup.Id);

                if (logFileName != string.Empty)
                {
                    CurrentLogFileName = GetNextLogFileName(logFileName);
                }
                else
                {
                    PRSqenceNo = "1";
                    CurrentLogFileName = string.Format("Log_{0}_{1}.trn", CurrentGroup.Name, DateTime.Now.ToString("MM-dd-yyyy_HHmmss"));
                }

            }
            catch (BcmsException exc)
            {
                ExceptionManager.Manage(exc, "Replication", CurrentGroup.Id, CurrentGroup.Name);
            }
            catch (Exception exc)
            {
                var bcms = new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while executing function signature SqlLogDataAccess.GetMaxLogFileName", exc);
                ExceptionManager.Manage(bcms, "Replication", CurrentGroup.Id, CurrentGroup.Name);
            }

        }

        public string GetNextLogFileName(string fileName)
        {
            //fileName = fileName.Replace(CurrentGroup.Name, "Name");

            //string[] files = fileName.Split(new[] { '_', '.' });

            int id = Convert.ToInt32(fileName) + 1;

            PRSqenceNo = id.ToString();

            return string.Format("Log_{0}_{1}.trn", CurrentGroup.Name, DateTime.Now.ToString("MM-dd-yyyy_HHmmss"));
        }

        public void sql2000healthMonitor()
        {
            Logger.DebugFormat("{0} : Start Executing Job Sql server2000 health", CurrentGroup.Name);
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                Health(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfServer.PRIPAddress, _wfDatabase.PRName, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            else
                Health(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfServer.DRIPAddress, _wfDatabase.DRName, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 health", CurrentGroup.Name);
        }

        public void KillProcess()
        {
            bool killed = false;
            Logger.DebugFormat("{0} : Start Executing Kill Process", CurrentGroup.Name);
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                killed = Sql2000WinLog.KillProcess(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                killed = Sql2000WinLog.KillProcess(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            if (killed)
                Logger.DebugFormat("{0} : Completed Executing Kill Process", CurrentGroup.Name);
            else
                Logger.DebugFormat("{0} : InCompleted Executing Kill Process", CurrentGroup.Name);
        }

        public void sql2000service()
        {
            Logger.DebugFormat("{0} : Start Executing Job Sql server2000 Service", CurrentGroup.Name);
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                _wfServer = ServerDataAccess.GetServerByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.DRServerId, CurrentGroup.PRServerId);
            else
                _wfServer = ServerDataAccess.GetServerByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
            Service(_wfServer.PRId, _wfServer.PRSudoUser, _wfServer.PRSudoPassword, _wfServer.PRIPAddress);
            Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Service for {1}", CurrentGroup.Name, _wfServer.PRIPAddress);
            Service(_wfServer.DRId, _wfServer.DRSudoUser, _wfServer.DRSudoPassword, _wfServer.DRIPAddress);
            Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Service for {1}", CurrentGroup.Name, _wfServer.DRIPAddress);
        }

        public void sql2000healthLogMonitor()
        {
            string CopiedFile = "";
            FileInfo file = null;
            Logger.DebugFormat("{0} : Start Executing Job Sql server2000 Log Generation", CurrentGroup.Name);
            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
            {
                _wfServer = ServerDataAccess.GetServerByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.DRServerId, CurrentGroup.PRServerId);
                _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.DRServerId, CurrentGroup.PRServerId);
            }
            else
            {
                _wfServer = ServerDataAccess.GetServerByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
                _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupIdSqlNative(CurrentGroup.Id, CurrentGroup.PRServerId, CurrentGroup.DRServerId);
            }
            SqlLog sqllogback = SqlLogDataAccess.GetLastLogs(CurrentGroup.Id, false);
            SqlLog sqlrestore = SqlLogDataAccess.GetLastLogs(CurrentGroup.Id, true);
            SqlLog sqlcopy = SqlLogDataAccess.GetCopiedLogs(CurrentGroup.Id);
            if (!string.IsNullOrEmpty(sqlcopy.CopiedFile))
            {
                file = new FileInfo(sqlcopy.CopiedFile);
                CopiedFile = file.Name;
            }
            SqlServer2000Log sqlserverlog = new SqlServer2000Log();
            sqlserverlog.GroupId = CurrentGroup.Id;
            sqlserverlog.LastBackUpFile = sqllogback.LogFileName;
            sqlserverlog.LastBackUpTime = sqllogback.LogGenerationTime.ToString();

            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                sqlserverlog.LastBackUpLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "PrimaryMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + sqllogback.LogFileName, _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            else
                sqlserverlog.LastBackUpLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "PrimaryMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + sqllogback.LogFileName, _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());

            sqlserverlog.LastRestoreFile = sqlrestore.LogFileName;
            sqlserverlog.LastRestoreTime = sqlrestore.LogAppliedTime.ToString();

            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                sqlserverlog.LastRestoreLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + sqlrestore.LogFileName, _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                sqlserverlog.LastRestoreLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + sqlrestore.LogFileName, _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString());

            sqlserverlog.LastCopyFile = sqlcopy.CopiedFile;
            sqlserverlog.LastCopyTime = sqlcopy.LogCopiedTime.ToString();

            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                sqlserverlog.LastCopyLSN = Sql2000WinLog.LogFileLsn(_wfServer.PRIPAddress, _wfDatabase.PRName, _wfDatabase.DatabaseSql.PRUserName, _wfDatabase.DatabaseSql.PRPassword, "DRMaster", _wfDatabase.DatabaseSql.PRBackupRestorePath + "\\" + CopiedFile, _wfDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            else
                sqlserverlog.LastCopyLSN = Sql2000WinLog.LogFileLsn(_wfServer.DRIPAddress, _wfDatabase.DRName, _wfDatabase.DatabaseSql.DRUserName, _wfDatabase.DatabaseSql.DRPassword, "DRMaster", _wfDatabase.DatabaseSql.DRBackupRestorePath + "\\" + CopiedFile, _wfDatabase.DatabaseSql.DRAuthenticationMode.ToString());


            sqlserverlog.DataLag = (sqlrestore.LogAppliedTime - sqllogback.LogGenerationTime).ToString().TrimStart('-');
            sqlserverlog.CreatorId = 1;
            sqlserverlog.CreateDate = DateTime.Now;
            var log = SqlLogDataAccess.CreateLogMonitor(sqlserverlog);
            Logger.DebugFormat("{0} : Completed Executing Job Sql server2000 Log Generation", CurrentGroup.Name);
        }

        public void sql2000FastCopy()
        {
            Logger.DebugFormat("{0} : Start Executing Fast Copy Job", CurrentGroup.Name);
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            _wfDatabase = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentGroup.Id);
            FastCopy.FastCopyJob fast = new FastCopy.FastCopyJob();
            try
            {
                fast.Synchronization(_wfServer, _wfDatabase, CurrentGroup);
                System.Threading.Thread.Sleep(10000);
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.Sql2000FastCopyFailed);
            }
        }

        public void Health(string PRIPAddress, string PRDBName, string DRIPAddress, string DRDBName, string PRUSer, string PRPassword, string DRUSer, string DRPassword, string AuthenMode)
        {
            string size = "";
            SqlServer2000 health = new SqlServer2000();
            health.GroupId = CurrentGroup.Id;
            health.DBEdition_PR = Sql2000WinLog.Edition(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBServicePack_PR = ServicePack(Sql2000WinLog.ServicePack(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode));
            health.DBInstance_PR = Sql2000WinLog.DBServerInstance(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBState_PR = Sql2000WinLog.DatabaseState(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.Database_PR = Sql2000WinLog.DatabaseName(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBRecoveryMode_PR = Sql2000WinLog.DBRecoveryModel(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBRestrictAccess_PR = Sql2000WinLog.DBRestrictAccessStatus(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBUpdateability_PR = Sql2000WinLog.DBUpdateability(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            size = Sql2000WinLog.DBsize(PRIPAddress, PRDBName, PRUSer, PRPassword, "PrimaryMaster", AuthenMode);
            health.DBSize_PR = size != "" ? (Convert.ToInt32(size) / 1024).ToString() : "NA";
            health.DBEdition_DR = Sql2000WinLog.Edition(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBServicePack_DR = ServicePack(Sql2000WinLog.ServicePack(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode));
            health.DBInstance_DR = Sql2000WinLog.DBServerInstance(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBState_DR = Sql2000WinLog.DatabaseState(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.Database_DR = Sql2000WinLog.DatabaseName(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBRecoveryMode_DR = Sql2000WinLog.DBRecoveryModel(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBRestrictAccess_DR = Sql2000WinLog.DBRestrictAccessStatus(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBUpdateability_DR = Sql2000WinLog.DBUpdateability(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            size = Sql2000WinLog.DBsize(DRIPAddress, DRDBName, DRUSer, DRPassword, "DRMaster", AuthenMode);
            health.DBSize_DR = size != "" ? (Convert.ToInt32(size) / 1024).ToString() : "NA";
            health.CreatorId = 1;
            health.CreateDate = DateTime.Now;
            var healthreturn = SqlLogDataAccess.CreateHealthMonitor(health);
        }


        private string ServicePack(string SPnumber)
        {

            switch (SPnumber)
            {
                case "8.00.2039":
                    return "SQL Server 2000 Service Pack 4";

                case "8.00.760":
                    return "SQL Server 2000 Service Pack 3";

                case "8.00.534":
                    return "SQL Server 2000 Service Pack 2";

                case "8.00.384":
                    return "SQL Server 2000 Service Pack 1";

                case "8.00.194":
                    return "SQL Server 2000 RTM";

                default:
                    return "Not Available";

            }
        }
        private void Service(int host, string user, string password, string ipaddr)
        {
            string[] services = { "MSSQLSERVER", "MSSQLServerADHelper", "SQLSERVERAGENT", "SharedAccess" };
            SqlServer2000Service service = new SqlServer2000Service();
            string result = "";
            foreach (string ser in services)
            {
                service.GroupId = CurrentGroup.Id;
                service.Server = host;
                service.ServiceName = ser;
                result = Sql2000WinLog.WindowServices(ipaddr, user, password, ser);
                if ((result != "Failure") && (result != ""))
                {
                    var splitresult = result.Split(';');
                    service.ServiceName = ser;
                    service.Status = splitresult[1];
                    service.Start_Mode = splitresult[2];
                    var res = SqlLogDataAccess.CreateServiceMonitor(service);
                }
                else
                {
                    service.ServiceName = ser;
                    service.Status = "--";
                    service.Start_Mode = "--";
                    var res = SqlLogDataAccess.CreateServiceMonitor(service);
                }

            }
        }

        public void RunStoreProcedurePR(string Procedure, string filename)
        {
            bool isSuccess = false;


            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                isSuccess = Sql2000Migration.MigrateLoginPR(WFServer.DRUserName, WFServer.DRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, Procedure, @WFDatabase.DatabaseSql.DRBackupRestorePath + "\\" + filename, WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            else
                isSuccess = Sql2000Migration.MigrateLoginPR(WFServer.PRUserName, WFServer.PRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFServer.PRIPAddress, Procedure, @WFDatabase.DatabaseSql.PRBackupRestorePath + "\\" + filename, WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            if (isSuccess)
            {
                Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
            }
            else
            {
                if (filename == "MigrateLogin.sql")
                    throw new BcmsException(BcmsExceptionType.Sql2000MigrateLoginPRFailed);
                else
                    throw new BcmsException(BcmsExceptionType.Sql2000MigrateServerRolePRFailed);
            }
        }

        public void RunScriptDr(string filename)
        {
            bool isSuccess = false;


            if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchBackStart)
                isSuccess = Sql2000Migration.MigrateLoginDR(WFServer.PRUserName, WFServer.PRPassword, WFDatabase.DatabaseSql.PRUserName, WFDatabase.DatabaseSql.PRPassword, WFServer.PRIPAddress, @WFDatabase.DatabaseSql.PRBackupRestorePath + "\\" + filename, WFDatabase.DatabaseSql.DRAuthenticationMode.ToString());
            else
                isSuccess = Sql2000Migration.MigrateLoginDR(WFServer.DRUserName, WFServer.DRPassword, WFDatabase.DatabaseSql.DRUserName, WFDatabase.DatabaseSql.DRPassword, WFServer.DRIPAddress, @WFDatabase.DatabaseSql.DRBackupRestorePath + "\\" + filename, WFDatabase.DatabaseSql.PRAuthenticationMode.ToString());
            if (isSuccess)
            {
                Logger.DebugFormat("{0} : Run DB option {1} is successfully", CurrentGroup.Name, "DR database" + CurrentDatabase.DRName);
            }
            else
            {
                if (filename == "MigrateLogin.sql")
                    throw new BcmsException(BcmsExceptionType.Sql2000MigrateLoginDRFailed);
                else
                    throw new BcmsException(BcmsExceptionType.Sql2000MigrateServerRoleDRFailed);
            }
        }

        public void SetServerStatus()
        {
            _wfServer = ServerDataAccess.GetServerByGroupId(CurrentGroup.Id);
            bool prstatus;
            bool drstatus;
            ServerStatus status;
            Logger.DebugFormat("{0} : Check Server Status : {1}", CurrentGroup.Name, _wfServer.PRIPAddress);
            prstatus = Sql2000Migration.CheckServerStatus(_wfServer.PRIPAddress, _wfServer.PRUserName, _wfServer.PRPassword);
            Thread.Sleep(5000);

            status = prstatus == true ? ServerStatus.Up : ServerStatus.Down;
            Logger.DebugFormat("{0} Server Status : {1}", _wfServer.PRIPAddress, prstatus);
            ServerDataAccess.UpdateServerByStatus(_wfServer.PRId, status);

            Logger.DebugFormat("{0} : Check Server Status : {1}", CurrentGroup.Name, _wfServer.DRIPAddress);
            drstatus = Sql2000Migration.CheckServerStatus(_wfServer.DRIPAddress, _wfServer.DRUserName, _wfServer.DRPassword);
            Thread.Sleep(5000);
            status = drstatus == true ? ServerStatus.Up : ServerStatus.Down;
            Logger.DebugFormat("{0} Server Status : {1}", _wfServer.DRIPAddress, drstatus);
            ServerDataAccess.UpdateServerByStatus(_wfServer.DRId, status);
        }




        #endregion


        #region Parallel

        private void UpdateParallelGroupWorkflow(string status)
        {
            CurrentParallelGroupWorkflow.Status = status;
            CurrentParallelGroupWorkflow.CurrentActionId = CurrentWorkflowActionId;
            CurrentParallelGroupWorkflow.CurrentActionName = CurrentWorkflowActionName;

            CurrentParallelGroupWorkflow.Message = string.IsNullOrEmpty(CurrentParallelGroupWorkflow.Message) ? string.Empty : CurrentParallelGroupWorkflow.Message;

            ParallelGroupWorkflowDataAccess.UpdateByStatusAndMessage(CurrentParallelGroupWorkflow);
        }

        private void AddParallelWorkflowActionResultStatus(string status)
        {
            ParallelWorkflowActionResult.WorkflowActionName = CurrentWorkflowActionName;
            ParallelWorkflowActionResult.ActionId = CurrentWorkflowActionId;
            ParallelWorkflowActionResult.StartTime = CurrentActionStartTime;
            ParallelWorkflowActionResult.EndTime = DateTime.Now;
            ParallelWorkflowActionResult.Status = status;
            ParallelWorkflowActionResult.ParallelGroupWorkflowId = CurrentParallelGroupWorkflow.Id;
            ParallelWorkflowActionResult.ParallelDROperationId = CurrentParallelGroupWorkflow.ParallelDROperationId;
            ParallelWorkflowActionResult.GroupId = CurrentGroup.Id;
            ParallelWorkflowActionResult.Message = string.Empty;
            ParallelWorkflowActionResult = ParallelWorkflowActionResultDataAccess.Add(ParallelWorkflowActionResult);

        }

        private void UpdateParallelWorkflowActionResultStatus(string status)
        {
            ParallelWorkflowActionResult.StartTime = CurrentActionStartTime;
            ParallelWorkflowActionResult.EndTime = DateTime.Now;
            ParallelWorkflowActionResult.Status = status;
            ParallelWorkflowActionResult = ParallelWorkflowActionResultDataAccess.Update(ParallelWorkflowActionResult);
        }

        #endregion

        private void UpdateGroupStatus(GroupState groupState, int replicationState, bool isEnable)
        {
            GroupDataAccess.UpdateGroupByReplicationStatus(CurrentGroup.Id, groupState, replicationState, isEnable);
        }

        private SCRConfig BuildSCRGroupEntity()
        {

            var scrconfig = new SCRConfig();

            var group = GroupDataAccess.GetGroupById(CurrentGroup.Id);
            if (group != null)
            {
                if (CurrentGroup.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                {
                    var prServer = ServerDataAccess.GetServersById(group.DRServerId);
                    scrconfig.PRServerIP = prServer.PRIPAddress;
                    scrconfig.PRUserID = prServer.PRUserName;
                    scrconfig.PRPassword = prServer.PRPassword;
                    scrconfig.PRServerName = prServer.PRName;

                    var drServer = ServerDataAccess.GetServersById(group.PRServerId);
                    scrconfig.DRServerIP = drServer.PRIPAddress;
                    scrconfig.DRUserID = drServer.PRUserName;
                    scrconfig.DRPassword = drServer.PRPassword;
                    scrconfig.DRServerName = drServer.PRName;

                    var prDB = DatabaseBaseDataAccess.GetDatabaseById(group.DRDatabaseId);

                    scrconfig.PRStorageGroupName = prDB.DatabaseExchange.DRStorageGroupName;
                    scrconfig.PRStorageMailboxDBName = prDB.DatabaseExchange.DRMailBoxDBName;

                    var drDB = DatabaseBaseDataAccess.GetDatabaseById(group.PRDatabaseId);


                    scrconfig.DRStorageGroupName = drDB.DatabaseExchange.PRStorageGroupName;
                    scrconfig.DRStorageMailboxDBName = drDB.DatabaseExchange.PRMailBoxDBName;


                    var replication = ReplicationBaseDataAccess.GetReplicationById(group.PRReplicationId);

                    scrconfig.prExchangeFolderPath = replication.SCR.DRInstallationPath;
                    scrconfig.prNewStorageMailboxPath = replication.SCR.DRNewMailboxPath;
                    scrconfig.prBcmsExchageComponentPath = replication.SCR.DRComponentPath;

                    scrconfig.drExchangeFolderPath = replication.SCR.PRInstallationPath;
                    scrconfig.drNewStorageMailboxPath = replication.SCR.PRNewMailboxPath;
                    scrconfig.drBcmsExchageComponentPath = replication.SCR.PRComponentPath;

                    scrconfig.ReplayLagTime = replication.SCR.ReplayLagTime;

                    scrconfig.BcmsExchangeExeName = "ExchangeEX.exe";
                    scrconfig.LicenseKey =
                        "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                }
                else
                {

                    var prServer = ServerDataAccess.GetServersById(group.PRServerId);
                    scrconfig.PRServerIP = prServer.PRIPAddress;
                    scrconfig.PRUserID = prServer.PRUserName;
                    scrconfig.PRPassword = prServer.PRPassword;
                    scrconfig.PRServerName = prServer.PRName;

                    var drServer = ServerDataAccess.GetServersById(group.DRServerId);
                    scrconfig.DRServerIP = drServer.PRIPAddress;
                    scrconfig.DRUserID = drServer.PRUserName;
                    scrconfig.DRPassword = drServer.PRPassword;
                    scrconfig.DRServerName = drServer.PRName;

                    //var prDB = DatabaseBaseDataAccess.GetDatabaseById(CurrentGroup.PRDatabaseId);

                    var prDB = DatabaseBaseDataAccess.GetDatabaseById(group.PRDatabaseId);

                    scrconfig.PRStorageGroupName = prDB.DatabaseExchange.PRStorageGroupName;
                    scrconfig.PRStorageMailboxDBName = prDB.DatabaseExchange.PRMailBoxDBName;

                    var drDB = DatabaseBaseDataAccess.GetDatabaseById(group.DRDatabaseId);


                    scrconfig.DRStorageGroupName = drDB.DatabaseExchange.DRStorageGroupName;
                    scrconfig.DRStorageMailboxDBName = drDB.DatabaseExchange.DRMailBoxDBName;


                    var replication = ReplicationBaseDataAccess.GetReplicationById(group.PRReplicationId);

                    scrconfig.prExchangeFolderPath = replication.SCR.PRInstallationPath;
                    scrconfig.prNewStorageMailboxPath = replication.SCR.PRNewMailboxPath;
                    scrconfig.prBcmsExchageComponentPath = replication.SCR.PRComponentPath;

                    scrconfig.drExchangeFolderPath = replication.SCR.DRInstallationPath;
                    scrconfig.drNewStorageMailboxPath = replication.SCR.DRNewMailboxPath;
                    scrconfig.drBcmsExchageComponentPath = replication.SCR.DRComponentPath;

                    scrconfig.ReplayLagTime = replication.SCR.ReplayLagTime;

                    scrconfig.BcmsExchangeExeName = "ExchangeEX.exe";
                    scrconfig.LicenseKey =
                        "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";

                }
            }
            return scrconfig;
        }


        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    _server = null;
                    _globalMirror = null;
                    _dscliserver = null;
                    _hmcPRInfo = null;
                    _hmcDRInfo = null;
                    _sshInfo = null;
                    _dscliPRInfo = null;
                    _dscliDRInfo = null;
                    _database = null;
                    _globalMirrorReplication = null;
                }
            }

            _isDisposed = true;
        }

        ~BcmsWorkFlowClient()
        {
            Dispose(false);
        }

        #endregion
    }
}