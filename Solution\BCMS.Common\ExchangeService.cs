﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Exchange", Namespace = "http://www.BCMS.com/types")]
    public class ExchangeService : BaseEntity
    {
        [DataMember]
        public int ServiceId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string ServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string PRServiceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PRServiceStartMode
        {
            get;
            set;
        }

        [DataMember]
        public string DRServiceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRServiceStartMode
        {
            get;
            set;
        }
    }
}