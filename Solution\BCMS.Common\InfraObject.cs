﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "InfraObject", Namespace = "http://www.ContinuityVault.com/types")]
    public class InfraObject : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public string Description
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessServiceId
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessFunctionId
        {
            get;
            set;
        }

        [DataMember]
        public int Type
        {
            get;
            set;
        }

        [DataMember]
        public int SubType
        {
            get;
            set;
        }

        [DataMember]
        public string PairGroupId
        {
            get;
            set;
        }

        [DataMember]
        public bool DRReady
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationType
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }

        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }

        [DataMember]
        public int PRDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int DRDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int PRReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int DRReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int Priority
        {
            get;
            set;
        }

        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationStatus
        {
            get;
            set;
        }

        [DataMember]
        public bool EnableDataSync
        {
            get;
            set;
        }

        [DataMember]
        public int DROperationStatus
        {
            get;
            set;
        }

        [DataMember]
        public int DROperationId
        {
            get;
            set;
        }

        [DataMember]
        public int SiteSolutionTypeId
        {
            get;
            set;
        }

        [DataMember]
        public int NearGroupId
        {
            get;
            set;
        }

        [DataMember]
        public int MonitoringWorkflow
        {
            get;
            set;
        }

        [DataMember]
        public int DrMonitorApplicationCheck
        {
            get;
            set;
        }

        [DataMember]
        public int DrMonitoringWorkflow
        {
            get;
            set;
        }

        [DataMember]
        public string Command { get; set; }

        [DataMember]
        public string Output { get; set; }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string ConfigureDataLag
        {
            get;
            set;
        }

        [DataMember]
        public int IsWorkflow
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId2
        {
            get;
            set;
        }

        [DataMember]
        public int DRServerId2
        {
            get;
            set;
        }

        #endregion Properties
    }
}