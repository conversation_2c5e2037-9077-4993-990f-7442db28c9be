﻿﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class FastCopyJobDataAccess:BaseDataAccess
    {

        public static bool UpdateFastCopyJobByReplicationTime(int fastId, string lastReplicationTime, string nexReplicationTime)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("FastCopyJob_UpdateByRepTime"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"fastid", DbType.Int32, fastId);
                    Database.AddInParameter(dbCommand, Dbstring+"iLastReplTime", DbType.AnsiString, lastReplicationTime);
                    Database.AddInParameter(dbCommand, Dbstring+"iNextScheduleTime", DbType.AnsiString, nexReplicationTime);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update FastCopyJob By ReplicationTime ", exc);
            }
        }

        public static bool UpdateFastCopyJobByMode(int mode, int id)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_UPDATEMODE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"imode", DbType.Int32, mode);
                    Database.AddInParameter(dbCommand, Dbstring+"JobId", DbType.Int32, id);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update FastCopyJob By Mode", exc);
            }
        }

        public static IList<FastCopyJob> GetByFastCopyId(int id)
        {
            IList<FastCopyJob> fastCopyJobList = new List<FastCopyJob>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_GETBYFASTCOPYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGlobalReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGlobalReader.Read())
                        {
                            var fastcopyjob = new FastCopyJob
                            {

                                Id = Convert.IsDBNull(myGlobalReader["Id"]) ? 0 : Convert.ToInt32(myGlobalReader["Id"]),
                                FastCopyId = Convert.IsDBNull(myGlobalReader["FastCopyId"]) ? 0 : Convert.ToInt32(myGlobalReader["FastCopyId"]),
                                SourceDirectory = Convert.IsDBNull(myGlobalReader["SourceDirectory"]) ? string.Empty : Convert.ToString(myGlobalReader["SourceDirectory"]),
                                DestinationDirectory = Convert.IsDBNull(myGlobalReader["DestinationDirectory"]) ? string.Empty : Convert.ToString(myGlobalReader["DestinationDirectory"]),
                                LastSuccessfullReplTime = Convert.IsDBNull(myGlobalReader["LastSuccessfullReplTime"]) ? string.Empty : Convert.ToString(myGlobalReader["LastSuccessfullReplTime"]),
                                NextScheduleRepTime = Convert.IsDBNull(myGlobalReader["NextScheduleRepTime"]) ? string.Empty : Convert.ToString(myGlobalReader["NextScheduleRepTime"]),
                                Mode = Convert.IsDBNull(myGlobalReader["ModeType"]) ? 0 : Convert.ToInt32(myGlobalReader["ModeType"].ToString()),
                                DeletionMode = Convert.IsDBNull(myGlobalReader["DeletionMode"]) ? 0 : Convert.ToInt32(myGlobalReader["DeletionMode"].ToString())
                                //Id = Convert.ToInt32(myGlobalReader[0]),
                                //FastCopyId = Convert.ToInt32(myGlobalReader[1].ToString()),
                                //SourceDirectory = myGlobalReader[2].ToString(),
                                //DestinationDirectory = myGlobalReader[3].ToString(),
                                //LastSuccessfullReplTime = myGlobalReader[4].ToString(),
                                //NextScheduleRepTime = myGlobalReader[5].ToString(),
                                //Mode = Convert.ToInt32(myGlobalReader[28].ToString()) 

                            };
                            fastCopyJobList.Add(fastcopyjob);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get fastcopyjob information by Id - " + id, exc);

            }
            return fastCopyJobList;
        }
  

        public static FastCopyJob GetFastCopyJobById(int id)
        {
            try
            {
                var fastcopyjob = new FastCopyJob();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGlobalReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGlobalReader.Read())
                        {
                            fastcopyjob.Id = Convert.IsDBNull(myGlobalReader["Id"])
                                                 ? 0
                                                 : Convert.ToInt32(myGlobalReader["Id"]);
                            fastcopyjob.FastCopyId = Convert.IsDBNull(myGlobalReader["FastCopyId"])
                                                         ? 0
                                                         : Convert.ToInt32(myGlobalReader["FastCopyId"]);
                            fastcopyjob.SourceDirectory = Convert.IsDBNull(myGlobalReader["SourceDirectory"])
                                                              ? string.Empty
                                                              : Convert.ToString(myGlobalReader["SourceDirectory"]);
                            fastcopyjob.DestinationDirectory = Convert.IsDBNull(myGlobalReader["DestinationDirectory"])
                                                                   ? string.Empty
                                                                   : Convert.ToString(
                                                                       myGlobalReader["DestinationDirectory"]);
                            fastcopyjob.LastSuccessfullReplTime =
                                Convert.IsDBNull(myGlobalReader["LastSuccessfullReplTime"])
                                    ? string.Empty
                                    : Convert.ToString(myGlobalReader["LastSuccessfullReplTime"]);
                            fastcopyjob.NextScheduleRepTime = Convert.IsDBNull(myGlobalReader["NextScheduleRepTime"])
                                                                  ? string.Empty
                                                                  : Convert.ToString(
                                                                      myGlobalReader["NextScheduleRepTime"]);
                            fastcopyjob.Mode = Convert.IsDBNull(myGlobalReader["ModeType"]) ? 0 : Convert.ToInt32(myGlobalReader["ModeType"].ToString());

                            fastcopyjob.DeletionMode = Convert.IsDBNull(myGlobalReader["DeletionMode"]) ? 0 : Convert.ToInt32(myGlobalReader["DeletionMode"].ToString());

                            //fastcopyjob.Id = Convert.ToInt32(myGlobalReader[0]);
                            //fastcopyjob.FastCopyId = Convert.ToInt32(myGlobalReader[1].ToString());
                            //fastcopyjob.SourceDirectory = myGlobalReader[2].ToString();
                            //fastcopyjob.DestinationDirectory = myGlobalReader[3].ToString();
                            //fastcopyjob.LastSuccessfullReplTime = myGlobalReader[4].ToString();
                            //fastcopyjob.NextScheduleRepTime = myGlobalReader[5].ToString();
                            //fastcopyjob.Mode = Convert.ToInt32(myGlobalReader[28].ToString());

                        }
                    }
                }

                return fastcopyjob;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get fastcopyjob information by Id - " + id, exc);
            }
        }

        public static IList<FastCopyJob> GetFastCopyJobListByReplicationId(int replicationid)
        {
            IList<FastCopyJob> fastCopyJobList = new List<FastCopyJob>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_GETBYREPLICATIONID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "ireplicationId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGlobalReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGlobalReader.Read())
                        {
                            var fastcopyjob = new FastCopyJob();

                            fastcopyjob.Id = Convert.IsDBNull(myGlobalReader["Id"])
                                                 ? 0
                                                 : Convert.ToInt32(myGlobalReader["Id"]);
                            fastcopyjob.FastCopyId = Convert.IsDBNull(myGlobalReader["FastCopyId"])
                                                         ? 0
                                                         : Convert.ToInt32(myGlobalReader["FastCopyId"]);
                            fastcopyjob.SourceDirectory = Convert.IsDBNull(myGlobalReader["SourceDirectory"])
                                                              ? string.Empty
                                                              : Convert.ToString(myGlobalReader["SourceDirectory"]);
                            fastcopyjob.DestinationDirectory = Convert.IsDBNull(myGlobalReader["DestinationDirectory"])
                                                                   ? string.Empty
                                                                   : Convert.ToString(
                                                                       myGlobalReader["DestinationDirectory"]);
                            fastcopyjob.LastSuccessfullReplTime =
                                Convert.IsDBNull(myGlobalReader["LastSuccessfullReplTime"])
                                    ? string.Empty
                                    : Convert.ToString(myGlobalReader["LastSuccessfullReplTime"]);
                            fastcopyjob.NextScheduleRepTime = Convert.IsDBNull(myGlobalReader["NextScheduleRepTime"])
                                                                  ? string.Empty
                                                                  : Convert.ToString(
                                                                      myGlobalReader["NextScheduleRepTime"]);
                            fastcopyjob.Mode = Convert.IsDBNull(myGlobalReader["ModeType"]) ? 0 : Convert.ToInt32(myGlobalReader["ModeType"].ToString());

                            fastCopyJobList.Add(fastcopyjob);
                        }
                    }
                }

                return fastCopyJobList;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get fastcopyjob information by Id - " + replicationid, exc);
            }
        }



        public static FastCopyJob GetFastCopyJobByReplicationId(int replicationid)
        {
            try
            {
                var fastcopyjob = new FastCopyJob();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_GETBYREPLICATIONID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"ireplicationId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGlobalReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGlobalReader.Read())
                        {
                            fastcopyjob.Id = Convert.IsDBNull(myGlobalReader["Id"])
                                                 ? 0
                                                 : Convert.ToInt32(myGlobalReader["Id"]);
                            fastcopyjob.FastCopyId = Convert.IsDBNull(myGlobalReader["FastCopyId"])
                                                         ? 0
                                                         : Convert.ToInt32(myGlobalReader["FastCopyId"]);
                            fastcopyjob.SourceDirectory = Convert.IsDBNull(myGlobalReader["SourceDirectory"])
                                                              ? string.Empty
                                                              : Convert.ToString(myGlobalReader["SourceDirectory"]);
                            fastcopyjob.DestinationDirectory = Convert.IsDBNull(myGlobalReader["DestinationDirectory"])
                                                                   ? string.Empty
                                                                   : Convert.ToString(
                                                                       myGlobalReader["DestinationDirectory"]);
                            fastcopyjob.LastSuccessfullReplTime =
                                Convert.IsDBNull(myGlobalReader["LastSuccessfullReplTime"])
                                    ? string.Empty
                                    : Convert.ToString(myGlobalReader["LastSuccessfullReplTime"]);
                            fastcopyjob.NextScheduleRepTime = Convert.IsDBNull(myGlobalReader["NextScheduleRepTime"])
                                                                  ? string.Empty
                                                                  : Convert.ToString(
                                                                      myGlobalReader["NextScheduleRepTime"]);
                            fastcopyjob.Mode = Convert.IsDBNull(myGlobalReader["ModeType"]) ? 0 : Convert.ToInt32(myGlobalReader["ModeType"].ToString());
                        }
                    }
                }

                return fastcopyjob;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get fastcopyjob information by Id - " + replicationid, exc);
            }
        }

        public static bool IsAllFastCopyJobCompleted(int fastCopyId, int mode)
        {
           
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_ISFINISHALLJOB"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iFastCopyId", DbType.Int32, fastCopyId);
                    Database.AddInParameter(dbCommand, Dbstring+"imode", DbType.Int32, mode);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var myScalar = Database.ExecuteScalar(dbCommand);

                    return myScalar == null;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All jobs are finished by fastCopyId", exc);
            }
        }
    }
}

