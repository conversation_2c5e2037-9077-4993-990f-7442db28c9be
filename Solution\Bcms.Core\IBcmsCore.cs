﻿namespace Bcms.Core
{
    public interface IBcmsCore
    {
        void Initialize(bool isScheduled);

        void Start();

        void Stop();

        void StopParallelService();


        void Pause(string jobName, string groupName);


        void Resume(string jobName, string groupName);


        void PauseGroup(string groupName);


        void ResumeGroup(string groupName);


        void PauseAll();


        void ResumeAll();


        bool IsGroupPaused(string groupName);


        void Delete(string jobName, string groupName);


        void Ping();


        void SwitchOver();


        void SwitchBack();

        //void PowerOn();
        //void PowerOff();

        void SwitchOverAction();

        void SwitchBackAction();

        void SqlNativeJobStatus();

        //void EventWorkFlow();

        void Custom();

        void CustomAction();

        void ExecuteBackup();

        void InitializeParallel();

        void TestConnectionAction();

        void SrdfDiscoveryAction();

        void PreparingScheduleWorkflowonclick();

        void ScheduleDiscoveryJobs();

        void GetVCenterServiceStatus();

        void PrepareDyanamicDatabase(int id, int reptype);

        //add
        void ExecuteParallelWorkflow();
        void PrepareResumeWorkflow();

        void TerminateJob();
    }
}