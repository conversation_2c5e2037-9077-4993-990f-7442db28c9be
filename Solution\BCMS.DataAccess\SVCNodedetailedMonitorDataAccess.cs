﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class SVCNodedetailedMonitorDataAccess : BaseDataAccess
    {
        public static bool AddSVCNodeDetailedMonitorLog(SVCNodeDetailedMonitor svcNodeDetailedlog)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SVCNODEDETAILMONITORLOG_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "IINFRAOBJECTID", DbType.Int32, svcNodeDetailedlog.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "IPRNODEID", DbType.AnsiString, svcNodeDetailedlog.PRNodeId);
                    Database.AddInParameter(cmd, Dbstring + "IPRNODENAME", DbType.AnsiString, svcNodeDetailedlog.PRNodeName);
                    Database.AddInParameter(cmd, Dbstring + "IPRNODESTATUS", DbType.AnsiString, svcNodeDetailedlog.PRNodeStatus);
                    Database.AddInParameter(cmd, Dbstring + "IPRSERVICEIPADDRESS", DbType.AnsiString, svcNodeDetailedlog.PRServiceIpAddress);
                    Database.AddInParameter(cmd, Dbstring + "IPRPRODUCTMACHTYPE", DbType.AnsiString, svcNodeDetailedlog.PRProductMachType);
                    Database.AddInParameter(cmd, Dbstring + "IPRMACHCODELEVEL", DbType.AnsiString, svcNodeDetailedlog.PRMachCodeLevel);
                    Database.AddInParameter(cmd, Dbstring + "IDRNODEID", DbType.AnsiString, svcNodeDetailedlog.DRNodeId);
                    Database.AddInParameter(cmd, Dbstring + "IDRNODENAME", DbType.AnsiString, svcNodeDetailedlog.DRNodeName);
                    Database.AddInParameter(cmd, Dbstring + "IDRNODESTATUS", DbType.AnsiString, svcNodeDetailedlog.DRNodeStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRSERVICEIPADDRESS", DbType.AnsiString, svcNodeDetailedlog.DRServiceIpAddress);
                    Database.AddInParameter(cmd, Dbstring + "IDRPRODUCTMACHTYPE", DbType.AnsiString, svcNodeDetailedlog.DRProductMachType);
                    Database.AddInParameter(cmd, Dbstring + "IDRMACHCODELEVEL", DbType.AnsiString, svcNodeDetailedlog.DRMachCodeLevel);


                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Node Detailed Monitor Logs InfraObject Id - " + svcNodeDetailedlog.InfraObjectId, exc);

            }

            return false;
        }
    }
}
