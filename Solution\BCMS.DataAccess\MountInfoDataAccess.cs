﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class MountInfoDataAccess : BaseDataAccess
    {

        public static bool AddMountInfo(MountPoint mount)
        {
            try
            {

                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("MountPoint_Create"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iUsedSpace", DbType.AnsiString, mount.UsedSpace);
                    db.AddInParameter(dbCommand, Dbstring+"iFreeSpace", DbType.AnsiString, mount.FreeSpace);
                    db.AddInParameter(dbCommand, Dbstring+"iCapacity", DbType.AnsiString, mount.Capacity);
                    db.AddInParameter(dbCommand, Dbstring+"iUsedPercent", DbType.AnsiString, mount.UsedPercent);
                    db.AddInParameter(dbCommand, Dbstring+"iServerType", DbType.Int32, mount.ServerType);
                    db.AddInParameter(dbCommand, Dbstring+"iGroupId", DbType.Int32, mount.GroupId);
                    int value = db.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert MountPoint information", exc);
            }
            return false;
        }
         
    }
}