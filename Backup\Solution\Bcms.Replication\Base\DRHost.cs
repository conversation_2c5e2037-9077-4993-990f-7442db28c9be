﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Text;
using Bcms.ExceptionHandler;

using Bcms.Replication.Entity;
using Bcms.Replication.Shared;
using Jscape.Ssh;
using Oracle.DataAccess.Client;
using PGlobalMirror;
using log4net;

namespace Bcms.Replication.Base
{
    public class DRHost : Host
    {
        private SecureConnection _drConnection;

        public SshSession DRSession { get; set; }

        private readonly object _lockObject = new object(); 

        public SecureConnection DRConnection
        {
            get
            {
                return _drConnection ?? (_drConnection = new SecureConnection());
            }
            set
            {
                _drConnection = value;
            }
        }

       private static readonly ILog Logger = LogManager.GetLogger(typeof(DRHost));

        public DRHost(string hostName, string userName, string password)
        {
            HostName = hostName;
            UserName = userName;
            Password = password;
        }

        //Added By Jeyapandi

        public DRHost(string hostName, string userName, string privatekey, string privateKeyPasspharase)
        {
            HostName = hostName;
            UserName = userName;
            Password = "";
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
        }

        //Modified By Jeyapandi For SSH Key Connection
        internal override bool Connect(bool isConnectDatabase)
        {

            try
            {
                lock (_lockObject)
                {

                    if (string.IsNullOrEmpty(Password) && !string.IsNullOrEmpty(PrivateKey))
                    {
                        SshParameters parameters = new SshParameters(HostName, UserName, Password);

                        parameters.SetPrivateKey(new FileInfo(PrivateKey), PrivateKeyPasspharase);

                        DRSession = new SshSession(parameters);

                    }
                    else
                    {
                        DRSession = new SshSession(HostName, UserName, Password);
                    }

                    DRSession.LicenseKey = Constants.QueryConstants.LicenseKey;

                    DRSession.SetShellPrompt("\\$|#|>", true);
                    DRSession.Connect();
                    if (isConnectDatabase)
                    {
                        return ConnectToDatabase(DRSession, DatabaseName);
                    }
                    return true;
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting DR host - " + HostName, sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occured while connecting DR host - " + HostName, exc);
            }
        }
        //End Modified By Jeyapandi For SSH Key Connection



        //internal override bool ConnectWithDatabaseName(string databaseName)
        //{
        //    try
        //    {
        //        DRSession = new SshSession(HostName, UserName, Password) { LicenseKey = Constants.QueryConstants.LicenseKey };
        //        DRSession.SetShellPrompt("\\$|#|>|:", true);
        //        DRSession.Connect();

        //        return ConnectToDatabase(DRSession, databaseName);
        //    }
        //    catch (SshAuthenticationException sshAuthentication)
        //    {
        //        throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
        //    }
        //    catch (SshException sshException)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting DR host - " + HostName, sshException);
        //    }
        //    catch (BcmsException)
        //    {
        //        throw;
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonUnhandled,
        //                                "Exception occured while connecting DR host - " + HostName, exc);
        //    }
        //}

        internal override void Disconnect()
        {
            if (DRSession != null)
            {
                DRSession.Disconnect();
            }
        }

        internal bool IsDatabaseRunning()
        {
            if (IsWindows)
            {
                return IsWinDatabaseRunning();
            }
            return OracleDB.DBRunning(new SSHInfo(HostName, UserName, Password), SudoUser, DatabaseName);
        }

        internal bool IsListnerRunning()
        {
            if (IsWindows)
            {
                return true;
            }
            return OracleDB.IsListnerRunning(new SSHInfo(HostName, UserName, Password), SudoUser);
        }

        internal override bool VerifyDatabaseMode()
        {
            try
            {
                if (IsWindows)
                {
                    return VerifyWindowsDatabaseMode();
                }

                LogHelper.LogHeaderAndMessage("DR DATABASE MODE COMMAND : ", Constants.QueryConstants.DatabaseModeAndRole);

                var drRole = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.DatabaseModeAndRole, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("DR DATABASE MODE OUTPUT :", drRole);

                return drRole.Contains("standby") && drRole.Contains("mounted");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while verify DR database mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while verify DR database mode", exc);
            }
          
        }

        private bool VerifyWindowsDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsDatabaseModeAndRole);

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDatabaseModeAndRole))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string mode = myDatabaseReader[1].ToString();
                            string role = myDatabaseReader[2].ToString();

                            if (mode == "MOUNTED" && role == "PHYSICAL STANDBY")
                            {
                                return true;
                            }
                            return false;

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATABASE MODE OUTPUT : ", exc);
                return false;
            }

            return false;
        }

        internal override bool VerifyUserStatus()
        {
            try
            {

                LogHelper.LogHeaderAndMessage("DR USER STATUS COMMAND : ", Constants.QueryConstants.ActiveUserCount);

                var usercount = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.ActiveUserCount, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("DR DATABASE USER STATUS :", usercount);

                if (usercount.Contains("count(*)") && usercount.Contains("-") && usercount.Contains("sql>"))
                {
                    usercount = usercount.Replace("\r\n", "");
                    usercount = usercount.Replace("\t", "");
                    usercount = usercount.Replace("count(*)", "");
                    usercount = usercount.Replace("-", "");
                    usercount = usercount.Replace("sql>", "").Trim();

                    if (Convert.ToInt32(usercount) > 0)
                    {
                      throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound, usercount + "Active user (" + usercount + ") found logged in status in DR Server(" + HostName + ")");
                    }

                    return true;
                }

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while DR get Active user count", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY DR USER STATUS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get DR Active user count", exc);
            }

            return false;

        }

        internal override string GetMaxSequenceNumber()
        {
            try
            {

                if (IsWindows)
                {
                    return WinDGGetMaxSequenceNumber();
                }

                LogHelper.LogHeaderAndMessage("DR DATBASE MAX SEQUENCE COMMAND : ", Constants.QueryConstants.MaxSequenceNo);

                string output = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.MaxSequenceNo, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("DR DATABASE MAX SEQUENCE OUTPUT :", output);

                return output;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while get DR Max Sequnce No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get DR Max Sequnce No", exc);
            }
            
        }

        internal bool ConnectServer()
        {
            return IsWindows ? SSHHelper.Ping(HostName) : SSHHelper.Connect(HostName, UserName, Password);
        }

        internal override bool Exit()
        {
            try
            {
                ExitPromt(DRSession);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal override bool MoveFiles(string sourcePath, string fileName, string targetPath)
        {
            var moveResult = MoveFiles(DRSession, sourcePath, fileName, targetPath);

            return moveResult == null || !moveResult.Contains("cannot move");
        }

        internal override void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt)
        {
            //
        }

        internal bool CancelRecoverMode()
        {
            //if (IsWindows)
            //{
            //    return WinDGSwitchDatabaseToPrimaryMode();
            //}


            var switchresult = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.CancelRecoverMode, Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CancelRecoverMode);

            LogHelper.LogHeaderAndMessage("CANCEL RECOVER MODE OUTPUT:", switchresult);

            return switchresult.Contains("database altered");

        }

        internal bool SwitchDatabaseToPrimaryMode()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGSwitchDatabaseToPrimaryMode();
                }


                LogHelper.LogHeaderAndMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE  : ", Constants.QueryConstants.SwitchToPrimaryMode);

                var switchresult = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.SwitchToPrimaryMode, Constants.QueryConstants.GreaterThan).ToLower();
                
                LogHelper.LogHeaderAndMessage("SWITCH DATABASE TO PRIMARY MODE OUTPUT:", switchresult);

                return switchresult.Contains("database altered");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while Swith database standby to primary mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE  STANDBY TO PRIMARY MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while Swith database standby to primary mode", exc);
            }
           
           
        }
     
        internal bool StartPrimaryDatabase()
        {

            try
            {
                if (IsWindows)
                {
                    return WinDGStartPrimaryDatabase();
                }

                LogHelper.LogHeaderAndMessage("START NEW PRIMARY DATABASE COMMAND : ", Constants.QueryConstants.StartPrimaryDatabase);

                var startDbResult = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.StartPrimaryDatabase,
                                                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("START NEW PRIMARY DATABASE OUTPUT:", startDbResult);

                return startDbResult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while start new Primary Database", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START NEW PRIMARY DATABASE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while  start new Primary Database", exc);
            }

          
        }

        internal bool SwitchPrimaryLogFile()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGSwitchPrimaryLogFile();
                }

                LogHelper.LogHeaderAndMessage("SWITCH PRIMARY LOGFILE COMMAND : ", Constants.QueryConstants.PrimarySwitchtoLogfile);

                var switchPrimaryResult = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.PrimarySwitchtoLogfile,
                                                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH PRIMARY LOGFILE OUTPUT:", switchPrimaryResult);

                return switchPrimaryResult.Contains("system altered");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOGFILEAUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOGFILE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while switch primary log file", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOGFILE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch primary log file", exc);
            }

         

        }

        internal string VerifyRecoveryMode()
        {
            return RunSshCommandsWithWait(DRSession, Constants.QueryConstants.DataGuardInRecovery, Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string VerifyArachiveLogGap()
        {
            return RunSshCommandsWithWait(DRSession, Constants.QueryConstants.VerifyArchiveGap, Constants.QueryConstants.GreaterThan).ToLower();

        }


        internal bool WinVerifyRecoveryMode()
        {
            try
            {
                string output = string.Empty;
                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinDataGuardInRecovery))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                             output += myDatabaseReader[0]+" ";
                        }
                    }
                }

                return output.ToLower().Contains("manage");
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR ARCHIVE LOG GAP  EXCEPTION : ", exc);

                return false;
            }
        }


        internal bool WinVerifyArachiveLogGap()
        {
            try
            {
                var db =CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinVerifyArchiveGap))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            string output = myDatabaseReader[0].ToString();
                            LogHelper.LogHeaderAndMessage("DR ARCHIVE LOG GAP  STATUS O/P ", output);
                            return output.ToLower().Contains("no rows selected");
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR ARCHIVE LOG GAP  EXCEPTION : ", exc);

                return false;
            }

            return false;
        }

        internal string ReplicateArachiveLogFastcopy(int groupId)
        {
            var result=RunSshCommandsWithWait(DRSession, "cd C:\\FastCopy1", Constants.QueryConstants.GreaterThan).ToLower();
            if (result.Contains("c:\\fastcopy"))
            {
                return
                    RunSshCommandsWithWait(DRSession, "FastCopy.exe " + groupId, Constants.QueryConstants.GreaterThan).
                        ToLower();
            }
            return null;

        }
        internal string FastcopyReplicationFile(string fastcopypath, int actionId)
        {

            Logger.InfoFormat(fastcopypath + "\\FastCopy.exe -R " + actionId.ToString());
            var result = RunSshCommandsWithWait(DRSession, fastcopypath + "\\FastCopy.exe -R " + actionId.ToString(), Constants.QueryConstants.GreaterThan).ToLower();

            return result;

        }
        internal string FastcopyReplicationFolder(string fastcopypath, int actionId)
        {
            Logger.InfoFormat(fastcopypath + "\\FastCopy.exe -RD " + actionId.ToString());
            var result = RunSshCommandsWithWait(DRSession, fastcopypath + "\\FastCopy.exe -RD " + actionId.ToString(), Constants.QueryConstants.GreaterThan).ToLower();

            return result;

        }

        internal string CheckDRDataGuard()
        {
            string result;

            var prdgResult = RunSshCommandsWithWait(DRSession, Constants.QueryConstants.DRDataGuardStatus,
                                                    Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DRDataGuardStatus);

            LogHelper.LogHeaderAndMessage("COMMAND Outpu: ", prdgResult);


            if(prdgResult.Contains("wait_for_log"))
            {
                result = "Wait for Log";
            }
            else if (prdgResult.Contains("wait_for_gap"))
            {
                result = "Wait for Gap";
            }
            else if (prdgResult.Contains("applying_log"))
            {
                result = "Applying log"; 
            }
            else if(prdgResult.Contains("closing"))
            {
                result = "Closing";
            }
            else if (prdgResult.Contains("arch"))
            {
                result = "Arch";
            }
            else if (prdgResult.Contains("connected"))
            {
                result = "Connected";
            }
            else
            {
                result = "N/A";
            }

            return  result;
        }

        internal string StartFastCopy(string hostname, string username, string password, string localDir, string remoteDir)
        {
            var sb = new StringBuilder();

            var commands = new List<string>
                                        {
                                            Constants.QueryConstants.ChangeFastCopyDirectory,

                                            string.Format("{0},{1},{2},{3},\"{4}\",\"{5}\"",
                                                          Constants.QueryConstants.StartFastCopyReplication,
                                            hostname,
                                            username,
                                            password,
                                            localDir,
                                            remoteDir)
                                        };

            foreach (var command in commands)
            {
                sb.Append(RunSshCommands(DRSession, command));
            }
            var connectResult = sb.ToString().ToLower();
            return connectResult;
        }


        internal string StartFastCopy(int groupId,int replicationId,bool iswithover)
        {

            try
            {
                if (IsWindows)
                {
                    //Windows FastCopy Log Replication

                    var repicationstatus = ReplicateArachiveLogFastcopy(groupId);

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", repicationstatus);

                    return repicationstatus;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    Constants.QueryConstants.ChangeFastCopyDirectory,
                    
                    iswithover?
                    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication,"-SO",groupId, replicationId):
                    string.Format("{0} {1} {2}", Constants.QueryConstants.StartFastCopyReplication,groupId, replicationId)
                };

                foreach (var command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command,1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());

                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {

                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCETION  :", exc.Message);

                return exc.Message;
            }   
           
        }



        internal string StartFastCopyReplicateArchiveFolder(string fastCopyPath,int workflowActionId,int replicationId, string sequence, string logfile)
        {

            try
            {
                if (IsWindows)
                {

                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}",fastCopyPath),

                    string.Format("{0} -WDB {1} {2} {3} {4}", Constants.QueryConstants.StartFastCopyReplication,workflowActionId,replicationId,sequence, logfile)
                };

                foreach (var command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());

                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occured while Replicate ARCHIVE files ", exc.Message);

                return string.Empty;
            }
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover, string logsequence, string logsequenceName,string path)
        {

            try
            {
                if (IsWindows)
                {
                    //Windows FastCopy Log Replication

                    var repicationstatus = ReplicateArachiveLogFastcopy(groupId);

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", repicationstatus);

                    return repicationstatus;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    "cd "+path,
                    iswithover?
                    string.Format("{0} {1} {2} {3} {4} {5}", Constants.QueryConstants.StartFastCopyReplication,"-SO",groupId, replicationId,logsequence,logsequenceName):
                    string.Format("{0} {1} {2} {3} {4} {5}", Constants.QueryConstants.StartFastCopyReplication,"-DB",groupId, replicationId,logsequence,logsequenceName)
                };

                foreach (var command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());

                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {

                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCETION  :", exc.Message);

                return exc.Message;
            }

        }


        internal string StartFastCopyReverseReplicateArchiveFolder(string fastCopyPath, int groupId, int replicationId)
        {

            if (IsWindows)
            {

            }

            //Linux FastCopy Log Replication

            var sb = new StringBuilder();

            var commands = new List<string>
                {
                    string.Format("cd {0}",fastCopyPath),

                    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication,"-SO",groupId, replicationId)
                };

            foreach (var command in commands)
            {
                sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1200000));
            }
            LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());

            return sb.ToString().ToLower();

        }



        internal string StartFastCopyReplicateRedoFiles(int workflowActionId,int replicationid,string fastCopyPath)
        {

            try
            {
                if (IsWindows)
                {

                    return string.Empty;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}",fastCopyPath),
                    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication,"-WF",workflowActionId,replicationid)
                };

                foreach (var command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(DRSession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE REDO FILES RESULT :", sb.ToString());

                return sb.ToString().ToLower();

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occured while Replicate REDO files ",exc.Message);

                return string.Empty;
            }
        }

        internal bool ApplyArchiveLogs()
        {
            if(IsWindows)
            {
               //
            }
            else
            {
                //if (string.IsNullOrEmpty(SudoUser))
                //{
                //    return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, Password), DatabaseName);
                //}
                return OracleDB.RecoverStandByDatabase(new SSHInfo(HostName, UserName, Password), SudoUser, DatabaseName);
            }

            return true;
        
        }

        internal string CheckDRDataGuardWindows()
        {
            string result;

            var prdgResult =  WinDGGetDRDataGuardStatus().ToLower();

            if (prdgResult.Contains("wait_for_log"))
            {
                result = "Wait for Log";
            }
            else if (prdgResult.Contains("wait_for_gap"))
            {
                result = "Wait for Gap";
            }
            else if (prdgResult.Contains("applying_log"))
            {
                result = "Applying log";
            }
            else if (prdgResult.Contains("closing"))
            {
                result = "Closing";
            }
            else if (prdgResult.Contains("arch"))
            {
                result = "Arch";
            }
            else if (prdgResult.Contains("connected"))
            {
                result = "Connected";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        private string WinDGGetDRDataGuardStatus()
        {
            try
            {
                string output = string.Empty;


                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName,DatabaseName,DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDRDataGuardStatus))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            return  myDatabaseReader[0].ToString();
                        }
                    }
                }

                return output;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR DATAGUARD STATUS : ", exc);
                
                return string.Empty;
            }
        }

        private bool IsWinDatabaseRunning()
        {
            try
            {
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB RUNNING EXCEPTION : ", exc);

                return false;
            }

        }

        private string WinDGGetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMaxSequenceNo);

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsMaxSequenceNo))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string squenceno = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("DR MAX SEQUNCE NO OUTPUT : ",squenceno);

                            return squenceno;
                        }
                    }
                }

                return string.Empty;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DR MAX SEQUNCE NO OUTPUT : ", exc);

                throw;
            }
        }

        private bool WinDGSwitchDatabaseToPrimaryMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsSwitchToPrimaryMode);
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsSwitchToPrimaryMode);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH STANDBY DATABASE TO PRIMARY EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGStartPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsStartPrimaryDatabase);
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsStartPrimaryDatabase);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START PRIMARY DATABASE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGSwitchPrimaryLogFile()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsPrimarySwitchtoLogfile);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsPrimarySwitchtoLogfile);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY LOG FILE EXCEPTION : ", exc);

                throw;
            }
        }


        internal string GetDRMaxLogSequnce()
        {
            try
            {
                string sequenceno = string.Empty;

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName,DatabaseName,DatabasePassword));
                
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinDRLatestLogSeqno))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            sequenceno = myDatabaseReader[0].ToString();
                        }
                    }
                }
                return sequenceno;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET DR LATEST LOG SEQUENCE EXCEPTION : ", exc);

                throw;
            }

        }



        public string ApplicationReplicationFastCopy(int applicationId, int fastcopyId)
        {
            string command = string.Format("F:\\DSExePath\\DataSync.exe {0} {1}", applicationId, fastcopyId);
            
            Logger.Info("Application Replication FastCopy started (client): "+command);

            RunSshCommandsWithNoWait(DRSession, command);

            return string.Empty;
        }


        public string ApplicationReplicationFastCopy(string option, int infraObjectId, int fastcopyJobId,string datasyncPath)
        {
            try
            {
                if (IsWindows)
                {
                    Logger.Info("Application Replication FastCopy started (client): ");
                    Logger.Info("Application Replication Jobs applicationId :" + infraObjectId);
                    Logger.Info("Application Replication Jobs fastcopyJobId :" + fastcopyJobId);


                    string letter = Path.GetPathRoot(datasyncPath).Remove(2);

                    var commands1 = new List<string>
                {
                    "cd "+datasyncPath,
                    letter,             
                    string.Format("DataSync.exe -RA {0} {1}",infraObjectId,fastcopyJobId)          
                };


                    foreach (var command in commands1)
                    {
                        RunSshCommandsWithNoWait(DRSession, command);

                    }

                    Logger.Info("Application Replication FastCopy Ended (client): ");
                    return string.Empty;
                }


                Logger.Info("Application Replication FastCopy started (client): ");
                Logger.Info("Application Replication Jobs applicationId :" + infraObjectId);
                Logger.Info("Application Replication Jobs fastcopyJobId :" + fastcopyJobId);



                String shellPrompt = "\\$|>|>";

                var commands = new List<string>
                {
                    "cd "+datasyncPath,
                    string.Format("java -jar DataSync.jar -RA {0} {1}",infraObjectId,fastcopyJobId)          
                };


                foreach (var command in commands)
                {
                    RunSshCommandsWithNoWait(DRSession, command);

                }

                Logger.Info("Application Replication FastCopy Ended (client): ");
                return string.Empty;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occured while Replicate REDO files ", exc.Message);

                return string.Empty;
            }
        }

    }
}