﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class XIVClustorGroupInfoDataAccess : BaseDataAccess
    {
        public static bool AddClustorGroupInfoMoniLogDetails(ClustorGroupInfoLogs ClustorGroupInfoLogs)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ClustGroup_Logs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobectID", DbType.Int32, ClustorGroupInfoLogs.InfraobectID);
                    Database.AddInParameter(cmd, Dbstring + "iGroupName", DbType.AnsiString, ClustorGroupInfoLogs.GroupName);
                    Database.AddInParameter(cmd, Dbstring + "iGroupState", DbType.AnsiString, ClustorGroupInfoLogs.GroupState);
                    Database.AddInParameter(cmd, Dbstring + "iNode", DbType.AnsiString, ClustorGroupInfoLogs.Node);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, ClustorGroupInfoLogs.Status); 


                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Clustor Group Info Detailed Monitor Logs InfraObject Id - " + ClustorGroupInfoLogs.InfraobectID, exc);

            }

            return false;
        }
    }
}
