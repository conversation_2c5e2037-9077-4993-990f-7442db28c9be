﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GroupLuns", Namespace = "http://www.Bcms.com/types")]
    public class GroupLuns : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        [DataMember]
        public string Luns
        {
            get;
            set;
        }

        [DataMember]
        public int Logs
        {
            get;
            set;
        }

        [DataMember]
        public string AGroup
        {
            get;
            set;
        }

        [DataMember]
        public string AMount
        {
            get;
            set;
        }

        [DataMember]
        public string BGroup
        {
            get;
            set;
        }

        [DataMember]
        public string BMount
        {
            get;
            set;
        }

        [DataMember]
        public string CGroup
        {
            get;
            set;
        }

        [DataMember]
        public string CMount
        {
            get;
            set;
        }

        [DataMember]
        public string DGroup
        {
            get;
            set;
        }

        [DataMember]
        public string DMount
        {
            get;
            set;
        }

        [DataMember]
        public string EGroup
        {
            get;
            set;
        }

        [DataMember]
        public string EMount
        {
            get;
            set;
        }

        [DataMember]
        public string FGroup
        {
            get;
            set;
        }

        [DataMember]
        public string FMount
        {
            get;
            set;
        }

        #endregion Properties
    }
}