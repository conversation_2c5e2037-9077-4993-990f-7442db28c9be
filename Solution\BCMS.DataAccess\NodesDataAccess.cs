﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Utility;

namespace Bcms.DataAccess
{
    public class NodesDataAccess : BaseDataAccess
    {
        public static Nodes GetNodeByDatabaseId(int databaseId)
        {
            var node = new Nodes();

            try
            {
               // var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Node_GetByDatabaseOracleRacId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iDatabaseId", DbType.Int32, databaseId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            node.Id = Convert.ToInt32(myDataLagReader[0]);
                        }


                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Nodes information By Database Id - " + databaseId, exc);
            }

            return node;
        }

        public static Nodes GetNodeById(int Id)
        {
            var node = new Nodes();

            try
            {
                //var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Nodes_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, Id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            node.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            ////node.BaseDatabaseId = reader.IsDBNull(FLD_BASEDATABASEID) ? 0 : reader.GetInt32(FLD_BASEDATABASEID);
                            node.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            node.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);

                            //node.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
                            //node.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
                            node.OracleSID = Convert.IsDBNull(reader["OracleSID"]) ? string.Empty : Convert.ToString(reader["OracleSID"]);

                            node.InstanceName = Convert.IsDBNull(reader["InstanceNo"]) ? string.Empty : Convert.ToString(reader["InstanceNo"]);


                            node.Status = Convert.IsDBNull(reader["Status"]) ? NodeStatus.Undefined : (NodeStatus)Enum.Parse(typeof(NodeStatus), Convert.ToString(reader["Status"]), true);
                            node.ArchieveLogPath = Convert.IsDBNull(reader["ArchieveLogPath"]) ? string.Empty : Convert.ToString(reader["ArchieveLogPath"]);

                            if (!Convert.IsDBNull(reader["IsPrimary"]))
                                //node.IsPrimary = Convert.ToBoolean(reader["IsPrimary"]);
                                node.IsPrimary = Convert.IsDBNull(reader["IsPrimary"]) ? 0 : Convert.ToInt32(reader["IsPrimary"]);

                            node.TargetArchieveLogPath = Convert.IsDBNull(reader["TargetArchieveLogPath"]) ? string.Empty : Convert.ToString(reader["TargetArchieveLogPath"]);

                            if (!Convert.IsDBNull(reader["IsConfigPath"]))
                                node.IsConfigPath = Convert.IsDBNull(reader["IsConfigPath"]) ? 0 : Convert.ToInt32(reader["IsConfigPath"]);



                            if (!Convert.IsDBNull(reader["IsActive"]))
                                node.IsActive = Convert.ToBoolean(reader["IsActive"]);

                            node.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            node.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
                            node.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            node.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);

                            // 02/02/2015

                            node.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"].ToString());
                            node.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : StringHelper.Md5Decrypt(reader["Password"].ToString());
                            node.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
                            node.Home = Convert.IsDBNull(reader["Home"]) ? string.Empty : Convert.ToString(reader["Home"]);
                            node.Redo = Convert.IsDBNull(reader["Redo"]) ? string.Empty : Convert.ToString(reader["Redo"]);
                            node.ASMGrid = Convert.IsDBNull(reader["ASMGrid"]) ? string.Empty : Convert.ToString(reader["ASMGrid"]);
                            node.IsAsm = Convert.IsDBNull(reader["IsASM"]) ? 0 : Convert.ToInt32(reader["IsASM"].ToString());
                            node.PRAsmInstanceName = Convert.IsDBNull(reader["ASMInstanceName"]) ? string.Empty : Convert.ToString(reader["ASMInstanceName"].ToString());
                            //node.DatabaseConnectivity = Convert.IsDBNull(reader["DatabaseConnectivity"]) ? string.Empty : Convert.ToString(reader["DatabaseConnectivity"].ToString());
                            node.PRASMPath = Convert.IsDBNull(reader["ASMGrid"]) ? string.Empty : Convert.ToString(reader["ASMGrid"].ToString());
                            node.PRASMUserName = Convert.IsDBNull(reader["ASMUserName"]) ? string.Empty : Convert.ToString(reader["ASMUserName"].ToString());
                            node.PRASMPassword = Convert.IsDBNull(reader["ASMPassword"]) ? string.Empty : Convert.ToString(reader["ASMPassword"].ToString());

                        }


                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Nodes information By Id - " + Id, exc);
            }

            return node;
        }
    }
}
