﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "hypervclusternode", Namespace = "http://www.BCMS.com/types")]
    public class hypervclusternode : BaseEntity
    {

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRClusterID
        {
            get;
            set;
        }

        [DataMember]
        public string DRClusterID
        {
            get;
            set;
        }

        [DataMember]
        public string PRClusterName
        {
            get;
            set;
        }

        [DataMember]
        public string DRClusterName
        {
            get;
            set;
        }

        [DataMember]
        public string PRClusterState
        {
            get;
            set;
        }

        [DataMember]
        public string DRClusterState
        {
            get;
            set;
        }

    }
}
