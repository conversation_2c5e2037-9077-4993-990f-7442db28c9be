﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ParallelGroupWorkflow", Namespace = "http://www.BCMS.com/types")]
    public class ParallelGroupWorkflow : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string InfraObjectName
        {
            get;
            set;
        }

        [DataMember]
        public int WorkflowId
        {
            get;
            set;
        }

        [DataMember]
        public string WorkflowName
        {
            get;
            set;
        }

        [DataMember]
        public int CurrentActionId
        {
            get;
            set;
        }
        
        [DataMember]
        public int ConditionalOperation
        {
            get;
            set;
        }

        [DataMember]
        public string CurrentActionName
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }
         [DataMember]
        public string ProgressStatus
        {
            get;
            set;
        }
        [DataMember]
        public string Message
        {
            get;
            set;
        }

        [DataMember]
        public int ParallelDROperationId
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public ParallelGroupWorkflow()
            : base()
        {
        }

        #endregion
    }
}