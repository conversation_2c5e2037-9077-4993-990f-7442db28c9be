﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bcms.Common;
using BCMS.Core.Utility;
using Bcms.ExceptionHandler;
using System.IO;
using System.ServiceProcess;
using System.Diagnostics;
using log4net.Repository.Hierarchy;
using log4net;
using Jscape.Ssh;

namespace Bcms.Core.FastCopy
{
    public class Sql2000Migration
    {
        public static SshSession Session = null;
        public const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
        private static readonly ILog Logger = LogManager.GetLogger(typeof(Sql2000Migration));

        public static bool MigrateLoginPR(string user, string password, string dbuser, string dbpassword, string server, string Proc, string fileName, string AuthMode)
        {
            Logger.InfoFormat("{0} : Started Running {1}", server, fileName);
            try
            {
                SshParameters parameters = new SshParameters(server, user, password);
                Session = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig()) { LicenseKey = LicenseKey };
                Session.SetShellPrompt("\\$|#|>", true);
                Session.Ssh.Timeout = 200000000;
                Session.Connect();
                string strOutput = Session.Send("cd\\");
                string querry = "";
                if (AuthMode != "Windows")
                    querry = @"osql -U" + dbuser + " -P" + dbpassword + " -dmaster -Q" + Proc + " -w400 -o" + fileName + "";
                else
                    querry = @"osql -E" + " -dmaster -Q" + Proc + " -w400 -o" + fileName + "";
                strOutput = Session.Send(querry);
                Session.Disconnect();
                return true;
            }
            catch (SshException ex)
            {
                Session.Disconnect();
                Logger.InfoFormat("{0} : Error in Running  {1} : {2}", server, fileName, ex.Message);
                return false;
            }
        }

        public static bool MigrateLoginDR(string user, string password, string dbuser, string dbpassword, string server, string fileName, string AuthMode)
        {
            Logger.InfoFormat("{0} : Started Running {1}", server, fileName);
            try
            {
                SshParameters parameters = new SshParameters(server, user, password);
                Session = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig()) { LicenseKey = LicenseKey };
                Session.SetShellPrompt("\\$|#|>", true);
                //const string shellPrompt = @"\\$|>|:|\?";
                //Session.SetShellPrompt(shellPrompt,true);
                Session.Ssh.Timeout = 200000000;
                Session.Connect();
                string strOutput = Session.Send("cd\\");
                string outFile = fileName.Contains("MigrateLogin.sql") ? fileName.Replace("MigrateLogin.sql", "MigrateLoginReport.rpt") : fileName.Replace("MigrateServerrole.sql", "MigrateServerroleReport.rpt");
                string querry = "";
                if (AuthMode != "Windows")
                    querry = @"osql -U" + dbuser + " -P" + dbpassword + " -i" + fileName + " -o" + outFile + "";
                else
                    querry = @"osql -E" + " -i" + fileName + " -o" + outFile + "";
                //"osql -Usa -PGalaxy*11 -i C:\\CMS\\MigrateLogin.sql -o C:\\CMS\\MyOutput.rpt"
                strOutput = Session.Send(querry);
                Session.Disconnect();
                return true;
            }
            catch (SshException ex)
            {
                Session.Disconnect();
                Logger.InfoFormat("{0} : Error in Running  {1} : {2}", server, fileName, ex.Message);
                return false;
            }
        }

        public static bool CheckServerStatus(string server, string user, string password)
        {
            try
            {
                SshParameters parameters = new SshParameters(server, user, password);
                Session = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig()) { LicenseKey = LicenseKey };
                Session.SetShellPrompt("\\$|#|>", true);
                Session.Ssh.Timeout = 200000000;
                Session.Connect();
                Logger.InfoFormat("{0} : * Check Server Status Online : {1}", server, "True");
                Session.Disconnect();
                return true;
            }
            catch (SshException ex)
            {
                Logger.InfoFormat("{0} : * Check Server Status Online : {1}", server, "False");
                return false;
            }
        }


    }
}
