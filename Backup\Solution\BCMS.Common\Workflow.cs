﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Workflow", Namespace = "www.BCMS.com")]
    public class Workflow : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public string Xml
        {
            get;
            set;
        }

        #endregion

        #region Constructor
        public Workflow()
            : base()
        {

        }

        #endregion
    }
}