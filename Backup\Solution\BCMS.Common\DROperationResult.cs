﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DROperationResult", Namespace = "http://www.BCMS.com/types")]
    public class DROperationResult : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ActionName
        {
            get;
            set;
        }

        [DataMember]
        public int ActionId
        {
            get;
            set;
        }


        [DataMember]
        public DateTime StartTime
        {
            get;
            set;
        }

        [DataMember]
        public DateTime EndTime
        {
            get;
            set;
        }

        [DataMember]
        public string ElapsedTime
        {
            get;
            set;
        }

        [DataMember]
        public string Message
        {
            get;
            set;
        }



        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public int DROperationId
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public DROperationResult()
            : base()
        {
        }

        #endregion
    }
}