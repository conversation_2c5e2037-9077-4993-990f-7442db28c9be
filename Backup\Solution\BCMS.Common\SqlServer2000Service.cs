﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlServer2000Service", Namespace = "http://www.Bcms.com/types")]


    public class SqlServer2000Service : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId 
        {
            get;
            set;
        }

        [DataMember]
        public int Server
        {
            get;
            set;
        }

        [DataMember]
        public string ServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string Start_Mode
        {
            get;
            set;
        }

        #endregion
        
        #region Constructor
        public SqlServer2000Service(): base()
        {

        }
        #endregion

    }
}
