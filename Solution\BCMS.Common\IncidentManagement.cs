﻿using Bcms.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ImpactAnalysis", Namespace = "http://www.ContinuityVault.com/types")]
    public class IncidentManagement : BaseEntity
    {
        #region Properties

        [DataMember]
        public int IncidentID
        {
            get;
            set;
        }

        [DataMember]
        public string IncidentName
        {
            get;
            set;
        }

        [DataMember]
        public string IncidentCode
        {
            get;
            set;
        }

        [DataMember]
        public DateTime IncidentTime
        {
            get;
            set;
        }

        [DataMember]
        public DateTime IncidentRecoveryTime
        {
            get;
            set;
        }

        [DataMember]
        public int Status
        {
            get;
            set;
        }

        [DataMember]
        public int InfraID
        {
            get;
            set;
        }

        [DataMember]
        public int InfraComponentID
        {
            get;
            set;
        }

        [DataMember]
        public string InfraComponentType
        {
            get;
            set;
        }

        [DataMember]
        public int SourceID
        {
            get;
            set;
        }


        [DataMember]
        public int SourceTypeID
        {
            get;
            set;
        }

        [DataMember]
        public int Flag1
        {
            get;
            set;
        }

        [DataMember]
        public int Flag2
        {
            get;
            set;
        }
        [DataMember]
        public int Flag3
        {
            get;
            set;
        }

        [DataMember]
        public int ParentBSID
        {
            get;
            set;
        }

        [DataMember]
        public int ParentBSImpactID
        {
            get;
            set;
        }
        [DataMember]
        public int ChildBSID
        {
            get;
            set;
        }

        [DataMember]
        public int ChildBSImpactID
        {
            get;
            set;
        }
        [DataMember]
        public int ParentBFID
        {
            get;
            set;
        }
        [DataMember]
        public int ParentBFImpactID
        {
            get;
            set;
        }
        [DataMember]
        public int ChildBFID
        {
            get;
            set;
        }
        [DataMember]
        public int ChildBFImpactID
        {
            get;
            set;
        }
        [DataMember]
        public int ParentBFCost
        {
            get;
            set;
        }
        [DataMember]
        public int ChildBFCost
        {
            get;
            set;
        }
        [DataMember]
        public string IncidentComment
        {
            get;
            set;
        }
        #endregion Properties
    }

}
