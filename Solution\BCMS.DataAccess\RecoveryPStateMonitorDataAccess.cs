﻿using System;
using System.Collections.Generic;
using System.Data;
using Bcms.Common;
using Bcms.Common.Shared;
using System.Data.Common;
using Bcms.ExceptionHandler;
using Bcms.Helper;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class RecoveryPStateMonitorDataAccess : BaseDataAccess 
    {

        public static bool AddRecoverPointState(RecoverPStateMonitor recoverPStateMonitor)
        {
            try
            {
                const string sp = "RecoverPointState_Create";
                // var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iGGroupName", DbType.AnsiString, recoverPStateMonitor.GGroupName);
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, recoverPStateMonitor.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring + "iTransferSource", DbType.AnsiString, recoverPStateMonitor.TransferSource);
                    Database.AddInParameter(dbCommand, Dbstring + "iCopy", DbType.AnsiString, recoverPStateMonitor.Copy);
                    Database.AddInParameter(dbCommand, Dbstring + "iJournal", DbType.AnsiString, recoverPStateMonitor.Journal);
                    Database.AddInParameter(dbCommand, Dbstring + "iStorageAccess", DbType.AnsiString, recoverPStateMonitor.StorageAccess);
                    Database.AddInParameter(dbCommand, Dbstring + "iLink", DbType.AnsiString, recoverPStateMonitor.Link);
                    Database.AddInParameter(dbCommand, Dbstring + "iDataTransferStatus", DbType.AnsiString, recoverPStateMonitor.DataTransferStatus);
                    Database.AddInParameter(dbCommand, Dbstring + "iSiteName", DbType.AnsiString, recoverPStateMonitor.SiteName);
                    Database.AddInParameter(dbCommand, Dbstring + "iApplianceVersion", DbType.AnsiString, recoverPStateMonitor.ApplicationVersion);
                   
                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert RecoverPoint State Monitor information", exc);
            }
            return false;
        }


        

        
    }
}
