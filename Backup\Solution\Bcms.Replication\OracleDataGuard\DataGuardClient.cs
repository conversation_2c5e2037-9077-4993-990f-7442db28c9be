﻿using System;
using System.Collections.Generic;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Jscape.Ssh;
using log4net;
using Bcms.Replication.Shared;

namespace Bcms.Replication.OracleDataGuard
{
    public enum State
    {
        Connected,
        Disconnected,
    }

    public class DataGuardClient
    {
        #region Variables

        private static readonly ILog Logger = LogManager.GetLogger(typeof(DataGuardClient));

        #endregion

        #region Properties

        public PrimaryHost PrimaryHost { get; set; }

        public DRHost DRHost { get; set; }

        public State PrimaryState { get; set;}

        public State DRState { get; set; }

        public string PRSudoUser { get; set; }

        public string DRSudoUser { get; set; }


        public string SudoUser { get; set; }

        public string PRDatabaseSID { get; set; }

        public string DRDatabaseSID { get; set; }

        public string PRDatabaseUserName { get; set; }

        public string PRDatabasePassword { get; set; }

        public string DRDatabaseUserName { get; set; }

        public string DRDatabasePassword { get; set; }

        public bool IsWindows { get; set; }

        #endregion

        #region Constructor

            public DataGuardClient()
            {
                 
            }

        public DataGuardClient(PrimaryHost primaryHost, DRHost drHost, string sudouser, string prdatabasesid,string drdatabasesid,bool iswindows)
        {
            if(primaryHost==null)
            {
                throw new ArgumentException("Primary Host cannot be null");
            }
            if(drHost==null)
            {
                throw new ArgumentException("DR Host cannot be null");  
            }
            //if(string.IsNullOrEmpty(sudouser))
            //{
            //    throw new ArgumentException("Sudo User cannot be null or empty");   
            //}
            //if (string.IsNullOrEmpty(prdatabasesid))
            //{
            //    throw new ArgumentException("PR Database Name cannot be null or empty");
            //}

            //if (string.IsNullOrEmpty(drdatabasesid))
            //{
            //    throw new ArgumentException("DR Database Name cannot be null or empty");
            //}

            PrimaryHost = primaryHost;
            DRHost = drHost;
            SudoUser = sudouser;
            PRDatabaseSID = prdatabasesid;
            DRDatabaseSID = drdatabasesid;
            IsWindows = iswindows;
        }

        public DataGuardClient(PrimaryHost primaryHost, string sudouser, string prdatabasesid, bool iswindows)
        {
            if (primaryHost == null)
            {
                throw new ArgumentException("Primary Host cannot be null");
            }
            //if (string.IsNullOrEmpty(sudouser))
            //{
            //    throw new ArgumentException("Sudo User cannot be null or empty");
            //}
            //if (string.IsNullOrEmpty(prdatabasesid))
            //{
            //    throw new ArgumentException("PR Database Name cannot be null or empty");
            //}

            PrimaryHost = primaryHost;
            SudoUser = sudouser;
            PRDatabaseSID = prdatabasesid;
            IsWindows = iswindows;
        }

        public DataGuardClient(DRHost drHost, string sudouser, string drdatabasesid, bool iswindows)
        {
           
            if (drHost == null)
            {
                throw new ArgumentException("DR Host cannot be null");
            }
            //if (string.IsNullOrEmpty(sudouser))
            //{
            //    throw new ArgumentException("Sudo User cannot be null or empty");
            //}
            //if (string.IsNullOrEmpty(drdatabasesid))
            //{
            //    throw new ArgumentException("DR Database Name cannot be null or empty");
            //}
            DRHost = drHost;
            SudoUser = sudouser;
            DRDatabaseSID = drdatabasesid;
            IsWindows = iswindows;
        }

        #endregion

        public void Connect()
        {
            PrimaryHost.SudoUser = SudoUser;

            PrimaryHost.DatabaseName = PRDatabaseSID;

            PrimaryHost.IsWindows = IsWindows;

            DRHost.SudoUser = SudoUser;

            DRHost.DatabaseName = DRDatabaseSID;

            DRHost.IsWindows = IsWindows;

            PrimaryHost.Connect(true);

            DRHost.Connect(true);
        }

        public bool ConnectPR()
        {
            try
            {
                PrimaryHost.IsWindows = IsWindows;
                
                PrimaryHost.DatabaseName = PRDatabaseSID;

                if (!IsWindows)
                {
                    PrimaryHost.SudoUser = SudoUser;

                    if (PrimaryHost.Connect(true))
                    {
                        LogHelper.LogHeaderAndMessage("PR SERVER STATUS :",string.Format(" PR Server {0} is connected", PrimaryHost.HostName));
                        return true;
                    }
                    LogHelper.LogHeaderAndMessage("PRIMARY SERVER STATUS :",string.Format("Primary Server {0} is not reachable",
                                                                PrimaryHost.HostName));
                    return false;
                }
                return true;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("PR CONNECTION ERROR : " + exc.Message);
                PrimaryHost.Disconnect();
                throw;
            }
        }

        public bool ConnectPRWithoutDatanbase()
        {
            try
            {
                PrimaryHost.IsWindows = IsWindows;

                PrimaryHost.DatabaseName = PRDatabaseSID;

                if (!IsWindows)
                {
                    PrimaryHost.SudoUser = SudoUser;

                    if (PrimaryHost.Connect(false))
                    {
                        LogHelper.LogHeaderAndMessage("PR SERVER STATUS :", string.Format(" PR Server {0} is connected", PrimaryHost.HostName));
                        return true;
                    }
                    LogHelper.LogHeaderAndMessage("PRIMARY SERVER STATUS :", string.Format("Primary Server {0} is not reachable",
                                                                PrimaryHost.HostName));
                    return false;
                }
                if (PrimaryHost.Connect(false))
                {
                    LogHelper.LogHeaderAndMessage("PR SERVER STATUS :", string.Format(" PR Server {0} is connected", PrimaryHost.HostName));
                    return true;
                }
                LogHelper.LogHeaderAndMessage("PRIMARY SERVER STATUS :", string.Format("Primary Server {0} is not reachable",
                                                                                       PrimaryHost.HostName));
                return false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("PR CONNECTION ERROR : " + exc.Message);
                PrimaryHost.Disconnect();
                throw;
            }
        }


        public bool ConnectDR()
        {
            try
            {
                DRHost.IsWindows = IsWindows;
                DRHost.DatabaseName = DRDatabaseSID;

                if (!IsWindows)
                {
                    DRHost.SudoUser = SudoUser;

                    DRHost.DatabaseName = DRDatabaseSID;

                    if (DRHost.Connect(true))
                    {
                        LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                      string.Format(" DR Server {0} is connected", DRHost.HostName));

                        return true;
                    }
                    LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                  string.Format("DR server {0} is not reachable", DRHost.HostName));
                    return false;
                }
                return true;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("DR CONNECTION ERROR : " + exc.Message);
                DRHost.Disconnect();
                throw;
            }
        }


        public bool ConnectDRWithoutDatanbase()
        {
            try
            {
                DRHost.IsWindows = IsWindows;
                DRHost.DatabaseName = DRDatabaseSID;

                if (!IsWindows)
                {
                    DRHost.SudoUser = SudoUser;

                    DRHost.DatabaseName = DRDatabaseSID;

                    if (DRHost.Connect(false))
                    {
                        LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                      string.Format(" DR Server {0} is connected", DRHost.HostName));

                        return true;
                    }
                    LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                  string.Format("DR server {0} is not reachable", DRHost.HostName));
                    return false;
                }
                else
                {
                    DRHost.SudoUser = SudoUser;

                    DRHost.DatabaseName = DRDatabaseSID;

                    if (DRHost.Connect(false))
                    {
                        LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                      string.Format(" DR Server {0} is connected", DRHost.HostName));

                        return true;
                    }
                    LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                  string.Format("DR server {0} is not reachable", DRHost.HostName));
                    return false;
                }
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("DR CONNECTION ERROR : " + exc.Message);
                DRHost.Disconnect();
                throw;
            }
        }
        public void VerifyDatabaseMode()
        {
            try
            {
                LogHelper.Header("VERIFY DATABASE MODE");

                if (ConnectPR() && ConnectDR())
                {
                     //CheckPointList();
                    
                    if(PrimaryHost.VerifyDatabaseMode())
                    {
                        LogHelper.LogHeaderAndMessage("PRIMARY DATABSE MODE STATUS :", string.Format("Primary Database {0} in Read Write mode", PrimaryHost.DatabaseName));
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAPrimaryReadWriteModeFailed);
                    }

                    if (DRHost.VerifyDatabaseMode())
                    {
                        LogHelper.LogHeaderAndMessage("DR DATABSE MODE STATUS :", string.Format("DR Database {0} mounted and standby mode", DRHost.DatabaseName));
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORADRMountStandbyFailed);
                    }
                }
            }
            catch(SshException exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SSHUnhandled Exception occured while verify Database Mode", exc);
            }

            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,"Unhandled Exception occured while verify Database Mode",exc);
            }
            finally
            {
                Disconnect();
            }

        }

        private void CheckPointList()
        {
             
           LogHelper.Header("CHECK POINT");

            PrimaryHost.GetDataFileCount();

            PrimaryHost.CheckDataFileError();

            PrimaryHost.VerifyRecoveryMode();

            PrimaryHost.CheckBackupMode();

            PrimaryHost.CheckDataFileStatus();

            PrimaryHost.CheckDataFileMode();

            PrimaryHost.IsCorruptedDataFile();

            PrimaryHost.CheckPointChange();

            PrimaryHost.CheckCurrentSCN();

            PrimaryHost.CheckNologgingOperation();

            PrimaryHost.CheckPrDataGuardStatus();

            PrimaryHost.CheckForceLogging();

            LogHelper.Header("CHECK POINT END");

        }

        public void VerifyUserStatus()
        {
            try
            {
                LogHelper.Header("VERIFY USER STATUS");

                if (ConnectPR())
                {
                    if (PrimaryHost.VerifyUserStatus())
                    {
                        Logger.InfoFormat("VERIFY USER STATUS : Primary Database ({0}) user status verified successfully", PrimaryHost.DatabaseName);
                    }
                }

            }
            catch(BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,"Exception occured while verify user status",exc);
            }
            finally
            {
               PrimaryHost.Disconnect();
            }

        }

        public void VerifyPrimaryJobs()
        {
            try
            {
                LogHelper.Header("VERIFY PRIMARY JOBS");

                if (ConnectPR())
                {
                    if (PrimaryHost.VerifyPrimaryProcess())
                    {
                        Logger.InfoFormat("VERIFY PRIMARY JOBS : Primary process verified successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while verify primary process", exc);
            }
            finally
            {
                PrimaryHost.Disconnect();
            }
        }

        public void VerifyMaxSequenceNo()
        {
            try
            {
                LogHelper.Header("VERIFY MAXSEQUENCE NO");

                if (ConnectPR() && ConnectDR())
                {
                    var prmaxNo = PrimaryHost.GetMaxSequenceNumber().Trim();

                    var drmaxNo = DRHost.GetMaxSequenceNumber().Trim();

                    if (prmaxNo.Equals(drmaxNo))
                    {
                        Logger.InfoFormat("VERIFY MAXSEQUENCE NO : Max Sequence number is verified successfully "+Environment.NewLine +
                            "Primary Sequence :" +prmaxNo+Environment.NewLine +
                            "DR Sequence :" + drmaxNo + Environment.NewLine);
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.ORALogsPending);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,"Exception occured while verify max sequence no",exc);
                
            }
            finally
            {
                Disconnect();
            }
            
        }

        public void SwitchToStandbyMode()
        {
            try
            {
                LogHelper.Header("SWITCH TO STANDBY MODE");

                if (ConnectPR())
                {
                    if (PrimaryHost.SwitchDatabaseToStandbyMode())
                    {
                        Logger.InfoFormat("SWITCH TO STANDBY MODE : Database({0}) Switch Database Primary to Standby mode successfully",PrimaryHost.DatabaseName);
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORASwitchDBToStandbyFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,"Exception occured while switch Database to Standby mode",exc);
            }
            finally
            {
                PrimaryHost.Disconnect();
            }
        }

        public void SwitchToPrimaryMode()
        {
            try
            {
                LogHelper.Header("SWITCH TO PRIMARY MODE ");

                if (ConnectDR())
                {
                    if (DRHost.SwitchDatabaseToPrimaryMode())
                    {
                        Logger.InfoFormat("SWITCH TO PRIMARY MODE : Database({0}) Switch Database Stanby to Primary mode successfully", DRHost.DatabaseName);
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORASwitchDBToPrimaryModeFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch Database to primary mode", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }

        public void StartPrimary()
        {
            try
            {
                LogHelper.Header("START PRIMARY DATABASE");

                if (ConnectDR())
                {
                    if (DRHost.StartPrimaryDatabase())
                    {
                        Logger.InfoFormat("START PRIMARY DATABASE : Start Primary Database ({0}) successfully",DRHost.DatabaseName);
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAStartPrimaryDBFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while start primary database", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }

        public void ShutdownPrimary()
        {
            try
            {
                LogHelper.Header("SHUTDOWN PRIMARY");

                if (ConnectPR())
                {
                    if (PrimaryHost.ShutdownPrimaryDatabase())
                    {
                        Logger.InfoFormat("SHUTDOWN PRIMARY : Shutdown Primary Database({0}) successfully",PrimaryHost.DatabaseName);
                    }
                    else
                    {
                         throw new BcmsException(BcmsExceptionType.ORAShutdownPrimaryDBFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while shutdown primary Database", exc);
            }
            finally
            {
              PrimaryHost.Disconnect();
            }
            
        }

        public void MountStandby()
        {
            try
            {
                LogHelper.Header("MOUNT STANDBY");

                if (ConnectPR())
                {
                    if (PrimaryHost.MountStandby())
                    {
                        Logger.InfoFormat("MOUNT STANDBY : Mount Standby Database ({0}) successfully",PrimaryHost.DatabaseName);
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAMountStandbyFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while mounting standby Database", exc);
            }
            finally
            {
                PrimaryHost.Disconnect();
            }
            
            
        }

        public void SwitchLogFile()
        {
            try
            {
                LogHelper.Header("SWITCH LOGFILE");

                if (ConnectDR())
                {
                    bool isSwitchLog = DRHost.SwitchPrimaryLogFile();

                    if (isSwitchLog)
                    {
                        Logger.InfoFormat("SWITCH LOGFILE : Switch Primary Log file successfully");
                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORASwitchPrimaryLogFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while switch primary log files", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }

        }

        public void StartStandbyRecovery()
        {
            try
            {
                LogHelper.Header("START STANDBY RECOVERY");

                if (ConnectPR())
                {
                    if (PrimaryHost.StartStandbyRecovery())
                    {
                        Logger.InfoFormat("START STANDBY RECOVERY : Start Standby Recovery successfully");

                    }
                    else
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAStartStandbyRecoveryFailed);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while standby recovery ", exc);
            }
            finally
            {
             PrimaryHost.Disconnect();  
            }
        }
        
        public void Disconnect()
        {
            try
            {
                PrimaryHost.Disconnect();
                
                DRHost.Disconnect();
                
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occured while disconnect host servers",exc);
            }
        }

        public List<string> CheckOracleDataGuardStatus()
        {
            var dataguardList=new List<string>();

            try
            {
                if (IsWindows)
                {
                    PrimaryHost.DatabaseName = PRDatabaseSID;
                    PrimaryHost.DatabaseUserName = PRDatabaseUserName;
                    PrimaryHost.DatabasePassword = PRDatabasePassword;

                    DRHost.DatabaseName = DRDatabaseSID;
                    DRHost.DatabaseUserName = DRDatabaseUserName;
                    DRHost.DatabasePassword = DRDatabasePassword;

                    return CheckOracleDataGuardStatusWin();
                }

                Connect();

                dataguardList.Add(PrimaryHost.CheckPRDataGuard());

                dataguardList.Add(DRHost.CheckDRDataGuard());

                Disconnect();

            }
            catch (Exception exc)
            {
                
                Disconnect();

                Logger.ErrorFormat("CHECK ORACLE DATAGUARD STATUS ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while checking Dataguard status", exc);
            }

            return dataguardList;
        }

        public bool CheckDataGuardRecoveryDR()
        {
            try
            {
                if (IsWindows)
                {
                    DRHost.DatabaseName = DRDatabaseSID;
                    DRHost.DatabasePassword = DRDatabasePassword;

                    return DRHost.WinVerifyRecoveryMode();
                }
                ConnectDR();
                Logger.Info("CheckDataGuardRecoveryDR DRHOST:" + DRHost.HostName + " connected sucessfully");
                var checkRecoveryDR = DRHost.VerifyRecoveryMode().ToLower();
                Logger.Info("CheckDataGuardRecoveryDR DRHOST:" + DRHost.HostName + " output:" + checkRecoveryDR);
                return checkRecoveryDR.Contains("manage");
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("CHECK DR DATAGUARD RECOVERY MODE ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while checking DR Dataguard recovery ", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }

        public bool CheckArachiveLogGapDR()
        {
            try
            {
                if(IsWindows)
                {
                    DRHost.DatabaseName = DRDatabaseSID;
                    DRHost.DatabasePassword = DRDatabasePassword;

                  return DRHost.WinVerifyArachiveLogGap();
                }

                ConnectDR();

                var checkArchiveDR = DRHost.VerifyArachiveLogGap();

                Logger.InfoFormat("ARCHIVE LOG GAP DR RESULT :" + checkArchiveDR.ToString());

                return checkArchiveDR.Contains("no rows selected");
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("CHECK DR DATAGUARD ARCHIVE LOG GAP ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while checking DR Dataguard archive log gap", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }


        public bool ReplicateArachiveLogDR(int groupId)
        {
            try
            {
                if (ConnectDRWithoutDatanbase())
                {
                    var checkArchiveDR = DRHost.ReplicateArachiveLogFastcopy(groupId);
                    Logger.InfoFormat("REPLICATE ARCHIVE LOG DR RESULT :" + checkArchiveDR.ToString());
                    return checkArchiveDR.Contains("success");
                }
                return false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("REPLICATE ARCHIVE LOG DR ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while replicating archivelog on DR using FastCopy :", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }
        public bool FastCopyReplicateFiles(string fastcopypath,int actionId)
        {
            try
            {
                if (ConnectDRWithoutDatanbase())
                {
                    var checkReplicationDR = DRHost.FastcopyReplicationFile(fastcopypath, actionId);
                    Logger.InfoFormat("REPLICATE FILE DR RESULT :" + checkReplicationDR);
                    return checkReplicationDR.Contains("success") || checkReplicationDR.Contains("true");
                }
                return false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("REPLICATE FILE DR ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while replicating FILE on DR using FastCopy :", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }
        public bool FastCopyReplicateFolder(string fastcopypath, int actionId)
        {
            try
            {
                if (ConnectDRWithoutDatanbase())
                {
                    var checkReplicationDR = DRHost.FastcopyReplicationFolder(fastcopypath, actionId);
                    Logger.InfoFormat("REPLICATE FOLDER DR RESULT :" + checkReplicationDR.ToString());
                    return checkReplicationDR.Contains("success") || checkReplicationDR.Contains("true");
                }
                return false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("REPLICATE FOLDER DR ERROR :" + exc.Message);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while replicating FOLDER on DR using FastCopy :", exc);
            }
            finally
            {
                DRHost.Disconnect();
            }
        }
        private List<string> CheckOracleDataGuardStatusWin()

        {
            var dataguardList = new List<string>
                {
                    PrimaryHost.CheckPRDataGuardWindows(), 
                    DRHost.CheckDRDataGuardWindows()
                };

            return dataguardList;
        }
    }
}
