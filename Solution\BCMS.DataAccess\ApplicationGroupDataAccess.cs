﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class ApplicationGroupDataAccess
    {

        //public static IList<ApplicationGroup> ApplicationGroupGetAll()
        //{
        //    const string SP = "ApplicationGroup_GetAll";

        //    IList<ApplicationGroup> applicationGroups=new List<ApplicationGroup>();

        //    var db = DatabaseFactory.CreateDatabase();

        //    using (DbCommand cmd = db.GetStoredProcCommand(SP))
        //    {
        //        using (IDataReader reader = db.ExecuteReader(cmd))
        //        {
        //            while (reader.Read())
        //            {
        //                const int FLD_ID = 0;
        //                const int FLD_NAME = 1;
        //                const int FLD_DESCRIPTION = 2;
        //                const int FLD_COMPANYID = 3;

        //                var applicationGroup = new ApplicationGroup();

        //                applicationGroup.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
        //                applicationGroup.Name = reader.IsDBNull(FLD_NAME) ? string.Empty : reader.GetString(FLD_NAME);
        //                applicationGroup.UserMessage  = reader.IsDBNull(FLD_DESCRIPTION)
        //                                                   ? string.Empty
        //                                                   : reader.GetString(FLD_DESCRIPTION);
        //              /*  applicationGroup.AlternativeNames = reader.IsDBNull(FLD_ALTERNATIVENAMES)
        //                                                        ? string.Empty
        //                                                        : reader.GetString(FLD_ALTERNATIVENAMES);
        //                applicationGroup.BusinessOwner = reader.IsDBNull(FLD_BUSINESSOWNER)
        //                                                     ? string.Empty
        //                                                     : reader.GetString(FLD_BUSINESSOWNER);
        //                applicationGroup.SupportTeam = reader.IsDBNull(FLD_SUPPORTTEAM)
        //                                                   ? string.Empty
        //                                                   : reader.GetString(FLD_SUPPORTTEAM);
        //                applicationGroup.ApplicationFunctions = reader.IsDBNull(FLD_APPLICATIONFUNCTIONS)
        //                                                            ? 0
        //                                                            : reader.GetInt32(FLD_APPLICATIONFUNCTIONS);
        //                applicationGroup.CriticalTimes = reader.IsDBNull(FLD_CRITICALTIMES)
        //                                                     ? string.Empty
        //                                                     : reader.GetString(FLD_CRITICALTIMES);
        //                applicationGroup.HeavyLoadTimes = reader.IsDBNull(FLD_HEAVYLOADTIMES)
        //                                                      ? string.Empty
        //                                                      : reader.GetString(FLD_HEAVYLOADTIMES);*/
        //                applicationGroup.ProfileId = reader.IsDBNull(FLD_COMPANYID) ? 0 : reader.GetInt32(FLD_COMPANYID);


        //                applicationGroups.Add(applicationGroup);
        //            }

        //        }
        //    }

        //    return applicationGroups;
        //}
        public static IList<ApplicationGroup> ApplicationGroupGetAll()
        {
            const string SP = "ApplicationGroup_GetAll";

            IList<ApplicationGroup> applicationGroups = new List<ApplicationGroup>();

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                using (IDataReader reader = db.ExecuteReader(cmd))
                {
                    while (reader.Read())
                    {
                        

                        var applicationGroup = new ApplicationGroup();

                        applicationGroup.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                        applicationGroup.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                        applicationGroup.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"]);
                        applicationGroup.ProfileId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]);



                        applicationGroups.Add(applicationGroup);
                    }

                }
            }

            return applicationGroups;
        }
    }
}