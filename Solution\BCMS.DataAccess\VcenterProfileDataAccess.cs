﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class VcenterProfileDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(VcenterProfileDataAccess));

        public static VCenterProfile GetVCenterProfilebyIsSelected(int Id)
        {
            var VCenterProfile = new VCenterProfile();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("GetVCenterProfilebyIsSelect"))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, Id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {

                            VCenterProfile.Id = Convert.ToInt32(myGroupReader["Id"]);
                            VCenterProfile.ProfileName = Convert.ToString(myGroupReader["ProfileName"]);
                            VCenterProfile.ProfileType = Convert.ToString(myGroupReader["ProfileType"]);
                            VCenterProfile.ServerID = Convert.ToInt32(myGroupReader["ServerId"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get VcenterProfile by IsSelected :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message + " ERROR MSG " + exc.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, exc.Message, exc);
            }

            return VCenterProfile;
        }

        public static VCenterProfile GetVCenterProfilebyId(int Id)
        {
            var VCenterProfile = new VCenterProfile();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("VCenterProfile_GetById"))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, Id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {

                            VCenterProfile.Id = Convert.ToInt32(myGroupReader["Id"]);
                            VCenterProfile.ProfileName = Convert.ToString(myGroupReader["ProfileName"]);
                            VCenterProfile.ProfileType = Convert.ToString(myGroupReader["ProfileType"]);
                            VCenterProfile.ServerID = Convert.ToInt32(myGroupReader["ServerId"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get VcenterProfile by IsSelected :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message + " ERROR MSG " + exc.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, exc.Message, exc);
            }

            return VCenterProfile;
        }

        public static IList<VCenterProfile> GetALLVCenterProfile()
        {

            IList<VCenterProfile> VCenterProfileList = new List<VCenterProfile>();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("VCenterProfile_GetALL"))
                {


#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var VCenterProfile = new VCenterProfile();

                            VCenterProfile.Id = Convert.ToInt32(myGroupReader["Id"]);
                            VCenterProfile.ProfileName = Convert.ToString(myGroupReader["ProfileName"]);
                            VCenterProfile.ProfileType = Convert.ToString(myGroupReader["ProfileType"]);
                            VCenterProfile.ServerID = Convert.ToInt32(myGroupReader["ServerId"]);

                            VCenterProfileList.Add(VCenterProfile);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR VCenter Profile GetALL:" + exc.InnerException == null ? exc.Message : exc.InnerException.Message + " ERROR MSG " + exc.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, exc.Message, exc);
            }

            return VCenterProfileList;
        }

        public static IList<VCenterProfileDetails> GetVCenterProfileDetailsbyProfileID(int Id)
        {
            IList<VCenterProfileDetails> PrifileList = new List<VCenterProfileDetails>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("VCenterProfileDetails_GetByProfileId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iProfileId", DbType.Int32, Id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var _VCenterProfileDetails = new VCenterProfileDetails();

                            _VCenterProfileDetails.Id = Convert.ToInt32(myGroupReader["Id"]);
                            _VCenterProfileDetails.ProfileId = Convert.ToInt32(myGroupReader["ProfileId"]);
                            _VCenterProfileDetails.ServerId = Convert.ToInt32(myGroupReader["ServerId"]);
                            _VCenterProfileDetails.ServerName = Convert.ToString(myGroupReader["ServerName"]);
                            _VCenterProfileDetails.MonitorServices = Convert.ToString(myGroupReader["MonitorServices"]);

                            PrifileList.Add(_VCenterProfileDetails);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get VCenter Profile  Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get VCenterProfileDetails Details", exc);

            }
            return PrifileList;
        }

        public static VCenterProfileDetails GetVCenterProfileDetailsbyProfileandServerID(int ProfileId, int ServerId)
        {
            VCenterProfileDetails _VCenterProfileDetails = new VCenterProfileDetails();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("VCPDetails_GetBySevProfileId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iServerId", DbType.Int32, ServerId);
                    Database.AddInParameter(dbCommand, Dbstring + "iProfileId", DbType.Int32, ProfileId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {

                            _VCenterProfileDetails.Id = Convert.ToInt32(myGroupReader["Id"]);
                            _VCenterProfileDetails.ProfileId = Convert.ToInt32(myGroupReader["ProfileId"]);
                            _VCenterProfileDetails.ServerId = Convert.ToInt32(myGroupReader["ServerId"]);
                            _VCenterProfileDetails.ServerName = Convert.ToString(myGroupReader["ServerName"]);
                            _VCenterProfileDetails.MonitorServices = Convert.ToString(myGroupReader["MonitorServices"]);


                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get VCenter Profile  Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get VCenterProfileDetails Details", exc);

            }
            return _VCenterProfileDetails;
        }


        public static bool UpdateVCenterProfileSelected(int id, int IsMonitor)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VCenterProfile_UpdateById"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsSelected", DbType.Int32, 0);
                    Database.AddInParameter(cmd, Dbstring + "iIsMonitor", DbType.Int32, IsMonitor);


#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return true;
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while VCenter Profile Details", exc);

            }
        }

        public static bool UpdateVCenterProfileByOS(int id, int IsMonitor, string osType)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VCProfile_OS_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iOsType", DbType.String, osType);
                    Database.AddInParameter(cmd, Dbstring + "iIsSelected", DbType.Int32, 0);
                    Database.AddInParameter(cmd, Dbstring + "iIsMonitor", DbType.Int32, IsMonitor);


#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return true;
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while VCenter Profile Details", exc);

            }
        }


        public static bool AddVCServMonitorStatus(VCenterProfileDetails _VCenterProfileDetails)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VCServMonitorLogs_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, _VCenterProfileDetails.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, _VCenterProfileDetails.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iServiceName", DbType.AnsiString, _VCenterProfileDetails.MonitorServices);
                    Database.AddInParameter(cmd, Dbstring + "iServiceStatus", DbType.AnsiString, _VCenterProfileDetails.ServiceStatus);
                    Database.AddInParameter(cmd, Dbstring + "iOSTYPE", DbType.AnsiString, _VCenterProfileDetails.OSTYPE);
                    //    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.AnsiString, 1);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Hyper V Details", exc);

            }
        }


        public static bool AddVCServMonitorVMStatusAll(int ProfileId, int ServerId, string PowerStatus, string ReadyState, string VMName, int IsIPSearched, string HostIP, string OSType)
        {

            try
            {

                //      VCVMMonitorStatus1_create
                using (DbCommand cmd = Database.GetStoredProcCommand("VCVMMonitorLogs1_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStatus", DbType.AnsiString, PowerStatus);
                    //    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);
                    Database.AddInParameter(cmd, Dbstring + "iReadyState", DbType.AnsiString, ReadyState);
                    Database.AddInParameter(cmd, Dbstring + "iVMName", DbType.AnsiString, VMName);
                    Database.AddInParameter(cmd, Dbstring + "iIsIPSearched", DbType.Int32, IsIPSearched);
                    Database.AddInParameter(cmd, Dbstring + "IPAddress", DbType.AnsiString, CryptographyHelper.Md5Encrypt(HostIP));
                    Database.AddInParameter(cmd, Dbstring + "OSTYPE", DbType.AnsiString, OSType);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Hyper V Details", exc);

            }
        }


        public static bool AddVCServMonitorVMStatus(int ProfileId, int ServerId, string PowerStatus, string ReadyState, string VMName, int IsIPSearched, string HostIP, string OSTYpe)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VCVMMonitorLogs_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStatus", DbType.AnsiString, PowerStatus);
                    //    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);
                    Database.AddInParameter(cmd, Dbstring + "iReadyState", DbType.AnsiString, ReadyState);
                    Database.AddInParameter(cmd, Dbstring + "iVMName", DbType.AnsiString, VMName);
                    Database.AddInParameter(cmd, Dbstring + "iIsIPSearched", DbType.Int32, IsIPSearched);
                    Database.AddInParameter(cmd, Dbstring + "IPAddress", DbType.AnsiString, CryptographyHelper.Md5Encrypt(HostIP));
                    Database.AddInParameter(cmd, Dbstring + "OSTYPE", DbType.AnsiString, OSTYpe);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Hyper V Details", exc);

            }
        }

        //        public static bool AddVCServMonitorVMStatus(VCenterProfileDetails _VCenterProfileDetails)
        //        {

        //            try
        //            {
        //                using (DbCommand cmd = Database.GetStoredProcCommand("VCVMMonitorLogs_create"))
        //                {
        //                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, _VCenterProfileDetails.ProfileId);
        //                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, _VCenterProfileDetails.ServerId);
        //                    Database.AddInParameter(cmd, Dbstring + "iPowerStatus", DbType.AnsiString, _VCenterProfileDetails.PowerStatus);
        //                //    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);
        //                   Database.AddInParameter(cmd, Dbstring + "iReadyState", DbType.AnsiString, _VCenterProfileDetails.ReadyState);

        //#if ORACLE
        //                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
        //#endif
        //                    int returnCode = Database.ExecuteNonQuery(cmd);

        //                    return returnCode > 0;
        //                }
        //            }
        //            catch (Exception exc)
        //            {
        //                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Hyper V Details", exc);

        //            }
        //        }

        public static bool DeleteServiceandVMStatusByProfileIdandServerID(int id)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("VCMVMStatus_DeleteByProfileId1"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }


            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Delete Service and VM Status By ProfileId Details", exc);
            }
        }

        public static bool DeleteServiceandVMStatusByProfileIdandOS(int id, string OsType)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("VCMVMStatus_DeleteByOS"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iOSType", DbType.String, OsType);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }


            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Delete Service and VM Status By ProfileId Details", exc);
            }
        }

        public static bool DeleteServiceandVMStatusByProfileId(int id)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("VCMVMStatus_DeleteByProfileId"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }


            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Delete Service and VM Status By ProfileId Details", exc);
            }
        }

        public static string GetDefaultMonitorServiceByOSType(string OSType)
        {
            string MonServices = "";
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("GetDefltMonServices_ByOsType"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iOSType", DbType.AnsiString, OSType);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            MonServices = Convert.ToString(myGroupReader["MonitorServices"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get VCenter Profile  Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get VCenterProfileDetails Details", exc);

            }
            return MonServices;
        }

        public static IList<VCenterProfile> GetProfileByType(string profileType)
        {
            IList<VCenterProfile> _objVCenterDetails;
            const string sp = "VCenterProfile_GetByType";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {

                Database.AddInParameter(cmd, Dbstring + "iType", DbType.String, profileType);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myGroupReader = Database.ExecuteReader(cmd))
                {
                    _objVCenterDetails = new List<VCenterProfile>();

                    while (myGroupReader.Read())
                    {
                        //VCenterProfileDetails _objVCenter = new VCenterProfileDetails();

                        //_objVCenter.ProfileId = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                        //_objVCenter.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);

                        //_objVCenterDetails.Add(_objVCenter);

                        var VCenterProfile = new VCenterProfile();

                        VCenterProfile.Id = Convert.ToInt32(myGroupReader["Id"]);
                        VCenterProfile.ProfileName = Convert.ToString(myGroupReader["ProfileName"]);
                        VCenterProfile.ProfileType = Convert.ToString(myGroupReader["ProfileType"]);
                        VCenterProfile.ServerID = Convert.ToInt32(myGroupReader["ServerId"]);

                        _objVCenterDetails.Add(VCenterProfile);
                    }

                }

                return _objVCenterDetails;
            }


        }

        public static IList<VCenterProfileDetails> GetProfileDetailsByServerId(int serverid)
        {
            IList<VCenterProfileDetails> _objVCenterDetails;
            const string sp = "VCProfileDetails_GetByServerID";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {

                Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, serverid);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    _objVCenterDetails = new List<VCenterProfileDetails>();

                    while (reader.Read())
                    {
                        VCenterProfileDetails _objVCenter = new VCenterProfileDetails();

                        _objVCenter.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
                        _objVCenter.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                        _objVCenter.ServerName = Convert.IsDBNull(reader["ServerName"]) ? string.Empty : Convert.ToString(reader["ServerName"]);
                        _objVCenter.MonitorServices = Convert.IsDBNull(reader["MonitorServices"]) ? string.Empty : Convert.ToString(reader["MonitorServices"]);
                        _objVCenter.OSTYPE = Convert.IsDBNull(reader["OSTYPE"]) ? string.Empty : Convert.ToString(reader["OSTYPE"]);

                        _objVCenterDetails.Add(_objVCenter);
                    }

                }

                return _objVCenterDetails;
            }
        }
    }
}
