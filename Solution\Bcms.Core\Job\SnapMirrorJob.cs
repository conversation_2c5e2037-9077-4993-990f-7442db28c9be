﻿using System;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.Core.Job
{
    public class SnapMirrorJob : IJob
    {
        #region IJob Members

        public const string Group = "group";
        private static readonly ILog Logger = LogManager.GetLogger(typeof(SnapMirrorJob));

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var group = data.Get(Group) as InfraObject;

            if (group != null)
            {
                InfraObject groupItem = InfraObjectDataAccess.GetInfraObjectById(group.Id);

                if (groupItem != null)
                {
                    if (groupItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : InfraObject in Maintenace mode skip Snap Mirror Monitoring Job",groupItem);
                        return;
                    }
                    MonitorSnapMirror(groupItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor SnapMirror Job ");
            }
        }

        public void MonitorSnapMirror(InfraObject group)
        {
            try
            {
                using (var client = new BcmsClient(group))
                {
                    client.MonitorSnapMirror();
                }

            }
            catch (BcmsException exc)
            {
                ExceptionManager.Manage(exc, JobName.MonitorSnapMirror.ToString(), group.Id, group.Name);
            }
            catch (Exception exc)
            {
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MonitorSnapMirror.ToString(), group.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.MonitorSnapMirror.ToString(), group.Id, group.Name);
            }
        }

        #endregion
    }
}