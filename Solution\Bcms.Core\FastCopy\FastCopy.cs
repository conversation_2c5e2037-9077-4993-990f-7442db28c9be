﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Jscape.Ssh;
using Bcms.Common;
using log4net;
using BCMS.Core.Utility;
using Jscape.Sftp;
using Bcms.ExceptionHandler;
using log4net.Repository.Hierarchy;

namespace Bcms.Core.FastCopy
{
    public class SqlFastCopyJob
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SqlFastCopyJob));

        public SshSession Session = null;
        public const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
        
        public void Synchronization(Server server, DatabaseBase database, InfraObject group)
        {
            _logger.Info("In Synchronization()");
            try
            {
                if (server != null)
                {
                    var str = string.Empty;
                    if ((group.IsWorkflow == 2) || (group.IsWorkflow == 4))
                    {
                        if (Connect(server.PRIPAddress, server.PRUserName, server.PRPassword))
                        {
                            var connect = StartWindowsFastCopy(server, group, database);
                            _logger.InfoFormat("Connected To Windows Server:{0}", server.PRIPAddress);
                            Disconnect();

                        }
                        else
                        {
                            _logger.InfoFormat("Unable to Connect To Windows Server:{0}", server.PRIPAddress);
                            throw new Exception();
                        }
                    }
                    else
                    {
                        if (Connect(server.DRIPAddress, server.DRUserName, server.DRPassword))
                        {
                            var connect = StartWindowsFastCopy(server, group, database);
                            _logger.InfoFormat("Connected To Windows Server:{0}", server.DRIPAddress);
                            Disconnect();
                        }
                        else
                        {
                            _logger.InfoFormat("Unable to Connect To Windows Server:{0}", server.PRIPAddress);
                            throw new Exception();
                        }
                    }
                }
            }
            catch (SftpException sftpExc)
            {
                Disconnect();
                throw new Exception(sftpExc.Message);
            }
        }

        public void AmazonFileReplication(Server server, int infraobjectId, int jobid, string option,string infraName,string dataSyncPath)
        {
            _logger.Info( infraName +" ================ Amazon File Replication Started ==================");
            
            try
            {
                if (Connect(server.PRIPAddress, server.PRUserName, server.PRPassword,22))
                {
                   string output = StartWindowsFastCopy(server, infraobjectId, jobid, option,dataSyncPath);

                    if(output.Contains("successfully"))
                        {
                            _logger.InfoFormat("{0} : Executing Sql Amazon DataSync Job ({1}) is completed successfully", infraName, jobid);
                        }
                        else
                       {
                         _logger.InfoFormat("{0} : Executing Sql Amazon DataSync Job ({1}) Having Error", infraName, jobid);
                        }
                    
                    Disconnect();
                }
                       
             
            }
            catch (SftpException sftpExc)
            {
                Disconnect();

                _logger.InfoFormat("{0} : Executing Sql Amazon DataSync Job ({1}) Having Error {2}", infraName, jobid, sftpExc.Message);

                throw;
            }        
        }


        internal string StartWindowsFastCopy(Server sever, int infraObjectId,int JobId,string option,string dataSyncPath)
        {
            try
            {
                var sb = new StringBuilder();
             
                var commands = new List<string>
                                        {
                                        "cd "+dataSyncPath,
                                         string.Format("{0} {1} {2} {3}","DataSync.exe",option,infraObjectId,JobId)
                                        };

                foreach (var command in commands)
                {

                    _logger.InfoFormat("Command:{0}", command.ToString());
                    sb.Append(RunSshCommandsWithWait(Session, command, ">"));
                                     
                }

                var connectResult = sb.ToString().ToLower();

                return connectResult;

            }
            catch (SshException sshExc)
            {
                _logger.ErrorFormat("{0}, sshException in SSHHElper:StartWindowsFastCopy() for server {1}", sshExc.Message, JobId);
                return "";
            }

            catch (Exception ex)
            {
                _logger.ErrorFormat("{0}, Error in SSHHElper:StartWindowsFastCopy() in server {1}", ex.Message, JobId);

                return "";
            }
        }


        internal string StartWindowsFastCopy(Server sever, InfraObject group, DatabaseBase database)
        {
            int i = 0;
            string IpAddress = "";
            string Password = "";
            if ((group.IsWorkflow == 2) || (group.IsWorkflow == 4))
            {
                IpAddress = sever.DRIPAddress;
                Password = sever.DRPassword;
            }
            else
            {
                IpAddress = sever.PRIPAddress;
                Password = sever.PRPassword;
            }

            try
            {
                var sb = new StringBuilder();
                string JarExeLocation = "C:\\FastCopy";
                var commands = new List<string>
                                        {
                                        "cd "+JarExeLocation,
                                            string.Format("{0} {1}","FastCopy.exe",group.Id),
                                            Password
                                        };

                foreach (var command in commands)
                {
                    //i++;
                    //sb.Append(RunSshCommandsNoWait(Session, command, ":"));
                    sb.Append(RunSshCommandsWithWait(Session, command, ":"));
                    // CommandsNoWait(Session, command, ":"));

                    // if (i <= 2)
                    _logger.InfoFormat("Command:{0}", command.ToString());
                }

                var connectResult = sb.ToString().ToLower();
                return connectResult;

            }
            catch (SshException sshExc)
            {
                _logger.ErrorFormat("{0}, sshException in SSHHElper:StartWindowsFastCopy() for server {1}", sshExc.Message, IpAddress);
                return "";
            }

            catch (Exception ex)
            {
                _logger.ErrorFormat("{0}, Error in SSHHElper:StartWindowsFastCopy() in server {1}", ex.Message, IpAddress);

                return "";
            }
        }

        protected string RunSshCommandsNoWait(SshSession session, string command, string prompt)
        {
            session.SendNoWait(command);
            return "";
            //return command;
        }

        protected string RunSshCommandsWithWait(SshSession session, string command, string prompt)
        {
            return session.SendWait(command, prompt, 1200000).Replace("\r\n", "").Replace("\t", "").Trim();
        }

        internal void Disconnect()
        {
            if (Session != null)
            {
                Session.Disconnect();
                _logger.InfoFormat("DisConnected Session ");
            }
        }

        internal bool Connect(string hostName, string userName, string password)
        {
            try
            {
                SshParameters parameters = new SshParameters(hostName, userName, password);
                Session = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig()) { LicenseKey = LicenseKey };
                Session.SetShellPrompt("\\$|#|>", true);
                //const string shellPrompt = @"\\$|>|:|\?";
                //Session.SetShellPrompt(shellPrompt,true);
                Session.Ssh.Timeout = 200000000;
                Session.Connect();
                return true;
            }

            catch (SshException sshExc)
            {
                //_logger.ErrorFormat("{0}, sshException ", sshExc.Message);
                _logger.ErrorFormat("Host Not reachable:Not able to connect to:{0}", hostName);

                return false;
            }
            catch (Exception ex)
            {
                //ex.Message
                _logger.ErrorFormat("{0}, Host not reachable", hostName);
                return false;
            }

        }

        internal bool Connect(string hostName, string userName, string password,int port)
        {
            int portNo = port == 0 ? 22 : port;

            try
            {
                SshParameters parameters = new SshParameters(hostName, portNo, userName, password);
                Session = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig()) { LicenseKey = LicenseKey };
                Session.SetShellPrompt("\\$|#|>", true);          
                Session.Ssh.Timeout = 200000000;
                Session.Connect();
                return true;
            }

            catch (SshException sshExc)
            {
                //_logger.ErrorFormat("{0}, sshException ", sshExc.Message);
                _logger.ErrorFormat("Host Not reachable:Not able to connect to:{0}", hostName);

                return false;
            }
            catch (Exception ex)
            {
                //ex.Message
                _logger.ErrorFormat("{0}, Host not reachable", hostName);
                return false;
            }

        }

    }
}
