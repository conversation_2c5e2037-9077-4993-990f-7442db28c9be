﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SeverConnection", Namespace = "http://www.Bcms.com/types")]
    public class SeverConnection : BaseEntity
    {
        #region Constructor
        public SeverConnection(): base()
        {
        }
        #endregion

        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public string IPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string SSHUserName
        {
            get;
            set;
        }

        [DataMember]
        public string SSHPassword
        {
            get;
            set;
        }

        [DataMember]
        public string OSType
        {
            get;
            set;
        }


        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public int IsChecked
        {
            get;
            set;
        }

        #endregion


    }
}
