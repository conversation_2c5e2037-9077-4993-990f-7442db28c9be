﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PDTake;
using PHyperV;
using PMSSQLNative;
using PFailoverClusters;

namespace Bcms.Core.Client
{
    public class HyperVClient : IDisposable
    {
        public HyperVClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

        public InfraObject CurrentInfraObject { get; set; }

        #region Variable
        public bool _isDisposed;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(HyperVClient));

        Server _HyperVserver;
        private DatabaseBase _prDatabase;
        private DatabaseBase _drDatabase;
        private Server _server;
        private DatabaseBase _database;
        private const int MaxAlertCount = 3;
        #endregion

        #region Properties

        public string DatabaseName { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string Port { get; set; }

        # endregion

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }


        public DatabaseBase CurrentDatabase
        {

            get
            {
                if (_database == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        // _database = DatabaseBaseDataAccess.GetDatabaseByInfraObjectId(CurrentInfraObject.Id);
                    }
                }
                return _database;
            }

            set
            {
                _database = value;
            }
        }

        private bool VerifyServerHyperV()
        {
            bool primaryserver = false;
            bool drserver = false;
            using (BCMS.Core.Utility.SSHHelper SSHHelper = new BCMS.Core.Utility.SSHHelper())
            {
                try
                {
                    int serverAlertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRServer : (int)AlertNotificationType.PRServer;

                    int serverAlertType1 = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRServer : (int)AlertNotificationType.DRServer;

                    var sentServerAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType, CurrentInfraObject.Id);

                    var sentServerAlertCount1 = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType1, CurrentInfraObject.Id);


                    if (SSHHelper.ConnectWindows(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword))
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                                                                           "InfraObject Name : " + CurrentInfraObject.Name + " Primary Server (" +
                                                                          (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + ") is connected."), "MonitorServerStatus", CurrentInfraObject.Id, CurrentInfraObject.Name);

                        }
                        HeatmapDataAccess.AddHeatmap(new Heatmap
                        {
                            InfraObjectId = CurrentInfraObject.Id,
                            EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                            BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                            HeatmapType = HeatmapType.Server.ToString(),
                            Status = ServerStatus.Up.ToString(),
                            IsAffected = false
                        });
                        primaryserver = true;

                        string ResolveMessage = CurrentInfraObject.Name + " : Production Server (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + ") is connected";
                        IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, 0, ResolveMessage);

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, true, CurrentInfraObject.Id);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable,
                                                                      "InfraObject Name : " + CurrentInfraObject.Name +
                                                                      " Primary Server (" +
                                                                      (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + ") is not reachable."), "MonitorServerStatus",
                                                    CurrentInfraObject.Id, CurrentInfraObject.Name);
                        }
                        HeatmapDataAccess.AddHeatmap(new Heatmap
                        {
                            InfraObjectId = CurrentInfraObject.Id,
                            EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                            BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                            HeatmapType = HeatmapType.Server.ToString(),
                            Status = ServerStatus.Down.ToString(),
                            IsAffected = true
                        });
                        primaryserver = false;

                        IncidentManagement inc = new IncidentManagement
                        {
                            IncidentName = "CP-Inc_Server_down",
                            IncidentTime = System.DateTime.Now,
                            InfraID = CurrentInfraObject.Id,
                            InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                            InfraComponentType = "Server",
                            IncidentComment = "Server Not Reachable"

                        };
                        IncidentManagementDataAccess.AddIncident(inc);

                    }
                    if (SSHHelper.ConnectWindows(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword))
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount1 > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                                                                        "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                                                                        (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + ") is connected."), "MonitorServerStatus",
                                                   CurrentInfraObject.Id, CurrentInfraObject.Name);

                        }
                        drserver = true;
                        HeatmapDataAccess.AddHeatmap(new Heatmap
                        {
                            InfraObjectId = CurrentInfraObject.Id,
                            EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                            BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                            HeatmapType = HeatmapType.Server.ToString(),
                            Status = ServerStatus.Up.ToString(),
                            IsAffected = false
                        });

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Down);
                        }
                        if (MaxAlertCount > sentServerAlertCount1)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, true, CurrentInfraObject.Id);


                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                                                    (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + ") is not reachable."), "MonitorServerStatus",
                                                    CurrentInfraObject.Id, CurrentInfraObject.Name);

                        }
                        drserver = false;
                        HeatmapDataAccess.AddHeatmap(new Heatmap
                        {
                            InfraObjectId = CurrentInfraObject.Id,
                            EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                            BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                            HeatmapType = HeatmapType.Server.ToString(),
                            Status = ServerStatus.Down.ToString(),
                            IsAffected = true
                        });
                    }
                    if (primaryserver && drserver)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }

                }
                catch (BcmsException bexc)
                {
                    throw;

                }
                catch (Exception exc)
                {
                    ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 0);
                    throw new BcmsException(BcmsExceptionType.CommonUnhandled, string.Format("{0} : Exception occured while Connecting Server", CurrentInfraObject.Name), exc);
                }
            }
        }


        public void MonitorHyperVReplicationComponent()
        {
            try
            {
                if (CurrentServer != null)
                {
                    string DatalageDetails = string.Empty;

                    CurrentServer.InfraObjectName = CurrentInfraObject.Name;

                    CurrentServer.InfraObjectId = CurrentInfraObject.Id;

                    if (VerifyServerHyperV())
                    {
                        if (CurrentInfraObject.PRReplicationId > 0)
                        {
                            Logger.Info("Hyper V Replication Id :" + CurrentInfraObject.PRReplicationId);

                            var hypervreplicationinfo = HyperVDataAccess.GetByReplicationId(CurrentInfraObject.PRReplicationId);
                            if (hypervreplicationinfo != null)
                            {
                                var hypevdeatilsToInsert = new HyperVDetails();

                                var hypervclustersummaryToInert = new hypervclustersummary();

                                var hypervclusternodeToinsert = new hypervclusternode();

                                PFailoverClusters.ClusterInfo PRclusterInfo = new PFailoverClusters.ClusterInfo();

                                PFailoverClusters.ClusterInfo DRclusterInfo = new PFailoverClusters.ClusterInfo();

                                List<PFailoverClusters.ClusterNode> PRClusterNode = new List<PFailoverClusters.ClusterNode>();

                                List<PFailoverClusters.ClusterNode> DRClusterNode = new List<PFailoverClusters.ClusterNode>();



                                //var ReplicationDetailsPR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), hypervreplicationinfo.PRVMName);
                                //var ReplicationDetailsDR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), hypervreplicationinfo.DRVMName);

                                if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                                {
                                    try
                                    {
                                        Logger.Info("HyperV SwitchOver Completed and using workflow monitoring is started for :" + CurrentInfraObject.Name);


                                        Logger.Info("PR Server : " + CurrentServer.DRIPAddress + "Is Part of Cluster :" + CurrentServer.DRIsPartOfCluster + " DR Server : " + CurrentServer.PRIPAddress + "Is Part of Cluster :" + CurrentServer.PRIsPartOfCluster);

                                        if (CurrentServer.PRIsPartOfCluster || CurrentServer.DRIsPartOfCluster)
                                        {

                                            if (CurrentServer.DRIsPartOfCluster)
                                            {
                                                Logger.Info("Fetching hyperv GetCluster Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName + " Cluster Group Name  " + hypervreplicationinfo.DRVMName);

                                                PRclusterInfo = FailoverClusterManager.GetClusterInfo(new PFailoverClusters.ClusterServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), CurrentServer.DRHostName, hypervreplicationinfo.DRVMName);

                                                Logger.Info("Completed Fetching hyperv GetCluster Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName + " Cluster Group Name  " + hypervreplicationinfo.DRVMName);


                                                Logger.Info("Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName);


                                                PRClusterNode = FailoverClusterManager.GetClusterNodeInfo(new PFailoverClusters.ClusterServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), CurrentServer.DRHostName);

                                                Logger.Info("Completed Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName);
                                            }
                                            if (CurrentServer.PRIsPartOfCluster)
                                            {

                                                Logger.Info("Fetching hyperv GetCluster Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName + " Cluster Group Name  " + hypervreplicationinfo.PRVMName);

                                                DRclusterInfo = FailoverClusterManager.GetClusterInfo(new PFailoverClusters.ClusterServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), CurrentServer.PRHostName, hypervreplicationinfo.PRVMName);

                                                Logger.Info("Completed Fetching hyperv GetCluster Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName + " Cluster Group Name  " + hypervreplicationinfo.PRVMName);


                                                Logger.Info("Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName);


                                                DRClusterNode = FailoverClusterManager.GetClusterNodeInfo(new PFailoverClusters.ClusterServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), CurrentServer.PRHostName);

                                                Logger.Info("Completed Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName);
                                            }


                                            if (PRclusterInfo != null && PRClusterNode != null)
                                            {
                                                hypervclustersummaryToInert.InfraobjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                                if (CurrentServer.DRIsPartOfCluster)
                                                {
                                                    hypervclustersummaryToInert.PRClusterName = !string.IsNullOrEmpty(CurrentServer.PRHostName) ? CurrentServer.PRHostName : "NA";
                                                    hypervclustersummaryToInert.PROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.PRIPAddress) ? CurrentServer.PRIPAddress : "NA";
                                                }
                                                else
                                                {
                                                    hypervclustersummaryToInert.PRClusterName = "NA";
                                                    hypervclustersummaryToInert.PROwnerIPAddress = "NA";
                                                }
                                                if (CurrentServer.PRIsPartOfCluster)
                                                {
                                                    hypervclustersummaryToInert.DROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.DRIPAddress) ? CurrentServer.DRIPAddress : "NA";
                                                    hypervclustersummaryToInert.DRClusterName = !string.IsNullOrEmpty(CurrentServer.DRHostName) ? CurrentServer.DRHostName : "NA";
                                                }
                                                else
                                                {
                                                    hypervclustersummaryToInert.DRClusterName = "NA";
                                                    hypervclustersummaryToInert.DROwnerIPAddress = "NA";
                                                }

                                                hypervclustersummaryToInert.PROwnerNode = !string.IsNullOrEmpty(PRclusterInfo.ClusterOwnerNodeName) ? PRclusterInfo.ClusterOwnerNodeName : "NA";
                                                hypervclustersummaryToInert.DROwnerNode = !string.IsNullOrEmpty(DRclusterInfo.ClusterOwnerNodeName) ? DRclusterInfo.ClusterOwnerNodeName : "NA";
                                                hypervclustersummaryToInert.PRBrokerName = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerName) ? PRclusterInfo.ClusteReplicaBrokerName : "NA";
                                                hypervclustersummaryToInert.DRBrokerName = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerName) ? DRclusterInfo.ClusteReplicaBrokerName : "NA";
                                                hypervclustersummaryToInert.PRBrokerState = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerState) ? PRclusterInfo.ClusteReplicaBrokerState : "NA";
                                                hypervclustersummaryToInert.DRBrokerState = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerState) ? DRclusterInfo.ClusteReplicaBrokerState : "NA";
                                                hypervclustersummaryToInert.PRBrokerIPAddress = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerIP) ? PRclusterInfo.ClusteReplicaBrokerIP : "NA";
                                                hypervclustersummaryToInert.DRBrokerIPAddress = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerIP) ? DRclusterInfo.ClusteReplicaBrokerIP : "NA";
                                                hypervclustersummaryToInert.PRBrokerNode = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerOwnerNode) ? PRclusterInfo.ClusteReplicaBrokerOwnerNode : "NA";
                                                hypervclustersummaryToInert.DRBrokerNode = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerOwnerNode) ? DRclusterInfo.ClusteReplicaBrokerOwnerNode : "NA";
                                                hypervclustersummaryToInert.PROwnerNodeList = !string.IsNullOrEmpty(PRclusterInfo.ClusterPrefOwnerNodeList) ? PRclusterInfo.ClusterPrefOwnerNodeList : "NA";
                                                hypervclustersummaryToInert.DROwnerNodeList = !string.IsNullOrEmpty(DRclusterInfo.ClusterPrefOwnerNodeList) ? DRclusterInfo.ClusterPrefOwnerNodeList : "NA";
                                                hypervclustersummaryToInert.PRNetworkIPAddress = !string.IsNullOrEmpty(PRclusterInfo.ClusterActiveNodeIP) ? PRclusterInfo.ClusterActiveNodeIP : "NA";
                                                hypervclustersummaryToInert.DRNetworkIPAddress = !string.IsNullOrEmpty(DRclusterInfo.ClusterActiveNodeIP) ? DRclusterInfo.ClusterActiveNodeIP : "NA";

                                                foreach (var item in PRClusterNode)
                                                {


                                                    hypervclusternodeToinsert.InfraobjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                                    hypervclusternodeToinsert.PRClusterID = !string.IsNullOrEmpty(item.ClusterNodeID) ? item.ClusterNodeID : "NA";
                                                    hypervclusternodeToinsert.DRClusterID = "NA";
                                                    hypervclusternodeToinsert.PRClusterName = !string.IsNullOrEmpty(item.ClusterNodeName) ? item.ClusterNodeName : "NA";
                                                    hypervclusternodeToinsert.DRClusterName = "NA";
                                                    hypervclusternodeToinsert.PRClusterState = !string.IsNullOrEmpty(item.ClusterNodeState) ? item.ClusterNodeState : "NA";
                                                    hypervclusternodeToinsert.DRClusterState = "NA";

                                                    try
                                                    {
                                                        var isSuccess = HyperVDataAccess.Addhypervclusternode(hypervclusternodeToinsert);
                                                        if (isSuccess)
                                                        {
                                                            Logger.Info("Inserted Hyperv Cluster Node Details Seccessfully for Infraobject :" + CurrentInfraObject.Name);
                                                        }
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        if (ex.InnerException != null)
                                                            Logger.Info("Exception: Inserting Hyperv Cluster Node Details:" + ex.InnerException);
                                                        else
                                                            Logger.Info("Exception: Inserting Hyperv Cluster Node Details::" + ex.Message);

                                                    }
                                                }

                                                try
                                                {
                                                    var isSuccess = HyperVDataAccess.Addhypervclustersummary(hypervclustersummaryToInert);
                                                    if (isSuccess)
                                                    {
                                                        Logger.Info("Inserting Hyperv Cluster Summary Details Seccessfully for Infraobject :" + CurrentInfraObject.Name);
                                                    }

                                                }
                                                catch (Exception ex)
                                                {
                                                    if (ex.InnerException != null)
                                                        Logger.Info("Exception: Inserting Hyperv Cluster Summary Details:" + ex.InnerException);
                                                    else
                                                        Logger.Info("Exception: Inserting Hyperv Cluster Summary Details::" + ex.Message);

                                                }

                                            }
                                            else
                                            {
                                                Logger.Info("hypervclustersummary or hypervclusternode is Getting Null");
                                            }

                                        }
                                        else
                                        {
                                            Logger.Info("PR Server : " + CurrentServer.DRIPAddress + "Is Not Part of Cluster :" + CurrentServer.DRIsPartOfCluster + "DR Server : " + CurrentServer.PRIPAddress + "Is Not Part of Cluster :" + CurrentServer.PRIsPartOfCluster + "Skip clusters Note and Cluster Info Monitoring");
                                        }


                                        Logger.Info("Fetching ReplicationDetailsPR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);
                                        var ReplicationDetailsPR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), hypervreplicationinfo.DRVMName);
                                        Logger.Info("Completed Fetching ReplicationDetailsPR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);

                                        Logger.Info("Fetching ReplicationDetailsDR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);
                                        var ReplicationDetailsDR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), hypervreplicationinfo.PRVMName);
                                        Logger.Info("Completed Fetching ReplicationDetailsDR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);

                                        Logger.Info("Fetching VMInfoPR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);
                                        var VMInfoPR = PHyperV.HypervManager.GetVMInfo(new PHyperV.HypervServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), hypervreplicationinfo.DRVMName);
                                        Logger.Info("Completed Fetching VMInfoPR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);

                                        Logger.Info("Fetching VMInfoDR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);
                                        var VMInfoDR = PHyperV.HypervManager.GetVMInfo(new PHyperV.HypervServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), hypervreplicationinfo.PRVMName);
                                        Logger.Info("Completed Fetching VMInfoDR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);




                                        if (ReplicationDetailsPR != null && ReplicationDetailsDR != null && VMInfoPR != null && VMInfoDR != null)
                                        {

                                            hypevdeatilsToInsert.InfraObjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                            hypevdeatilsToInsert.PRVMState = !string.IsNullOrEmpty(VMInfoPR.VMState) ? VMInfoPR.VMState : "NA";
                                            hypevdeatilsToInsert.DRVMState = !string.IsNullOrEmpty(VMInfoDR.VMState) ? VMInfoDR.VMState : "NA";
                                            hypevdeatilsToInsert.PRVMClustered = !string.IsNullOrEmpty(VMInfoPR.VMClusteredState) ? VMInfoPR.VMClusteredState : "NA";
                                            hypevdeatilsToInsert.DRVMClustered = !string.IsNullOrEmpty(VMInfoDR.VMClusteredState) ? VMInfoDR.VMClusteredState : "NA";
                                            hypevdeatilsToInsert.PRVMIPAdress = !string.IsNullOrEmpty(VMInfoPR.VMIPAddress) ? VMInfoPR.VMIPAddress : "NA";
                                            hypevdeatilsToInsert.DRVMIPAdress = !string.IsNullOrEmpty(VMInfoDR.VMIPAddress) ? VMInfoDR.VMIPAddress : "NA";
                                            hypevdeatilsToInsert.PRVMNetworkStatus = !string.IsNullOrEmpty(VMInfoPR.VMNetworkStatus) ? VMInfoPR.VMNetworkStatus : "NA";
                                            hypevdeatilsToInsert.DRVMNetworkStatus = !string.IsNullOrEmpty(VMInfoDR.VMNetworkStatus) ? VMInfoDR.VMNetworkStatus : "NA";
                                            hypevdeatilsToInsert.PRReplicationState = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationState) ? ReplicationDetailsPR.ReplicationState : "NA";
                                            hypevdeatilsToInsert.DRReplicationState = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationState) ? ReplicationDetailsDR.ReplicationState : "NA";
                                            hypevdeatilsToInsert.PRReplicationMode = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationMode) ? ReplicationDetailsPR.ReplicationMode : "NA";
                                            hypevdeatilsToInsert.DRReplicationMode = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationMode) ? ReplicationDetailsDR.ReplicationMode : "NA";
                                            hypevdeatilsToInsert.PRCurrentPrimaryServer = !string.IsNullOrEmpty(ReplicationDetailsPR.PrimaryServer) ? ReplicationDetailsPR.PrimaryServer : "NA";
                                            hypevdeatilsToInsert.DRCurrentPrimaryServer = !string.IsNullOrEmpty(ReplicationDetailsDR.PrimaryServer) ? ReplicationDetailsDR.PrimaryServer : "NA";
                                            hypevdeatilsToInsert.PRCurrentReplicaServer = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicaServer) ? ReplicationDetailsPR.ReplicaServer : "NA";
                                            hypevdeatilsToInsert.DRCurrentReplicaServer = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicaServer) ? ReplicationDetailsDR.ReplicaServer : "NA";
                                            hypevdeatilsToInsert.PRReplicationHealth = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationHealth) ? ReplicationDetailsPR.ReplicationHealth : "NA";
                                            hypevdeatilsToInsert.DRReplicationHealth = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationHealth) ? ReplicationDetailsDR.ReplicationHealth : "NA";
                                            hypevdeatilsToInsert.PRReplicaPort = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationPort) ? ReplicationDetailsPR.ReplicationPort : "NA";
                                            hypevdeatilsToInsert.DRReplicaPort = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationPort) ? ReplicationDetailsDR.ReplicationPort : "NA";
                                            hypevdeatilsToInsert.PRAuthType = !string.IsNullOrEmpty(ReplicationDetailsPR.AuthenticationType) ? ReplicationDetailsPR.AuthenticationType : "NA";
                                            hypevdeatilsToInsert.DRAuthType = !string.IsNullOrEmpty(ReplicationDetailsDR.AuthenticationType) ? ReplicationDetailsDR.AuthenticationType : "NA";
                                            hypevdeatilsToInsert.PRSizeReplicated = !string.IsNullOrEmpty(ReplicationDetailsPR.PendingDataSize) ? ReplicationDetailsPR.PendingDataSize : "NA";
                                            hypevdeatilsToInsert.DRSizeReplicated = !string.IsNullOrEmpty(ReplicationDetailsDR.PendingDataSize) ? ReplicationDetailsDR.PendingDataSize : "NA";
                                            hypevdeatilsToInsert.LastSynchronized = !string.IsNullOrEmpty(ReplicationDetailsPR.LastSyncTime) ? ReplicationDetailsPR.LastSyncTime : "NA";
                                            hypevdeatilsToInsert.HyperVDatalag = !string.IsNullOrEmpty(ReplicationDetailsPR.DataLag) ? ReplicationDetailsPR.DataLag : "NA";
                                            DatalageDetails = !string.IsNullOrEmpty(ReplicationDetailsPR.DataLag) ? ReplicationDetailsPR.DataLag : "NA";
                                        }
                                        else
                                        {
                                            Logger.Info("HyperV ReplicationDetails And VMInfo Getting Null for :" + CurrentInfraObject.Name);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (ex.InnerException != null)
                                            Logger.Info("Exception: Getting  Replication and VM Details:" + ex.InnerException);
                                        else
                                            Logger.Info("Exception: Getting  Replication and VM Details:" + ex.Message);
                                    }
                                }
                                else
                                {

                                    try
                                    {
                                        Logger.Info("PR Server : " + CurrentServer.PRIPAddress + "Is Part of Cluster :" + CurrentServer.PRIsPartOfCluster + " DR Server : " + CurrentServer.DRIPAddress + "Is Part of Cluster :" + CurrentServer.DRIsPartOfCluster);

                                        if (CurrentServer.PRIsPartOfCluster || CurrentServer.DRIsPartOfCluster)
                                        {
                                            if (CurrentServer.PRIsPartOfCluster)
                                            {

                                                Logger.Info("Fetching hyperv GetCluster Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName + " Cluster Group Name  " + hypervreplicationinfo.PRVMName);

                                                PRclusterInfo = FailoverClusterManager.GetClusterInfo(new PFailoverClusters.ClusterServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), CurrentServer.PRHostName, hypervreplicationinfo.PRVMName);

                                                Logger.Info("Completed Fetching hyperv GetCluster Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName + " Cluster Group Name  " + hypervreplicationinfo.PRVMName);


                                                Logger.Info("Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName);


                                                PRClusterNode = FailoverClusterManager.GetClusterNodeInfo(new PFailoverClusters.ClusterServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), CurrentServer.PRHostName);

                                                Logger.Info("Completed Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.PRIPAddress + " Cluster Name :" + CurrentServer.PRHostName);
                                            }
                                            if (CurrentServer.DRIsPartOfCluster)
                                            {

                                                Logger.Info("Fetching hyperv GetCluster Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName + " Cluster Group Name  " + hypervreplicationinfo.DRVMName);

                                                DRclusterInfo = FailoverClusterManager.GetClusterInfo(new PFailoverClusters.ClusterServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), CurrentServer.DRHostName, hypervreplicationinfo.DRVMName);

                                                Logger.Info("Completed Fetching hyperv GetCluster Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName + " Cluster Group Name  " + hypervreplicationinfo.DRVMName);


                                                Logger.Info("Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName);


                                                DRClusterNode = FailoverClusterManager.GetClusterNodeInfo(new PFailoverClusters.ClusterServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), CurrentServer.DRHostName);

                                                Logger.Info("Completed Fetching hyperv Get Cluster Node Info for Server  : " + CurrentServer.DRIPAddress + " Cluster Name :" + CurrentServer.DRHostName);
                                            }


                                            if (PRclusterInfo != null && PRClusterNode != null)
                                            {
                                                hypervclustersummaryToInert.InfraobjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                                if (CurrentServer.PRIsPartOfCluster)
                                                {
                                                    hypervclustersummaryToInert.PRClusterName = !string.IsNullOrEmpty(CurrentServer.PRHostName) ? CurrentServer.PRHostName : "NA";
                                                    hypervclustersummaryToInert.PROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.PRIPAddress) ? CurrentServer.PRIPAddress : "NA";
                                                }
                                                else
                                                {
                                                    hypervclustersummaryToInert.PRClusterName = "NA";
                                                    hypervclustersummaryToInert.PROwnerIPAddress = "NA";
                                                }
                                                if (CurrentServer.DRIsPartOfCluster)
                                                {
                                                    hypervclustersummaryToInert.DROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.DRIPAddress) ? CurrentServer.DRIPAddress : "NA";
                                                    hypervclustersummaryToInert.DRClusterName = !string.IsNullOrEmpty(CurrentServer.DRHostName) ? CurrentServer.DRHostName : "NA";
                                                }
                                                else
                                                {
                                                    hypervclustersummaryToInert.DRClusterName = "NA";
                                                    hypervclustersummaryToInert.DROwnerIPAddress = "NA";
                                                }
                                                //hypervclustersummaryToInert.PRClusterName = !string.IsNullOrEmpty(CurrentServer.PRHostName) ? CurrentServer.PRHostName : "NA";
                                                //hypervclustersummaryToInert.DRClusterName = !string.IsNullOrEmpty(CurrentServer.DRHostName) ? CurrentServer.DRHostName : "NA";
                                                //hypervclustersummaryToInert.PROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.PRIPAddress) ? CurrentServer.PRIPAddress : "NA";
                                                //hypervclustersummaryToInert.DROwnerIPAddress = !string.IsNullOrEmpty(CurrentServer.DRIPAddress) ? CurrentServer.DRIPAddress : "NA";
                                                hypervclustersummaryToInert.PROwnerNode = !string.IsNullOrEmpty(PRclusterInfo.ClusterOwnerNodeName) ? PRclusterInfo.ClusterOwnerNodeName : "NA";
                                                hypervclustersummaryToInert.DROwnerNode = !string.IsNullOrEmpty(DRclusterInfo.ClusterOwnerNodeName) ? DRclusterInfo.ClusterOwnerNodeName : "NA";
                                                hypervclustersummaryToInert.PRBrokerName = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerName) ? PRclusterInfo.ClusteReplicaBrokerName : "NA";
                                                hypervclustersummaryToInert.DRBrokerName = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerName) ? DRclusterInfo.ClusteReplicaBrokerName : "NA";
                                                hypervclustersummaryToInert.PRBrokerState = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerState) ? PRclusterInfo.ClusteReplicaBrokerState : "NA";
                                                hypervclustersummaryToInert.DRBrokerState = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerState) ? DRclusterInfo.ClusteReplicaBrokerState : "NA";
                                                hypervclustersummaryToInert.PRBrokerIPAddress = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerIP) ? PRclusterInfo.ClusteReplicaBrokerIP : "NA";
                                                hypervclustersummaryToInert.DRBrokerIPAddress = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerIP) ? DRclusterInfo.ClusteReplicaBrokerIP : "NA";
                                                hypervclustersummaryToInert.PRBrokerNode = !string.IsNullOrEmpty(PRclusterInfo.ClusteReplicaBrokerOwnerNode) ? PRclusterInfo.ClusteReplicaBrokerOwnerNode : "NA";
                                                hypervclustersummaryToInert.DRBrokerNode = !string.IsNullOrEmpty(DRclusterInfo.ClusteReplicaBrokerOwnerNode) ? DRclusterInfo.ClusteReplicaBrokerOwnerNode : "NA";
                                                hypervclustersummaryToInert.PROwnerNodeList = !string.IsNullOrEmpty(PRclusterInfo.ClusterPrefOwnerNodeList) ? PRclusterInfo.ClusterPrefOwnerNodeList : "NA";
                                                hypervclustersummaryToInert.DROwnerNodeList = !string.IsNullOrEmpty(DRclusterInfo.ClusterPrefOwnerNodeList) ? DRclusterInfo.ClusterPrefOwnerNodeList : "NA";
                                                hypervclustersummaryToInert.PRNetworkIPAddress = !string.IsNullOrEmpty(PRclusterInfo.ClusterActiveNodeIP) ? PRclusterInfo.ClusterActiveNodeIP : "NA";
                                                hypervclustersummaryToInert.DRNetworkIPAddress = !string.IsNullOrEmpty(DRclusterInfo.ClusterActiveNodeIP) ? DRclusterInfo.ClusterActiveNodeIP : "NA";


                                                foreach (var item in PRClusterNode)
                                                {

                                                    Logger.Info("Cluster Name :" + item.ClusterNodeName);

                                                    hypervclusternodeToinsert.InfraobjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                                    hypervclusternodeToinsert.PRClusterID = !string.IsNullOrEmpty(item.ClusterNodeID) ? item.ClusterNodeID : "NA";
                                                    hypervclusternodeToinsert.DRClusterID = "NA";
                                                    hypervclusternodeToinsert.PRClusterName = !string.IsNullOrEmpty(item.ClusterNodeName) ? item.ClusterNodeName : "NA";
                                                    hypervclusternodeToinsert.DRClusterName = "NA";
                                                    hypervclusternodeToinsert.PRClusterState = !string.IsNullOrEmpty(item.ClusterNodeState) ? item.ClusterNodeState : "NA";
                                                    hypervclusternodeToinsert.DRClusterState = "NA";


                                                    try
                                                    {
                                                        var isSuccess = HyperVDataAccess.Addhypervclusternode(hypervclusternodeToinsert);
                                                        if (isSuccess)
                                                        {
                                                            Logger.Info("Inserted Hyperv Cluster Node Details Seccessfully for Infraobject :" + CurrentInfraObject.Name);
                                                        }
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        if (ex.InnerException != null)
                                                            Logger.Info("Exception: Inserting Hyperv Cluster Node Details:" + ex.InnerException);
                                                        else
                                                            Logger.Info("Exception: Inserting Hyperv Cluster Node Details::" + ex.Message);

                                                    }
                                                }

                                                try
                                                {
                                                    var isSuccess = HyperVDataAccess.Addhypervclustersummary(hypervclustersummaryToInert);
                                                    if (isSuccess)
                                                    {
                                                        Logger.Info("Inserting Hyperv Cluster Summary Details Seccessfully for Infraobject :" + CurrentInfraObject.Name);
                                                    }

                                                }
                                                catch (Exception ex)
                                                {
                                                    if (ex.InnerException != null)
                                                        Logger.Info("Exception: Inserting Hyperv Cluster Summary Details:" + ex.InnerException);
                                                    else
                                                        Logger.Info("Exception: Inserting Hyperv Cluster Summary Details::" + ex.Message);

                                                }

                                            }
                                            else
                                            {
                                                Logger.Info("hypervclustersummary or hypervclusternode is Getting Null");
                                            }

                                        }
                                        else
                                        {
                                            Logger.Info("PR Server : " + CurrentServer.PRIPAddress + "Is Not Part of Cluster :" + CurrentServer.PRIsPartOfCluster + "DR Server : " + CurrentServer.DRIPAddress + "Is Not Part of Cluster :" + CurrentServer.DRIsPartOfCluster + "Skip clusters Note and Cluster Info Monitoring");
                                        }

                                        Logger.Info("Fetching ReplicationDetailsPR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);
                                        var ReplicationDetailsPR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), hypervreplicationinfo.PRVMName);
                                        Logger.Info("Completed Fetching ReplicationDetailsPR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);

                                        Logger.Info("Fetching ReplicationDetailsDR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);
                                        var ReplicationDetailsDR = PHyperV.HypervManager.GetReplicationInfo(new PHyperV.HypervServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), hypervreplicationinfo.DRVMName);
                                        Logger.Info("Completed Fetching ReplicationDetailsDR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);

                                        Logger.Info("Fetching VMInfoPR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);
                                        var VMInfoPR = PHyperV.HypervManager.GetVMInfo(new PHyperV.HypervServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword), hypervreplicationinfo.PRVMName);
                                        Logger.Info("Completed Fetching VMInfoPR for :" + CurrentServer.PRIPAddress + "VM Name :" + hypervreplicationinfo.PRVMName);

                                        Logger.Info("Fetching VMInfoDR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);
                                        var VMInfoDR = PHyperV.HypervManager.GetVMInfo(new PHyperV.HypervServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword), hypervreplicationinfo.DRVMName);
                                        Logger.Info("Completed Fetching VMInfoDR for :" + CurrentServer.DRIPAddress + "VM Name :" + hypervreplicationinfo.DRVMName);


                                        if (ReplicationDetailsPR != null && ReplicationDetailsDR != null && VMInfoPR != null && VMInfoDR != null)
                                        {
                                            hypevdeatilsToInsert.InfraObjectId = CurrentInfraObject.Id > 0 ? CurrentInfraObject.Id : 0;
                                            hypevdeatilsToInsert.PRVMState = !string.IsNullOrEmpty(VMInfoPR.VMState) ? VMInfoPR.VMState : "NA";
                                            hypevdeatilsToInsert.DRVMState = !string.IsNullOrEmpty(VMInfoDR.VMState) ? VMInfoDR.VMState : "NA";
                                            hypevdeatilsToInsert.PRVMClustered = !string.IsNullOrEmpty(VMInfoPR.VMClusteredState) ? VMInfoPR.VMClusteredState : "NA";
                                            hypevdeatilsToInsert.DRVMClustered = !string.IsNullOrEmpty(VMInfoDR.VMClusteredState) ? VMInfoDR.VMClusteredState : "NA";
                                            hypevdeatilsToInsert.PRVMIPAdress = !string.IsNullOrEmpty(VMInfoPR.VMIPAddress) ? VMInfoPR.VMIPAddress : "NA";
                                            hypevdeatilsToInsert.DRVMIPAdress = !string.IsNullOrEmpty(VMInfoDR.VMIPAddress) ? VMInfoDR.VMIPAddress : "NA";
                                            hypevdeatilsToInsert.PRVMNetworkStatus = !string.IsNullOrEmpty(VMInfoPR.VMNetworkStatus) ? VMInfoPR.VMNetworkStatus : "NA";
                                            hypevdeatilsToInsert.DRVMNetworkStatus = !string.IsNullOrEmpty(VMInfoDR.VMNetworkStatus) ? VMInfoDR.VMNetworkStatus : "NA";
                                            hypevdeatilsToInsert.PRReplicationState = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationState) ? ReplicationDetailsPR.ReplicationState : "NA";
                                            hypevdeatilsToInsert.DRReplicationState = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationState) ? ReplicationDetailsDR.ReplicationState : "NA";
                                            hypevdeatilsToInsert.PRReplicationMode = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationMode) ? ReplicationDetailsPR.ReplicationMode : "NA";
                                            hypevdeatilsToInsert.DRReplicationMode = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationMode) ? ReplicationDetailsDR.ReplicationMode : "NA";
                                            hypevdeatilsToInsert.PRCurrentPrimaryServer = !string.IsNullOrEmpty(ReplicationDetailsPR.PrimaryServer) ? ReplicationDetailsPR.PrimaryServer : "NA";
                                            hypevdeatilsToInsert.DRCurrentPrimaryServer = !string.IsNullOrEmpty(ReplicationDetailsDR.PrimaryServer) ? ReplicationDetailsDR.PrimaryServer : "NA";
                                            hypevdeatilsToInsert.PRCurrentReplicaServer = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicaServer) ? ReplicationDetailsPR.ReplicaServer : "NA";
                                            hypevdeatilsToInsert.DRCurrentReplicaServer = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicaServer) ? ReplicationDetailsDR.ReplicaServer : "NA";
                                            hypevdeatilsToInsert.PRReplicationHealth = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationHealth) ? ReplicationDetailsPR.ReplicationHealth : "NA";
                                            hypevdeatilsToInsert.DRReplicationHealth = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationHealth) ? ReplicationDetailsDR.ReplicationHealth : "NA";
                                            hypevdeatilsToInsert.PRReplicaPort = !string.IsNullOrEmpty(ReplicationDetailsPR.ReplicationPort) ? ReplicationDetailsPR.ReplicationPort : "NA";
                                            hypevdeatilsToInsert.DRReplicaPort = !string.IsNullOrEmpty(ReplicationDetailsDR.ReplicationPort) ? ReplicationDetailsDR.ReplicationPort : "NA";
                                            hypevdeatilsToInsert.PRAuthType = !string.IsNullOrEmpty(ReplicationDetailsPR.AuthenticationType) ? ReplicationDetailsPR.AuthenticationType : "NA";
                                            hypevdeatilsToInsert.DRAuthType = !string.IsNullOrEmpty(ReplicationDetailsDR.AuthenticationType) ? ReplicationDetailsDR.AuthenticationType : "NA";
                                            hypevdeatilsToInsert.PRSizeReplicated = !string.IsNullOrEmpty(ReplicationDetailsPR.PendingDataSize) ? ReplicationDetailsPR.PendingDataSize : "NA";
                                            hypevdeatilsToInsert.DRSizeReplicated = !string.IsNullOrEmpty(ReplicationDetailsDR.PendingDataSize) ? ReplicationDetailsDR.PendingDataSize : "NA";
                                            hypevdeatilsToInsert.LastSynchronized = !string.IsNullOrEmpty(ReplicationDetailsPR.LastSyncTime) ? ReplicationDetailsPR.LastSyncTime : "NA";
                                            hypevdeatilsToInsert.HyperVDatalag = !string.IsNullOrEmpty(ReplicationDetailsPR.DataLag) ? ReplicationDetailsPR.DataLag : "NA";
                                            DatalageDetails = !string.IsNullOrEmpty(ReplicationDetailsPR.DataLag) ? ReplicationDetailsPR.DataLag : "NA";
                                        }
                                        else
                                        {
                                            Logger.Info("HyperV ReplicationDetails And VMInfo Getting Null for :" + CurrentInfraObject.Name);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (ex.InnerException != null)
                                            Logger.Info("Exception: Getting  Replication and VM Details:" + ex.InnerException);
                                        else
                                            Logger.Info("Exception: Getting  Replication and VM Details:" + ex.Message);
                                    }
                                }

                                try
                                {
                                    if ((!string.IsNullOrEmpty(hypevdeatilsToInsert.PRVMState)) || (!string.IsNullOrEmpty(hypevdeatilsToInsert.DRVMState)))
                                    {
                                        var isSuccess = HyperVDataAccess.AddHyperVDetailsStatus(hypevdeatilsToInsert);
                                        if (isSuccess)
                                        {
                                            Logger.InfoFormat("{0} :HyperV Datalag time :{1} is updated", CurrentInfraObject.Name, DatalageDetails);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (ex.InnerException != null)
                                        Logger.Info("Exception: Inserting  AddHyperVDetailsStatus:" + ex.InnerException);
                                    else
                                        Logger.Info("Exception: Inserting  AddHyperVDetailsStatus:" + ex.Message);
                                }
                            }
                            else
                            {
                                Logger.Info("Hyper V Replication info is null for replication id :" + CurrentInfraObject.PRReplicationId);
                            }
                        }
                        else
                        {
                            Logger.Info("Hyper V Replication Id null or zero");
                        }
                    }
                    else
                    {
                        Logger.Info("Hyper V PR and DR Server is not reachable");
                    }

                }
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorHyperVComponent:" + CurrentInfraObject.Name + exc.Message);
                throw;
            }
            catch (Exception exception)
            {
                Logger.Info("Exception:MonitorHyperVComponent:" + CurrentInfraObject.Name + exception.Message);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occurred while performing monitor Hyper V", exception);
            }
        }


        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }


        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~HyperVClient()
        {
            Dispose(false);
        }
    }
}
