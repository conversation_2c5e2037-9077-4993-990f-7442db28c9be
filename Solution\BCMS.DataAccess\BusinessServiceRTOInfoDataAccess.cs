﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class BusinessServiceRTOInfoDataAccess : BaseDataAccess
    {
        public static bool AddBusinessServiceRTOInfo(BusinessServiceRTOInfo businessServiceRtoInfo)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("BusinessServiceRTOInfo_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, businessServiceRtoInfo.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessFunctionId", DbType.Int32, businessServiceRtoInfo.BusinessFunctionId);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, businessServiceRtoInfo.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iCurrentRTO", DbType.String, businessServiceRtoInfo.CurrentRTO);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert BusinessServiceRPTOInfo information", exc);
            }
            return false;
        }
    }
}
