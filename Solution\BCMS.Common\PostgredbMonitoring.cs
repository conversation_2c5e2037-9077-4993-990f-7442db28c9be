﻿using System.Runtime.Serialization;
using Bcms.Common.Base;
namespace Bcms.Common
{
    public class PostgredbMonitoring : BaseEntity
    {

        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string DBVersionPR { get; set; }

        [DataMember]
        public string DBVersionDR { get; set; }

        [DataMember]
        public string DBServiceStatusPR { get; set; }

        [DataMember]
        public string DBServiceStatusDR { get; set; }

        [DataMember]
        public string DBClusterStatePR { get; set; }

        [DataMember]
        public string DBClusterStateDR { get; set; }

        [DataMember]
        public string DBRecoveryStatusPR { get; set; }

        [DataMember]
        public string DBRecoveryStatusDR { get; set; }

        [DataMember]
        public string DBDataDirectorypathPR { get; set; }

        [DataMember]
        public string DBDataDirectorypathDR { get; set; }

        #endregion
    }
}
