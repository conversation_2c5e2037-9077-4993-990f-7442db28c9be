﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess;
using Bcms.Replication.Base;
using Bcms.Replication.Shared;


namespace Bcms.Replication.FastCopyOracle
{
    public class FastCopyOracleClient
    {
        public PrimaryHost PrimaryHost { get; set; }

        public DRHost DRHost { get; set; }

        public string RemoteDir { get; set; }

        public string LocalDir { get; set; }

        public string ProcessCode { get; set; }

      //  public string CurrentDatabase { get; set; }

        public string SudoUser { get; set; }

        public string DatabaseSID { get; set; }

        public string FastCopyPath { get; set; }

        public int InfraObjectId { get; set; }

        public int ReplicationId { get; set; }

        public int WorkflowActionId { get; set; }

        public bool IsSwithOver { get; set; }



        public bool IsWindows { get; set;}


       public FastCopyOracleClient()
       {
           
       }

       public FastCopyOracleClient(PrimaryHost primaryHost, DRHost drHost)
       {
           PrimaryHost = primaryHost;

           DRHost = drHost;
       }


       public FastCopyOracleClient(int infrObjectId,int replicationId,bool iswindows)
       {
           if (infrObjectId <= 0)
           {
               throw new ArgumentException("InfraObject Id cannot be zero");
           }
           if (replicationId <= 0)
           {
               throw new ArgumentException("FastCopy Id cannot be zero");
           }

           InfraObjectId = infrObjectId;

           ReplicationId = replicationId;

           IsWindows = iswindows;
       }

       public void ApplyIncrementalLogs()
       {
           try
           {
               ConnectDR();

               LogHelper.LogMessage("==============" + DatabaseSID + " : Apply Logs in DR started=====");

               LogHelper.LogMessage("DR HOST NAME :" + DRHost.HostName);

               DRHost.SudoUser = SudoUser;

               LogHelper.LogMessage("SUDO USER :" + DRHost.SudoUser);

               DRHost.DatabaseName = DatabaseSID;

               LogHelper.LogMessage("DATABASE NAME :" + DRHost.DatabaseName);

               DRHost.ApplyArchiveLogs();

               LogHelper.LogMessage("==============" + DatabaseSID + " : Apply Logs in DR completed=====");


               DRHost.Disconnect();
           }
           catch(Exception exc)
           {
               LogHelper.LogMessage("ERROR occured (" + DatabaseSID + ")applying logs ====="+exc.Message);

               DRHost.Disconnect();
               throw;
           }

       }

       public string StartFastCopyReplication()
       {
           try
           {
               ConnectDR();

               LogHelper.LogMessage("=======FAST COPY ARCHIVE SYNC STARTED=====");

               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Replicating, (int)ReplicationState.PerformingDataSync, true);

               string output = DRHost.StartFastCopy(InfraObjectId, ReplicationId, IsSwithOver);

               LogHelper.LogMessage("=======FAST COPY ARCHIVE SYNC COMPLETED =====");

               UpdateReplicationStatus(InfraObjectId, InfraObjectState.None, (int)ReplicationState.DataSyncCompleted, false);

               //if (output.Contains("success"))
               //{

               LogHelper.LogMessage("==============" + DatabaseSID + " : Apply Logs in DR started=====");

               LogHelper.LogMessage("DR HOST NAME :"+DRHost.HostName);

                   DRHost.SudoUser = SudoUser;

                   LogHelper.LogMessage("SUDO USER :" + DRHost.SudoUser);

                   DRHost.DatabaseName = DatabaseSID;

                   LogHelper.LogMessage("DATABASE NAME :" + DRHost.DatabaseName);

                   UpdateReplicationStatus(InfraObjectId, InfraObjectState.None, (int)ReplicationState.ApplyingIncrementalLogs, false);


                   DRHost.ApplyArchiveLogs();

                   UpdateReplicationStatus(InfraObjectId, InfraObjectState.Active, (int)ReplicationState.CompletedSuccessfully, true);
               //}

               DRHost.Disconnect();
               
               return output;
           }
           catch
           {
               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Active, (int)ReplicationState.CompletedWithError, true);

               DRHost.Disconnect();
               throw;
           }
       }

       public string StartFastCopyReplication(string logsequence, string filename,string datasyncPath)
       {
           try
           {
               ConnectDR();

               LogHelper.LogMessage("=======FAST COPY ARCHIVE SYNC STARTED=====");

               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Replicating, (int)ReplicationState.PerformingDataSync, true);

               string output = DRHost.StartFastCopy(InfraObjectId, ReplicationId, IsSwithOver, logsequence, filename, datasyncPath);

               LogHelper.LogMessage("=======FAST COPY ARCHIVE SYNC COMPLETED =====");

               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Active, (int)ReplicationState.DataSyncCompleted, true);

               //if (output.Contains("success"))
               //{

               //LogHelper.LogMessage("==============" + DatabaseSID + " : Apply Logs in DR started=====");

               //LogHelper.LogMessage("DR HOST NAME :" + DRHost.HostName);

               //DRHost.SudoUser = SudoUser;

               //LogHelper.LogMessage("SUDO USER :" + DRHost.SudoUser);

               //DRHost.DatabaseName = DatabaseSID;

               //LogHelper.LogMessage("DATABASE NAME :" + DRHost.DatabaseName);

               //UpdateReplicationStatus(GroupId, InfraObjectState.None, (int)ReplicationState.ApplyingIncrementalLogs, false);


               //DRHost.ApplyArchiveLogs();

               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Active, (int)ReplicationState.CompletedSuccessfully, true);
               //}

               DRHost.Disconnect(); 

               return string.Empty;
           }
           catch
           {
               UpdateReplicationStatus(InfraObjectId, InfraObjectState.Active, (int)ReplicationState.CompletedWithError, true);
               DRHost.Disconnect();
               throw;
           }
       }

       public bool ConnectDR()
       {
           try
           {
               DRHost.IsWindows = IsWindows;

               if (!IsWindows)
               {
                   if (DRHost.Connect(false))
                   {
                       LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                     string.Format(" DR Server {0} is connected", DRHost.HostName));

                       return true;
                   }
                   LogHelper.LogHeaderAndMessage("DR SERVER STATUS :",
                                                 string.Format("DR server {0} is not reachable", DRHost.HostName));
                   return false;
               }
               return true;
           }
           catch (Exception exc)
           {
               LogHelper.LogHeaderAndErrorMessage("DR CONNECTION ERROR : " , exc.Message);
               DRHost.Disconnect();
               throw;
           }
       }

       public bool FatCopyRedofilesReplication()
       {
           try
           {
               ConnectDR();

               string output= DRHost.StartFastCopyReplicateRedoFiles(WorkflowActionId,ReplicationId,FastCopyPath);

               DRHost.Disconnect();

               return IsDataSyncExecuteSuccessfully(ReplicationId);
           }
           catch (Exception exc)
           {
               DRHost.Disconnect();

               LogHelper.LogHeaderAndErrorMessage("REPLICATE REDO FILE ERROR : ", exc.Message);

               return false;
           }
       }

       private bool IsDataSyncExecuteSuccessfully(int replicationId)
       {

           try
           {
             FastCopyJob  jobList = FastCopyJobDataAccess.GetFastCopyJobByReplicationId(replicationId);

               if (jobList != null)
               {
                   LogHelper.LogMessage("DataSync Job("+replicationId+") current Mode is "+jobList.Mode);

                   if (jobList.Mode == 1)
                   {
                       return true;
                   }
                   if (jobList.Mode == 2)
                   {
                       return false;
                   }
               }
           }
           catch (Exception exc)
           {
               LogHelper.LogHeaderAndErrorMessage("REPLICATE REDO FILE ERROR : ", exc.Message);
           }
         
           return false;

       }

       public bool FastCopyArchiveFileReplicationWithSequence(string sequence,string logfile)
       {
           try
           {
               ConnectDR();
               LogHelper.LogHeaderAndMessage("FASTCOPYPATH : ", FastCopyPath);
               LogHelper.LogHeaderAndMessage("WORKFLOWACTIONID : ", WorkflowActionId.ToString());
               LogHelper.LogHeaderAndMessage("REPLICATIONID : ", ReplicationId.ToString());
               LogHelper.LogHeaderAndMessage("SEQUENCE : ", sequence);
               LogHelper.LogHeaderAndMessage("LOGFILE : ", logfile);
               string output = DRHost.StartFastCopyReplicateArchiveFolder(FastCopyPath, WorkflowActionId, ReplicationId,sequence, logfile);

               DRHost.Disconnect();

               //Check FastCopy JOB check

               return IsDataSyncExecuteSuccessfully(ReplicationId);
               
           }
           catch (Exception exc)
           {
               DRHost.Disconnect();

               LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE FOLDER ERROR : ", exc.Message);
               return false;
           }
       }

       public bool FastCopyReverseArchiveFileReplication()
       {
           try
           {
               ConnectDR();

               string output = DRHost.StartFastCopyReverseReplicateArchiveFolder(FastCopyPath, InfraObjectId, ReplicationId);

               DRHost.Disconnect();

               return true;
           }
           catch (Exception exc)
           {
               DRHost.Disconnect();

               LogHelper.LogHeaderAndErrorMessage("REPLICATE REVERSE ARCHIVE FOLDER ERROR : ", exc.Message);
               return false;
           }
       }

       public void UpdateReplicationStatus(int groupId,InfraObjectState infraObjectState,int replicationstate,bool isUpdateInfraObjectState)
       {
           InfraObjectDataAccess.UpdateInfraObjectByReplicationStatus(groupId, infraObjectState, replicationstate, isUpdateInfraObjectState);
       }
    }
}