﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PMSSQLDBMirroring;
using System.Net.NetworkInformation;
using System.Data.SqlClient;
using PMSSQLNative;

namespace Bcms.Core.Client
{
    public class MSSQLAlwaysONClient : IDisposable
    {
        public MSSQLAlwaysONClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }
        public InfraObject CurrentInfraObject { get; set; }

        #region Variable
        public bool _isDisposed;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(MSSqlNative2008Client));
        Server _MssqlNative2008eserver;
        private DatabaseBase _prDatabase;
        private DatabaseBase _drDatabase;
        private Server _server;
        private DatabaseBase _database;
        private const int MaxAlertCount = 3;
        # endregion

        #region Properties

        public string DatabaseName { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string Port { get; set; }

        # endregion

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }

        public DatabaseBase CurrentDatabase
        {

            get
            {
                if (_database == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        //_database = DatabaseBaseDataAccess.GetDatabaseByGroupId(CurrentInfraObject.Id, CurrentInfraObject.PRDatabaseId, CurrentInfraObject.DRDatabaseId);
                        _database = DatabaseBaseDataAccess.GetDatabaseByInfraObjectId(CurrentInfraObject.Id);
                    }
                }
                return _database;
            }

            set
            {
                _database = value;
            }
        }

        public void MonitorMssqlalwaysonComponent()
        {
            try
            {
                string AGgropName = string.Empty;
                string AGgrouprole = string.Empty;
                string InstanceName = string.Empty;
                string PRauthmode = string.Empty;
                string DRauthmode = string.Empty;
                string PRInstance = string.Empty;
                string drinstance = string.Empty;

                MSSQLServer _sqlServerPR, _sqlServerDR;
                MSSQLDatabase _sqlDatabasePR, _sqlDatabaseDR;

                Logger.InfoFormat("Monitoring MSSQL AlwaysON component..");
                if (CurrentServer != null && CurrentDatabase != null)
                {
                    CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                    CurrentServer.InfraObjectId = CurrentInfraObject.Id;
                    CurrentServer.JobName = JobName.MSSqlAlwaysONJob;

                    var InfraObjicetInfo = InfraObjectDataAccess.GetInfraObjectById(CurrentInfraObject.Id);
                    if (InfraObjicetInfo.PRReplicationId != 0)
                    {
                        var replication = ReplicationBaseDataAccess.GetReplicationById(InfraObjicetInfo.PRReplicationId);
                        if (replication != null)
                        {
                            if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                            {
                                AGgropName = replication.Mssqlalwayson.GroupName;
                                AGgrouprole = replication.Mssqlalwayson.GroupRole;

                                if (CurrentDatabase.DatabaseSqlNative2008.PRInstancename != string.Empty)
                                {
                                    CurrentServer.PRIPAddress = CurrentServer.PRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                                }

                                if (CurrentDatabase.DatabaseSqlNative2008.DRInstancename != string.Empty)
                                {
                                    CurrentServer.DRIPAddress = CurrentServer.DRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                                }


                                PRInstance = CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                                drinstance = CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                                //  CurrentDatabase.DatabaseSqlNative2008.PRInstancename = CurrentDatabase.DatabaseSqlNative2008.DRInstancename;

                                // CurrentDatabase.DatabaseSqlNative2008.DRInstancename = CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                                _sqlServerDR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                _sqlServerPR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                                if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    PRauthmode = "sql";
                                }
                                else
                                {
                                    PRauthmode = "windows";
                                }
                                if (CurrentDatabase.DatabaseSqlNative2008.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    DRauthmode = "sql";
                                }
                                else
                                {
                                    DRauthmode = "windows";
                                }
                                _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), PRauthmode);
                                _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), DRauthmode);

                            }
                            else
                            {
                                PRInstance = CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                                drinstance = CurrentDatabase.DatabaseSqlNative2008.DRInstancename;

                                AGgropName = replication.Mssqlalwayson.GroupName;
                                AGgrouprole = replication.Mssqlalwayson.GroupRole;

                                if (CurrentDatabase.DatabaseSqlNative2008.PRInstancename != string.Empty)
                                {
                                    CurrentServer.PRIPAddress = CurrentServer.PRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                                }

                                if (CurrentDatabase.DatabaseSqlNative2008.DRInstancename != string.Empty)
                                {
                                    CurrentServer.DRIPAddress = CurrentServer.DRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                                }

                                _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                                if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    PRauthmode = "sql";
                                }
                                else
                                {
                                    PRauthmode = "windows";
                                }
                                if (CurrentDatabase.DatabaseSqlNative2008.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    DRauthmode = "sql";
                                }
                                else
                                {
                                    DRauthmode = "windows";
                                }
                                _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), PRauthmode);
                                _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), DRauthmode);

                            }
                            Logger.InfoFormat(" Retrived replication AlwaysON info for Infraobject " + CurrentInfraObject.Name + ":" + " AvailbilityGroupName: " + AGgropName + "," + " Availibility Group Role: " + AGgrouprole);
                            var sqlg = new MSSQLAlwaysONClient(CurrentInfraObject);
                            if (sqlg.VerifyServerAndDatabseforsqlnative2008(_sqlServerPR, _sqlServerDR))
                            {
                                MSSQLAlwaysonMonitor _AGMonitorStatus = new MSSQLAlwaysonMonitor();

                                // MSSQLAlwaysonlogs_create
                                //  MSQLAlwaysonMonitorstatus_create

                                AvailabilityGroupSummary _avgrp = new AvailabilityGroupSummary();
                                _avgrp = MSSQLAlwaysOnActions.GetAvabilityGroup(_sqlDatabasePR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, AGgropName, PRInstance, _sqlDatabaseDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, AGgropName, drinstance);
                                Logger.InfoFormat("Retrived Alwayas on summary..");
                                //  MSSQLAlwaysonMonitor _SqlNative2008MonitorStatus = new MSSQLAlwaysonMonitor();
                                if (_avgrp != null)
                                {
                                    Logger.InfoFormat("Binding Primary Secondary alwayson  Info ..");
                                    _AGMonitorStatus.InfraObjectId = CurrentInfraObject.Id;
                                    _AGMonitorStatus.PRInstanceName = _avgrp.PRInstanceName;
                                    _AGMonitorStatus.PRAGroupname = _avgrp.PRAGroupname;
                                    _AGMonitorStatus.PRAGrouprole = _avgrp.PRAGrouprole;
                                    _AGMonitorStatus.PRReplicaMode = _avgrp.PReplicaMode;
                                    _AGMonitorStatus.PRFailovermode = _avgrp.PRFailovermode;
                                    _AGMonitorStatus.PRPSroleallowconn = _avgrp.PRPSroleallowconn;
                                    _AGMonitorStatus.PRAGOperationalstate = _avgrp.PRAGOperationalstate;
                                    _AGMonitorStatus.PRAGconnectedstate = _avgrp.PRAGconnectedstate;
                                    _AGMonitorStatus.PRDBSynchronizationState = _avgrp.PRDBSynchronizationState;
                                    _AGMonitorStatus.PRDBSynchronizationhealth = _avgrp.PRDBSynchronizationhealth;
                                    _AGMonitorStatus.PRDBstate = _avgrp.PRDBstate;
                                    _AGMonitorStatus.PRDBsynchron_state_onavaildatabase = _avgrp.PRDBsynchron_state_onavaildatabase;
                                    _AGMonitorStatus.PREndpointPortNumber = _avgrp.PREndpointPortNumber;
                                    _AGMonitorStatus.PRLastSentLSN = _avgrp.PRLastSentLSN;
                                    _AGMonitorStatus.PRLastSentTime = String.IsNullOrEmpty(_avgrp.PRLastSentTime) ? "" : Convert.ToString(_avgrp.PRLastSentTime);
                                    _AGMonitorStatus.PRLastReceivedLSN = _avgrp.PRLastReceivedLSN;
                                    _AGMonitorStatus.PRLastReceivedTime = String.IsNullOrEmpty(_avgrp.PRLastReceivedTime) ? "" : Convert.ToString(_avgrp.PRLastReceivedTime);
                                    _AGMonitorStatus.PRLastRedoneLSN = _avgrp.PRLastRedoneLSN;
                                    _AGMonitorStatus.PRLastRedoneTime = String.IsNullOrEmpty(_avgrp.PRLastRedoneTime) ? "" : Convert.ToString(_avgrp.PRLastRedoneTime);
                                    _AGMonitorStatus.PRLastCommitLSN = _avgrp.PRLastCommitLSN;
                                    _AGMonitorStatus.PRLastCommitTime = String.IsNullOrEmpty(_avgrp.PRLastCommitTime) ? "" : Convert.ToString(_avgrp.PRLastCommitTime);
                                    _AGMonitorStatus.PRLogSendQueueSizeinKB = _avgrp.LogSendQueueSizeinKB;
                                    _AGMonitorStatus.PRRedoQueueSizeinKB = _avgrp.RedoQueueSizeinKB;
                                    //DR
                                    _AGMonitorStatus.DRInstanceName = _avgrp.DRInstanceName;
                                    _AGMonitorStatus.DRAGroupname = _avgrp.DRAGroupname;
                                    _AGMonitorStatus.DRAGrouprole = _avgrp.DRAGrouprole;
                                    _AGMonitorStatus.DRReplicaMode = _avgrp.DReplicaMode;
                                    _AGMonitorStatus.DRFailovermode = _avgrp.DRFailovermode;
                                    _AGMonitorStatus.DRPSroleallowconn = _avgrp.DRPSroleallowconn;
                                    _AGMonitorStatus.DRAGOperationalstate = _avgrp.DRAGOperationalstate;
                                    _AGMonitorStatus.DRAGconnectedstate = _avgrp.DRAGconnectedstate;
                                    _AGMonitorStatus.DRDBSynchronizationState = _avgrp.DRDBSynchronizationState;
                                    _AGMonitorStatus.DRDBSynchronizationhealth = _avgrp.DRDBSynchronizationhealth;
                                    _AGMonitorStatus.DRDBstate = _avgrp.DRDBstate;
                                    _AGMonitorStatus.DRDBsynchron_state_onavaildatabase = _avgrp.DRDBsynchron_state_onavaildatabase;
                                    _AGMonitorStatus.DREndpointPortNumber = _avgrp.DREndpointPortNumber;
                                    _AGMonitorStatus.DRLastSentLSN = _avgrp.DRLastSentLSN;
                                    _AGMonitorStatus.DRLastSentTime = String.IsNullOrEmpty(_avgrp.DRLastSentTime) ? "" : Convert.ToString(_avgrp.DRLastSentTime);
                                    _AGMonitorStatus.DRLastReceivedLSN = _avgrp.DRLastReceivedLSN;
                                    _AGMonitorStatus.DRLastReceivedTime = String.IsNullOrEmpty(_avgrp.DRLastReceivedTime) ? "" : Convert.ToString(_avgrp.DRLastReceivedTime);
                                    _AGMonitorStatus.DRLastRedoneLSN = _avgrp.DRLastRedoneLSN;
                                    _AGMonitorStatus.DRLastRedoneTime = String.IsNullOrEmpty(_avgrp.DRLastRedoneTime) ? "" : Convert.ToString(_avgrp.DRLastRedoneTime);
                                    _AGMonitorStatus.DRLastCommitLSN = _avgrp.DRLastCommitLSN;
                                    _AGMonitorStatus.DRLastCommitTime = String.IsNullOrEmpty(_avgrp.DRLastCommitTime) ? "" : Convert.ToString(_avgrp.DRLastCommitTime);
                                    _AGMonitorStatus.DRLogSendQueueSizeinKB = _avgrp.LogSendQueueSizeinKB;
                                    _AGMonitorStatus.DRRedoQueueSizeinKB = _avgrp.RedoQueueSizeinKB;

                                    var prdbhealth = SqlNativeProcess.GetSQLHealth(_sqlDatabasePR);
                                    var drhealth = SqlNativeProcess.GetSQLHealth(_sqlDatabaseDR);


                                    _AGMonitorStatus.PRMSSQLEdition = prdbhealth.MSSQLServerEdition;
                                    _AGMonitorStatus.PRDatabaseSize = prdbhealth.DatabaseSize;
                                    _AGMonitorStatus.DRMSSQLEdition = drhealth.MSSQLServerEdition;
                                    _AGMonitorStatus.DRDatabaseSize = drhealth.DatabaseSize;


                                    String PRLSN = _avgrp.PRLastHardenedLSN;
                                    String DRLSN = _avgrp.DRLastHardenedLSN;
                                    TimeSpan _span = new TimeSpan();
                                    if (PRLSN == DRLSN)
                                    {
                                        _AGMonitorStatus.DataLag = Convert.ToString(_span.Add(TimeSpan.Zero));
                                    }
                                    else
                                    {
                                        var datalagoutput = "";
                                        if (_AGMonitorStatus.PRReplicaMode != null)
                                        {
                                            if ((_AGMonitorStatus.PRReplicaMode.ToLower() == "secondary" || _AGMonitorStatus.PRReplicaMode.ToLower() == "resolving") && (CurrentDatabase.DatabaseSqlNative2008.PRInstancename == _AGMonitorStatus.PRInstanceName))
                                            {
                                                datalagoutput = MSSQLAlwaysOnActions.GetDatalag(_sqlDatabasePR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, AGgropName);
                                            }
                                            else
                                            {
                                                datalagoutput = MSSQLAlwaysOnActions.GetDatalag(_sqlDatabaseDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, AGgropName);
                                            }
                                        }
                                        else
                                        {

                                            datalagoutput = MSSQLAlwaysOnActions.GetDatalag(_sqlDatabaseDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, AGgropName);


                                        }
                                        _AGMonitorStatus.DataLag = Convert.ToString(datalagoutput);
                                        //  return _span;
                                    }
                                    _AGMonitorStatus.CreateDate = DateTime.Now;
                                    _AGMonitorStatus.CreatorId = 1;
                                    _AGMonitorStatus.IsActive = true;

                                    Logger.InfoFormat(" Retrived PR MssqlAlwayson info for Infraobject " + CurrentInfraObject.Name + ":" + " PRDatabaseName: " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "," + " PRIpAddress: " + CurrentServer.PRIPAddress);
                                    Logger.InfoFormat(" Retrived PR  MssqlAlwayson Avaibility group info for Infraobject " + CurrentInfraObject.Name + ":" + "Instance name: " + " " + _AGMonitorStatus.PRInstanceName + "," + " GroupName: " + " " + _AGMonitorStatus.PRAGroupname + "," + " Group Role: " + " " + _AGMonitorStatus.PRAGrouprole);
                                    Logger.InfoFormat(" PRReplicaMode: " + " " + _AGMonitorStatus.PRReplicaMode + "," + " PRFailoveMode " + " " + _AGMonitorStatus.PRFailovermode + "," + " Primary/Secondary Role Allow connections: " + " " + _AGMonitorStatus.PRPSroleallowconn);
                                    Logger.InfoFormat("PR Availability Group Operational State: " + " " + _AGMonitorStatus.PRDBSynchronizationState + " PR Availability Group Connected State: " + " " + _AGMonitorStatus.PRAGconnectedstate);
                                    Logger.InfoFormat(" PR Database State: " + " " + _AGMonitorStatus.PRDBstate + "PR DB Suspended Reason: " + " " + _AGMonitorStatus.PRDBsynchron_state_onavaildatabase);
                                    Logger.InfoFormat(" Endpoint Port Number: " + " " + _AGMonitorStatus.PREndpointPortNumber + "Last Sent LSN (At Secondary): " + " " + _AGMonitorStatus.PRLastSentLSN);


                                    //var isSuccess = MSSQLAlwaysonMonitorDataAccess.AddalwayasonLogDetails(_AGMonitorStatus);

                                    //if (isSuccess)
                                    //{
                                    //    Logger.InfoFormat("Added Mssqlalwayson monitorstatus data", CurrentInfraObject.Name);
                                    //}

                                    var istrue = MSSQLAlwaysonMonitorDataAccess.AddalwayasonStatusDetails(_AGMonitorStatus);
                                    if (istrue)
                                    {
                                        Logger.InfoFormat("Added Mssqlalwayson logs data", CurrentInfraObject.Name);
                                    }
                                    BusinessServiceRPOInfo busRpo = new BusinessServiceRPOInfo();
                                    if (_AGMonitorStatus.DataLag != null)
                                    {
                                        Logger.InfoFormat("Binding Buisness Info details..");
                                        busRpo.BusinessFunctionId = CurrentInfraObject.BusinessFunctionId;
                                        busRpo.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                                        busRpo.InfraObjectId = CurrentInfraObject.Id;
                                        busRpo.CurrentRPO = Convert.ToString(_AGMonitorStatus.DataLag);
                                        Logger.InfoFormat("Retrived business Rpo of Mssqlalwayson Infraobject:" + CurrentInfraObject.Name, "Buisness ServiceId:" + busRpo.BusinessServiceId, "Current RPO " + busRpo.CurrentRPO);
                                        var issuccess = BusinessServiceRPOInfoDataAccess.AddBusinessServiceRPOInfo(busRpo);
                                        if (issuccess)
                                        {
                                            Logger.InfoFormat(CurrentInfraObject.Name + ":" + " Business Service RPO Populated " + " " + busRpo.CurrentRPO);
                                            Logger.InfoFormat(CurrentInfraObject.Name + ":" + " MSSqlalwayson Business Service Data inserted " + " " + busRpo.CurrentRPO);

                                        }
                                    }

                                    ProcessMSSql2k8Alerts(_sqlDatabasePR, _sqlDatabaseDR, _AGMonitorStatus);

                                }
                                else
                                {
                                    ProcessMSSql2k8Alerts(_sqlDatabasePR, _sqlDatabaseDR, _AGMonitorStatus);
                                }
                                //   Logger.InfoFormat(" Retrived PathInfo of SqlNative2008 for Infraobject " + CurrentInfraObject.Name + ":" + " PRLocalPath: " + PRLocalPath + "," + " DRLocalPath: " + DRLocalPath + "," + " PRNetworkPath: " + PRNetworkPath + "," + "DRNetworkPath:" + DRNetworkPath + "," + " SqlCmdPath " + Sqlcmdpath);
                            }
                        }
                    }
                }

            }
            catch (BcmsException exc)
            {
                Logger.Info(" Exception occured for Infraobject : " + " " + CurrentInfraObject.Name + " " + exc.Message + " " + exc.InnerException.Message);
                throw;
            }
        }

        public void Dispose()
        {
            throw new NotImplementedException();
        }
        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~MSSQLAlwaysONClient()
        {
            Dispose(false);
        }

        # region VerifyServerAndDatabse

        public bool VerifyPRServer()
        {
            if (SqlNativeIsAvailableHost(CurrentServer.PRIPAddress))
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        public bool VerifyDRServer()
        {
            if (SqlNativeIsAvailableHost(CurrentServer.DRIPAddress))
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        private bool SqlNativeIsAvailableHost(string hostName)
        {
            var ping = new System.Net.NetworkInformation.Ping();
            try
            {
                PingReply reply = ping.Send(hostName);
                return reply != null && reply.Status == IPStatus.Success;
            }
            catch (Exception ex)
            {
                Logger.Error("Exception occurred while ping host (" + hostName + ") .Message " + ex.Message);
                return false;
            }
        }

        private bool VerifyPRDatabase()
        {
            string con = string.Empty;
            if (CurrentDatabase.DatabaseSqlNative2008.PRInstancename != string.Empty)
            {
                CurrentServer.PRIPAddress = CurrentServer.PRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                con = "Server = " + CurrentServer.PRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
            }
            else
            {
                con = "Server = " + CurrentServer.PRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
            }
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify Primary SqlServer Database .Message " + e.Message);

                return false;
            }
        }

        private bool VerifyDRDatabase()
        {
            string con = string.Empty;
            if (CurrentDatabase.DatabaseSqlNative2008.DRInstancename != string.Empty)
            {
                CurrentServer.DRIPAddress = CurrentServer.DRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                con = "Server = " + CurrentServer.DRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
            }
            else
            {
                con = "Server = " + CurrentServer.DRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
            }
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message);
                return false;
            }
        }

        public bool VerifyServerAndDatabseforsqlnative2008(MSSQLServer prSSHInfo, MSSQLServer drSSHInfo)
        {
            bool prserververify = VerifyPRServer();
            bool drserververify = VerifyDRServer();
            bool prdatabaseverify = false;
            bool drdatabaseverify = false;
            string prdatabasestate;
            string drdatabasestate;
            bool prIsException = false;
            bool drIsException = false;

            VerifyServer(prSSHInfo, prserververify, drSSHInfo, drserververify);

            if (prserververify)
            {

                prdatabaseverify = VerifyPRDatabaseForsqlnative2008();
                prdatabasestate = VerifyPRDatabasechkForsqlnative2008();
                prIsException = VerifyPRDatabaseExeForsqlnative2008();
                VerifyPRDatabaseInstanceStatus(prdatabaseverify, prdatabasestate, prIsException);
            }
            if (drserververify)
            {
                drdatabaseverify = VerifyDRDatabaseForsqlnative2008();
                drdatabasestate = VerifyDRDatabasechkForsqlnative2008();
                drIsException = VerifyDRDatabaseExeForsqlnative2008();
                VerifyDRDatabaseInstanceStatus(drdatabaseverify, drdatabasestate, drIsException);
            }

            if (prserververify && drserververify && prdatabaseverify && drdatabaseverify)
            {
                return true;
            }

            return false;

            //if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
            //{
            //    bool drserververify = VerifyDRServer();
            //    bool drdatabaseverify = false;
            //    string drdatabasestate;
            //   // VerifyDRServer(drserververify);
            //    if (drserververify)
            //    {
            //        drdatabaseverify = VerifyDRDatabaseForsqlnative2008();
            //        drdatabasestate = VerifyDRDatabasechkForsqlnative2008();
            //        VerifyDRDatabaseInstanceStatus(drdatabaseverify, drdatabasestate);
            //    }
            //    if (drserververify && drdatabaseverify)
            //    {
            //        return true;
            //    }
            //}
            //else
            //{
            //    bool prserververify = VerifyPRServer();
            //    bool prdatabaseverify = false;
            //    string prdatabasestate;
            //    //VerifyPRServer(prserververify);
            //    if (prserververify)
            //    {
            //        prdatabaseverify = VerifyPRDatabaseForsqlnative2008();
            //        prdatabasestate = VerifyPRDatabasechkForsqlnative2008();
            //        VerifyPRDatabaseInstanceStatus(prdatabaseverify, prdatabasestate);
            //    }

            //    if (prserververify && prdatabaseverify)
            //    {
            //        return true;
            //    }
            //}
            //return false;
        }

        private bool VerifyPRDatabaseForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = string.Empty;
                if (CurrentDatabase.DatabaseSqlNative2008.PRInstancename != string.Empty)
                {
                    //CurrentServer.PRIPAddress = CurrentServer.PRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                    CurrentServer.PRIPAddress = CurrentServer.PRIPAddress;
                    con = "Server = " + CurrentServer.PRIPAddress + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
                }
                else
                {
                    con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
                }
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return true;
                }
                catch (Exception e)
                {
                    Logger.Error("Exception occurred while verify Primary SqlServer Database with sql authentication " + e.Message);

                    return false;
                }
            }
            else
            {
                string con = string.Empty;
                if (CurrentDatabase.DatabaseSqlNative2008.PRInstancename != string.Empty)
                {
                    //CurrentServer.PRIPAddress = CurrentServer.PRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.PRInstancename;
                    CurrentServer.PRIPAddress = CurrentServer.PRIPAddress;
                    con = "Server = " + CurrentServer.PRIPAddress + "; Database = master ; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
                }
                else
                {
                    con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = master ; Trusted_Connection=yes;Connection Timeout=45";
                }
                //  Server=*************;Database=CP;Trusted_Connection=No;Connection Timeout=45
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return true;
                }
                catch (Exception e)
                {
                    Logger.Error("Exception occurred while verify Primary SqlServer Database with windows authentication " + e.Message);

                    return false;
                }
            }
        }

        private bool VerifyDRDatabaseForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = string.Empty;
                if (CurrentDatabase.DatabaseSqlNative2008.DRInstancename != string.Empty)
                {
                    //CurrentServer.DRIPAddress = CurrentServer.DRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                    CurrentServer.DRIPAddress = CurrentServer.DRIPAddress;
                    con = "Server = " + CurrentServer.DRIPAddress + "; Database = master ; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
                }
                else
                {
                    con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master ; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
                }
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return true;
                }
                catch (Exception e)
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message);
                    return false;
                }
            }
            else
            {
                string con = string.Empty;
                if (CurrentDatabase.DatabaseSqlNative2008.DRInstancename != string.Empty)
                {
                    //CurrentServer.DRIPAddress = CurrentServer.DRIPAddress + "\\" + CurrentDatabase.DatabaseSqlNative2008.DRInstancename;
                    CurrentServer.DRIPAddress = CurrentServer.DRIPAddress;
                    con = "Server = " + CurrentServer.DRIPAddress + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
                }
                else
                {
                    con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master ;Trusted_Connection=yes;Connection Timeout=45";
                }
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return true;
                }
                catch (Exception e)
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message);
                    return false;
                }
            }
        }

        private string VerifyDRDatabasechkForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return "Database connected successfully";
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message + " ," + e.InnerException.Message);
                        return e.InnerException.Message;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return "Authentication Failured to Database !!!";
                        }
                        else
                        {
                            return e.Message;
                        }
                    }
                }
            }
            else
            {
                string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master; Trusted_Connection=yes;Connection Timeout=45";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return "Database connected successfully";
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message + " ," + e.InnerException.Message);
                        return e.InnerException.Message;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return "Authentication Failured to Database !!!";
                        }
                        else
                        {
                            return e.Message;
                        }
                    }
                }
            }
        }

        private string VerifyPRDatabasechkForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database=master; User Id= " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return "Database connected successfully !!!";
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message + " ," + e.InnerException.Message);
                        return e.InnerException.Message;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return "Authentication Failured to Database !!!";
                        }
                        else
                        {
                            return e.Message;
                        }
                    }
                }
            }
            else
            {
                string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = master;Trusted_Connection=yes;Connection Timeout=45";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return "Database connected successfully !!!";
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message + " ," + e.InnerException.Message);
                        return e.InnerException.Message;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return "Authentication Failured to Database !!!";
                        }
                        else
                        {
                            return e.Message;
                        }
                    }
                }
            }
        }

        private bool VerifyPRDatabaseExeForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return false;
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message + " ," + e.InnerException.Message);
                        return true;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return true;
                        }
                        else
                        {
                            return true;
                        }
                    }
                }
            }
            else
            {
                string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = master;Trusted_Connection=yes;Connection Timeout=45";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return false;
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message + " ," + e.InnerException.Message);
                        return true;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return true;
                        }
                        else
                        {
                            return true;
                        }
                    }
                }
            }
        }

        private bool VerifyDRDatabaseExeForsqlnative2008()
        {
            if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return false;
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message + " ," + e.InnerException.Message);
                        return true;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with sql authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return true;
                        }
                        else
                        {
                            return true;
                        }
                    }
                }
            }
            else
            {
                string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = master;Trusted_Connection=yes;Connection Timeout=45";
                SqlConnection cn = new SqlConnection(con);
                try
                {
                    cn.Open();
                    cn.Close();
                    return false;
                }
                catch (Exception e)
                {
                    if (e.InnerException != null)
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message + " ," + e.InnerException.Message);
                        return true;
                    }
                    else
                    {
                        Logger.Error("Exception occurred while verify DR SqlServer Database with windows authentication " + e.Message);
                        if (e.Message.Contains("Login failed"))
                        {
                            return true;
                        }
                        else
                        {
                            return true;
                        }
                    }
                }
            }
        }



        private void VerifyPRDatabaseInstanceStatus(bool verifyprdatabase, string Prdbexception, bool IsException)
        {
            string databasename = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName;
            int alertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRDBcheck : (int)AlertNotificationType.PRDBcheck;

            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (verifyprdatabase)
            {

                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseUP,
                                                              "InfraObject Name : " + CurrentInfraObject.Name +
                                                              " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) +
                                                              " Primary Database Instance: " + databasename + " is in Online State "), "MonitorPRDatabaseStatus",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);

                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Up.ToString(),
                        IsAffected = false
                    });

                    string ResolveMessage = CurrentInfraObject.Name + " : Production Database (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName) + ") is up";
                    IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId, 0, ResolveMessage);

                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));

                    if (IsException)
                    {
                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " , " + " Cannot access database " + " " + databasename + " " + "on server" + " " + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + " " + "due to Reason :" + " " + Prdbexception), "MonitorPRDatabaseStatus",
                                                CurrentInfraObject.Id, CurrentInfraObject.Name);
                    }
                    else
                    {

                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown,
                                                            "InfraObject Name : " + CurrentInfraObject.Name +
                                                            " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) +
                                                            " Primary Database Instance: " + databasename + " is in Offline State"), "MonitorPRDatabaseStatus",
                                          CurrentInfraObject.Id, CurrentInfraObject.Name);
                    }
                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Down.ToString(),
                        IsAffected = true
                    });

                    IncidentManagement inc = new IncidentManagement
                    {
                        IncidentName = "CP-Inc_DB_down",
                        IncidentTime = System.DateTime.Now,
                        InfraID = CurrentInfraObject.Id,
                        InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        InfraComponentType = "Database",
                        IncidentComment = "Database is Down"

                    };
                    IncidentManagementDataAccess.AddIncident(inc);
                }
            }

        }

        private void VerifyDRDatabaseInstanceStatus(bool verifydrdatabase, string drdbexception)
        {
            string databasename = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName;
            int alertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (verifydrdatabase)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseUP,
                                       "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + " DR Database Instance: " + databasename + " is Up "), "",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);


                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Up.ToString(),
                        IsAffected = false
                    });

                    //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                    //{
                    //    InfraObjectId = CurrentInfraObject.Id,
                    //    EntityId = CurrentDatabase.DRDBId,
                    //    Status = 0,
                    //    ResolveMessage = CurrentInfraObject.Name + " : DR Database (" + databasename + ") is up"
                    //});

                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));



                    //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + CurrentServer.DRHostName + " Cannot access database :" + databasename + "on server" + CurrentServer.DRIPAddress + " " + "due to Reason :" + drdbexception), "MonitorDRDatabaseStatus",
                    //                   CurrentInfraObject.Id, CurrentInfraObject.Name);

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " , " + " Cannot access database " + " " + databasename + " " + "on server" + " " + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + " " + "due to Reason :" + " " + drdbexception), "MonitorPRDatabaseStatus",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);




                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Down.ToString(),
                        IsAffected = true
                    });

                    //ImpactAnalysisDataAccess.AddImpactAnalysis(new ImpactAnalysis
                    //{
                    //    InfraObjectId = CurrentInfraObject.Id,
                    //    EntityId = CurrentDatabase.DRDBId,
                    //    ImpactType = ImpactType.DatabaseDown.ToString(),
                    //    Status = 1,
                    //    ImpactMessage = "Database is Down "
                    //});
                }
            }
        }

        private void VerifyDRDatabaseInstanceStatus(bool verifydrdatabase, string drdbexception, bool IsException)
        {
            string databasename = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName;
            int alertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (verifydrdatabase)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseUP,
                                       "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + " DR Database Instance: " + databasename + " is in Online State "), "MonitorDRDatabaseStatus",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);


                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Up.ToString(),
                        IsAffected = false
                    });



                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));



                    //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + CurrentServer.DRHostName + " Cannot access database :" + databasename + "on server" + CurrentServer.DRIPAddress + " " + "due to Reason :" + drdbexception), "MonitorDRDatabaseStatus",
                    //                   CurrentInfraObject.Id, CurrentInfraObject.Name);
                    if (IsException)
                    {

                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " , " + " Cannot access database " + " " + databasename + " " + "on server" + " " + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + " " + "due to Reason :" + " " + drdbexception), "MonitorPRDatabaseStatus",
                        CurrentInfraObject.Id, CurrentInfraObject.Name);
                    }
                    else
                    {
                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown,
                                                             "InfraObject Name : " + CurrentInfraObject.Name +
                                                             " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) +
                                                             " DR Database Instance: " + databasename + " is in Offline State"), "MonitorDRDatabaseStatus",
                                           CurrentInfraObject.Id, CurrentInfraObject.Name);
                    }


                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Down.ToString(),
                        IsAffected = true
                    });


                }
            }
        }

        private void VerifyServer(MSSQLServer prSSHInfo, bool verifyprserver, MSSQLServer drSSHInfo, bool verifydrserver)
        {
            using (BCMS.Core.Utility.SSHHelper SSHHelper = new BCMS.Core.Utility.SSHHelper())
            {
                try
                {
                    string authmode = string.Empty;
                    if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                    {
                        authmode = "sql";
                    }
                    else
                    {
                        authmode = "windows";
                    }
                    MSSQLServer _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    MSSQLDatabase _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), authmode);

                    MSSQLServer _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                    MSSQLDatabase _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), authmode);

                    int serverAlertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRServer : (int)AlertNotificationType.PRServer;

                    int serverAlertType1 = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRServer : (int)AlertNotificationType.DRServer;

                    var sentServerAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType, CurrentInfraObject.Id);

                    var sentServerAlertCount1 = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType1, CurrentInfraObject.Id);


                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, "PR"))
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                                                                           "InfraObject Name : " + CurrentInfraObject.Name + " Primary Server (" +
                                                                          (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? drSSHInfo.Host : prSSHInfo.Host) + ") is connected."), "MSSQL2kXServerStatus", CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            string ResolveMessage = CurrentInfraObject.Name + " : Production Server (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId) + ") is connected";
                            IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, 0, ResolveMessage);
                        }
                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, true, CurrentInfraObject.Id);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable,
                                                                      "InfraObject Name : " + CurrentInfraObject.Name +
                                                                      " Primary Server (" +
                                                                    (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? drSSHInfo.Host : prSSHInfo.Host) + ") is not reachable."), "MSSQL2kXServerStatus",
                                                    CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });

                            IncidentManagement inc = new IncidentManagement
                            {
                                IncidentName = "CP-Inc_Server_down",
                                IncidentTime = System.DateTime.Now,
                                InfraID = CurrentInfraObject.Id,
                                InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                InfraComponentType = "Server",
                                IncidentComment = "Server Not Reachable"

                            };
                            IncidentManagementDataAccess.AddIncident(inc);
                        }
                    }
                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, "DR"))
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount1 > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                                                                        "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                                                                       (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? prSSHInfo.Host : drSSHInfo.Host) + ") is connected."), "MonitorMSSQL2kXServerStatus",
                                                   CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });


                        }
                    }
                    else
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount1)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, true, CurrentInfraObject.Id);

                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                                                    (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? prSSHInfo.Host : drSSHInfo.Host) + ") is not reachable."), "MSSQL2kXServerStatus",
                                                    CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });


                        }
                    }

                }
                catch (BcmsException exc)
                {
                    throw;
                }
                catch (Exception exc)
                {
                    ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 0);
                    throw new BcmsException(BcmsExceptionType.CommonUnhandled, string.Format("{0} : Exception occured while Connecting Server", CurrentInfraObject.Name), exc);
                }
            }
        }

        #endregion


        private void ProcessMSSql2k8Alerts(MSSQLDatabase _mssqlPRDB, MSSQLDatabase _mssqlDRDB, MSSQLAlwaysonMonitor _sqlnativeMonitorStatus)
        {
            #region Mssqlalwayson PR DB State
            if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "online")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Online State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "offline")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in offline State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "shutdown")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Shutdown State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "restoring")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Restoring State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "recovering")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Recovering State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "recovery pending")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Recovery Pending State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "suspect")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Suspect State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBstate.ToLower() == "emergency")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQL alwayson PR Database State is in Emergency State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonPRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
            }

            #endregion

            #region Mssqlalwayson DR DataBase State
            if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "online")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson DR Database State is in Online State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "offline")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson DR Database State is in offline State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "shutdown")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson DR Database State is in Shutdown State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "restoring")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson DR Database State is in Restoring State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "recovering")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson DR Database State is in Recovering State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "recovery pending")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson Database State is in Recovery Pending State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "suspect")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson Database State is in Suspect State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBstate.ToLower() == "emergency")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQL alwayson Database State is in Emergency State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlalwaysonDRDBstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
            }

            #endregion

            #region Database PR syncstate
            //   Database Synchronization State (NOT SYNCHRONIZING / SYNCHRONIZING / SYNCHRONIZED / REVERTING / INITIALIZING)

            if (_sqlnativeMonitorStatus.PRDBSynchronizationState.ToLower() == "not synchronizing")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is not synchronizing State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysonprdbsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLalwaysonDBntsyncState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBSynchronizationState.ToUpper() == "SYNCHRONIZING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is SYNCHRONIZING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysonprdbsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLalwaysonDBsyncState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBSynchronizationState.ToUpper() == "SYNCHRONIZED")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in SYNCHRONIZED State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysonprdbsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLalwaysonDBsynchronized, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBSynchronizationState.ToUpper() == "REVERTING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in REVERTING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysonprdbsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateReverting, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.PRDBSynchronizationState.ToUpper() == "INITIALIZING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in INITIALIZING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysonprdbsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateintializing, serviceAlertType);
            }
            #endregion

            #region Database DR syncstate

            if (_sqlnativeMonitorStatus.DRDBSynchronizationState.ToLower() == "not synchronizing")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase State is in not synchronizing State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysondrsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLalwaysonDBntsyncState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBSynchronizationState.ToUpper() == "SYNCHRONIZING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase State is in SYNCHRONIZING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysondrsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLalwaysonDBsyncState, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBSynchronizationState.ToUpper() == "SYNCHRONIZED")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase State is in SYNCHRONIZED State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysondrsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLalwaysonDBsynchronized, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBSynchronizationState.ToUpper() == "REVERTING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase State is in REVERTING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysondrsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateReverting, serviceAlertType);
            }
            else if (_sqlnativeMonitorStatus.DRDBSynchronizationState.ToUpper() == "INITIALIZING")
            {
                string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase State is in INITIALIZING State;";
                int serviceAlertType = (int)AlertNotificationType.Mssqlalwaysondrsyncstate;
                ProcessNativeAlerts(_sqlnativeMonitorStatus, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateintializing, serviceAlertType);
            }
            #endregion
        }

        private void ProcessNativeAlerts(MSSQLAlwaysonMonitor _sqlnativeMonitorStatus, string PRServiceAlertmessage, string type, int status, BcmsExceptionType exceptiontype, int serviceAlertType)
        {
            //int serviceAlertType = string.Equals("PR", type) ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

            var sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Service), serviceAlertType, _sqlnativeMonitorStatus.InfraObjectId);
            if (status == 1)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, false,
                        _sqlnativeMonitorStatus.InfraObjectId);

                    ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqlAlwaysOnMonitor", _sqlnativeMonitorStatus.InfraObjectId, CurrentInfraObject.Name);
                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, true,
                        _sqlnativeMonitorStatus.InfraObjectId);
                    ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqlAlwaysOnMonitor", _sqlnativeMonitorStatus.InfraObjectId, CurrentInfraObject.Name);

                }
            }
        }
    }
}
