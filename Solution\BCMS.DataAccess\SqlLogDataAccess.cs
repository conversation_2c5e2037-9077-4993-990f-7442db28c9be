﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Utility;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using System.IO;

namespace Bcms.DataAccess
{
    public class SqlLogDataAccess:BaseDataAccess
    {
        public static string GetMaxLogFileName(int groupId)
        {
            string logFileName = string.Empty;
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLLOGGEN_GETNEXTLOGNAME"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("SqlLogCur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            logFileName = mySqlLogReader[0].ToString();
                        }
                    }
                }
                return logFileName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature GetMaxLogFileName for group Id(" + groupId + ")", exc);

            }
        }

        public static SqlLog GetLastLogs(int groupId, bool isApplied)
        {
            var sqlLog = new SqlLog();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLLOGGENERATION_GETLASTLOG"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
                    Database.AddInParameter(dbCommand, Dbstring+"isApplied", DbType.Boolean, isApplied);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("SqlLogCur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            if (isApplied)
                            {
                                sqlLog.Id = Convert.ToInt32(mySqlLogReader[0]);
                                sqlLog.LogFileName = mySqlLogReader[1].ToString();
                                sqlLog.DRSequenceNo = mySqlLogReader[2].ToString();
                                sqlLog.LogAppliedTime = Convert.ToDateTime(mySqlLogReader[3]);
                            }
                            else
                            {
                                sqlLog.Id = Convert.ToInt32(mySqlLogReader[0]);
                                sqlLog.LogFileName = mySqlLogReader[1].ToString();
                                sqlLog.PRSequenceNo = mySqlLogReader[2].ToString();
                                sqlLog.LogGenerationTime = Convert.ToDateTime(mySqlLogReader[3]);
                                sqlLog.IsApplied = Convert.ToInt32(mySqlLogReader[4]);
                                sqlLog.InfraObjectId = Convert.ToInt32(mySqlLogReader[5]);
                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature GetLastLogs for group Id(" + groupId + ")", exc);
            }
            return sqlLog;
        }

        public static List<SqlLog> GetApplyLogs(int groupId)
        {
            var applyLog = new List<SqlLog>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLLOGGENERATION_GETAPPLYLOG"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("SqlLogCur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            var sqlLog = new SqlLog
                            {
                                Id = Convert.ToInt32(mySqlLogReader[0]),
                                LogFileName = mySqlLogReader[1].ToString(),
                                PRSequenceNo = mySqlLogReader[2].ToString(),
                                LogGenerationTime = Convert.ToDateTime(mySqlLogReader[3]),
                                IsApplied = Convert.ToInt32(mySqlLogReader[4]),
                                InfraObjectId = Convert.ToInt32(mySqlLogReader[5])
                            };

                            applyLog.Add(sqlLog);
                        }
                    }
                }
                return applyLog;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while executing function signature GetApplyLogs for group Id(" + groupId + ")", exc);
            }
        }

        public static bool AddSqlLogGenerate(SqlLog sqlLog)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("SQLLOGGENERATION_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, sqlLog.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPRSequenceNo", DbType.AnsiString, sqlLog.PRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlLog.InfraObjectId);
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogGeneration entry ", exc);
            }
        }

        public static bool AddSqlLogApplied(SqlLog sqlLog)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLLOGAPPLIED_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, sqlLog.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iDRSequenceNo", DbType.AnsiString, sqlLog.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlLog.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, sqlLog.Id);
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlLogApplied entry ", exc);
            }
        }

        public static bool AddSqlLogDetails(SqlLog sqlLog)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SqlLogDetails_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, sqlLog.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPRSequenceNo", DbType.AnsiString, sqlLog.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRSequenceNo", DbType.AnsiString, sqlLog.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iLogGenerationTime", DbType.DateTime, sqlLog.LogGenerationTime);
                    Database.AddInParameter(cmd, Dbstring+"iLogAppliedTime", DbType.DateTime, sqlLog.LogAppliedTime);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag", DbType.AnsiString, sqlLog.DataLag);
                    Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, sqlLog.InfraObjectId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlLogDetails entry ", exc);
            }

        }

        public static bool UpdateSqlLogGenerate(int id)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLLOGGENERATION_UPDATEBYID"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while while updating SqlLogGenerate entry By Id (" + id + ")", exc);
            }
        }

        public static bool CreateHealthMonitor(SqlServer2000 sqlserver2000)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQL2000_HEALTH_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlserver2000.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iDBEdition_PR", DbType.AnsiString, sqlserver2000.DBEdition_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBServicePack_PR", DbType.AnsiString, sqlserver2000.DBServicePack_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBInstance_PR", DbType.AnsiString, sqlserver2000.DBInstance_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBState_PR", DbType.AnsiString, sqlserver2000.DBState_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDatabase_PR", DbType.AnsiString, sqlserver2000.Database_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBRecoveryMode_PR", DbType.AnsiString, sqlserver2000.DBRecoveryMode_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBRestrictAccess_PR", DbType.AnsiString, sqlserver2000.DBRestrictAccess_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBUpdateability_PR", DbType.AnsiString, sqlserver2000.DBUpdateability_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBSize_PR", DbType.AnsiString, sqlserver2000.DBSize_PR);
                    Database.AddInParameter(cmd, Dbstring+"iDBEdition_DR", DbType.AnsiString, sqlserver2000.DBEdition_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBServicePack_DR", DbType.AnsiString, sqlserver2000.DBServicePack_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBInstance_DR", DbType.AnsiString, sqlserver2000.DBInstance_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBState_DR", DbType.AnsiString, sqlserver2000.DBState_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDatabase_DR", DbType.AnsiString, sqlserver2000.Database_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBRecoveryModel_DR", DbType.AnsiString, sqlserver2000.DBRecoveryMode_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBRestrictAccess_DR", DbType.AnsiString, sqlserver2000.DBRestrictAccess_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBUpdateability_DR", DbType.AnsiString, sqlserver2000.DBUpdateability_DR);
                    Database.AddInParameter(cmd, Dbstring+"iDBSize_DR", DbType.AnsiString, sqlserver2000.DBSize_DR);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, sqlserver2000.CreatorId);
                    /*Database.AddInParameter(cmd, Dbstring+"iCreateDate", DbType.DateTime, sqlserver2000.CreateDate);*/
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Sql200Cur"));
#endif
                    
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SQL2000 health entry ", exc);
            }

        }

        public static bool CreateLogMonitor(SqlServer2000Log sqlserverlog)
        {
            try
            {
                
                using (DbCommand cmd = Database.GetStoredProcCommand("SQL2000LOG_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlserverlog.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iLastBackUpFile", DbType.AnsiString, sqlserverlog.LastBackUpFile);
                    Database.AddInParameter(cmd, Dbstring+"iLastBackUpTime", DbType.AnsiString, sqlserverlog.LastBackUpTime);
                    Database.AddInParameter(cmd, Dbstring+"iLastBackUpLSN", DbType.AnsiString, sqlserverlog.LastBackUpLSN);
                    Database.AddInParameter(cmd, Dbstring+"iLastCopyFile", DbType.AnsiString, sqlserverlog.LastCopyFile);
                    Database.AddInParameter(cmd, Dbstring+"iLastCopyTime", DbType.AnsiString, sqlserverlog.LastCopyTime);
                    Database.AddInParameter(cmd, Dbstring+"iLastCopyLSN", DbType.AnsiString, sqlserverlog.LastCopyLSN);
                    Database.AddInParameter(cmd, Dbstring+"iLastRestoreFile", DbType.AnsiString, sqlserverlog.LastRestoreFile);
                    Database.AddInParameter(cmd, Dbstring+"iLastRestoreTime", DbType.AnsiString, sqlserverlog.LastRestoreTime);
                    Database.AddInParameter(cmd, Dbstring+"iLastRestoreLSN", DbType.AnsiString, sqlserverlog.LastRestoreLSN);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag", DbType.AnsiString, sqlserverlog.DataLag);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, sqlserverlog.CreatorId);
                  
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SQL2000 Log entry ", exc);
            }

        }

        public static SqlLog GetLastCopiedLogs(int groupid)
        {
            try
            {
                SqlLog sql = new SqlLog();
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQL2000LASTCOPIEDLOG_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iJobId", DbType.Int32, groupid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Sql200Cur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            string File = "";
                            
                            if(!string.IsNullOrEmpty(mySqlLogReader[6].ToString()))
                            {
                             FileInfo info=new FileInfo(mySqlLogReader[6].ToString());
                             File = info.Name;
                            }
                            sql.CopiedFile = string.IsNullOrEmpty(mySqlLogReader[6].ToString()) ? "" : File;
                            sql.LogCopiedTime = Convert.ToDateTime(mySqlLogReader[12]);
                        }
                    }
                }
                return sql;
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while Reading SQL2000 Copied Log entry ", ex);
            }

        }

        public static bool CreateServiceMonitor(SqlServer2000Service services)
        {
            try
            {
                
                using (DbCommand cmd = Database.GetStoredProcCommand("SQL2000SERVICE_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, services.GroupId);
                    Database.AddInParameter(cmd, Dbstring+"iServer", DbType.Int32, services.Server);
                    Database.AddInParameter(cmd, Dbstring+"iServiceName", DbType.AnsiString, services.ServiceName);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, services.Status);
                    Database.AddInParameter(cmd, Dbstring+"iStart_Mode", DbType.AnsiString, services.Start_Mode);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, services.CreatorId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SQL2000 Service entry ", exc);
            }

        }

        public static SqlLog GetCopiedLogs(int groupId)
        {
            SqlLog sqlLog = new SqlLog();
            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLLOGGEN_GETLASTCOPIEDLOG"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Sql200Cur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            sqlLog.Id = Convert.ToInt32(mySqlLogReader["Id"]);
                            sqlLog.CopiedFile = mySqlLogReader["LastFileName"].ToString();
                            sqlLog.LogCopiedTime = Convert.ToDateTime(mySqlLogReader["UpdateDate"]);
                            sqlLog.InfraObjectId = Convert.ToInt32(mySqlLogReader["JobId"]);
                        }
                    }
                }
                return sqlLog;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while executing function signature GetCopiedLogs for group Id(" + groupId + ")", exc);
            }
        }
    }
}