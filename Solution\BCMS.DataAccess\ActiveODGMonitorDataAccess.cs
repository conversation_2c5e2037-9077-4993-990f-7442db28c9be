﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using System.Collections.Generic;


namespace Bcms.DataAccess
{
    public class ActiveODGMonitorDataAccess : BaseDataAccess
    {
        public static bool ActiveODGDatabaseDetails(ActiveODGDatabase objActiveODGDatabase)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODG_DBLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.String, objActiveODGDatabase.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_NAME", DbType.String, objActiveODGDatabase.Database_name);
                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_UNIQUENAME", DbType.String, objActiveODGDatabase.Database_uniquename);
                    Database.AddInParameter(cmd, Dbstring + "iINSTANCEID", DbType.String, objActiveODGDatabase.Instanceid);
                    Database.AddInParameter(cmd, Dbstring + "IINSTANCENAME", DbType.String, objActiveODGDatabase.Instancename);
                    Database.AddInParameter(cmd, Dbstring + "iINSTANCE_STARTUPTIME", DbType.DateTime, objActiveODGDatabase.Instance_startuptime);

                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_CREATEDTIME", DbType.DateTime, objActiveODGDatabase.Database_createdtime);
                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_VERSION", DbType.String, objActiveODGDatabase.Database_version);
                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_ROLE", DbType.String, objActiveODGDatabase.Database_role);
                    Database.AddInParameter(cmd, Dbstring + "iOPENMODE", DbType.String, objActiveODGDatabase.Openmode);
                    Database.AddInParameter(cmd, Dbstring + "iDATABASE_INCARNATION", DbType.String, objActiveODGDatabase.Database_incarnation);

                    Database.AddInParameter(cmd, Dbstring + "iRESET_LOGSCHANGE", DbType.String, objActiveODGDatabase.Reset_logschange);
                    Database.AddInParameter(cmd, Dbstring + "iRESET_LOGSMODE", DbType.String, objActiveODGDatabase.Reset_logsmode);
                    Database.AddInParameter(cmd, Dbstring + "iCONTROL_FILETYPE", DbType.String, objActiveODGDatabase.Control_filetype);
                    //Database.AddInParameter(cmd, Dbstring + "iCONTROL_FILENAME", DbType.String, objActiveODGDatabase.Control_filename);
                    Database.AddInParameter(cmd, Dbstring + "iCURRENTSCN", DbType.String, objActiveODGDatabase.Currentscn);

                    //Database.AddInParameter(cmd, Dbstring + "iPARAMETERFILE", DbType.String, objActiveODGDatabase.Parameterfile);
                    Database.AddInParameter(cmd, Dbstring + "iARCHIVE_MODE", DbType.String, objActiveODGDatabase.Archive_mode);
                    Database.AddInParameter(cmd, Dbstring + "iFLASHBACK_ON", DbType.String, objActiveODGDatabase.Flashback_on);
                    //Database.AddInParameter(cmd, Dbstring + "iPLATFORM_NAME", DbType.String, objActiveODGDatabase.Platform_name);
                    Database.AddInParameter(cmd, Dbstring + "iDBSIZE", DbType.String, objActiveODGDatabase.Dbsize);

                    Database.AddInParameter(cmd, Dbstring + "iDB_CREATE_FILE_DEST", DbType.String, objActiveODGDatabase.Db_create_file_dest);
                    Database.AddInParameter(cmd, Dbstring + "iDB_FILE_NAME_CONVERT", DbType.String, objActiveODGDatabase.Db_file_name_convert);
                    Database.AddInParameter(cmd, Dbstring + "iDB_CREATE_ONLINE_LOG_DEST1", DbType.String, objActiveODGDatabase.Db_create_online_log_dest1);
                    Database.AddInParameter(cmd, Dbstring + "iLOG_FILE_NAME_CONVERT", DbType.String, objActiveODGDatabase.Log_file_name_convert);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RECOVERY_FILE_DEST", DbType.String, objActiveODGDatabase.Db_recovery_file_dest);

                    Database.AddInParameter(cmd, Dbstring + "iDB_RECOVERY_FILE_DEST_SIZE", DbType.String, objActiveODGDatabase.Db_recovery_file_dest_size);
                    Database.AddInParameter(cmd, Dbstring + "iDB_FLASHBACK_RETENTION_TARGET", DbType.String, objActiveODGDatabase.Db_flashback_retention_target);
                    Database.AddInParameter(cmd, Dbstring + "iSERVICES", DbType.String, objActiveODGDatabase.Services);
                    Database.AddInParameter(cmd, Dbstring + "iPRDRTYPE", DbType.String, objActiveODGDatabase.PRDRType);
                    Database.AddInParameter(cmd, Dbstring + "iCREATORID", DbType.String, objActiveODGDatabase.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, objActiveODGDatabase.GroupNodeId);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess information", exc);
            }
        }

        public static bool ActiveODGReplicationDetails(ActiveODGReplication activeODGReplication)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGRepliLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.String, activeODGReplication.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iACTIVE_DG_ENABLED", DbType.String, activeODGReplication.Active_DG_Enabled);
                    Database.AddInParameter(cmd, Dbstring + "iARCHIVE_DEST_LOCATION", DbType.String, activeODGReplication.Archive_Dest_Location);
                    Database.AddInParameter(cmd, Dbstring + "iPROTECTION_MODE", DbType.String, activeODGReplication.Protection_mode);
                    Database.AddInParameter(cmd, Dbstring + "iTRANSMIT_MODE", DbType.String, activeODGReplication.Transmit_mode);
                    Database.AddInParameter(cmd, Dbstring + "iRECOVERY_MODE", DbType.String, activeODGReplication.Recovery_mode);

                    Database.AddInParameter(cmd, Dbstring + "iAFFIRM", DbType.String, activeODGReplication.Affirm);
                    Database.AddInParameter(cmd, Dbstring + "iARCHIVER", DbType.String, activeODGReplication.Archiver);
                    Database.AddInParameter(cmd, Dbstring + "iARCHIVELOG_COMPRESSION", DbType.String, activeODGReplication.Archivelog_compression);
                    Database.AddInParameter(cmd, Dbstring + "iDELAY_MINS", DbType.String, activeODGReplication.Delay_mins);
                    Database.AddInParameter(cmd, Dbstring + "iLOG_SEQUENCE", DbType.String, activeODGReplication.Log_sequence);

                    Database.AddInParameter(cmd, Dbstring + "iDG_BROKER_STATUS", DbType.String, activeODGReplication.Dg_broker_status);
                    Database.AddInParameter(cmd, Dbstring + "iREMOTE_LOGIN_PASSWORDFILE", DbType.String, activeODGReplication.Remote_login_passwordfile);
                    Database.AddInParameter(cmd, Dbstring + "iSTANDBY_FILE_MANAGEMENT", DbType.String, activeODGReplication.Standby_file_management);
                    Database.AddInParameter(cmd, Dbstring + "iSTANDBY_REDO_LOGS", DbType.String, activeODGReplication.Standby_redo_logs);
                    Database.AddInParameter(cmd, Dbstring + "iTRANSPORT_LAG", DbType.String, activeODGReplication.Transport_lag);

                    Database.AddInParameter(cmd, Dbstring + "iAPPLY_LAG", DbType.String, activeODGReplication.Apply_lag);
                    Database.AddInParameter(cmd, Dbstring + "iAPPLY_FINISH_TIME", DbType.String, activeODGReplication.Apply_finish_time);
                    Database.AddInParameter(cmd, Dbstring + "iESTIMATED_STARTUP_TIME", DbType.String, activeODGReplication.Estimated_startup_time);
                    Database.AddInParameter(cmd, Dbstring + "iFORCE_LOGGING", DbType.String, activeODGReplication.Force_logging);
                    //Database.AddInParameter(cmd, Dbstring + "iArchive_generation_hourly", DbType.String, activeODGReplication.Archive_generation_hourly);

                    Database.AddInParameter(cmd, Dbstring + "iSWITCHOVER_STATUS", DbType.String, activeODGReplication.Switchover_status);
                    Database.AddInParameter(cmd, Dbstring + "iLOG_ARCHIVE_CONFIG", DbType.String, activeODGReplication.Log_archive_config);
                    Database.AddInParameter(cmd, Dbstring + "iFAL_SERVER", DbType.String, activeODGReplication.Fal_server);
                    Database.AddInParameter(cmd, Dbstring + "iFAL_CLIENT", DbType.String, activeODGReplication.Fal_client);
                    Database.AddInParameter(cmd, Dbstring + "iDATAGUARD_STATUS", DbType.String, activeODGReplication.Dataguard_status);

                    Database.AddInParameter(cmd, Dbstring + "iPRDRTYPE", DbType.String, activeODGReplication.PRDRTYPE);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.String, activeODGReplication.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.String, activeODGReplication.GroupNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iThreadId", DbType.String, activeODGReplication.GroupNodeId);

                    Database.AddInParameter(cmd, Dbstring + "iRecoveryStatus", DbType.String, activeODGReplication.RecoverStatus);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess information", exc);
            }
        }

        public static bool ActiveODGReplicationNewDetails(ActiveODGReplicationNew activeODGReplication)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("aodgrepli_logs_create"))
                {


                    Database.AddInParameter(cmd, Dbstring + "iPRACTIVE_DG_ENABLED", DbType.String, activeODGReplication.PRActive_DG_Enabled);
                    Database.AddInParameter(cmd, Dbstring + "iPRARCHIVE_DEST_LOCATION", DbType.String, activeODGReplication.PRArchive_Dest_Location);
                    Database.AddInParameter(cmd, Dbstring + "iPRPROTECTION_MODE", DbType.String, activeODGReplication.PRProtection_mode);
                    Database.AddInParameter(cmd, Dbstring + "iPRTRANSMIT_MODE", DbType.String, activeODGReplication.PRTransmit_mode);
                    Database.AddInParameter(cmd, Dbstring + "iPRRECOVERY_MODE", DbType.String, activeODGReplication.PRRecovery_mode);

                    Database.AddInParameter(cmd, Dbstring + "iPRAFFIRM", DbType.String, activeODGReplication.PRAffirm);
                    Database.AddInParameter(cmd, Dbstring + "iPRARCHIVER", DbType.String, activeODGReplication.PRArchiver);
                    Database.AddInParameter(cmd, Dbstring + "iPRARCHIVELOG_COMPRESSION", DbType.String, activeODGReplication.PRArchivelog_compression);
                    Database.AddInParameter(cmd, Dbstring + "iPRDELAY_MINS", DbType.String, activeODGReplication.PRDelay_mins);
                    Database.AddInParameter(cmd, Dbstring + "iPRLOG_SEQUENCE", DbType.String, activeODGReplication.PRLog_sequence);

                    Database.AddInParameter(cmd, Dbstring + "iPRDG_BROKER_STATUS", DbType.String, activeODGReplication.PRDg_broker_status);
                    Database.AddInParameter(cmd, Dbstring + "iPRREMOTE_LOGIN_PASSWORDFILE", DbType.String, activeODGReplication.PRRemote_login_passwordfile);
                    Database.AddInParameter(cmd, Dbstring + "iPRSTANDBY_FILE_MANAGEMENT", DbType.String, activeODGReplication.PRStandby_file_management);
                    Database.AddInParameter(cmd, Dbstring + "iPRSTANDBY_REDO_LOGS", DbType.String, activeODGReplication.PRStandby_redo_logs);
                    Database.AddInParameter(cmd, Dbstring + "iPRTRANSPORT_LAG", DbType.String, activeODGReplication.PRTransport_lag);

                    Database.AddInParameter(cmd, Dbstring + "iPRAPPLY_LAG", DbType.String, activeODGReplication.PRApply_lag);
                    Database.AddInParameter(cmd, Dbstring + "iPRAPPLY_FINISH_TIME", DbType.String, activeODGReplication.PRApply_finish_time);
                    Database.AddInParameter(cmd, Dbstring + "iPRESTIMATED_STARTUP_TIME", DbType.String, activeODGReplication.PREstimated_startup_time);
                    Database.AddInParameter(cmd, Dbstring + "iPRFORCE_LOGGING", DbType.String, activeODGReplication.PRForce_logging);
                    //Database.AddInParameter(cmd, Dbstring + "iArchive_generation_hourly", DbType.String, activeODGReplication.Archive_generation_hourly);
                    //--
                    Database.AddInParameter(cmd, Dbstring + "iPRSWITCHOVER_STATUS", DbType.String, activeODGReplication.PRSwitchover_status);
                    Database.AddInParameter(cmd, Dbstring + "iPRLOG_ARCHIVE_CONFIG", DbType.String, activeODGReplication.PRLog_archive_config);
                    Database.AddInParameter(cmd, Dbstring + "iPRFAL_SERVER", DbType.String, activeODGReplication.PRFal_server);
                    Database.AddInParameter(cmd, Dbstring + "iPRFAL_CLIENT", DbType.String, activeODGReplication.PRFal_client);
                    Database.AddInParameter(cmd, Dbstring + "iPRDATAGUARD_STATUS", DbType.String, activeODGReplication.PRDataguard_status);


                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, 1);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, activeODGReplication.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, activeODGReplication.GroupNodeId);
                    //DR 

                    Database.AddInParameter(cmd, Dbstring + "iDRACTIVE_DG_ENABLED", DbType.String, activeODGReplication.DRActive_DG_Enabled);
                    Database.AddInParameter(cmd, Dbstring + "iDRARCHIVE_DEST_LOCATION", DbType.String, activeODGReplication.DRArchive_Dest_Location);
                    Database.AddInParameter(cmd, Dbstring + "iDRPROTECTION_MODE", DbType.String, activeODGReplication.DRProtection_mode);
                    Database.AddInParameter(cmd, Dbstring + "iDRTRANSMIT_MODE", DbType.String, activeODGReplication.DRTransmit_mode);
                    Database.AddInParameter(cmd, Dbstring + "iDRRECOVERY_MODE", DbType.String, activeODGReplication.DRRecovery_mode);

                    Database.AddInParameter(cmd, Dbstring + "iDRAFFIRM", DbType.String, activeODGReplication.DRAffirm);
                    Database.AddInParameter(cmd, Dbstring + "iDRARCHIVER", DbType.String, activeODGReplication.DRArchiver);
                    Database.AddInParameter(cmd, Dbstring + "iDRARCHIVELOG_COMPRESSION", DbType.String, activeODGReplication.DRArchivelog_compression);
                    Database.AddInParameter(cmd, Dbstring + "iDRDELAY_MINS", DbType.String, activeODGReplication.DRDelay_mins);
                    Database.AddInParameter(cmd, Dbstring + "iDRLOG_SEQUENCE", DbType.String, activeODGReplication.DRLog_sequence);

                    Database.AddInParameter(cmd, Dbstring + "iDRDG_BROKER_STATUS", DbType.String, activeODGReplication.DRDg_broker_status);
                    Database.AddInParameter(cmd, Dbstring + "iDRREMOTE_LOGIN_PASSWORDFILE", DbType.String, activeODGReplication.DRRemote_login_passwordfile);
                    Database.AddInParameter(cmd, Dbstring + "iDRSTANDBY_FILE_MANAGEMENT", DbType.String, activeODGReplication.DRStandby_file_management);
                    Database.AddInParameter(cmd, Dbstring + "iDRSTANDBY_REDO_LOGS", DbType.String, activeODGReplication.DRStandby_redo_logs);
                    Database.AddInParameter(cmd, Dbstring + "iDRTRANSPORT_LAG", DbType.String, activeODGReplication.DRTransport_lag);

                    Database.AddInParameter(cmd, Dbstring + "iDRAPPLY_LAG", DbType.String, activeODGReplication.DRApply_lag);
                    Database.AddInParameter(cmd, Dbstring + "iDRAPPLY_FINISH_TIME", DbType.String, activeODGReplication.DRApply_finish_time);
                    Database.AddInParameter(cmd, Dbstring + "iDRESTIMATED_STARTUP_TIME", DbType.String, activeODGReplication.DREstimated_startup_time);
                    Database.AddInParameter(cmd, Dbstring + "iDRFORCE_LOGGING", DbType.String, activeODGReplication.DRForce_logging);
                    //Database.AddInParameter(cmd, Dbstring + "iArchive_generation_hourly", DbType.String, activeODGReplication.Archive_generation_hourly);

                    Database.AddInParameter(cmd, Dbstring + "iDRSWITCHOVER_STATUS", DbType.String, activeODGReplication.DRSwitchover_status);
                    Database.AddInParameter(cmd, Dbstring + "iDRLOG_ARCHIVE_CONFIG", DbType.String, activeODGReplication.DRLog_archive_config);
                    Database.AddInParameter(cmd, Dbstring + "iDRFAL_SERVER", DbType.String, activeODGReplication.DRFal_server);
                    Database.AddInParameter(cmd, Dbstring + "iDRFAL_CLIENT", DbType.String, activeODGReplication.DRFal_client);
                    Database.AddInParameter(cmd, Dbstring + "iDRDATAGUARD_STATUS", DbType.String, activeODGReplication.DRDataguard_status);

                    Database.AddInParameter(cmd, Dbstring + "iPRTHREADID", DbType.String, activeODGReplication.PRThreadId);
                    Database.AddInParameter(cmd, Dbstring + "iDRTHREADID", DbType.String, activeODGReplication.DRThreadId);





                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
               // throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess information", exc);
            }
        }

        public static bool ActiveODGInstanceDetails(ActiveODGInstance _ActiveODGInstance)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveinstanceLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.String, _ActiveODGInstance.InfraId);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, _ActiveODGInstance.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceId", DbType.Int32, _ActiveODGInstance.InstanceId);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceStartUpTime", DbType.DateTime, _ActiveODGInstance.InstanceStartUpTime);
                    Database.AddInParameter(cmd, Dbstring + "iOpenMode", DbType.String, _ActiveODGInstance.OpenMode);
                    Database.AddInParameter(cmd, Dbstring + "iPRDRType", DbType.String, _ActiveODGInstance.PrDrType);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.String, _ActiveODGInstance.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iIsClusterDatabase", DbType.Int32, Convert.ToInt32(_ActiveODGInstance.IsClusterDatabase));
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, _ActiveODGInstance.GroupNodeId);

                    Database.AddInParameter(cmd, Dbstring + "iControlfileName", DbType.String, _ActiveODGInstance.ControlfileName);
                    Database.AddInParameter(cmd, Dbstring + "iPlatform_name", DbType.String, _ActiveODGInstance.Platform_name);
                    Database.AddInParameter(cmd, Dbstring + "iParameterFile", DbType.String, _ActiveODGInstance.ParameterFile);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess information", exc);
            }
        }

        public static bool ActiveODGTNSServiceDetails(ActiveODGTNSService _ActiveODGTNSService, int PRNodeId, int DRNodeId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveTNSLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGTNSService.InfraId);
                    Database.AddInParameter(cmd, Dbstring + "iPRService", DbType.String, _ActiveODGTNSService.PRService);
                    Database.AddInParameter(cmd, Dbstring + "iDRService", DbType.String, _ActiveODGTNSService.DRService);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, PRNodeId);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess information", exc);
            }
        }

        public static bool ActiveODGReplicationNonOdgDetails(ActiveODGReplicationNonOdg _ActiveODGReplicationNonOdg)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveRepNonOdgLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGReplicationNonOdg.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iPRArchiveDestLocation", DbType.String, _ActiveODGReplicationNonOdg.PRArchiveDestLocation);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseIncarnation", DbType.Int32, Convert.ToInt32(_ActiveODGReplicationNonOdg.PRDatabaseIncarnation));
                    Database.AddInParameter(cmd, Dbstring + "iPRLogSequence", DbType.Int32, Convert.ToInt32(_ActiveODGReplicationNonOdg.PRLogSequence));
                    Database.AddInParameter(cmd, Dbstring + "iPRStandbyFileManagement", DbType.String, _ActiveODGReplicationNonOdg.PRStandbyFileManagement);
                    Database.AddInParameter(cmd, Dbstring + "iPRForce_Logging", DbType.String, _ActiveODGReplicationNonOdg.PRForce_Logging);

                    Database.AddInParameter(cmd, Dbstring + "iDRArchiveDestLocation", DbType.String, _ActiveODGReplicationNonOdg.DRArchiveDestLocation);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseIncarnation", DbType.Int32, _ActiveODGReplicationNonOdg.DRDatabaseIncarnation);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSequence", DbType.Int32, Convert.ToInt32(_ActiveODGReplicationNonOdg.DRLogSequence));
                    Database.AddInParameter(cmd, Dbstring + "iDRStandbyFileManagement", DbType.String, _ActiveODGReplicationNonOdg.DRStandbyFileManagement);
                    Database.AddInParameter(cmd, Dbstring + "iDRForce_Logging", DbType.String, _ActiveODGReplicationNonOdg.DRForce_Logging);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, _ActiveODGReplicationNonOdg.GroupNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGArchiveLogsizeWeeklyDetails(int InfraId, System.Collections.Generic.List<decimal> list, int PRNodeId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGWeeklyLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, InfraId);
                    Database.AddInParameter(cmd, Dbstring + "iArchiveDate", DbType.Date, DateTime.Now.AddDays(-1));
                    Database.AddInParameter(cmd, Dbstring + "iDay00", DbType.Decimal, Convert.ToDecimal(list[0]));
                    Database.AddInParameter(cmd, Dbstring + "iDay01", DbType.Decimal, Convert.ToDecimal(list[1]));
                    Database.AddInParameter(cmd, Dbstring + "iDay02", DbType.Decimal, Convert.ToDecimal(list[2]));
                    Database.AddInParameter(cmd, Dbstring + "iDay03", DbType.Decimal, Convert.ToDecimal(list[3]));

                    Database.AddInParameter(cmd, Dbstring + "iDay04", DbType.Decimal, Convert.ToDecimal(list[4]));
                    Database.AddInParameter(cmd, Dbstring + "iDay05", DbType.Decimal, Convert.ToDecimal(list[5]));
                    Database.AddInParameter(cmd, Dbstring + "iDay06", DbType.Decimal, Convert.ToDecimal(list[6]));
                    Database.AddInParameter(cmd, Dbstring + "iTotalMB", DbType.Decimal, Convert.ToDecimal(list[7]));
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, PRNodeId);


                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;

                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGArchiveLogHourlyCountDetails(ActiveODGArchiveODGHourlyCount _ActiveODGArchiveODGHourlyCount, int PRNodeId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGHourlylog_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGArchiveODGHourlyCount.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iArchiveDate", DbType.DateTime, Convert.ToDateTime(_ActiveODGArchiveODGHourlyCount.ArchiveDate));
                    Database.AddInParameter(cmd, Dbstring + "ihour00", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[0]));
                    Database.AddInParameter(cmd, Dbstring + "ihour01", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[1]));
                    Database.AddInParameter(cmd, Dbstring + "ihour02", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[2]));
                    Database.AddInParameter(cmd, Dbstring + "ihour03", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[3]));

                    Database.AddInParameter(cmd, Dbstring + "ihour04", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[4]));
                    Database.AddInParameter(cmd, Dbstring + "ihour05", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[5]));
                    Database.AddInParameter(cmd, Dbstring + "ihour06", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[6]));
                    Database.AddInParameter(cmd, Dbstring + "ihour07", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[7]));

                    Database.AddInParameter(cmd, Dbstring + "ihour08", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[8]));
                    Database.AddInParameter(cmd, Dbstring + "ihour09", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[9]));
                    Database.AddInParameter(cmd, Dbstring + "ihour10", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[10]));
                    Database.AddInParameter(cmd, Dbstring + "ihour11", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[11]));


                    Database.AddInParameter(cmd, Dbstring + "ihour12", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[12]));
                    Database.AddInParameter(cmd, Dbstring + "ihour13", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[13]));
                    Database.AddInParameter(cmd, Dbstring + "ihour14", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[14]));
                    Database.AddInParameter(cmd, Dbstring + "ihour15", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[15]));

                    Database.AddInParameter(cmd, Dbstring + "ihour16", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[16]));
                    Database.AddInParameter(cmd, Dbstring + "ihour17", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[17]));
                    Database.AddInParameter(cmd, Dbstring + "ihour18", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[18]));
                    Database.AddInParameter(cmd, Dbstring + "ihour19", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[19]));

                    Database.AddInParameter(cmd, Dbstring + "ihour20", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[20]));
                    Database.AddInParameter(cmd, Dbstring + "ihour21", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[21]));
                    Database.AddInParameter(cmd, Dbstring + "ihour22", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[22]));
                    Database.AddInParameter(cmd, Dbstring + "ihour23", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[23]));
                    Database.AddInParameter(cmd, Dbstring + "iTotal", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveODGHourlyCount.ArchiveHourly[24]));

                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, PRNodeId);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGArchiveLogHourlySizeDetails(ActiveODGArchiveHourlySize _ActiveODGArchiveHourlySize, int GroupNodeId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGSizeLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGArchiveHourlySize.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iArchiveDate", DbType.DateTime, Convert.ToDateTime(_ActiveODGArchiveHourlySize.ArchiveDate));
                    Database.AddInParameter(cmd, Dbstring + "ihour00", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[0]));
                    Database.AddInParameter(cmd, Dbstring + "ihour01", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[1]));
                    Database.AddInParameter(cmd, Dbstring + "ihour02", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[2]));
                    Database.AddInParameter(cmd, Dbstring + "ihour03", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[3]));

                    Database.AddInParameter(cmd, Dbstring + "ihour04", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[4]));
                    Database.AddInParameter(cmd, Dbstring + "ihour05", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[5]));
                    Database.AddInParameter(cmd, Dbstring + "ihour06", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[6]));
                    Database.AddInParameter(cmd, Dbstring + "ihour07", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[7]));

                    Database.AddInParameter(cmd, Dbstring + "ihour08", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[8]));
                    Database.AddInParameter(cmd, Dbstring + "ihour09", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[9]));
                    Database.AddInParameter(cmd, Dbstring + "ihour10", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[10]));
                    Database.AddInParameter(cmd, Dbstring + "ihour11", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[11]));


                    Database.AddInParameter(cmd, Dbstring + "ihour12", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[12]));
                    Database.AddInParameter(cmd, Dbstring + "ihour13", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[13]));
                    Database.AddInParameter(cmd, Dbstring + "ihour14", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[14]));
                    Database.AddInParameter(cmd, Dbstring + "ihour15", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[15]));

                    Database.AddInParameter(cmd, Dbstring + "ihour16", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[16]));
                    Database.AddInParameter(cmd, Dbstring + "ihour17", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[17]));
                    Database.AddInParameter(cmd, Dbstring + "ihour18", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[18]));
                    Database.AddInParameter(cmd, Dbstring + "ihour19", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[19]));

                    Database.AddInParameter(cmd, Dbstring + "ihour20", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[20]));
                    Database.AddInParameter(cmd, Dbstring + "ihour21", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[21]));
                    Database.AddInParameter(cmd, Dbstring + "ihour22", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[22]));
                    Database.AddInParameter(cmd, Dbstring + "ihour23", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[23]));
                    Database.AddInParameter(cmd, Dbstring + "iTotalMB", DbType.Decimal, Convert.ToDecimal(_ActiveODGArchiveHourlySize.ArchiveHourlySize[24]));

                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, GroupNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGClusterDetails(ActiveODGCluster _ActiveODGCluster)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGClusterLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGCluster.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iClusterName", DbType.String, _ActiveODGCluster.ClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iClusterWareActiveVersion", DbType.String, _ActiveODGCluster.ClusterWareActiveVersion);
                    Database.AddInParameter(cmd, Dbstring + "iOHASStatus", DbType.String, _ActiveODGCluster.OHASStatus);
                    Database.AddInParameter(cmd, Dbstring + "iCRSStatus", DbType.String, _ActiveODGCluster.CRSStatus);
                    Database.AddInParameter(cmd, Dbstring + "iCSSStatus", DbType.String, _ActiveODGCluster.CSSStatus);

                    Database.AddInParameter(cmd, Dbstring + "iEVMStatus", DbType.String, _ActiveODGCluster.EVMStatus);
                    Database.AddInParameter(cmd, Dbstring + "iClusterListener", DbType.String, _ActiveODGCluster.ClusterListener);
                    Database.AddInParameter(cmd, Dbstring + "iSCANStatus", DbType.String, _ActiveODGCluster.ScanStatus);
                    Database.AddInParameter(cmd, Dbstring + "iScanListenerStatus", DbType.String, _ActiveODGCluster.ScanListenerStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRDRType", DbType.String, _ActiveODGCluster.PRDRType);

                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, _ActiveODGCluster.GroupNodeId);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGMultiTenancyDetails(ActiveODGMultiTenancy _ActiveODGMultiTenancy)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGMultiLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGMultiTenancy.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iCDB", DbType.String, _ActiveODGMultiTenancy.CDB);
                    Database.AddInParameter(cmd, Dbstring + "iContainers", DbType.String, _ActiveODGMultiTenancy.Containers);
                    Database.AddInParameter(cmd, Dbstring + "iPDBS", DbType.String, _ActiveODGMultiTenancy.PDBS);
                    Database.AddInParameter(cmd, Dbstring + "iPRDRType", DbType.String, _ActiveODGMultiTenancy.PRDRType);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, _ActiveODGMultiTenancy.GroupNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGPluggableDetails(ActiveODGPluggable _ActiveODGPluggable)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveODGPluggDBLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGPluggable.InfraID);

                    Database.AddInParameter(cmd, Dbstring + "iDBId", DbType.String, _ActiveODGPluggable.DBId);

                    Database.AddInParameter(cmd, Dbstring + "iPRPDBName", DbType.String, _ActiveODGPluggable.PRPDBName);
                    Database.AddInParameter(cmd, Dbstring + "iPRConnectionId", DbType.String, _ActiveODGPluggable.PRConnectionId);
                    Database.AddInParameter(cmd, Dbstring + "iPRPDBId", DbType.String, _ActiveODGPluggable.PRPDBId);
                    Database.AddInParameter(cmd, Dbstring + "iPRPDBMode", DbType.String, _ActiveODGPluggable.PRPDBMode);

                    Database.AddInParameter(cmd, Dbstring + "iPRLogging", DbType.String, _ActiveODGPluggable.PRLogging);
                    Database.AddInParameter(cmd, Dbstring + "iPRForceLogging", DbType.String, _ActiveODGPluggable.PRForceLogging);
                    Database.AddInParameter(cmd, Dbstring + "iPRRecoveryStatus", DbType.String, _ActiveODGPluggable.PRRecoveryStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRPDBSize", DbType.Decimal, _ActiveODGPluggable.PRPDBSize);

                    Database.AddInParameter(cmd, Dbstring + "iDRPDBName", DbType.String, _ActiveODGPluggable.DRPDBName);
                    Database.AddInParameter(cmd, Dbstring + "iDRConnectionId", DbType.String, _ActiveODGPluggable.DRConnectionId);
                    Database.AddInParameter(cmd, Dbstring + "iDRPDBId", DbType.String, _ActiveODGPluggable.DRPDBId);
                    Database.AddInParameter(cmd, Dbstring + "iDRPDBMode", DbType.String, _ActiveODGPluggable.DRPDBMode);

                    Database.AddInParameter(cmd, Dbstring + "iDRLogging", DbType.String, _ActiveODGPluggable.DRLogging);
                    Database.AddInParameter(cmd, Dbstring + "iDRForceLogging", DbType.String, _ActiveODGPluggable.DRForceLogging);
                    Database.AddInParameter(cmd, Dbstring + "iDRRecoveryStatus", DbType.String, _ActiveODGPluggable.DRRecoveryStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRPDBSize", DbType.Decimal, _ActiveODGPluggable.DRPDBSize);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }

        public static bool ActiveODGASMDetails(ActiveODGASMDetails _ActiveODGASMDetails)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ActiveASMLogs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, _ActiveODGASMDetails.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.String, _ActiveODGASMDetails.Name);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.String, _ActiveODGASMDetails.State);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.String, _ActiveODGASMDetails.Type);
                    Database.AddInParameter(cmd, Dbstring + "iTotalMB", DbType.String, _ActiveODGASMDetails.TotalMB);
                    Database.AddInParameter(cmd, Dbstring + "iFreeMB", DbType.String, _ActiveODGASMDetails.FreeMB);
                    Database.AddInParameter(cmd, Dbstring + "iUsed", DbType.String, _ActiveODGASMDetails.Used);
                    Database.AddInParameter(cmd, Dbstring + "iPRDRType", DbType.String, _ActiveODGASMDetails.PRDRType);
                    Database.AddInParameter(cmd, Dbstring + "iGroupNodeId", DbType.Int32, 0);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, 1);
                    Database.AddInParameter(cmd, Dbstring + "iSrno", DbType.Int32, _ActiveODGASMDetails.SRNO);

                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                return false;
                //throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ActiveODGMonitorDataAccess : ActiveODGReplicationNonOdgDetails Method", exc);
            }
        }



        public static List<ActiveODGArchiveHourlySize> ActiveODGArchiveHourlySizeGetByInfraId(int infraobjectId)
        {
            List<ActiveODGArchiveHourlySize> ActiveODGresult = new List<ActiveODGArchiveHourlySize>();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("AODGBYHOUR_STATUS_GETBYDATE"))
                {

                    Database.AddInParameter(cmd, Dbstring + "IINFRAOBJECTID", DbType.Int32, infraobjectId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(cmd))
                    {
                        while (myReader.Read())
                        {
                            var activeodg = new ActiveODGArchiveHourlySize();

                            activeodg.ArchiveDate = Convert.IsDBNull(myReader["ArchiveDate"]) ? DateTime.MinValue : Convert.ToDateTime(myReader["ArchiveDate"]);

                            activeodg.TOTALMB = Convert.ToDecimal(myReader["TOTALMB"]);



                            ActiveODGresult.Add(activeodg);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                return null;
                //throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All parallel action completed", exc);
            }
            return ActiveODGresult;
        }



    }
}
