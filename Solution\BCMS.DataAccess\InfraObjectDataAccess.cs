﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class InfraObjectDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(InfraObjectDataAccess));

        public static bool PairInfraObjectActive(int groupId)
        {
          

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_IsActive"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_alertnoti"));
#endif
                    var myScalar = Database.ExecuteScalar(dbCommand);
                    return myScalar != null;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check pair infraobject is active", exc);
            }
        }

        public static IList<InfraObject> GetActiveGroups(bool isNormalCopy)
        {
            IList<InfraObject> infraObjectList = new List<InfraObject>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetByActive"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"isTrue", DbType.Boolean, isNormalCopy);
                    #if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
                    #endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var infraObject = new InfraObject();

                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.DRReady = Convert.ToBoolean(myGroupReader["DRReady"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);

                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           

                            infraObjectList.Add(infraObject);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Active InfraObject Details", exc);

            }
            return infraObjectList;
        }

        public static IList<InfraObject> GetAllInfraObjects()
        {
            IList<InfraObject> infraObjectList = new List<InfraObject>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var infraObject = new InfraObject();


                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.DRReady = Convert.ToBoolean(myGroupReader["DRReady"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);

                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           


                            infraObjectList.Add(infraObject);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get All InfraObject Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get All InfraObject Details", exc);

            }
            return infraObjectList;
        }

        public static InfraObject GetInfraObjectByWorkflowEnable(int type)
        {
            var infraObject = new InfraObject();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetByIsWorkflow"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iIsWorkflow", DbType.Int32, type);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {

                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);

                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get InfraObject By Workflow enable Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message +" ERROR MSG " +exc.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, exc.Message, exc);
            }

            return infraObject;
        }

        public static InfraObject GetInfraObjectByDataSyncEnable(int type)
        {
            var infraObject = new InfraObject();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetByIsDataSync"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iDataSync", DbType.Int32, type);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                          //  infraObject.ConfigureDataLag = Convert.ToString(myGroupReader["ConfigureDataLag"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);
                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, exc.Message, exc);
            }

            return infraObject;
        }

        public static InfraObject GetInfraObjectById(int id)
        {
            var infraObject = new InfraObject();

            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.DRReady = Convert.ToBoolean(myGroupReader["DRReady"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);
                            //infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            //infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObject;
        }

        public static IList<InfraObject> GetInfraObjectByAffectedHeatMap(int businessServiceId)
        {
            var infraObjects = new List<InfraObject>();

            try
            {
              

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObj_GetbyAffectedHeatmap"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iBusinessServiceId", DbType.String, businessServiceId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var infraobject = new InfraObject();

                            infraobject.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            infraobject.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            infraobject.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"]);
                            infraobject.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]);
                            infraobject.BusinessFunctionId = Convert.IsDBNull(reader["BusinessFunctionId"]) ? 0 : Convert.ToInt32(reader["BusinessFunctionId"]);
                            infraobject.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                            infraobject.SubType = Convert.IsDBNull(reader["SubType"]) ? 0 : Convert.ToInt32(reader["SubType"]);
                            infraobject.DRReady = !Convert.IsDBNull(reader["DRReady"]) && Convert.ToBoolean(reader["DRReady"]);
                            infraobject.ReplicationType = Convert.IsDBNull(reader["RecoveryType"]) ? 0 : Convert.ToInt32(reader["RecoveryType"]);
                            infraobject.PRServerId = Convert.IsDBNull(reader["PRServerId"]) ? 0 : Convert.ToInt32(reader["PRServerId"]);
                            infraobject.DRServerId = Convert.IsDBNull(reader["DRServerId"]) ? 0 : Convert.ToInt32(reader["DRServerId"]);
                            infraobject.PRDatabaseId = Convert.IsDBNull(reader["PRDatabaseId"]) ? 0 : Convert.ToInt32(reader["PRDatabaseId"]);
                            infraobject.DRDatabaseId = Convert.IsDBNull(reader["DRDatabaseId"]) ? 0 : Convert.ToInt32(reader["DRDatabaseId"]);
                            infraobject.PRReplicationId = Convert.IsDBNull(reader["PRReplicationId"]) ? 0 : Convert.ToInt32(reader["PRReplicationId"]);
                            infraobject.DRReplicationId = Convert.IsDBNull(reader["DRReplicationId"]) ? 0 : Convert.ToInt32(reader["DRReplicationId"]);
                            infraobject.Priority = Convert.IsDBNull(reader["Priority"]) ? 0 : Convert.ToInt32(reader["Priority"]);
                            infraobject.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                            infraobject.ReplicationStatus = Convert.IsDBNull(reader["ReplicationStatus"]) ? 0 : Convert.ToInt32(reader["ReplicationStatus"]);
                            infraobject.EnableDataSync = !Convert.IsDBNull(reader["EnableDataSync"]) && Convert.ToBoolean(reader["EnableDataSync"]);
                            infraobject.DROperationStatus = Convert.IsDBNull(reader["DROperationStatus"]) ? 0 : Convert.ToInt32(reader["DROperationStatus"]);
                            infraobject.DROperationId = Convert.IsDBNull(reader["DROperationId"]) ? 0 : Convert.ToInt32(reader["DROperationId"]);
                            infraobject.SiteSolutionTypeId = Convert.IsDBNull(reader["SiteSolutionTypeId"]) ? 0 : Convert.ToInt32(reader["SiteSolutionTypeId"]);
                            infraobject.NearGroupId = Convert.IsDBNull(reader["NearGroupId"]) ? 0 : Convert.ToInt32(reader["NearGroupId"]);
                            infraobject.MonitoringWorkflow = Convert.IsDBNull(reader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(reader["MonitoringWorkflow"]);
                            infraobject.DrMonitorApplicationCheck = Convert.IsDBNull(reader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(reader["WORKFLOWTYPE"]);
                            infraobject.DrMonitoringWorkflow = Convert.IsDBNull(reader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(reader["DRMONITORWORKFLOW"]);

                            infraobject.PRServerId2 = Convert.IsDBNull(reader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(reader["SRM_PRSERVERID"]);
                            infraobject.DRServerId2 = Convert.IsDBNull(reader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(reader["SRM_DRSERVERID"]);
           

                            infraObjects.Add(infraobject);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details By AffectedHeatMap", exc);

            }
            return infraObjects;
        }

        public static IList<InfraObject> GetInfraObjectByStorageImageId(string storageImageId)
        {
            var infraObjects = new List<InfraObject>();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObj_GetByStorageImageId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iStorageImageId", DbType.String, storageImageId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var infraObject = new InfraObject();



                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.ConfigureDataLag = Convert.ToString(myGroupReader["ConfigureDataLag"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);

                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           


                            infraObjects.Add(infraObject);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Group Details By Replication Id", exc);

            }
            return infraObjects;
        }

        public static bool UpdateInfraObjectByState(int infraObjectId, InfraObjectState state)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_UpdateByState"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iState", DbType.AnsiString, state.ToString());
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;

#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by state ", exc);
            }

        }

        public static bool UpdateByEnableDataSync(int infraObjectId, bool enableDataSync)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("INFRAOBJECT_UPDTBYENBLDTASYNC"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iEnableDataSync", DbType.Int32, enableDataSync);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update EnableDataSync", exc);
            }

        }
        
        public static bool UpdateInfraObjectByReplicationStatus(int infraObjectId, InfraObjectState state, int replState, bool isTrue)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_UpdtByRepliStatus"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iState", DbType.AnsiString, state.ToString());
                    Database.AddInParameter(dbCommand, Dbstring+"iReplStatus", DbType.Int32, replState);
                    Database.AddInParameter(dbCommand, Dbstring+"isTrue", DbType.Int32, isTrue);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;

#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by replication status ", exc);
            }

        }

        public static bool IsAllInfraObjectFinishNormalCopy(int value, string storageimageId)
        {
           
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Group_IsFinishNormalCopy"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iReplStatus", DbType.Int32, value);
                    Database.AddInParameter(dbCommand, Dbstring+"iStorageImageId", DbType.AnsiString, storageimageId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var myScalar = Database.ExecuteScalar(dbCommand);

                    return myScalar == null;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All Groups are finish replication", exc);
            }
        }

        public static bool UpdateWorkflowOperation(int infraObjectId, int drOperationStatus, int dropertion)
        {
            const string sp = "INFRAOBJECT_UPDTBYWRKFLWOPE";


            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                Database.AddInParameter(cmd, Dbstring+"iDROperationStatus", DbType.Int32, drOperationStatus);
                Database.AddInParameter(cmd, Dbstring+"iDROperationId", DbType.Int32, dropertion);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                var returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE
                return returnCode < 0;

#endif
                return returnCode > 0;
            }
        }

        public static bool UpdateGroupByPRDRServer(int groupId, int pRserverID, int dRserverID)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Group_UpdateByPRDRServer"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iGroupId", DbType.Int32, groupId);
                    Database.AddInParameter(dbCommand, Dbstring+"iPRserverID", DbType.Int32, pRserverID);
                    Database.AddInParameter(dbCommand, Dbstring+"iDRserverID", DbType.Int32, dRserverID);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                return isuccess < 0;

#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by state ", exc);
            }

        }

        /// <summary>
        /// It will Return Infraobject Details by its name.
        /// </summary>
        /// <param name="name"> Infra Object Name </param>
        /// <returns>InfraObject</returns>
        public static InfraObject GetInfraObjectByName(string name)
        {
            var infraObject = new InfraObject();

            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObject_GetByName"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iName", DbType.AnsiString, name);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            infraObject.Id = Convert.ToInt32(myGroupReader["Id"]);
                            infraObject.Name = Convert.ToString(myGroupReader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(myGroupReader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(myGroupReader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(myGroupReader["Type"]);
                            infraObject.DRReady = Convert.ToBoolean(myGroupReader["DRReady"]);
                            infraObject.ReplicationType = Convert.ToInt32(myGroupReader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(myGroupReader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(myGroupReader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(myGroupReader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(myGroupReader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(myGroupReader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(myGroupReader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(myGroupReader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(myGroupReader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(myGroupReader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(myGroupReader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(myGroupReader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(myGroupReader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(myGroupReader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(myGroupReader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(myGroupReader["DRMONITORWORKFLOW"]);
                            
                            infraObject.PRServerId2 = Convert.IsDBNull(myGroupReader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(myGroupReader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(myGroupReader["SRM_DRSERVERID"]);
           
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by name - " + name, exc);
            }

            return infraObject;
        }



        //PRASANNA 

        public static IList<InfraObject> InfraObject_GetByServerId(int iid)
        {
            IList<InfraObject> Infra = new List<InfraObject>();
            try
            {


                using (DbCommand dbCommand = DataAccessBase.Database.GetStoredProcCommand("InfraObject_GetByServerId"))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, iid);

#if ORACLE
                    dbCommand.Parameters.Add(new OracleParameter("cur", OracleDbType.Cursor, 0, ParameterDirection.Output, true, 0, 0, String.Empty, DataRowVersion.Default, DBNull.Value));
#endif
                    using (IDataReader reader = DataAccessBase.Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var infraObject = new InfraObject();
                            infraObject.Id = Convert.ToInt32(reader["Id"]);
                            infraObject.Name = Convert.ToString(reader["Name"]);
                            infraObject.BusinessServiceId = Convert.ToInt32(reader["BusinessServiceId"]);
                            infraObject.BusinessFunctionId = Convert.ToInt32(reader["BusinessFunctionId"]);
                            infraObject.Type = Convert.ToInt32(reader["Type"]);
                            infraObject.DRReady = Convert.ToBoolean(reader["DRReady"]);
                            infraObject.ReplicationType = Convert.ToInt32(reader["RecoveryType"]);
                            infraObject.PRServerId = Convert.ToInt32(reader["PRServerId"]);
                            infraObject.DRServerId = Convert.ToInt32(reader["DRServerId"]);
                            infraObject.PRDatabaseId = Convert.ToInt32(reader["PRDatabaseId"]);
                            infraObject.DRDatabaseId = Convert.ToInt32(reader["DRDatabaseId"].ToString());
                            infraObject.PRReplicationId = Convert.ToInt32(reader["PRReplicationId"]);
                            infraObject.DRReplicationId = Convert.ToInt32(reader["DRReplicationId"]);
                            infraObject.State = Convert.ToString(reader["State"]);
                            infraObject.IsWorkflow = Convert.ToInt32(reader["DROperationStatus"]);
                            infraObject.DROperationId = Convert.ToInt32(reader["DROperationId"]);
                            infraObject.MonitoringWorkflow = Convert.IsDBNull(reader["MonitoringWorkflow"]) ? 0 : Convert.ToInt32(reader["MonitoringWorkflow"]);
                            infraObject.DrMonitorApplicationCheck = Convert.IsDBNull(reader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(reader["WORKFLOWTYPE"]);
                            infraObject.DrMonitoringWorkflow = Convert.IsDBNull(reader["DRMONITORWORKFLOW"]) ? 0 : Convert.ToInt32(reader["DRMONITORWORKFLOW"]);

                            infraObject.PRServerId2 = Convert.IsDBNull(reader["SRM_PRSERVERID"]) ? 0 : Convert.ToInt32(reader["SRM_PRSERVERID"]);
                            infraObject.DRServerId2 = Convert.IsDBNull(reader["SRM_DRSERVERID"]) ? 0 : Convert.ToInt32(reader["SRM_DRSERVERID"]);

                            //infraObject.IsQueueMonitor = Convert.IsDBNull(reader["ISQUEUEMONITOR"]) ? 0 : Convert.ToInt32(reader["ISQUEUEMONITOR"]);



                            Infra.Add(infraObject);




                        }

                        if (Infra.Count > 0)
                        {

                            return Infra;


                        }


                    }


                }

            }
            catch (Exception exc)
            {
                Logger.Error("ERROR Get All InfraObject Details :" + exc.InnerException == null ? exc.Message : exc.InnerException.Message);

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get All InfraObject Details", exc);
            }
            return Infra;
        }
    }
}
