﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;


namespace Bcms.DataAccess
{
    public class BusinessServiceDataAccess:BaseDataAccess
    {
        public static IList<BusinessService> GetActiveBusinessServices()
        {
            IList<BusinessService> businessServiceList = new List<BusinessService>();
            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("BusinessService_GetByActive"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var businessService = new BusinessService();

                            businessService.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            businessService.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            businessService.Description = Convert.IsDBNull(reader["Description"])
                                ? string.Empty
                                : Convert.ToString(reader["Description"]);
                            businessService.DRReadyness = !Convert.IsDBNull(reader["DRReadyness"]) &&
                                                          Convert.ToBoolean(reader["DRReadyness"]);
                            businessService.CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]);
                            businessService.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"]);
                            businessService.ConfiguredRPO = Convert.IsDBNull(reader["ConfiguredRPO"])
                                ? string.Empty
                                : Convert.ToString(reader["ConfiguredRPO"]);
                            businessService.ConfiguredRTO = Convert.IsDBNull(reader["ConfiguredRTO"])
                                ? string.Empty
                                : Convert.ToString(reader["ConfiguredRTO"]);
                            businessService.ConfiguredMAO = Convert.IsDBNull(reader["ConfiguredMTPOD"])
                                ? string.Empty
                                : Convert.ToString(reader["ConfiguredMTPOD"]);

                            businessServiceList.Add(businessService);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get ActiveGroup Details", exc);

            }
            return businessServiceList;


        }
    }
}