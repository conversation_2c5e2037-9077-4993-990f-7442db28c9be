﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
   public class MSqlMirroring : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PRDatabaseName { get; set; }

        [DataMember]
        public string DRDatabaseName { get; set; }

        [DataMember]
        public string PRDBRole { get; set; }

        [DataMember]
        public string DRDBRole { get; set; }

        [DataMember]
        public string PRMirroringState { get; set; }

        [DataMember]
        public string DRMirroringState { get; set; }

        [DataMember]
        public string PRLogGenerateRate { get; set; }

        [DataMember]
        public string DRLogGenerateRate { get; set; }

        [DataMember]
        public string PRUnsentLog { get; set; }

        [DataMember]
        public string DRUnsentLog { get; set; }

        [DataMember]
        public string PRSentRate { get; set; }

        [DataMember]
        public string DRSentRate { get; set; }

        [DataMember]
        public string PRUnrestoredLog { get; set; }

        [DataMember]
        public string DRUnrestoredLog { get; set; }

        [DataMember]
        public string PRRecoveryRate { get; set; }

        [DataMember]
        public string DRRecoveryRate { get; set; }

        [DataMember]
        public string PRTransactionDelay { get; set; }

        [DataMember]
        public string DRTransactionDelay { get; set; }

        [DataMember]
        public string PRTransactionPerSecond { get; set; }

        [DataMember]
        public string DRTransactionPerSecond { get; set; }

        [DataMember]
        public string PRAverageDelay { get; set; }

        [DataMember]
        public string DRAverageDelay { get; set; }

        [DataMember]
        public DateTime PRTimeRecorded { get; set; }

        [DataMember]
        public DateTime DRTimeRecorded { get; set; }

        [DataMember]
        public DateTime PRTimeBehind { get; set; }

        [DataMember]
        public DateTime DRTimeBehind { get; set; }

        [DataMember]
        public DateTime PRLocalTime { get; set; }

        [DataMember]
        public DateTime DRLocalTime { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        #endregion Properties

        #region Constructor

        public MSqlMirroring()
            : base()
        {
        }
        #endregion Constructor
    }
}
