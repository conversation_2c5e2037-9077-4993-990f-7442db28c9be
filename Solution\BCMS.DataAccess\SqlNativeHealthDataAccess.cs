﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Utility;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;
namespace Bcms.DataAccess
{
    public class SqlNativeHealthDataAccess:BaseDataAccess
    {

        public static string GethealthbyId(int groupId)
        {
            string healthName = string.Empty;
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLNATIVEHEALTH_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySqlhealthReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlhealthReader.Read())
                        {
                            healthName = mySqlhealthReader[0].ToString();
                        }
                    }
                }
                return healthName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature Health GetbyId for group Id(" + groupId + ")", exc);

            }
        }

        public static bool AddSqlNativeHealthLogs(SqlnativeHealth sqlnativeHealth)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTH_CREATE"))

              
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNativeHealthStatus(SqlnativeHealth sqlnativeHealth)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTHMONSTAT_CRTUPD"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);

                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring +"iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }
            return false;
        }

        public static bool UpdateSqlNativeHealth(SqlnativeHealth sqlnativeHealth)
        {
            try
            {
                
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTHMONSTAT_CRTUPD"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring +"iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring +"iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring+"iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring+"iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);

                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring +"iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogNative_Create entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNativeHealthParameter(SqlNativeHealthParamete SqlNativeHealthParamete)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SqlHealthParameter_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, SqlNativeHealthParamete.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iDatabase_UpdateabilityPR", DbType.AnsiString, SqlNativeHealthParamete.Database_UpdateabilityPR);
                    Database.AddInParameter(cmd, Dbstring+"iDatabase_UpdateabilityDR", DbType.AnsiString, SqlNativeHealthParamete.Database_UpdateabilityDR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQLServer_EditionPR", DbType.AnsiString, SqlNativeHealthParamete.MSSQLServer_EditionPR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQLServer_EditionDR", DbType.AnsiString, SqlNativeHealthParamete.MSSQLServer_EditionDR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Server_ReleasePR", DbType.AnsiString, SqlNativeHealthParamete.MSSQL_Server_ReleasePR);
                    Database.AddInParameter(cmd, Dbstring+"iMSSQL_Server_ReleaseDR", DbType.AnsiString, SqlNativeHealthParamete.MSSQL_Server_ReleaseDR);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseSizePR", DbType.AnsiString, SqlNativeHealthParamete.DatabaseSizePR);
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseSizeDR", DbType.AnsiString, SqlNativeHealthParamete.DatabaseSizeDR);
                    Database.AddInParameter(cmd, Dbstring+"iIsActive", DbType.Int32, SqlNativeHealthParamete.IsActive);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, SqlNativeHealthParamete.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
        #if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
        #endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }
            return false;
        }

    }
}
