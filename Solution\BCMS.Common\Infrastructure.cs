﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class Infrastructure : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public int CPUThreshold { get; set; }

        [DataMember]
        public int MemoryThreshold { get; set; }

        [DataMember]
        public string MountPointThreshold { get; set; }

        [DataMember]
        public string UserNames { get; set; }

        [DataMember]
        public string FileName { get; set; }


        #endregion Properties
    }
}