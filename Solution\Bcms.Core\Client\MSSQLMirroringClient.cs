﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using PVMWare;
using Bcms.Helper;
using Bcms.DataAccess;
using MSSQLHelper;

namespace Bcms.Core.Client
{
    public class MSSQLMirroringClient : IDisposable
    {
        public MSSQLMirroringClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

        public MSSQLMirroringClient()
        {
            // TODO: Complete member initialization
        }
        #region Variable
        public bool _isDisposed;
        private Server _server;
        private DatabaseBase _database;
        private const int MaxAlertCount = 3;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(MSSQLMirroringClient));
        # endregion

        public InfraObject CurrentInfraObject { get; set; }

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }

        public DatabaseBase CurrentDatabase
        {

            get
            {
                if (_database == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _database = DatabaseBaseDataAccess.GetDatabaseByInfraObjectId(CurrentInfraObject.Id);
                    }
                }
                return _database;
            }

            set
            {
                _database = value;
            }
        }

        public void MonitorMSSQLMirroringComponent()
        {
            try
            {
                if (CurrentServer != null && CurrentDatabase != null)
                {
                    CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                    CurrentServer.InfraObjectId = CurrentInfraObject.Id;
                    CurrentServer.JobName = JobName.MonitorServer;
                    if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                    {
                        SqlMirroring _SqlPRMirroring = new SqlMirroring();
                        _SqlPRMirroring = MSSqlHelper.MirroringMonitoringStatus(CurrentServer.DRIPAddress, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRDatabaseSID, Convert.ToString(CurrentDatabase.DatabaseSql.DRPort));
                        SqlMirroring _SqlDRMirroring = new SqlMirroring();
                        _SqlDRMirroring = MSSqlHelper.MirroringMonitoringStatus(CurrentServer.PRIPAddress, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRDatabaseSID, Convert.ToString(CurrentDatabase.DatabaseSql.PRPort));
                        MSqlMirroring _MSqlMirroring = new MSqlMirroring();

                        _MSqlMirroring.PRDatabaseName = string.IsNullOrEmpty(_SqlPRMirroring.DATABASE_NAME) ? "NA" : _SqlPRMirroring.DATABASE_NAME;
                        _MSqlMirroring.DRDatabaseName = string.IsNullOrEmpty(_SqlDRMirroring.DATABASE_NAME) ? "NA" : _SqlDRMirroring.DATABASE_NAME;
                        _MSqlMirroring.PRDBRole = string.IsNullOrEmpty(_SqlPRMirroring.ROLE_OFDB_QUERY_FIRED) ? "NA" : _SqlPRMirroring.ROLE_OFDB_QUERY_FIRED;
                        _MSqlMirroring.DRDBRole = string.IsNullOrEmpty(_SqlDRMirroring.ROLE_OFDB_QUERY_FIRED) ? "NA" : _SqlDRMirroring.ROLE_OFDB_QUERY_FIRED;
                        _MSqlMirroring.PRMirroringState = string.IsNullOrEmpty(_SqlPRMirroring.MIRRORING_STATE) ? "NA" : _SqlPRMirroring.MIRRORING_STATE;
                        _MSqlMirroring.DRMirroringState = string.IsNullOrEmpty(_SqlDRMirroring.MIRRORING_STATE) ? "NA" : _SqlDRMirroring.MIRRORING_STATE;
                        _MSqlMirroring.PRLogGenerateRate = string.IsNullOrEmpty(_SqlPRMirroring.LOG_GENERAT_RATE) ? "NA" : _SqlPRMirroring.LOG_GENERAT_RATE;
                        _MSqlMirroring.DRLogGenerateRate = string.IsNullOrEmpty(_SqlDRMirroring.LOG_GENERAT_RATE) ? "NA" : _SqlDRMirroring.LOG_GENERAT_RATE;
                        _MSqlMirroring.PRUnsentLog = string.IsNullOrEmpty(_SqlPRMirroring.UNSENT_LOG) ? "NA" : _SqlPRMirroring.UNSENT_LOG;
                        _MSqlMirroring.DRUnsentLog = string.IsNullOrEmpty(_SqlDRMirroring.UNSENT_LOG) ? "NA" : _SqlDRMirroring.UNSENT_LOG;
                        _MSqlMirroring.PRSentRate = string.IsNullOrEmpty(_SqlPRMirroring.SENT_RATE) ? "NA" : _SqlPRMirroring.SENT_RATE;
                        _MSqlMirroring.DRSentRate = string.IsNullOrEmpty(_SqlDRMirroring.SENT_RATE) ? "NA" : _SqlDRMirroring.SENT_RATE;
                        _MSqlMirroring.PRUnrestoredLog = string.IsNullOrEmpty(_SqlPRMirroring.UNRESTORED_LOG) ? "NA" : _SqlPRMirroring.UNRESTORED_LOG;
                        _MSqlMirroring.DRUnrestoredLog = string.IsNullOrEmpty(_SqlDRMirroring.UNRESTORED_LOG) ? "NA" : _SqlDRMirroring.UNRESTORED_LOG;
                        _MSqlMirroring.PRRecoveryRate = string.IsNullOrEmpty(_SqlPRMirroring.RECOVERY_RATE) ? "NA" : _SqlPRMirroring.RECOVERY_RATE;
                        _MSqlMirroring.DRRecoveryRate = string.IsNullOrEmpty(_SqlDRMirroring.RECOVERY_RATE) ? "NA" : _SqlDRMirroring.RECOVERY_RATE;

                        _MSqlMirroring.PRTransactionDelay = string.IsNullOrEmpty(_SqlPRMirroring.TRANSACTION_DELAY) ? "NA" : _SqlPRMirroring.TRANSACTION_DELAY;
                        _MSqlMirroring.DRTransactionDelay = string.IsNullOrEmpty(_SqlDRMirroring.TRANSACTION_DELAY) ? "NA" : _SqlDRMirroring.TRANSACTION_DELAY;

                        _MSqlMirroring.PRTransactionPerSecond = string.IsNullOrEmpty(_SqlPRMirroring.TRANSACTION_PER_SEC) ? "NA" : _SqlPRMirroring.TRANSACTION_PER_SEC;
                        _MSqlMirroring.DRTransactionPerSecond = string.IsNullOrEmpty(_SqlDRMirroring.TRANSACTION_PER_SEC) ? "NA" : _SqlDRMirroring.TRANSACTION_PER_SEC;

                        _MSqlMirroring.PRAverageDelay = string.IsNullOrEmpty(_SqlPRMirroring.AVERAGE_DELAY) ? "NA" : _SqlPRMirroring.AVERAGE_DELAY;
                        _MSqlMirroring.DRAverageDelay = string.IsNullOrEmpty(_SqlDRMirroring.AVERAGE_DELAY) ? "NA" : _SqlDRMirroring.AVERAGE_DELAY;

                        _MSqlMirroring.PRTimeRecorded = Convert.ToDateTime(_SqlPRMirroring.TIME_RECORDED);
                        _MSqlMirroring.DRTimeRecorded = Convert.ToDateTime(_SqlDRMirroring.TIME_RECORDED);

                        _MSqlMirroring.PRTimeBehind = Convert.ToDateTime(_SqlPRMirroring.TIME_BEHIND);
                        _MSqlMirroring.DRTimeBehind = Convert.ToDateTime(_SqlDRMirroring.TIME_BEHIND);

                        _MSqlMirroring.PRLocalTime = Convert.ToDateTime(_SqlPRMirroring.LOCAL_TIME);
                        _MSqlMirroring.DRLocalTime = Convert.ToDateTime(_SqlDRMirroring.LOCAL_TIME);
                        var isSuccess = MSSQLMirroringDataAccess.AddMSSQLMonitorStatus(_MSqlMirroring, CurrentInfraObject.Id);
                        if (isSuccess)
                        {
                            Logger.InfoFormat("MSSQl Mirroring Replication Monitor is created", CurrentInfraObject.Name);
                        }
                    }
                    else
                    {
                        SqlMirroring _SqlPRMirroring = new SqlMirroring();
                        _SqlPRMirroring = MSSqlHelper.MirroringMonitoringStatus(CurrentServer.PRIPAddress, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRDatabaseSID, Convert.ToString(CurrentDatabase.DatabaseSql.PRPort));
                        SqlMirroring _SqlDRMirroring = new SqlMirroring();
                        _SqlDRMirroring = MSSqlHelper.MirroringMonitoringStatus(CurrentServer.DRIPAddress, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRDatabaseSID, Convert.ToString(CurrentDatabase.DatabaseSql.DRPort));
                        MSqlMirroring _MSqlMirroring = new MSqlMirroring();

                        _MSqlMirroring.PRDatabaseName = string.IsNullOrEmpty(_SqlPRMirroring.DATABASE_NAME) ? "NA" : _SqlPRMirroring.DATABASE_NAME;
                        _MSqlMirroring.DRDatabaseName = string.IsNullOrEmpty(_SqlDRMirroring.DATABASE_NAME) ? "NA" : _SqlDRMirroring.DATABASE_NAME;
                        _MSqlMirroring.PRDBRole = string.IsNullOrEmpty(_SqlPRMirroring.ROLE_OFDB_QUERY_FIRED) ? "NA" : _SqlPRMirroring.ROLE_OFDB_QUERY_FIRED;
                        _MSqlMirroring.DRDBRole = string.IsNullOrEmpty(_SqlDRMirroring.ROLE_OFDB_QUERY_FIRED) ? "NA" : _SqlDRMirroring.ROLE_OFDB_QUERY_FIRED;
                        _MSqlMirroring.PRMirroringState = string.IsNullOrEmpty(_SqlPRMirroring.MIRRORING_STATE) ? "NA" : _SqlPRMirroring.MIRRORING_STATE;
                        _MSqlMirroring.DRMirroringState = string.IsNullOrEmpty(_SqlDRMirroring.MIRRORING_STATE) ? "NA" : _SqlDRMirroring.MIRRORING_STATE;
                        _MSqlMirroring.PRLogGenerateRate = string.IsNullOrEmpty(_SqlPRMirroring.LOG_GENERAT_RATE) ? "NA" : _SqlPRMirroring.LOG_GENERAT_RATE;
                        _MSqlMirroring.DRLogGenerateRate = string.IsNullOrEmpty(_SqlDRMirroring.LOG_GENERAT_RATE) ? "NA" : _SqlDRMirroring.LOG_GENERAT_RATE;
                        _MSqlMirroring.PRUnsentLog = string.IsNullOrEmpty(_SqlPRMirroring.UNSENT_LOG) ? "NA" : _SqlPRMirroring.UNSENT_LOG;
                        _MSqlMirroring.DRUnsentLog = string.IsNullOrEmpty(_SqlDRMirroring.UNSENT_LOG) ? "NA" : _SqlDRMirroring.UNSENT_LOG;
                        _MSqlMirroring.PRSentRate = string.IsNullOrEmpty(_SqlPRMirroring.SENT_RATE) ? "NA" : _SqlPRMirroring.SENT_RATE;
                        _MSqlMirroring.DRSentRate = string.IsNullOrEmpty(_SqlDRMirroring.SENT_RATE) ? "NA" : _SqlDRMirroring.SENT_RATE;
                        _MSqlMirroring.PRUnrestoredLog = string.IsNullOrEmpty(_SqlPRMirroring.UNRESTORED_LOG) ? "NA" : _SqlPRMirroring.UNRESTORED_LOG;
                        _MSqlMirroring.DRUnrestoredLog = string.IsNullOrEmpty(_SqlDRMirroring.UNRESTORED_LOG) ? "NA" : _SqlDRMirroring.UNRESTORED_LOG;
                        _MSqlMirroring.PRRecoveryRate = string.IsNullOrEmpty(_SqlPRMirroring.RECOVERY_RATE) ? "NA" : _SqlPRMirroring.RECOVERY_RATE;
                        _MSqlMirroring.DRRecoveryRate = string.IsNullOrEmpty(_SqlDRMirroring.RECOVERY_RATE) ? "NA" : _SqlDRMirroring.RECOVERY_RATE;

                        _MSqlMirroring.PRTransactionDelay = string.IsNullOrEmpty(_SqlPRMirroring.TRANSACTION_DELAY) ? "NA" : _SqlPRMirroring.TRANSACTION_DELAY;
                        _MSqlMirroring.DRTransactionDelay = string.IsNullOrEmpty(_SqlDRMirroring.TRANSACTION_DELAY) ? "NA" : _SqlDRMirroring.TRANSACTION_DELAY;

                        _MSqlMirroring.PRTransactionPerSecond = string.IsNullOrEmpty(_SqlPRMirroring.TRANSACTION_PER_SEC) ? "NA" : _SqlPRMirroring.TRANSACTION_PER_SEC;
                        _MSqlMirroring.DRTransactionPerSecond = string.IsNullOrEmpty(_SqlDRMirroring.TRANSACTION_PER_SEC) ? "NA" : _SqlDRMirroring.TRANSACTION_PER_SEC;

                        _MSqlMirroring.PRAverageDelay = string.IsNullOrEmpty(_SqlPRMirroring.AVERAGE_DELAY) ? "NA" : _SqlPRMirroring.AVERAGE_DELAY;
                        _MSqlMirroring.DRAverageDelay = string.IsNullOrEmpty(_SqlDRMirroring.AVERAGE_DELAY) ? "NA" : _SqlDRMirroring.AVERAGE_DELAY;

                        _MSqlMirroring.PRTimeRecorded = Convert.ToDateTime(_SqlPRMirroring.TIME_RECORDED);
                        _MSqlMirroring.DRTimeRecorded = Convert.ToDateTime(_SqlDRMirroring.TIME_RECORDED);

                        _MSqlMirroring.PRTimeBehind = Convert.ToDateTime(_SqlPRMirroring.TIME_BEHIND);
                        _MSqlMirroring.DRTimeBehind = Convert.ToDateTime(_SqlDRMirroring.TIME_BEHIND);

                        _MSqlMirroring.PRLocalTime = Convert.ToDateTime(_SqlPRMirroring.LOCAL_TIME);
                        _MSqlMirroring.DRLocalTime = Convert.ToDateTime(_SqlDRMirroring.LOCAL_TIME);
                        var isSuccess = MSSQLMirroringDataAccess.AddMSSQLMonitorStatus(_MSqlMirroring, CurrentInfraObject.Id);
                        if (isSuccess)
                        {
                            Logger.InfoFormat("MSSQl Mirroring Replication Monitor is created", CurrentInfraObject.Name);
                        }
                    }
                }
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorMSSQLMirroringComponent:" + CurrentInfraObject.Name + exc.Message);
                throw;
            }
            catch (Exception exception)
            {
                Logger.Info("Exception:MonitorMSSQLMirroringComponent:" + CurrentInfraObject.Name + exception.Message);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occurred while performing monitor MSSQL Mirroring", exception);
            }
        }

        public void RaiseMqSqlMirroringAlert(MSqlMirroring _MSqlMirroring, Server server, DatabaseBase database, string DBName)
        {
            bool status = true;
            int alertType;
            string message = "";
            BcmsExceptionType bcmsexceptiontype;
            alertType = (int)AlertNotificationType.DBcheck;
            if ((!string.IsNullOrEmpty(_MSqlMirroring.PRMirroringState)) && (_MSqlMirroring.PRMirroringState.ToLower() != "synchronized"))
            {
                status = false;
                bcmsexceptiontype = BcmsExceptionType.MSSQL_MIRRORING_State_synchronized;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Database(" + DBName + ") State: is not synchronized ";
            }
            //else if ((!string.IsNullOrEmpty(mshealth.DatabaseState)) && (mshealth.DatabaseState.ToLower() == "Shutdown"))
            //{
            //    status = false;
            //    bcmsexceptiontype = BcmsExceptionType.MSSQL_EMCSRDF_DatabaseState_Shutdown;
            //    message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Database(" + DBName + ") State: is Shutdown ";
            //}
            else
            {
                bcmsexceptiontype = BcmsExceptionType.MSSQL_MIRRORING_State_Normal;
                message = "InfraObject Name : " + CurrentInfraObject.Name + " <br/> HostIPAddress :" + CurrentServer.PRIPAddress + " Database(" + DBName + ") Mirroring State: is " + _MSqlMirroring.PRMirroringState.ToLower();
            }
            RaiseMqSqlMirroringAlert(status, alertType, bcmsexceptiontype, message, "MonitorMSSqlMirroringJobState");
        }

        private void RaiseMqSqlMirroringAlert(bool AlertStatus, int alertType, BcmsExceptionType bcmsexceptiontype, string message, string Alertdetails)
        {
            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (AlertStatus)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));
                    ExceptionManager.Manage(new BcmsException(bcmsexceptiontype, message), Alertdetails, CurrentInfraObject.Id, CurrentInfraObject.Name);
                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));
                    ExceptionManager.Manage(new BcmsException(bcmsexceptiontype, message), Alertdetails, CurrentInfraObject.Id, CurrentInfraObject.Name);
                }
            }
        }

        //public void MqSqlMirroringStatus(string IPAddress, string DBName, string USer, string Password, string Port)
        //{
        //    Logger.Info(CurrentInfraObject.Name + " : MqSqlMirroringStatus() MSSql Mirroring Status Monitor:");
        //    string size = "";
        //    var MsSqlMirroringStatus = new MsSqlEmcSrdfFullDB();
        //    MsSqlMirroringStatus.InfraobjectId = CurrentInfraObject.Id;
        //    try
        //    {
        //        try
        //        {
        //            if (!string.IsNullOrEmpty(MSSqlHelper.DbState(IPAddress, DBName, USer, Password, Port)))
        //            {
        //                MsSqlMirroringStatus.DatabaseState = MSSqlHelper.DbState(IPAddress, DBName, USer, Password, Port);
        //            }
        //            Logger.Info("================== MSSqlHelper.DbState Result = " + MsSqlMirroringStatus.DatabaseState);
        //        }
        //        catch (BcmsException exc)
        //        {
        //            Logger.Info(CurrentInfraObject.Name + " : Error in  MSSqlHelper.DbState MSSql Mirroring Monitor Status");
        //            ExceptionManager.Manage(exc, JobName.MSSQLMIrroring.ToString(), CurrentInfraObject.Id, "MSSqlMirroringMonitoringJob");
        //        }

        //        if (MsSqlMirroringStatus.DatabaseState.ToLower().Contains("online"))
        //        {
        //            //try
        //            //{
        //            //    if (!string.IsNullOrEmpty(MSSqlHelper.GetInstance(IPAddress, DBName, USer, Password, Port)))
        //            //    {
        //            //        MsSqlEmcSrdf.InstanceName = MSSqlHelper.GetInstance(IPAddress, DBName, USer, Password, Port);
        //            //    }
        //            //    Logger.Info("================== MSSqlHelper.GetInstance Result = " + MsSqlEmcSrdf.InstanceName);
        //            //}
        //            //catch (BcmsException exc)
        //            //{
        //            //    Logger.Info(CurrentInfraObject.Name + " : Error in  MSSqlHelper.GetInstance MSSql  Monitor ");
        //            //    ExceptionManager.Manage(exc, JobName.MSSQLMIrroring.ToString(), CurrentInfraObject.Id, "MSSqlMirroringMonitoringJob");
        //            //}
        //            try
        //            {
        //                if (!string.IsNullOrEmpty(MSSqlHelper.DbRestrictAccessStatus(IPAddress, DBName, USer, Password, Port)))
        //                {
        //                    MsSqlMirroringStatus.RestrictAccessStatus = MSSqlHelper.DbRestrictAccessStatus(IPAddress, DBName, USer, Password, Port);
        //                }
        //                Logger.Info("================== MSSqlHelper.DbRestrictAccessStatus Result = " + MsSqlMirroringStatus.RestrictAccessStatus);
        //            }
        //            catch (BcmsException exc)
        //            {
        //                Logger.Info(CurrentInfraObject.Name + " : Error in DbRestrictAccessStatus() MqSql Monitor ");
        //                ExceptionManager.Manage(exc, JobName.MSSQLMIrroring.ToString(), CurrentInfraObject.Id, "MSSqlMirroringMonitoringJob");
        //            }
        //        }
        //        else
        //        {
        //            MsSqlMirroringStatus.InstanceName = "N/A";
        //            MsSqlMirroringStatus.RestrictAccessStatus = "N/A";
        //        }

        //        MsSqlMirroringStatus.CreatorId = 1;
        //        MsSqlMirroringStatus.CreateDate = DateTime.Now;
        //        var MsSqlMirroringStatusReturn = MSSQLEmcsrdfDataAccess.CreateMssqlMonitor(MsSqlMirroringStatus);
        //        Logger.Info(CurrentInfraObject.Name + " : MSSqlStatus() Saved to database MSSql Job Logs and Status");
        //    }
        //    catch (BcmsException exc)
        //    {
        //        Logger.Info(CurrentInfraObject.Name + " : Error in MqSqlMirroringStatus() MSSql Monitor ");
        //        ExceptionManager.Manage(exc, JobName.MSSQLMIrroring.ToString(), CurrentInfraObject.Id, "MSSqlMirroringMonitoringJob");
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.Info(CurrentInfraObject.Name + " : Error MqSqlMirroringStatus() MqSql Monitor :");
        //        var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Bcms.Common.Shared.Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MSSQLMIrroring.ToString(), "MSSqlMirroringMonitoringJob", string.Empty, ExceptionType.UnHandled), exc);

        //        ExceptionManager.Manage(bcmsException, JobName.MSSQLMIrroring.ToString(), CurrentInfraObject.Id, "MSSqlMirroringMonitoringJob");
        //    }
        //}

        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }
        ~MSSQLMirroringClient()
        {
            Dispose(false);
        }
        #endregion
    }
}
