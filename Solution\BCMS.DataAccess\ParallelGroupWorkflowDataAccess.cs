﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using log4net;

namespace Bcms.DataAccess
{
    public class ParallelGroupWorkflowDataAccess : BaseDataAccess
    {
        private static object _lockObject = new object();
        private readonly static ILog _logger = LogManager.GetLogger(typeof(ParallelGroupWorkflowDataAccess));

        public static IList<ParallelGroupWorkflow> GetByParallelDROperationId(int drId)
        {
            var parallelworkflow = new List<ParallelGroupWorkflow>();

            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGROUPWF_GETBYOPERATID"))
            {
                Database.AddInParameter(cmd, Dbstring + "iParallelDrOperationId", DbType.Int32, drId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    while (myParallelWorkflowReader.Read())
                    {

                        var parallelGroup = new ParallelGroupWorkflow();

                        parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                        parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                        parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                        parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                        parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                        parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                        parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);
                        //WF Pause resume
                        parallelGroup.IsResume = Convert.IsDBNull(myParallelWorkflowReader["IsResume"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsResume"]);
                        parallelGroup.IsReExecute = Convert.IsDBNull(myParallelWorkflowReader["IsReExecute"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsReExecute"]);
                        parallelGroup.IsPause = Convert.IsDBNull(myParallelWorkflowReader["IsPause"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsPause"]);
                        parallelGroup.JobName = Convert.IsDBNull(myParallelWorkflowReader["JobName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["JobName"]);

                        // parallel.Id = myParallelWorkflowReader.IsDBNull(0) ? 0 : myParallelWorkflowReader.GetInt32(0);
                        // parallel.GroupId = myParallelWorkflowReader.IsDBNull(1) ? 0 : myParallelWorkflowReader.GetInt32(1);
                        // parallel.GroupName = myParallelWorkflowReader.IsDBNull(2) ? string.Empty : myParallelWorkflowReader.GetString(2);
                        // parallel.WorkflowId = myParallelWorkflowReader.IsDBNull(3) ? 0 : myParallelWorkflowReader.GetInt32(3);
                        // parallel.WorkflowName = myParallelWorkflowReader.IsDBNull(4) ? string.Empty : myParallelWorkflowReader.GetString(4);
                        // parallel.CurrentActionId = myParallelWorkflowReader.IsDBNull(5) ? 0 : myParallelWorkflowReader.GetInt32(5);
                        // parallel.CurrentActionName = myParallelWorkflowReader.IsDBNull(6) ? string.Empty : myParallelWorkflowReader.GetString(6);
                        // parallel.Status = myParallelWorkflowReader.IsDBNull(7) ? string.Empty : myParallelWorkflowReader.GetString(7);
                        // parallel.Message = myParallelWorkflowReader.IsDBNull(8) ? string.Empty : myParallelWorkflowReader.GetString(8);
                        // parallel.ParallelDROperationId = myParallelWorkflowReader.IsDBNull(9) ? 0 : myParallelWorkflowReader.GetInt32(9);
                        // parallel.ConditionalOperation = myParallelWorkflowReader.IsDBNull(10) ? 0 : myParallelWorkflowReader.GetInt32(10);
                        //// parallel.ConditionalOperation = myParallelWorkflowReader.IsDBNull(10) ? ConditionalOperationType.None : (ConditionalOperationType)Enum.Parse(typeof(ConditionalOperationType), myParallelWorkflowReader.GetString(10), true);

                        parallelworkflow.Add(parallelGroup);
                    }
                }

                return parallelworkflow;
            }

        }

        public static ParallelGroupWorkflow GetById(int id)
        {
            var parallelGroup = new ParallelGroupWorkflow();

            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGROUPWORKFLOW_GETBYID"))
            {
                Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelWorkflowReader.Read())
                    {
                        parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                        parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                        parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                        parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                        parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                        parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                        parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);
                        //WF Pause resume
                        parallelGroup.IsResume = Convert.IsDBNull(myParallelWorkflowReader["IsResume"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsResume"]);
                        parallelGroup.IsReExecute = Convert.IsDBNull(myParallelWorkflowReader["IsReExecute"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsReExecute"]);
                        parallelGroup.IsPause = Convert.IsDBNull(myParallelWorkflowReader["IsPause"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsPause"]);
                        parallelGroup.JobName = Convert.IsDBNull(myParallelWorkflowReader["JobName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["JobName"]);
                        parallelGroup.ProgressStatus = Convert.IsDBNull(myParallelWorkflowReader["ProgressStatus"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["ProgressStatus"]);
                        //parallel.Id = myParallelWorkflowReader.IsDBNull(0) ? 0 : myParallelWorkflowReader.GetInt32(0);

                        //parallel.GroupId = myParallelWorkflowReader.IsDBNull(1) ? 0 : myParallelWorkflowReader.GetInt32(1);

                        //parallel.GroupName = myParallelWorkflowReader.IsDBNull(2) ? string.Empty : myParallelWorkflowReader.GetString(2);

                        //parallel.WorkflowId = myParallelWorkflowReader.IsDBNull(3) ? 0 : myParallelWorkflowReader.GetInt32(3);
                        //parallel.WorkflowName = myParallelWorkflowReader.IsDBNull(4) ? string.Empty : myParallelWorkflowReader.GetString(4);

                        //parallel.CurrentActionId = myParallelWorkflowReader.IsDBNull(5) ? 0 : myParallelWorkflowReader.GetInt32(5);
                        //parallel.CurrentActionName = myParallelWorkflowReader.IsDBNull(6) ? string.Empty : myParallelWorkflowReader.GetString(6);


                        //parallel.Status = myParallelWorkflowReader.IsDBNull(7) ? string.Empty : myParallelWorkflowReader.GetString(7);
                        //parallel.Message = myParallelWorkflowReader.IsDBNull(8) ? string.Empty : myParallelWorkflowReader.GetString(8);
                        //parallel.ParallelDROperationId = myParallelWorkflowReader.IsDBNull(9) ? 0 : myParallelWorkflowReader.GetInt32(9);
                        //parallel.ConditionalOperation = myParallelWorkflowReader.IsDBNull(10) ? 0 : myParallelWorkflowReader.GetInt32(10);
                        ////parallel.ConditionalOperation = myParallelWorkflowReader.IsDBNull(10) ? ConditionalOperationType.None : (ConditionalOperationType)Enum.Parse(typeof(ConditionalOperationType), myParallelWorkflowReader.GetString(10), true);

                    }
                    else
                    {
                        parallelGroup = null;
                    }
                }

                return parallelGroup;
            }

        }

        public static bool UpdateByStatusAndMessage(ParallelGroupWorkflow parallel)
        {
            try
            {
                lock (_lockObject)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("ParallelGRWF_UpdateActionSTUS"))
                    {
                        Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallel.Id);
                        Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, parallel.CurrentActionId);
                        Database.AddInParameter(cmd, Dbstring + "iActionName", DbType.AnsiString, parallel.CurrentActionName);
                        Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallel.Status);
                        Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, string.IsNullOrEmpty(parallel.Message) ? string.Empty : parallel.Message);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("ParallelGroupWF_Cur"));
#endif
                        int isuccess = Database.ExecuteNonQuery(cmd);
                        return isuccess > 0;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel GroupWorkflow Details by status and message ", exc);
            }
        }

        public static bool UpdateByProgress(int id, string progress)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGPWF_UPDATEBYPROGRESS"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iProgress", DbType.AnsiString, progress);
                    int isuccess = Database.ExecuteNonQuery(cmd);
                    return isuccess > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel by progress ", exc);
            }
        }

        public static bool UpdateByConditionOperation(int id, int operationId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ParallelGpWkflo_UpdtByCndiOp"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iOperationId", DbType.Int32, operationId);
                    int isuccess = Database.ExecuteNonQuery(cmd);
                    return isuccess > 0;
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel GroupWorkflow Details by status and message ", exc);
            }


        }

        public static bool UpdateStatusById(int id, string status)
        {
            try
            {

                const string sp = "PARALLELGPWF_UPDATEBYSTATUBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, status);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public static bool IsWorkflowGetStartConfirmation(int id)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("PARALLELGPWF_ISStart"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var myScalar = Database.ExecuteScalar(dbCommand);

                    return myScalar == null;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check IsWorkflow GetStart Confirmation", exc);
            }
        }

        public static string GetRTOByParallelGroupWorkflowId(int groupWorkflowId, int infraobjectId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ParallelGRWF_GetRTO"))
                {
                    Database.AddInParameter(cmd, Dbstring + "parallelGroupId", DbType.Int32, groupWorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "infraObjectId", DbType.Int32, infraobjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("ParallelGroupWF_Cur"));
#endif
                    return Database.ExecuteScalar(cmd).ToString();
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel GroupWorkflow Details by status and message ", exc);
            }
        }


        public static IList<ParallelGroupWorkflow> GetByParallelDROperationIdWFId(int drId, int workflowId)
        {
            try
            {
                var parallelworkflow = new List<ParallelGroupWorkflow>();

                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGROUPWF_GETBYDROPWFID"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParallelDrOperationId", DbType.Int32, drId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkFlowId", DbType.Int32, workflowId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("ParallelGroupWF_Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {

                            var parallel = new ParallelGroupWorkflow();

                            parallel.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            parallel.InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                            parallel.InfraObjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]);
                            parallel.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            parallel.WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            parallel.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
                            parallel.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"]);
                            parallel.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            parallel.Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]);
                            parallel.ParallelDROperationId = Convert.IsDBNull(reader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(reader["ParallelDROperationId"]);
                            parallel.ConditionalOperation = Convert.IsDBNull(reader["ConditionalOperation"]) ? 0 : Convert.ToInt32(reader["ConditionalOperation"]);

                            parallelworkflow.Add(parallel);
                        }
                    }

                    return parallelworkflow;
                }
            }
            catch (Exception exe)
            {
                throw;
            }
        }

        public static ParallelGroupWorkflow GetByParallelGroupDROperIdWFId(int ParallelDROperationId, int workflowId)
        {
            var parallelGroup = new ParallelGroupWorkflow();

            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGRWF_GETBYDRIDWFID"))
            {
                Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32, ParallelDROperationId);
                Database.AddInParameter(cmd, Dbstring + "iworkflowId", DbType.Int32, workflowId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelWorkflowReader.Read())
                    {
                        parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                        parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                        parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                        parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                        parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                        parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                        parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);

                    }
                    else
                    {
                        parallelGroup = null;
                    }
                }

                return parallelGroup;
            }

        }

        //Added by Karthick B for ITIT-10566
        public static bool UpdateResumeStatusGrpId(int id, int resumeStatus, int reExecuteStatus)
        {
            try
            {
                const string sp = "ParalGrpWF_UpdateResumeStatus";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddOutParameter(cmd, "@ReturnCode", DbType.Int32, 4);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iresumeStatus", DbType.AnsiString, resumeStatus);
                    Database.AddInParameter(cmd, Dbstring + "ireExecuteStatus", DbType.AnsiString, reExecuteStatus);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

#if (ORACLE || MSSQL)
                    return returnCode < 0;
#endif

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        public static bool UpdateJobNameByGrpId(int GrpWFId, int drOperationId, int isPause, string jobName)
        {

            try
            {

                //const string sp = "UpdateJobName_ByGrpId";

                const string sp = "UPDATEJOBNAME_BYGRPID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddOutParameter(cmd, "@ReturnCode", DbType.Int32, 4);
                    Database.AddInParameter(cmd, Dbstring + "iGroupId", DbType.Int32, GrpWFId);
                    Database.AddInParameter(cmd, Dbstring + "iDROpeId", DbType.Int32, drOperationId);
                    Database.AddInParameter(cmd, Dbstring + "iIsPause", DbType.Int32, isPause);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.AnsiString, jobName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

#if (ORACLE || MSSQL)
                    return returnCode < 0;
#endif
                    return returnCode > 0;

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating ParallelGroupWorkflow  UpdateJoNameByGrpId Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public static bool UpdatePauseStatusGrpId(int id, int isPause, int reExecuteStatus)
        {
            try
            {

                //const string sp = "PARALLELGPWF_UpdateResumeStatus";
                const string sp = "PARALLELGPWF_UpdatePauseStatus";


                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsReExecute", DbType.Int32, reExecuteStatus);
                    Database.AddInParameter(cmd, Dbstring + "iIsPause", DbType.Int32, isPause);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode < 0;


                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occured while execute UpdatePauseStatusGrpId : " + ex.Message.ToString());
                if (ex.InnerException != null)
                    _logger.Error("InnerException occured while execute UpdatePauseStatusGrpId : " + ex.InnerException.ToString());
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating UpdatePauseStatusGrpId Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        public static IList<ParallelGroupWorkflow> GetAllPause()
        {
            try
            {
                var parallelworkflow = new List<ParallelGroupWorkflow>();

            //using (DbCommand cmd = Database.GetStoredProcCommand("ParallelGroupWorkflow_GetAllPause"))
            using (DbCommand cmd = Database.GetStoredProcCommand("ParalGrpWF_GetAllPause"))
            {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    while (myParallelWorkflowReader.Read())
                    {

                        var parallelGroup = new ParallelGroupWorkflow();

                        parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                        parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                        parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                        parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                        parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                        parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelGroup.IsResume = Convert.IsDBNull(myParallelWorkflowReader["IsResume"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsResume"]);
                        parallelGroup.IsReExecute = Convert.IsDBNull(myParallelWorkflowReader["IsReExecute"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsReExecute"]);
                        parallelGroup.IsPause = Convert.IsDBNull(myParallelWorkflowReader["IsPause"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsPause"]);
                        parallelGroup.JobName = Convert.IsDBNull(myParallelWorkflowReader["JobName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["JobName"]);
                        parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                        parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);
                        parallelGroup.ProgressStatus = Convert.IsDBNull(myParallelWorkflowReader["ProgressStatus"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["ProgressStatus"]);



                        parallelworkflow.Add(parallelGroup);
                    }
                }

                return parallelworkflow;
            }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occured while execute GetAllPause : " + ex.Message.ToString());
                if (ex.InnerException != null)
                    _logger.Error("InnerException occured while execute GetAllPause : " + ex.InnerException.ToString());
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating GetAllPause Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #region Pause Resume workflow

        public static IList<ParallelGroupWorkflow> GetGroupWorkflowByReExcute(int reExecute)
        {
            try
            {
                var parallelworkflow = new List<ParallelGroupWorkflow>();

            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGRWF_GetReExecute"))
            {
                Database.AddInParameter(cmd, Dbstring + "ireExecute", DbType.Int32, reExecute);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    while (myParallelWorkflowReader.Read())
                    {
                        var parallelGroup = new ParallelGroupWorkflow();

                        parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                        parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                        parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                        parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                        parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                        parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                        parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);
                        parallelGroup.IsResume = Convert.IsDBNull(myParallelWorkflowReader["IsResume"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsResume"]);
                        parallelGroup.IsReExecute = Convert.IsDBNull(myParallelWorkflowReader["IsReExecute"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsReExecute"]);
                        parallelGroup.IsPause = Convert.IsDBNull(myParallelWorkflowReader["IsPause"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsPause"]);
                        parallelGroup.JobName = Convert.IsDBNull(myParallelWorkflowReader["JobName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["JobName"]);
                        parallelGroup.ProgressStatus = Convert.IsDBNull(myParallelWorkflowReader["ProgressStatus"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["ProgressStatus"]);

                        parallelworkflow.Add(parallelGroup);
                    }

                }

                return parallelworkflow;
            }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occured while execute GetGroupWorkflowByReExcute : " + ex.Message.ToString());
                if (ex.InnerException != null)
                    _logger.Error("InnerException occured while execute GetGroupWorkflowByReExcute : " + ex.InnerException.ToString());
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating GetGroupWorkflowByReExcute Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }
        //Added by Karthick B for ITIT-10566

        #endregion

    }
}