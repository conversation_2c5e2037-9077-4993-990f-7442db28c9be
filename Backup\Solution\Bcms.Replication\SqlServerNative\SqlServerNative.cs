﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using log4net;
using LogshippingNative;
using Bcms.Common;
using Bcms.Replication.AdoUtility;
using System.ServiceProcess;
using System.Diagnostics;
using System.IO;

namespace Bcms.Replication.SqlServerNative
{
    public class SqlServerNative
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(SqlServerNative));
        #region Server

        public static string RunStoreProcedurePR_Native(string user, string password, string server, string SharefolderDR, string Proc,string loginfile)
        {
            try
            {
                var fileName = SharefolderDR.TrimEnd('\\') + "\\" + loginfile;
                Logger.InfoFormat("Login Migration Primary File Name : {0} ", fileName);
                StreamWriter file = new StreamWriter(fileName);
                ProcessStartInfo proc = new ProcessStartInfo();
                //string cmd = string.Format(@"-U{0} -P{1} -S{2} -d{3} -q{4} -o{5}", user, password, server, "master", Proc, fileName);
                string cmd = string.Format(@"-U{0} -P{1} -S{2} -d{3} -Q{4}", user, password, server, "master", Proc);
                proc.FileName = "sqlcmd.exe";
                proc.RedirectStandardInput = false;
                proc.RedirectStandardOutput = true;
                proc.Arguments = cmd;
                proc.UseShellExecute = false;
                Process p = Process.Start(proc);
                string res;
                res = p.StandardOutput.ReadToEnd();
                file.WriteLine(res);
                p.WaitForExit();
                file.Flush();
                file.Close();
                p.Dispose();
                return "Successfully done Migrating Login Primary";
            }
            catch (Exception ex)
            {
                Logger.InfoFormat("Error in Login Migration Primary : {0}",ex.Message);
                return "Failure done Migrating Login Primary";
            }
        }

        public static string RunScriptDr_Native(string user, string password, string server, string SharefolderDR, string loginfile)
        {
            try
            {
                var fileName = SharefolderDR.TrimEnd('\\') + "\\" + loginfile;
                Logger.InfoFormat("Login Mogration Secondary File Name : {0} ", fileName);
                ProcessStartInfo proc = new ProcessStartInfo();
                string cmd = string.Format(@"-U{0} -P{1} -S{2} -d{3} -i{4}", user, password, server, "master", fileName);
                proc.FileName = "sqlcmd.exe";
                proc.RedirectStandardInput = true;
                proc.RedirectStandardOutput = false;
                proc.Arguments = cmd;
                proc.UseShellExecute = false;
                Process p = Process.Start(proc);
                p.WaitForExit();
                return "Successfully done Migrating Login Seconday";
            }
            catch (Exception ex)
            {
                Logger.InfoFormat("Error in Login Migration Seconday : {0}", ex.Message);
                return "Failure done Migrating Login Seconday";
            }
        }



    }
}
        # endregion