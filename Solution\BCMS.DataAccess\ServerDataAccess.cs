﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Utility;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using System.Collections.Generic;
using log4net;
using Bcms.DataAccess.Base;
using System.Text;
using System.Diagnostics;
using System.Threading;
using CMDExec;
using CyberArkLib;
using CyberArk.AIM.NetPasswordSDK;
using CyberArk.AIM.NetPasswordSDK.Exceptions;
using System.Net;


namespace Bcms.DataAccess
{
    public class ServerDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ServerDataAccess));

        #region Server

        //public static Server GetServerByGroupId(int groupId)
        //{
        //    var server = new Server();
        //    try
        //    {
        //      

        //        using (DbCommand dbCommand = db.GetStoredProcCommand("Server_GetByGroupId"))
        //        {
        //            db.AddInParameter(dbCommand, Dbstring+"iGroupId", DbType.Int32, groupId);
        //            using (IDataReader myServerReader = db.ExecuteReader(dbCommand))
        //            {
        //                while (myServerReader.Read())
        //                {

        //                    if (myServerReader["Type"].ToString().Contains("PRDBServer") || myServerReader["Type"].ToString().Contains("PRESXIServer"))
        //                    {
        //                        server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
        //                        server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
        //                        server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
        //                        server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
        //                        server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
        //                        server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(myServerReader["EnableSudoAccess"].ToString());

        //                        server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
        //                        if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
        //                            server.PRSudoPassword = string.Empty;
        //                        else
        //                        {
        //                            server.PRSudoPassword = StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
        //                        }
        //                        // server.PRSudoPassword = Convert.IsDBNull(myServerReader["SudoPassword"]) ? string.Empty : myServerReader["SudoPassword"].ToString();
        //                        server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
        //                        server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();
        //                    }
        //                    else if (myServerReader[3].ToString().Contains("DRDBServer") || myServerReader[3].ToString().Contains("DRESXIServer"))
        //                    {
        //                        server.DRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
        //                        server.DRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
        //                        server.DRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
        //                        server.DRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
        //                        server.DRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
        //                        server.DREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(myServerReader["EnableSudoAccess"].ToString());

        //                        server.DRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
        //                        if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
        //                            server.DRSudoPassword = string.Empty;
        //                        else
        //                        {
        //                            server.DRSudoPassword =
        //                                StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
        //                        }

        //                        //server.DRSudoPassword = Convert.IsDBNull(myServerReader["SudoPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
        //                        server.DROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
        //                        server.DRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();
        //                    }


        //                    //if (myServerReader[3].ToString().Contains("PRDBServer") || myServerReader[3].ToString().Contains("PRESXIServer"))
        //                    //{
        //                    //    server.PRId = myServerReader.IsDBNull(0) ? 0 : myServerReader.GetInt32(0);
        //                    //    server.PRName = myServerReader.IsDBNull(1) ? string.Empty : myServerReader.GetString(1);
        //                    //    server.PRIPAddress = myServerReader.IsDBNull(4)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(4));
        //                    //    server.PRUserName = myServerReader.IsDBNull(5)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(5));
        //                    //    server.PRPassword = myServerReader.IsDBNull(6)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(6)); 
        //                    //   server.PREnableSudoAccess = !myServerReader.IsDBNull(7) && myServerReader.GetBoolean(7);

        //                    //    server.PRSudoUser = myServerReader.IsDBNull(8) ? string.Empty: myServerReader.GetString(8);
        //                    //    server.PRSudoPassword = myServerReader.IsDBNull(9)? string.Empty: myServerReader.GetString(9);
        //                    //    server.PROSType = myServerReader.IsDBNull(13) ? string.Empty : myServerReader.GetString(13);
        //                    //    server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader.GetString(15);
        //                    //}
        //                    //else if (myServerReader[3].ToString().Contains("DRDBServer") || myServerReader[3].ToString().Contains("DRESXIServer"))
        //                    //{
        //                    //    server.DRId = myServerReader.IsDBNull(0) ? 0 : myServerReader.GetInt32(0);
        //                    //    server.DRName = myServerReader.IsDBNull(1) ? string.Empty : myServerReader.GetString(1);
        //                    //    server.DRIPAddress = myServerReader.IsDBNull(4)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(4));
        //                    //    server.DRUserName = myServerReader.IsDBNull(5)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(5));
        //                    //    server.DRPassword = myServerReader.IsDBNull(6)
        //                    //                             ? string.Empty
        //                    //                             : StringHelper.Md5Decrypt(myServerReader.GetString(6));
        //                    //  server.DREnableSudoAccess = !myServerReader.IsDBNull(7) && myServerReader.GetBoolean(7);



        //                    //    server.DRSudoUser = myServerReader.IsDBNull(8) ? string.Empty : myServerReader.GetString(8);
        //                    //    server.DRSudoPassword = myServerReader.IsDBNull(9) ? string.Empty :myServerReader.GetString(9);
        //                    //    server.DROSType = myServerReader.IsDBNull(13) ? string.Empty : myServerReader.GetString(13);
        //                    //    server.DRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader.GetString(15);
        //                    //}
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Group Id - " + groupId, exc);

        //    }
        //    return server;
        //}

        public static Server GetServerByInfraObjectId(int infraObjectId)
        {
            var server = new Server();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_GetByInfraObjectId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {

                        while (myServerReader.Read())
                        {
                            Logger.Info("Reading " + infraObjectId);
                            if (myServerReader["Type"].ToString().Contains("PRDBServer") || myServerReader["Type"].ToString().Contains("PRESXIServer") || myServerReader["Type"].ToString().Contains("PRAppServer"))
                            {
                                Logger.Info("Reading Type PR" + infraObjectId);
                                server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                Logger.Info("Reading Type PR" + server.PRId);
                                server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                Logger.Info("Reading Type PRName" + server.PRName);
                                server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                Logger.Info("Reading Type server.PRIPAddress" + server.PRIPAddress);
                                server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.PRPassword = string.Empty;
                                }
                                else
                                {
                                    server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                                }

                                server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.PREnableSudoAccess)
                                {
                                    if (server.PRId > 0)
                                    {
                                        server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                    }
                                }

                                server.PREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                                server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
                                    server.PRSudoPassword = string.Empty;
                                else
                                {
                                    server.PRSudoPassword = StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                }

                                server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                //Added By Jeyapandi 10/6/2014
                                server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPath"].ToString()) != string.Empty && (myServerReader["SshKeyPath"].ToString()) != string.Empty)
                                    {
                                        server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != "N/A")
                                    {
                                        server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt((myServerReader["SshKeyPassword"].ToString()));
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                                server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);

                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.PRHostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);
                                server.PRIsPartOfCluster = Convert.IsDBNull(myServerReader["IsPartOfCluster"]) ? false : Convert.ToBoolean(myServerReader["IsPartOfCluster"]);
                                server.WinRMPort = Convert.IsDBNull(myServerReader["WinRMPort"]) ? 0 : Convert.ToInt32(myServerReader["WinRMPort"]);
                                server.ProxyAccessType = Convert.IsDBNull(myServerReader["ProxyAccessType"]) ? string.Empty : Convert.ToString(myServerReader["ProxyAccessType"]);
                                if (server.IsSingleSignOnEnabled)
                                {
                                    server = GetServerDetails(server);
                                }
                                //End

                            }
                            else if (myServerReader[3].ToString().Contains("DRDBServer") || myServerReader[3].ToString().Contains("DRESXIServer") || myServerReader["Type"].ToString().Contains("DRAppServer"))
                            {
                                //Modify By Jeyapandi 10/6/2014
                                server.DRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.DRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.DRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.DRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.DRPassword = string.Empty;

                                }
                                else
                                {
                                    server.DRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));

                                }

                                server.DREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.DREnableSudoAccess)
                                {
                                    if (server.DRId > 0)
                                    {
                                        server.DRSubstitute_Authentication = GetSubAuthByServerId(server.DRId);
                                    }
                                }

                                server.DREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                                server.DRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
                                    server.DRSudoPassword = string.Empty;
                                else
                                {
                                    server.DRSudoPassword =
                                        StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                }


                                server.DROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.DRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                server.DRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPath"].ToString()) != string.Empty && (myServerReader["SshKeyPath"].ToString()) != string.Empty)
                                    {
                                        server.DRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPath = string.Empty;
                                }
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != "N/A")
                                    {
                                        server.DRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt((myServerReader["SshKeyPassword"].ToString()));
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPassword = string.Empty;
                                }
                                server.DRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.DRHostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);
                                server.DRIsPartOfCluster = Convert.IsDBNull(myServerReader["IsPartOfCluster"]) ? false : Convert.ToBoolean(myServerReader["IsPartOfCluster"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);
                                server.WinRMPort = Convert.IsDBNull(myServerReader["WinRMPort"]) ? 0 : Convert.ToInt32(myServerReader["WinRMPort"]);
                                server.ProxyAccessType = Convert.IsDBNull(myServerReader["ProxyAccessType"]) ? string.Empty : Convert.ToString(myServerReader["ProxyAccessType"]);
            
                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);
                                }

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by InfraObject Id - " + infraObjectId, exc);

            }
            return server;
        }

        public static Server GetServersById(int id)
        {
            var server = new Server();
            try
            {
                using (DbCommand cmmond = Database.GetStoredProcCommand("Server_GetById"))
                {
                    Database.AddInParameter(cmmond, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(cmmond))
                    {
                        while (myServerReader.Read())
                        {
                            server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                            server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : Convert.ToString(myServerReader["Type"]);
                            server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                            server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                            {
                                server.PRPassword = string.Empty;
                            }
                            else
                            {
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                            }

                            server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                            if (server.PREnableSudoAccess)
                            {
                                if (server.PRId > 0)
                                {
                                    server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                }
                            }

                            server.PREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                            server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();

                            server.PRSudoPassword = string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());

                            //server.PRSudoPassword = Convert.IsDBNull(myServerReader["SudoPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                            server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                            server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                            //Added By Jeyapandi 10/6/2014
                            server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            //if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            if (server.PRIsUseSShkeyAuth == 1)
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (server.PRIsUseSShkeyAuth == 1)
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.PRHostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);

                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                            if (server.IsSingleSignOnEnabled)
                            {

                                server = GetServerDetails(server);
                            }


                            //   server.PRId = myServerReader.IsDBNull(0) ? 0 : myServerReader.GetInt32(0);
                            //   server.PRName = myServerReader.IsDBNull(1) ? string.Empty : myServerReader.GetString(1);
                            //   server.PRIPAddress = myServerReader.IsDBNull(4)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(4));
                            //   server.PRUserName = myServerReader.IsDBNull(5)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(5));
                            //   server.PRPassword = myServerReader.IsDBNull(6)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(6));
                            //server.PREnableSudoAccess = !myServerReader.IsDBNull(7) && myServerReader.GetBoolean(7);


                            //   server.PRSudoUser = myServerReader.IsDBNull(8) ? string.Empty : myServerReader.GetString(8);
                            //   server.PRSudoPassword = myServerReader.IsDBNull(9) ? string.Empty : myServerReader.GetString(9);
                            //   server.PROSType = myServerReader.IsDBNull(13) ? string.Empty : myServerReader.GetString(13);
                            //   server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader.GetString(15);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Id - " + id, exc);
            }
            return server;
        }

        public static Server GetServerByName(string name)
        {
            var server = new Server();
            try
            {
                using (DbCommand cmmond = Database.GetStoredProcCommand("Server_GetByName"))
                {
                    Database.AddInParameter(cmmond, Dbstring + "iName", DbType.AnsiString, name);
#if ORACLE
                    cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(cmmond))
                    {
                        while (myServerReader.Read())
                        {
                            server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                            server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : Convert.ToString(myServerReader["Type"]);
                            server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                            server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                            {
                                server.PRPassword = string.Empty;
                            }
                            else
                            {
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                            }

                            server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                            if (server.PREnableSudoAccess)
                            {
                                if (server.PRId > 0)
                                {
                                    server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                }
                            }
                            server.PREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                            server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();

                            server.PRSudoPassword = string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());

                            //server.PRSudoPassword = Convert.IsDBNull(myServerReader["SudoPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                            server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                            server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                            //Added By Jeyapandi 10/6/2014
                            server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            //if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            if (server.PRIsUseSShkeyAuth == 1)
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (server.PRIsUseSShkeyAuth == 1)
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);
                          
                            if (server.IsSingleSignOnEnabled)
                            {

                                server = GetServerDetails(server);
                            }

                            //   server.PRId = myServerReader.IsDBNull(0) ? 0 : myServerReader.GetInt32(0);
                            //   server.PRName = myServerReader.IsDBNull(1) ? string.Empty : myServerReader.GetString(1);
                            //   server.PRIPAddress = myServerReader.IsDBNull(4)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(4));
                            //   server.PRUserName = myServerReader.IsDBNull(5)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(5));
                            //   server.PRPassword = myServerReader.IsDBNull(6)
                            //                            ? string.Empty
                            //                            : StringHelper.Md5Decrypt(myServerReader.GetString(6));
                            //server.PREnableSudoAccess = !myServerReader.IsDBNull(7) && myServerReader.GetBoolean(7);


                            //   server.PRSudoUser = myServerReader.IsDBNull(8) ? string.Empty : myServerReader.GetString(8);
                            //   server.PRSudoPassword = myServerReader.IsDBNull(9) ? string.Empty : myServerReader.GetString(9);
                            //   server.PROSType = myServerReader.IsDBNull(13) ? string.Empty : myServerReader.GetString(13);
                            //   server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader.GetString(15);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Server Name - " + name, exc);
            }
            return server;
        }

        public static Server GetServersByName(string name)
        {
            var server = new Server();
            try
            {
                using (DbCommand cmmond = Database.GetStoredProcCommand("Server_GetByName"))
                {
                    Database.AddInParameter(cmmond, Dbstring + "iName", DbType.AnsiString, name);
#if ORACLE
                    cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(cmmond))
                    {
                        while (myServerReader.Read())
                        {

                            if (myServerReader["Type"].ToString().Contains("PR"))
                            {
                                server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : Convert.ToString(myServerReader["Type"]);
                                server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.PRPassword = string.Empty;
                                }
                                else
                                {
                                    server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                                }

                                server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.PREnableSudoAccess)
                                {
                                    if (server.PRId > 0)
                                    {
                                        server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                    }
                                }

                                server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();

                                server.PRSudoPassword = string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());

                                //server.PRSudoPassword = Convert.IsDBNull(myServerReader["SudoPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                //Added By Jeyapandi 10/6/2014
                                server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                //if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                    {
                                        server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                    {
                                        server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);
                                }
                            }
                            else if (myServerReader["Type"].ToString().Contains("DR"))
                            {
                                server.DRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.DRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : Convert.ToString(myServerReader["Type"]);
                                server.DRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.DRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.DRPassword = string.Empty;

                                }
                                else
                                {
                                    server.DRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));

                                }

                                server.DREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.DREnableSudoAccess)
                                {
                                    if (server.DRId > 0)
                                    {
                                        server.DRSubstitute_Authentication = GetSubAuthByServerId(server.DRId);
                                    }
                                }

                                server.DRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
                                    server.DRSudoPassword = string.Empty;
                                else
                                {
                                    server.DRSudoPassword =
                                        StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                }


                                server.DROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.DRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                server.DRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPath"].ToString()) != string.Empty && (myServerReader["SshKeyPath"].ToString()) != string.Empty)
                                    {
                                        server.DRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPath = string.Empty;
                                }
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != "N/A")
                                    {
                                        server.DRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt((myServerReader["SshKeyPassword"].ToString()));
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPassword = string.Empty;
                                }
                                server.DRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Server Name - " + name, exc);
            }
            return server;
        }

        public static Server GetServersByIdForRac(int id)
        {
            var server = new Server();
            try
            {
                using (DbCommand cmmond = Database.GetStoredProcCommand("Server_GetById"))
                {
                    Database.AddInParameter(cmmond, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(cmmond))
                    {

                        while (myServerReader.Read())
                        {

                            if (myServerReader["Type"].ToString().Contains("PRDBServer") || myServerReader["Type"].ToString().Contains("PRESXIServer") || myServerReader["Type"].ToString().Contains("PRAppServer"))
                            {
                                server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.PRPassword = string.Empty;
                                }
                                else
                                {
                                    server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                                }

                                server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));


                                if (server.PREnableSudoAccess)
                                {
                                    if (server.PRId > 0)
                                    {
                                        server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                    }
                                }

                                server.PREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                                server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
                                    server.PRSudoPassword = string.Empty;
                                else
                                {
                                    server.PRSudoPassword = StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                }

                                server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                //Added By Jeyapandi 10/6/2014
                                server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPath"].ToString()) != string.Empty && (myServerReader["SshKeyPath"].ToString()) != string.Empty)
                                    {
                                        server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                                if (server.PRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != "N/A")
                                    {
                                        server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt((myServerReader["SshKeyPassword"].ToString()));
                                    }
                                    else
                                    {
                                        server.PRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                                server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                server.PRHostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);

                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);
                                }

                            }
                            else if (myServerReader[3].ToString().Contains("DRDBServer") || myServerReader[3].ToString().Contains("DRESXIServer") || myServerReader["Type"].ToString().Contains("DRAppServer"))
                            {
                                //Modify By Jeyapandi 10/6/2014
                                server.DRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.DRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.DRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.DRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                                {
                                    server.DRPassword = string.Empty;

                                }
                                else
                                {
                                    server.DRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));

                                }

                                server.DREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                server.DREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);

                                if (server.DREnableSudoAccess)
                                {
                                    if (server.DRId > 0)
                                    {
                                        server.DRSubstitute_Authentication = GetSubAuthByServerId(server.DRId);
                                    }
                                }


                                server.DRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                if (string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()))
                                    server.DRSudoPassword = string.Empty;
                                else
                                {
                                    server.DRSudoPassword =
                                        StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                }


                                server.DROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.DRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                server.DRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPath"].ToString()) != string.Empty && (myServerReader["SshKeyPath"].ToString()) != string.Empty)
                                    {
                                        server.DRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPath = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPath = string.Empty;
                                }
                                if (server.DRIsUseSShkeyAuth == 1)
                                {
                                    if ((myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != string.Empty && (myServerReader["SshKeyPassword"].ToString()) != "N/A")
                                    {
                                        server.DRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt((myServerReader["SshKeyPassword"].ToString()));
                                    }
                                    else
                                    {
                                        server.DRSSHKeyPassword = string.Empty;
                                    }
                                }
                                else
                                {
                                    server.DRSSHKeyPassword = string.Empty;
                                }
                                server.DRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                server.DRHostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);

                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);

                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Id for Rac- " + id, exc);
            }
            return server;
        }

        // Added by Neeraj for 24Hours status report
        public static IList<Server> GetByGroupId(int groupId)
        {

            IList<Server> serverlist = new List<Server>();
            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_GetByInfraObjectId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    //cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {
                            var server = new Server();
                            server.PRId = Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = myServerReader["Name"].ToString();
                            server.PRType = myServerReader["Type"].ToString();
                            server.PRIPAddress = StringHelper.Md5Decrypt(myServerReader["IPAddress"].ToString());
                            server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader["Status"].ToString();
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            //Added By Jeyapandi 10/6/2014
                            server.PRIsUseSShkeyAuth = !Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (!Convert.IsDBNull(myServerReader["SshKeyPassword"]))
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                            if (server.IsSingleSignOnEnabled)
                            {

                                server = GetServerDetails(server);
                            }

                            serverlist.Add(server);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by InfraObject Id - " + groupId, exc);

            }
            return serverlist;

        }

        public static Server GetServersByIdforSCR(int id)
        {
            var server = new Server();
            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {
                            server.PRId = Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = myServerReader["Name"].ToString();
                            server.PRIPAddress = StringHelper.Md5Decrypt(myServerReader["IPAddress"].ToString());
                            server.PRUserName = StringHelper.Md5Decrypt(myServerReader["SSHUserName"].ToString());
                            server.PRPassword = StringHelper.Md5Decrypt(myServerReader["SSHPassword"].ToString());
                            server.PRSudoUser = myServerReader["SudoUser"].ToString();
                            server.PROSType = myServerReader["OSType"].ToString();
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            //server.PRVirtualImagePath = myServerReader[8].ToString();

                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);

                            if (server.IsSingleSignOnEnabled)
                            {

                                server = GetServerDetails(server);
                            }

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Id - " + id, exc);
            }
            return server;
        }

        public static bool UpdateServerByType(int groupId, string serverType)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_UpdateByType"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iServerId", DbType.Int32, groupId);
                    Database.AddInParameter(dbCommand, Dbstring + "iserverType", DbType.AnsiString, serverType.ToString());

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;                
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Details by Type ", exc);
            }

        }

        public static bool UpdateServerByStatus(int id, ServerStatus status)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_UpdateByStatus"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);

                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, status.ToString());
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    if (isuccess > 0)
                        Logger.InfoFormat("{0} Server Status : {1}", id, status);
#if ORACLE
                    return isuccess < 0;                
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Details by Type ", exc);
            }

        }

        public static bool UpdateServerStatusById(int id, int serverstatus)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Serverstatus_UpdateById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring + "iserverstatus", DbType.Int32, serverstatus);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;                
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Status by ID ", exc);
            }

        }

        public static Server GetServerByGroupIdSqlNative(int groupId, int Id, int drid)
        {
            var server = new Server();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_GetByInfraObjectId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {

                            if (Convert.ToInt32(myServerReader["Id"]) == Id)
                            {
                                server.PRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                                server.PREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.PREnableSudoAccess)
                                {
                                    if (server.PRId > 0)
                                    {
                                        server.PRSubstitute_Authentication = GetSubAuthByServerId(server.PRId);
                                    }
                                }
                                
                                server.PREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);
                                server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                server.PRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                server.PRSudoPassword = string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : myServerReader["Type"].ToString();
                                server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);

                                }

                            }
                            else if (Convert.ToInt32(myServerReader["Id"]) == drid)
                            {
                                server.DRId = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                                server.DRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : Convert.ToString(myServerReader["Name"]);
                                server.DRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                                server.DRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                                server.DRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                                server.DREnableSudoAccess = !Convert.IsDBNull("EnableSudoAccess") && Convert.ToBoolean(Convert.ToInt32(myServerReader["EnableSudoAccess"]));

                                if (server.DREnableSudoAccess)
                                {
                                    if (server.DRId > 0)
                                    {
                                        server.DRSubstitute_Authentication = GetSubAuthByServerId(server.DRId);
                                    }
                                }
                                server.DREnableAccess = Convert.IsDBNull("EnableSudoAccess") ? 0 : Convert.ToInt32(myServerReader["EnableSudoAccess"]);
                                server.DRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                                server.DRSudoUser = Convert.IsDBNull(myServerReader["SudoUser"]) ? string.Empty : myServerReader["SudoUser"].ToString();
                                server.DRSudoPassword = string.IsNullOrEmpty(myServerReader["SudoPassword"].ToString()) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SudoPassword"].ToString());
                                server.DRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : myServerReader["Type"].ToString();
                                server.DROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : myServerReader["OSType"].ToString();
                                server.DRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();

                                //Added by Neeraj for SSO Authentication
                                server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                                server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                                server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                                server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);

                                server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                                server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                                server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                                server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                                if (server.IsSingleSignOnEnabled)
                                {

                                    server = GetServerDetails(server);

                                }

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by InfraObject Id - " + groupId, exc);

            }
            return server;
        }

        public static SeverConnection GetServerStatusByIsChecked()
        {
            var serverconnection = new SeverConnection();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Serverstatus_IsChecked"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            serverconnection.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            serverconnection.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            serverconnection.IPAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString(reader["IPAddress"]);
                            serverconnection.SSHUserName = Convert.IsDBNull(reader["SSHUserName"]) ? string.Empty : Convert.ToString(reader["SSHUserName"]);
                            serverconnection.SSHPassword = Convert.IsDBNull(reader["SSHPassword"]) ? string.Empty : Convert.ToString(reader["SSHPassword"]);
                            serverconnection.OSType = Convert.IsDBNull(reader["OSType"]) ? string.Empty : Convert.ToString(reader["OSType"]);
                            serverconnection.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            serverconnection.IsChecked = Convert.IsDBNull(reader["IsChecked"]) ? 0 : Convert.ToInt32(reader["IsChecked"]);
                            serverconnection.IsActive = Convert.ToBoolean(reader["IsActive"]);
                            serverconnection.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            serverconnection.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            serverconnection.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            serverconnection.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                            serverconnection.HostName = Convert.IsDBNull(reader["HostName"]) ? string.Empty : Convert.ToString(reader["HostName"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get ServerStatus information by IsConnected - ", exc);
            }
            return serverconnection;
        }

        public static bool UpdateServerStatus(bool Result)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("ServerStatus_StatusUpdate"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iResult", DbType.Int32, Result);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Status ", exc);
            }

        }

        public static bool ServerUpdateByIsVerified(Server data)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_UpdateByIsVerified"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, data.Id);
                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, data.PRStatus);
                    Database.AddInParameter(dbCommand, Dbstring + "iIsVerified", DbType.Int32, data.IsVerified);
                    Database.AddInParameter(dbCommand, Dbstring + "iErrorMessage", DbType.AnsiString, data.ErrorMessage);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update ServerUpdateByIsVerified ", exc);
            }
        }

        public static IList<Server> GetServerNotVerified()
        {
            IList<Server> serverlist = new List<Server>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SERVER_NOTVERIFIED"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {
                            var server = new Server();
                            server.Id = Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = Convert.IsDBNull(myServerReader["Name"]) ? string.Empty : myServerReader["Name"].ToString();
                            server.PRType = Convert.IsDBNull(myServerReader["Type"]) ? string.Empty : myServerReader["Type"].ToString();
                            server.PRStatus = Convert.IsDBNull(myServerReader["Status"]) ? string.Empty : myServerReader["Status"].ToString();
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            server.PRIPAddress = Convert.IsDBNull(myServerReader["IPAddress"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["IPAddress"]));
                            server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                            {
                                server.PRPassword = string.Empty;
                            }
                            else
                            {
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                            }
                            server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : Convert.ToString(myServerReader["OSType"]);


                            if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (!Convert.IsDBNull(myServerReader["SshKeyPassword"]))
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);

                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);
                            server.WinRMPort = Convert.IsDBNull(myServerReader["WinRMPort"]) ? 0 : Convert.ToInt32(myServerReader["WinRMPort"]);
                            server.ProxyAccessType = Convert.IsDBNull(myServerReader["ProxyAccessType"]) ? string.Empty : Convert.ToString(myServerReader["ProxyAccessType"]);
                            if (server.IsSingleSignOnEnabled)
                            {

                                server = GetServerDetails(server);
                            }

                            serverlist.Add(server);
                        }
                    } return serverlist;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while GetServerNotVerified - ", exc);

            }
        }

        public static bool ServerIsVerifiedUpdate(Server data)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("UpdateServerVerifyStatusById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, data.Id);
                    Database.AddInParameter(dbCommand, Dbstring + "iIsVerified", DbType.Int32, data.IsVerified);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update ServerUpdateByIsVerified ", exc);
            }
        }

        public static IList<Server> GetAllServer()
        {
            IList<Server> serverlist = new List<Server>();
            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("SERVER_GETALL"))
                {

#if ORACLE
                    //cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {
                            var server = new Server();
                            server.PRId = Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = myServerReader["Name"].ToString();
                            server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : Convert.ToString(myServerReader["OSType"]);
                            server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                            if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                            {
                                server.PRPassword = string.Empty;
                            }
                            else
                            {
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                            }
                            server.PRType = myServerReader["Type"].ToString();
                            server.PRIPAddress = StringHelper.Md5Decrypt(myServerReader["IPAddress"].ToString());
                            server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader["Status"].ToString();
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            //Added By Jeyapandi 10/6/2014
                            server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (!Convert.IsDBNull(myServerReader["SshKeyPassword"]))
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                            server = GetServerDetails(server);
                            //End

                            serverlist.Add(server);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get All Server information ", exc);
            }
            return serverlist;
        }

        public static IList<Server> GetAllerverByOsType(string osType)
        {
            IList<Server> serverlist = new List<Server>();
            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("SERVER_GETALLBYOSTYPE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iOSType", DbType.String, osType);
#if ORACLE
                    //cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {
                            var server = new Server();
                            server.PRId = Convert.ToInt32(myServerReader["Id"]);
                            server.PRName = myServerReader["Name"].ToString();
                            server.PROSType = Convert.IsDBNull(myServerReader["OSType"]) ? string.Empty : Convert.ToString(myServerReader["OSType"]);
                            server.PRUserName = Convert.IsDBNull(myServerReader["SSHUserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHUserName"]));
                            if (string.IsNullOrEmpty(myServerReader["SSHPassword"].ToString()) || myServerReader["SSHPassword"].ToString() == "N/A")
                            {
                                server.PRPassword = string.Empty;
                            }
                            else
                            {
                                server.PRPassword = Convert.IsDBNull(myServerReader["SSHPassword"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(myServerReader["SSHPassword"]));
                            }
                            server.PRType = myServerReader["Type"].ToString();
                            server.PRIPAddress = StringHelper.Md5Decrypt(myServerReader["IPAddress"].ToString());
                            server.PRStatus = myServerReader.IsDBNull(15) ? string.Empty : myServerReader["Status"].ToString();
                            server.PRPort = Convert.IsDBNull(myServerReader["Port"]) ? 0 : Convert.ToInt32(myServerReader["Port"]);
                            //Added By Jeyapandi 10/6/2014
                            server.PRIsUseSShkeyAuth = Convert.IsDBNull(myServerReader["IsUseSshKeyAuth"]) ? 0 : Convert.ToInt32(myServerReader["IsUseSshKeyAuth"]);
                            if (!Convert.IsDBNull(myServerReader["SshKeyPath"]))
                            {
                                if (myServerReader["SshKeyPath"].ToString() != string.Empty && myServerReader["SshKeyPath"].ToString() != string.Empty)
                                {
                                    server.PRSSHKeyPath = Convert.IsDBNull(myServerReader["SshKeyPath"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPath"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPath = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPath = string.Empty;
                            }
                            if (!Convert.IsDBNull(myServerReader["SshKeyPassword"]))
                            {
                                if (myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != string.Empty && myServerReader["SshKeyPassword"].ToString() != "N/A")
                                {
                                    server.PRSSHKeyPassword = Convert.IsDBNull(myServerReader["SshKeyPassword"]) ? string.Empty : StringHelper.Md5Decrypt(myServerReader["SshKeyPassword"].ToString());
                                }
                                else
                                {
                                    server.PRSSHKeyPassword = string.Empty;
                                }
                            }
                            else
                            {
                                server.PRSSHKeyPassword = string.Empty;
                            }
                            //Added by Neeraj for SSO Authentication
                            server.HostName = Convert.IsDBNull(myServerReader["HostName"]) ? string.Empty : Convert.ToString(myServerReader["HostName"]);
                            server.SSOType = Convert.IsDBNull(myServerReader["SSOTypeId"]) ? 0 : Convert.ToInt32(myServerReader["SSOTypeId"]);
                            server.IsSingleSignOnEnabled = Convert.IsDBNull(myServerReader["SSOEnabled"]) ? false : Convert.ToBoolean(myServerReader["SSOEnabled"]);
                            server.SSOProfileID = Convert.IsDBNull(myServerReader["SSOProfileID"]) ? 0 : Convert.ToInt32(myServerReader["SSOProfileID"]);
                            server.SafeName = Convert.IsDBNull(myServerReader["Safe"]) ? string.Empty : Convert.ToString(myServerReader["Safe"]);
                            server.Object = Convert.IsDBNull(myServerReader["Object"]) ? string.Empty : Convert.ToString(myServerReader["Object"]);
                            server.FolderPath = Convert.IsDBNull(myServerReader["Folder"]) ? string.Empty : Convert.ToString(myServerReader["Folder"]);
                            server.Reason = Convert.IsDBNull(myServerReader["Reason"]) ? string.Empty : Convert.ToString(myServerReader["Reason"]);

                            server = GetServerDetails(server);
                            //End

                            serverlist.Add(server);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by OS TYPE - " + osType, exc);
            }
            return serverlist;
        }


        #endregion

        #region SSO

        private static Server GetServerDetails(Server server)
        {
            //  server.IsSingleSignOnEnabled = true;
            //  server.SSOType = (int)SSOType.TPAM;


            if (!server.IsSingleSignOnEnabled) return server;

            int iSSOType = server.SSOType;
            Logger.Info("SingleSignOn Started :");
            switch (iSSOType)
            {
                case ((int)SSOType.TPAM):
                    {
                        Logger.Info("SingleSignOn TPAM :");
                        if (!string.IsNullOrEmpty(server.PRName))
                            server.PRPassword = GetTPAMPassword(server.HostName, server.PRUserName, server.PRPassword);

                        if (!string.IsNullOrEmpty(server.DRName))
                            server.DRPassword = GetTPAMPassword(server.HostName, server.DRPassword, server.PRPassword);
                        break;
                    }
                case ((int)SSOType.Cyberark):
                    {
                        Logger.Info("SingleSignOn CyberArk :");
                        //Add the cyberark logic
                        if (!string.IsNullOrEmpty(server.PRName))
                            server.PRPassword = GetCyberArkPassword(server);

                        if (!string.IsNullOrEmpty(server.DRName))
                            server.DRPassword = GetCyberArkPassword(server);
                        break;
                    }
                case ((int)SSOType.Arcos):
                    {
                        Logger.Info("SingleSignOn Arcos :");
                        //Add the Arcos logic
                        if (!string.IsNullOrEmpty(server.PRName))
                            server.PRPassword = GetArcosPassword(server, "PR");

                        if (!string.IsNullOrEmpty(server.DRName))
                            server.DRPassword = GetArcosPassword(server, "DR");
                        break;
                    }

            }

            return server;

        }

        private static string GetTPAMPassword(string strHostName, string strUserName, string strPassword)
        {
            Logger.Info("Retrieving Password for Host: " + strHostName);

            string strCommand = ConstructTPAMCommand(strHostName, strUserName);

            if (!string.IsNullOrEmpty(strCommand))
            {
                Thread.Sleep(1000);
                strPassword = ExecuteCommand(strCommand + strPassword);

                if (!string.IsNullOrEmpty(strPassword))
                    Logger.Info("Password Retrived for Host: " + strHostName); //+ "is: " + strPassword);
                else
                    Logger.Error("Unable to get Password");
            }
            else
                Logger.Error("Error while creating TPAM Command");


            return strPassword;
        }

        public static string GetCyberArkPassword(Server objServer)
        {
            Logger.Info("SingleSignOn CyberArk Started:");
            string strPassword = string.Empty;
            // CyberArkParam objParam = new CyberArkParam();
            PSDKPasswordRequest objParam = new PSDKPasswordRequest();
            PSDKPassword password = null;
            try
            {
                // SSOConfiguration objSSOconfig = SingleSignOnDataAccess.GetBySSOTypeId(Convert.ToInt32(SSOType.Cyberark));
                SSOConfiguration objSSOconfig = SingleSignOnDataAccess.GetBySSOTypeIdProfileId(Convert.ToInt32(SSOType.Cyberark), objServer.SSOProfileID);
                // Server serverSSO = GetServersById(objServer.Id);

                objParam.ConnectionPort = objSSOconfig.Port;
                objParam.ConnectionTimeout = objSSOconfig.ConnectionTimeout;
                // objParam.CredentialFilePath = objSSOconfig.CredentialFilePath;
                //objParam.FolderPath = objServer.FolderPath;
                //objParam.SafeName = objServer.SafeName;

                objParam.CredFilePath = objSSOconfig.CredentialFilePath;
                objParam.Folder = objServer.FolderPath;
                objParam.Safe = objServer.SafeName;
                objParam.Object = objServer.Object;
                objParam.Reason = objServer.Reason;

                // @"C:\Program Files (x86)\CyberArk\ApplicationPasswordProvider\Vault\AppProviderUser.cred";
                //  strPassword = CyberArkLib.CyberArkExec.GetPassword(objParam);
                Logger.Info("SingleSignOn CyberArk GetPassword:");

                password = PasswordSDK.GetPassword(objParam);

                if (password != null)
                {
                    strPassword = password.Content;
                }               
            }
            catch (Exception ex)
            {
                Logger.Error("Exception Occurred while getting Password (SingleSignOn CyberArk):" + ex.Message);
                strPassword = string.Empty;
            }
            return strPassword;
        }

        public static string GetArcosPassword(Server objServer, string strPRDR)
        {
            Logger.Info("SingleSignOn Arcos Started: ");
            string strPassword = string.Empty;

            ARCOSAPIOnline.ARCOSAPI001 objARCOSAPI001 = new ARCOSAPIOnline.ARCOSAPI001();

            try
            {
                SSOConfiguration objSSOconfig = SingleSignOnDataAccess.GetBySSOTypeIdProfileId(Convert.ToInt32(SSOType.Arcos), objServer.SSOProfileID);
                String password = String.Empty;

                objARCOSAPI001.Url = objSSOconfig.ARCOSOnlineUrl;
                if (objARCOSAPI001.Url.ToUpper().StartsWith("HTTPS") == true)
                {
                    ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });
                }

                string shareKey = StringHelper.Md5Decrypt(Convert.ToString(objSSOconfig.SharedKey));
                string domainUser = string.Empty;
                string userName = string.Empty;
                string profileName = string.Empty;
                string serverIP = string.Empty;

                if (!string.IsNullOrEmpty(objSSOconfig.HostName) && objSSOconfig.HostName.Trim().Contains("@"))
                {
                    string[] arcosData = objSSOconfig.HostName.Trim().Split(new Char[] { '@' });
                    if (arcosData != null && arcosData.Length > 0)
                    {
                        //userName = arcosData[0];
                        profileName = arcosData[1];
                    }
                }

                if (strPRDR.ToUpper().Equals("PR"))
                {
                    serverIP = objServer.PRIPAddress;
                    domainUser = objServer.PRUserName;
                }
                else if (strPRDR.ToUpper().Equals("DR"))
                {
                    serverIP = objServer.DRIPAddress;
                    domainUser = objServer.DRUserName;
                }

                if (!string.IsNullOrEmpty(domainUser))
                {
                    if (domainUser.Trim().Contains("\\"))
                    {
                        string[] userData = domainUser.Trim().Split(new Char[] { '\\' });
                        if (userData != null && userData.Length > 0)
                        {
                            //userName = arcosData[0];
                            userName = userData[1];
                        }
                    }
                    else
                    {
                        userName = domainUser;
                    }
                }
                else
                {
                    Logger.Error("UserName is empty or null in (SingleSignOn Arcos GetPassword ) ");
                }

                password = objARCOSAPI001.RequestServicePassword(objSSOconfig.ARCOSWebAPIURL, shareKey, profileName, serverIP, objSSOconfig.ServiceType, userName, objSSOconfig.DBInstance);

                if (password.Length > 1)
                {
                    Logger.Info("SingleSignOn Arcos GetPassword for serverIP : " + serverIP + " has been successfully generated.");
                    strPassword = password.ToString();
                }
                else
                {
                    Logger.Error("Unable To Retrieve Data From ARCOS.");
                }

            }
            catch (Exception ex)
            {
                Logger.Error("Exception Occurred while getting Password (SingleSignOn Arcos): " + ex.Message);
                strPassword = string.Empty;
            }

            return strPassword;
        }

        private static string ExecuteCommand(string command)
        {
            return Bcms.Helper.CMDHelper.ExecuteCommand("cmd.exe", command);
        }

        public static string ConstructTPAMCommand(string strHostName, string strUserName)
        {
            // Get TPAM configuration details from the DB
            // D:\Data\PLINK.exe -ssh -i D:\TPAMKey\TPAMKY.ppk cpapp@************* Retrieve  Retrieve --SystemName norkomdb-uat --AccountName cpapp RequestNote  Automation 
            string strCommand = string.Empty;
            StringBuilder sb = new StringBuilder();

            SSOConfiguration objSSOconfig = SingleSignOnDataAccess.GetBySSOTypeId(Convert.ToInt32(SSOType.TPAM));

            if (objSSOconfig != null)
            {
                sb.Append(objSSOconfig.ExcecutionPath);
                sb.Append(" -ssh -i ");
                sb.Append(objSSOconfig.KeyLocation);
                sb.Append(" ");
                sb.Append(objSSOconfig.HostName);
                sb.Append("@");
                sb.Append(objSSOconfig.IpAddress);
                sb.Append(" Retrieve ");
                sb.Append(" Retrieve ");
                sb.Append("--SystemName ");
                sb.Append(strHostName);
                sb.Append(" --AccountName ");
                sb.Append(strUserName);
                sb.Append(" RequestNote ");
                sb.Append(" Automation ");

                strCommand = sb.ToString();  //"echo ";
            }

            Logger.Info("TPAM Command:" + strCommand);
            return strCommand;
        }

        #endregion


        #region ASU Sudo su 

        public static IList<SubAuthentication> GetSubAuthByServerId(int serverId)
        {
            IList<SubAuthentication> subauthlist = new List<SubAuthentication>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SubstiAuth_GetAllByServerId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iServerId", DbType.Int32, serverId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            SubAuthentication subsAuthn = new SubAuthentication();

                            subsAuthn.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

                            subsAuthn.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);

                            subsAuthn.SubAuthenticationType = Convert.IsDBNull(reader["Auth_Type"]) ? 0 : Convert.ToInt32(reader["Auth_Type"]);

                            subsAuthn.SubPath = Convert.IsDBNull(reader["Path"])
                                ? string.Empty
                                : Convert.ToString(reader["Path"]);

                            subsAuthn.SubUser = Convert.IsDBNull(reader["UserName"])
                                ? string.Empty
                                : Convert.ToString(reader["UserName"]);

                            subsAuthn.SubPassword = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]) == string.Empty ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(reader["Password"]));

                            subsAuthn.IsActive = Convert.IsDBNull(reader["IsActive"]) ? false : Convert.ToBoolean(reader["IsActive"]);

                            subsAuthn.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                                ? 0
                                : Convert.ToInt32(reader["CreatorId"]);

                            subsAuthn.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["CreateDate"]);

                            subsAuthn.UpdatorId = Convert.IsDBNull(reader["UpdatorId"])
                                ? 0
                                : Convert.ToInt32(reader["UpdatorId"]);

                            subsAuthn.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["UpdateDate"]);


                            subauthlist.Add(subsAuthn);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Substitute Authentication Information By ServerId - " + serverId, exc);

            }
            return subauthlist;

        }
        #endregion'

    }
}
