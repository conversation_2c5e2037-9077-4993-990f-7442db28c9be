﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Bcms.Util;


namespace Bcms.Impl.AdoJobStore.Common
{
    public class MySqlDbProvider
    {
        //public static DbMetadata SetMySqlProviderProperties()
        //{
        //    var dbMetadata=new DbMetadata
        //                       {
        //                           AssemblyName = "MySql.Data",
        //                           ProductName = "MySql",
        //                           ConnectionType = Type.GetType("MySql.Data.MySqlClient.MySqlConnection"),
        //                           CommandType = Type.GetType("MySql.Data.MySqlClient.MySqlCommand"),
        //                           ParameterType = Type.GetType("MySql.Data.MySqlClient.MySqlParameter"),
        //                           CommandBuilderType = Type.GetType("MySql.Data.MySqlClient.MySqlCommandBuilder"),
        //                           ParameterDbType = Type.GetType("MySql.Data.MySqlClient.MySqlDbType"),
        //                           ParameterDbTypePropertyName = "MySqlDbType",
        //                           ParameterNamePrefix = "?",
        //                           ExceptionType = Type.GetType("MySql.Data.MySqlClient.MySqlException"),
        //                           UseParameterNamePrefixInParameterCollection = true,
        //                           BindByName = true
        //                       };

        //    return dbMetadata;
        //}
    }
}
