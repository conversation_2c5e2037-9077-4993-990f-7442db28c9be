﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class SybaseWithSRSDataAccess : BaseDataAccess
    {
        public static bool AddSybaseWithSRSMonitorStatus(SybaseWithSRSMonitor _sybaseWithSRSMonitor)
        {
            try
            {
                const string sp = "SybaseSRSMonitrStatus_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _sybaseWithSRSMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseStatusPR", DbType.AnsiString, _sybaseWithSRSMonitor.DatabaseStatusPR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseStatusDR", DbType.AnsiString, _sybaseWithSRSMonitor.DatabaseStatusDR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseLoggingsStatusPR", DbType.AnsiString, _sybaseWithSRSMonitor.DatabaseLoggingsStatusPR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseLoggingsStatusDR", DbType.AnsiString, _sybaseWithSRSMonitor.DatabaseLoggingsStatusDR);
                    Database.AddInParameter(cmd, Dbstring + "iDatadevicedetailsPR", DbType.AnsiString, _sybaseWithSRSMonitor.DatadeviceDetailsPR);
                    Database.AddInParameter(cmd, Dbstring + "iDatadevicedetailsDR", DbType.AnsiString, _sybaseWithSRSMonitor.DatadeviceDetailsDR);
                    Database.AddInParameter(cmd, Dbstring + "iDataSpaceUsedPR", DbType.AnsiString, _sybaseWithSRSMonitor.DataSpaceUsedPR);
                    Database.AddInParameter(cmd, Dbstring + "iDataSpaceUsedDR", DbType.AnsiString, _sybaseWithSRSMonitor.DataSpaceUsedDR);
                    Database.AddInParameter(cmd, Dbstring + "iLogDeviceDetailsPR", DbType.AnsiString, _sybaseWithSRSMonitor.LogDeviceDetailsPR);
                    Database.AddInParameter(cmd, Dbstring + "iLogDeviceDetailsDR", DbType.AnsiString, _sybaseWithSRSMonitor.LogDeviceDetailsDR);
                    Database.AddInParameter(cmd, Dbstring + "iLogSpaceUsedPR", DbType.AnsiString, _sybaseWithSRSMonitor.LogSpaceUsedPR);
                    Database.AddInParameter(cmd, Dbstring + "iLogSpaceUsedDR", DbType.AnsiString, _sybaseWithSRSMonitor.LogSpaceUsedDR);
                    Database.AddInParameter(cmd, Dbstring + "iRep_agentStatusPR", DbType.AnsiString, _sybaseWithSRSMonitor.Rep_agentStatusPR);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationServerStatus", DbType.AnsiString, _sybaseWithSRSMonitor.ReplicationServerStatus);
                    Database.AddInParameter(cmd, Dbstring + "iTransactionOriginTimePR", DbType.AnsiString, _sybaseWithSRSMonitor.TransactionOriginTimePR);
                    Database.AddInParameter(cmd, Dbstring + "iTransactionCommitTimeDR", DbType.AnsiString, _sybaseWithSRSMonitor.TransactionCommitTimeDR);
                    Database.AddInParameter(cmd, Dbstring + "iLatency", DbType.AnsiString, _sybaseWithSRSMonitor.Latency);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationServerVersion", DbType.AnsiString, _sybaseWithSRSMonitor.ReplicationServerVersion);
                    Database.AddInParameter(cmd, Dbstring + "iRSSDDataDeviceDetails", DbType.AnsiString, _sybaseWithSRSMonitor.RSSDDataDeviceDetails);
                    Database.AddInParameter(cmd, Dbstring + "iRSSDLogSpaceUsed", DbType.AnsiString, _sybaseWithSRSMonitor.RSSDLogSpaceUsed);
                    
                    Database.AddInParameter(cmd, Dbstring + "iRSSDDATASpaceUsed", DbType.AnsiString, _sybaseWithSRSMonitor.RSSDDataSpaceUsed);
                    Database.AddInParameter(cmd, Dbstring + "iRSSDLogDeviceDetails", DbType.AnsiString, _sybaseWithSRSMonitor.RSSDLogDeviceDetails);
                    Database.AddInParameter(cmd, Dbstring + "iPhysicalConnectionState", DbType.AnsiString, _sybaseWithSRSMonitor.PhysicalConnectionState);
                    Database.AddInParameter(cmd, Dbstring + "iHealth", DbType.AnsiString, _sybaseWithSRSMonitor.Health);
                    Database.AddInParameter(cmd, Dbstring + "iActiveConnectionState", DbType.AnsiString, _sybaseWithSRSMonitor.ActiveConnectionState);
                    Database.AddInParameter(cmd, Dbstring + "iStandbyConnectionState", DbType.AnsiString, _sybaseWithSRSMonitor.StandbyConnectionState);
                    Database.AddInParameter(cmd, Dbstring + "iOperationinProgress", DbType.AnsiString, _sybaseWithSRSMonitor.OperationinProgress);
                    Database.AddInParameter(cmd, Dbstring + "iStableDeviceStatus", DbType.AnsiString, _sybaseWithSRSMonitor.StableDeviceStatus);
                    Database.AddInParameter(cmd, Dbstring + "iQuiesceState", DbType.AnsiString, _sybaseWithSRSMonitor.QuiesceState);
                    Database.AddInParameter(cmd, Dbstring + "iFailedTransactions", DbType.AnsiString, _sybaseWithSRSMonitor.FailedTransactions);

                    Database.AddInParameter(cmd, Dbstring + "iDataServerStatusPR", DbType.AnsiString, _sybaseWithSRSMonitor._prDataServerStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDataServerStatusDR", DbType.AnsiString, _sybaseWithSRSMonitor._drDataServerStatus);
                    Database.AddInParameter(cmd, Dbstring + "iBackupServerStatusPR", DbType.AnsiString, _sybaseWithSRSMonitor._prBackUpServerStatus);
                    Database.AddInParameter(cmd, Dbstring + "iBackupServerStatusDR", DbType.AnsiString, _sybaseWithSRSMonitor._drBackUpServerStatus);

                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                    if (returnCode >= 0)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,

                    "Exception occurred in AddSybaseWithSRSMonitorStatus() while Insert Record in sybaseWithSRSmonitoring." +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
    }
}
