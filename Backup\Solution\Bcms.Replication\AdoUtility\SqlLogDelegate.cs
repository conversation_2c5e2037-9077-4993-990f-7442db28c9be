﻿using System;
using System.Data;
using System.Globalization;
using Bcms.Common;
using Bcms.Replication.Interface;

namespace Bcms.Replication.AdoUtility
{
    public class SqlLogDelegate  
    {
        public IDBManager Manager { get; set; }

        public Group CurrentInfraObject { get; set; }


        public SqlLogDelegate(Group group, Server server)
        {
            Manager = new DBManager(group, server);

            CurrentInfraObject = group;
        }

        public bool LogGeneration(DatabaseBase database, string logFileName)
        {
            try
            {
                if (CurrentInfraObject.IsWorkflow == 2)
                {
                    string commandText = string.Format("BACKUP LOG {0} TO  DISK = N'{1}\\{2}\\{3}'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace('_', ' '), database.DatabaseSql.PRPassword, logFileName);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, commandText, CommandType.Text, "DR"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }
                }
                else
                {
                    string commandText = string.Format("BACKUP LOG {0} TO  DISK = N'{1}\\{2}\\{3}'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace('_', ' '), database.DatabaseSql.PRPassword, logFileName);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, commandText, CommandType.Text, "Primary"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool LastLogGeneration(DatabaseBase database, string logFileName)
        {
            try
            {
                if (CurrentInfraObject.IsWorkflow == 2)
                {
                    string commandText = string.Format(@"BACKUP LOG {0} TO  DISK = N'{1}{2}\\{3}' WITH STANDBY ='{4}{5}\\{6}DRUndo.ldf'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace('_', ' '), database.DatabaseSql.PRPassword, logFileName, database.DatabaseSql.PRUndoFilePath.Replace('_', ' '), database.DatabaseSql.PRPassword, database.DatabaseSql.PRPassword);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, commandText, CommandType.Text, "DRMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }

                }
                else
                {
                    string commandText = string.Format(@"BACKUP LOG {0} TO  DISK = N'{1}{2}\\{3}' WITH STANDBY ='{4}{5}\\{6}PRUndo.ldf'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace('_', ' '), database.DatabaseSql.PRPassword, logFileName, database.DatabaseSql.PRUndoFilePath.Replace('_', ' '), database.DatabaseSql.PRPassword, database.DatabaseSql.PRPassword);


                    using (IDbCommand cmd = Manager.PrepareCommand(database, commandText, CommandType.Text, "PrimaryMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool RestoreLog(DatabaseBase database, SqlLog sqlLog)
        {
            try
            {
                if (CurrentInfraObject.IsWorkflow==2)
                {
                    string command = string.Format(CultureInfo.InvariantCulture, @"RESTORE LOG {0} FROM  DISK = N'{1}{2}\{3}' WITH STANDBY = '{4}{5}\{6}PRUndo.ldf'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace("_", " "), database.DatabaseSql.PRPassword, sqlLog.LogFileName, database.DatabaseSql.PRUndoFilePath.Replace("_", " "), database.DatabaseSql.PRPassword, database.DatabaseSql.PRPassword);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.Text, "PrimaryMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }
                }
                else
                {
                    string command = string.Format(CultureInfo.InvariantCulture, @"RESTORE LOG {0} FROM  DISK = N'{1}{2}\{3}' WITH STANDBY = '{4}{5}\{6}DRUndo.ldf'", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace("_", " "), database.DatabaseSql.PRPassword, sqlLog.LogFileName, database.DatabaseSql.PRUndoFilePath.Replace("_", " "), database.DatabaseSql.PRPassword, database.DatabaseSql.PRPassword);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.Text, "DRMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }

                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool RestoreLastLog(DatabaseBase database, SqlLog sqlLog)
        {
            try
            {
                if (CurrentInfraObject.IsWorkflow==2)
                {
                    string command = string.Format(CultureInfo.InvariantCulture, @"RESTORE LOG {0} FROM  DISK = N'{1}{2}\{3}' WITH NORECOVERY", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace("_", " "), database.DatabaseSql.PRPassword, sqlLog.LogFileName);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.Text, "PrimaryMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }


                }
                else
                {
                    string command = string.Format(CultureInfo.InvariantCulture, @"RESTORE LOG {0} FROM  DISK = N'{1}{2}\{3}' WITH NORECOVERY", database.DatabaseSql.PRPassword, database.DatabaseSql.PRTransLogPath.Replace("_", " "), database.DatabaseSql.PRPassword, sqlLog.LogFileName);

                    using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.Text, "DRMaster"))
                    {
                        int returnVale = cmd.ExecuteNonQuery();

                        cmd.Connection.Close();

                        return returnVale != 0;
                    }

                }



            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool KillProcess(DatabaseBase database, string type)
        {
            try
            {
                const string command = "usp_killprocess";

                using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.StoredProcedure, type))
                {
                    Manager.AddCommandParameter(cmd, "@dbname", DbType.AnsiString, database.DatabaseSql.PRPassword);

                    int returnVale = cmd.ExecuteNonQuery();

                    cmd.Connection.Close();

                    return returnVale != 0;
                }
            }
            catch (Exception)
            {
                return false;

            }

        }

        public bool DBOption(DatabaseBase database, string type)
        {
            try
            {
                const string command = "sp_dboption";

                using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.StoredProcedure, type))
                {
                    Manager.AddCommandParameter(cmd, "@dbname", DbType.AnsiString, database.DatabaseSql.PRPassword);
                    Manager.AddCommandParameter(cmd, "@optname", DbType.AnsiString, "single user");
                    Manager.AddCommandParameter(cmd, "@optvalue", DbType.AnsiString, "FALSE");

                    int returnVale = cmd.ExecuteNonQuery();

                    cmd.Connection.Close();

                    return returnVale != 0;
                }
            }
            catch (Exception)
            {
                return false;

            }

        }

        public bool RestoreDatabaseWithRecovery(DatabaseBase database, string type)
        {
            try
            {
                string command = string.Format("RESTORE database {0} with recovery", database.DatabaseSql.PRPassword);

                using (IDbCommand cmd = Manager.PrepareCommand(database, command, CommandType.Text, type))
                {
                    int returnVale = cmd.ExecuteNonQuery();

                    cmd.Connection.Close();

                    return returnVale != 0;
                }
            }
            catch (Exception)
            {
                return false;

            }

        }
    }
}