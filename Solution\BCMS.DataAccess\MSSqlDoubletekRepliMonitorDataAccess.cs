﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;

using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class MSSqlDoubleteRepliMonitorkDataAccess : BaseDataAccess
    {
        public static bool AddMssqldoubletakeMonitorStatus(Bcms.Common.MSSqlDoubletek _doubletakeMonitor)
        {
            const string sp = "mssqldbltkrepmonStatus_create";
            try
            { 
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _doubletakeMonitor.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, _doubletakeMonitor.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.AnsiString, _doubletakeMonitor.JobName);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationStatus", DbType.AnsiString, _doubletakeMonitor.ReplicationStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSourceServer", DbType.AnsiString, _doubletakeMonitor.SourceServer);
                    Database.AddInParameter(cmd, Dbstring + "iTargetServer", DbType.AnsiString, _doubletakeMonitor.TargetServer);
                    Database.AddInParameter(cmd, Dbstring + "iJobType", DbType.AnsiString, _doubletakeMonitor.JobType);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationQueue", DbType.AnsiString, _doubletakeMonitor.ReplicationQueue);
                    Database.AddInParameter(cmd, Dbstring + "iJobActivity", DbType.AnsiString, _doubletakeMonitor.JobActivity);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorStatus", DbType.AnsiString, _doubletakeMonitor.MirrorStatus);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorPercentComplete", DbType.AnsiString, _doubletakeMonitor.MirrorPercentComplete);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorBytesRemaining", DbType.AnsiString, _doubletakeMonitor.MirrorBytesRemaining);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.AnsiString, _doubletakeMonitor.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.AnsiString, _doubletakeMonitor.UpdatorId);

                  //  Database.AddInParameter(cmd, Dbstring + "iCreateDate", DbType.DateTime, _doubletakeMonitor.CreateDate);
                    // int returnCode = Database.ExecuteNonQuery(cmd);
                    // return returnCode > 0;
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting doubletake mssqldbltkrepmonStatus_create", exc);
            }
            return false;

        }

        public static bool AddMssqldoubletakeLogStatusdetails(Bcms.Common.MSSqlDoubletek _doubletakeMonitor)
        {
            const string sp = "mssqldbltkrepmonlogs_create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _doubletakeMonitor.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, _doubletakeMonitor.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.AnsiString, _doubletakeMonitor.JobName);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationStatus", DbType.AnsiString, _doubletakeMonitor.ReplicationStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSourceServer", DbType.AnsiString, _doubletakeMonitor.SourceServer);
                    Database.AddInParameter(cmd, Dbstring + "iTargetServer", DbType.AnsiString, _doubletakeMonitor.TargetServer);
                    Database.AddInParameter(cmd, Dbstring + "iJobType", DbType.AnsiString, _doubletakeMonitor.JobType);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationQueue", DbType.AnsiString, _doubletakeMonitor.ReplicationQueue);
                    Database.AddInParameter(cmd, Dbstring + "iJobActivity", DbType.AnsiString, _doubletakeMonitor.JobActivity);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorStatus", DbType.AnsiString, _doubletakeMonitor.MirrorStatus);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorPercentComplete", DbType.AnsiString, _doubletakeMonitor.MirrorPercentComplete);
                    Database.AddInParameter(cmd, Dbstring + "iMirrorBytesRemaining", DbType.AnsiString, _doubletakeMonitor.MirrorBytesRemaining);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.AnsiString, _doubletakeMonitor.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.AnsiString, _doubletakeMonitor.UpdatorId);
                    //  Database.AddInParameter(cmd, Dbstring + "iCreateDate", DbType.DateTime, _doubletakeMonitor.CreateDate);
                    // int returnCode = Database.ExecuteNonQuery(cmd);
                    // return returnCode > 0;
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting doubletake mssqldbltkrepmonlogs_create", exc);
            }
            return false;

        }

//        MSSqlDoubletek IMSSqlDoubletekRepliMonitorDataAccess.GetByInfraObjectId(int infraObjectId)
//        {
//            try
//            {
//                const string sp = "MSSQLDBLREPLIMONI_GTBYINFRAID";

//                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
//                {
//                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(cmd))
//                    {
//                        return reader.Read()
//                            ? (CreateEntityBuilder<MSSqlDoubletek>()).BuildEntity(reader, new MSSqlDoubletek())
//                            : null;
//                    }
//                }
//            }
//            catch (Exception exc)
//            {
//                throw new CpException(CpExceptionType.DataAccessFetchOperation,
//                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
//                    "Error In DAL While Executing Function Signature IMSSqlDoubletekRepliMonitorDataAccess.GetByInfraObjectId(" +
//                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
//            }
//        }


        public static MSSqlDoubletek GetMssqlDoubleTakeByInfraobjectId(int id)
        {
            var infraObject = new MSSqlDoubletek();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("MSSQLDBLREPLIMONI_GTBYINFRAID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myGroupReader = Database.ExecuteReader(dbCommand))
                    {
          
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObject;
        }
    }
}
        

