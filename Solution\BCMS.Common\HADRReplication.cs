﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "HADRReplication", Namespace = "http://www.BCMS.com/types")]
    public class HADRReplication : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string HRole
        {
            get;
            set;
        }

        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public string SyncMode
        {
            get;
            set;
        }

        [DataMember]
        public string ConnectionStatus
        {
            get;
            set;
        }

        [DataMember]
        public string HeartbeatsMissed
        {
            get;
            set;
        }

        [DataMember]
        public string LocalHost
        {
            get;
            set;
        }

        [DataMember]
        public string LocalService
        {
            get;
            set;
        }

        [DataMember]
        public string RemoteHost
        {
            get;
            set;
        }

        [DataMember]
        public string RemoteService
        {
            get;
            set;
        }

        [DataMember]
        public string Timeout
        {
            get;
            set;
        }

        [DataMember]
        public string LogGap
        {
            get;
            set;
        }

        #endregion Properties
    }
}