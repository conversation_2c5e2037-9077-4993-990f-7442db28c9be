﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ParallelDROperation", Namespace = "http://www.BCMS.com/types")]
    public class ParallelDROperation : BaseEntity
    {
        #region Properties

        [DataMember]
        public DateTime StartTime
        {
            get;
            set;
        }

        [DataMember]
        public DateTime EndTime
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string Description
        {
            get;
            set;
        }
      [DataMember]
        public ActionMode ActionMode
        {
            get;
            set;
        }
        #endregion

        #region Constructor

        public ParallelDROperation()
            : base()
        {
        }

        #endregion
    }
}