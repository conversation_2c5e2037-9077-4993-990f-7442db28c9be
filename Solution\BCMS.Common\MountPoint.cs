﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MountPoint", Namespace = "http://www.Bcms.com/types")]
    public class MountPoint : BaseEntity
    {
        #region Properties

        [DataMember]
        public string UsedSpace
        {
            get;
            set;
        }

        [DataMember]
        public string FreeSpace
        {
            get;
            set;
        }

        [DataMember]
        public string Capacity
        {
            get;
            set;
        }

        [DataMember]
        public string UsedPercent
        {
            get;
            set;
        }

        [DataMember]
        public int ServerType
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        #endregion Properties
    }
}