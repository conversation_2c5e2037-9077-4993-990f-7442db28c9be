﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlnativeServices", Namespace = "http://www.BCMS.com/types")]
    public class SqlnativeServices: BaseEntity
    {
        #region Properties
        [DataMember]
 
        public int InfraObjectId { get; set; }

        public string Server { get; set; }

        public string Service_Name { get; set; }

        public string Status { get; set; }

        public string Start_Mode { get; set; }

        #endregion

        #region Constructor
        public SqlnativeServices(): base()
        {
        }
        #endregion
    }
}
