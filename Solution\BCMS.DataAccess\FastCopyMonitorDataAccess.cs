﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using BCMS.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class FastCopyMonitorDataAccess : BaseDataAccess
    {
        public static FastCopyMonitor GetByJobId(int jobId)
        {
            try
            {
                var fastCopyMonitor = new FastCopyMonitor();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYMONITORlog_GETBYJOBID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iJobId", DbType.Int32, jobId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            fastCopyMonitor.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            fastCopyMonitor.JobId = Convert.IsDBNull(reader["JobId"]) ? 0 : Convert.ToInt32(reader["JobId"]);
                            fastCopyMonitor.SourceIP = Convert.IsDBNull(reader["SourceIP"])
                                ? string.Empty
                                : Convert.ToString(reader["SourceIP"]);
                            fastCopyMonitor.DestinationIP = Convert.IsDBNull(reader["DestinationIP"])
                                ? string.Empty
                                : Convert.ToString(reader["DestinationIP"]);
                            fastCopyMonitor.SourcePath = Convert.IsDBNull(reader["SourcePath"])
                                ? string.Empty
                                : Convert.ToString(reader["SourcePath"]);
                            fastCopyMonitor.DestinationPath = Convert.IsDBNull(reader["DestinationPath"])
                                ? string.Empty
                                : Convert.ToString(reader["DestinationPath"]);
                            fastCopyMonitor.LastFileName = Convert.IsDBNull(reader["LastFileName"])
                                ? string.Empty
                                : Convert.ToString(reader["LastFileName"]);
                            fastCopyMonitor.LastFileSize = Convert.IsDBNull(reader["LastFileSize"])
                                ? string.Empty
                                : Convert.ToString(reader["LastFileSize"]);
                            fastCopyMonitor.TotalFilesCount = Convert.IsDBNull(reader["TotalFilesCount"])
                                ? string.Empty
                                : Convert.ToString(reader["TotalFilesCount"]);
                            fastCopyMonitor.TotalFilesSize = Convert.IsDBNull(reader["TotalFilesSize"])
                                ? string.Empty
                                : Convert.ToString(reader["TotalFilesSize"]);
                            fastCopyMonitor.LockFilesCount = Convert.IsDBNull(reader["LockFilesCount"])
                                ? string.Empty
                                : Convert.ToString(reader["LockFilesCount"]);
                            fastCopyMonitor.IncrementalFilesCount = Convert.IsDBNull(reader["IncrementalFilesCount"])
                                ? string.Empty
                                : Convert.ToString(reader["IncrementalFilesCount"]);
                            fastCopyMonitor.SkippedFilesCount = Convert.IsDBNull(reader["SkippedFilesCount"])
                                ? string.Empty
                                : Convert.ToString(reader["SkippedFilesCount"]);
                            fastCopyMonitor.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

                            if (!Convert.IsDBNull(reader["IsInfraObject"]))
                                fastCopyMonitor.IsInfraObject = Convert.ToBoolean(reader["IsInfraObject"]);

                            fastCopyMonitor.StartTime = Convert.IsDBNull(reader["StartTime"])
                                ? string.Empty
                                : Convert.ToString(reader["StartTime"]);
                            fastCopyMonitor.EndTime = Convert.IsDBNull(reader["EndTime"])
                                ? string.Empty
                                : Convert.ToString(reader["EndTime"]);
                            fastCopyMonitor.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["CreateDate"]);
                        }
                    }
                }

                return fastCopyMonitor;
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,

                    "Error In While Executing Function Signature FastCopymonitorDataAccess.GetByjobId(" + jobId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
    }
}
