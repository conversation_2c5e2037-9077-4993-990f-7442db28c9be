﻿using System;
using System.Data;

using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class ReplicationBaseDataAccess : BaseDataAccess
    {
        public static ReplicationBase GetReplicationById(int Id)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("ReplicationBase_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, Id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            var replicationtype = (ReplicationType)Enum.Parse(typeof(ReplicationType), myDatabaseReader[2].ToString(), true);
                            switch (replicationtype)
                            {
                                case ReplicationType.OracleDataGuard:

                                    return BuildDataGuardEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.MSSCR:

                                    return BuildExchangeEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.IBMGlobalMirror:

                                    return BuildGlobalMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.EMCSRDF:
                                case ReplicationType.EMCSRDFMSSQLFullDB:
                                case ReplicationType.EMCSRDFVMAXMSSQLFULLDB:
                                case ReplicationType.EMCSRDFDMXMSSQLFULLDB:
                                case ReplicationType.EMCSRDFOracleFullDB:
                                case ReplicationType.EMCSRDFVMAXOracleFULLDB:
                                case ReplicationType.EMCSRDFDMXOracleFULLDB:
                                case ReplicationType.EMCSRDFOracleLogShipping:

                                    return BuildEMCSRDFEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.DataSync:
                                case ReplicationType.VMWareDataSync:

                                    return BuildFastCopyEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.MSSqlNetAppSnapMirror:
                                case ReplicationType.VMWareSnapmirror:

                                    return BuildMSSqlNetAppSnapMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.OracleWithDataSync:

                                    return BuildFastCopyEntity(Convert.ToInt32(myDatabaseReader[0]));                                               

                                case ReplicationType.HITACHITrueCopy:
                                case ReplicationType.VMWareHitachiUR:
                                case ReplicationType.HITACHIUROracleFullDB:            
                                case ReplicationType.HitachiOracleFullDBRac:
                                    //return BuildHITACHIHUREntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.AppHitachiUr:

                                    return BuildHITACHIHUREntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.MSExchangeDAG:

                                    return BuildExchangeDagEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.SQLNative2008:

                                    return BuildSqlNative2008Entity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.MSSQLAlwaysOn:

                                    return BuildMssqlAlwaysonEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.EC2S3DataSync:

                                    return BuildEC2S3DataSyncEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.MSSQLDoubleTakeFullDB:
                                    return BuildMssqlDoubleTakeEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.ApplicationDoubleTake:
                                    return BuildMssqlDoubleTakeEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.SVCGlobalMirrorORMetroMirror:
                                case ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                                case ReplicationType.OracleFullDBSVCGlobalMirror:

                                    return BuildSVCGlobalMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.SVCMSSQLFullDB:
                                    return BuildSVCGlobalMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.VMWareWithSVC:
                                    return BuildSVCGlobalMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.HitachiURMSSQLFullDB:
                                    return BuildHITACHIHUREntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.GlobalMirrorMssqlFullDB:
                                    return BuildGlobalMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.NetAppSnapMirrorPostgresFullDB:
                                    return BuildMSSqlNetAppSnapMirrorEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.SybaseWithSRS:
                                    return BuildSybaseWithSRSEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.ApplicationeBDR:
                                    return BuildMssqlDoubleTakeEntity(Convert.ToInt32(myDatabaseReader[0]));
                                case ReplicationType.DRNET:
                                    return BuildDRNETEntity(Convert.ToInt32(myDatabaseReader[0]));

                                case ReplicationType.RecoverPoint:
                                case ReplicationType.RecoverPointOracleFULLDB:
                                case ReplicationType.RecoverPointMSSQLFULLDB:
                                    return BuildRecoverPointEntity(Convert.ToInt32(myDatabaseReader[0]));

                            }
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Id - " + Id, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildDataGuardEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("DataGuard_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {

                            replication.DataGuard.Mode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DataGuardMode.Undefined : (DataGuardMode)Enum.Parse(typeof(DataGuardMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                            replication.DataGuard.Service = Convert.IsDBNull(myDatabaseReader["Service"]) ? string.Empty : Convert.ToString(myDatabaseReader["Service"]);
                            replication.DataGuard.ProtectionMode = Convert.IsDBNull(myDatabaseReader["ProtectionMode"]) ? string.Empty : Convert.ToString(myDatabaseReader["ProtectionMode"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildExchangeEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("SCR_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.SCR.PRServerId = Convert.IsDBNull(myDatabaseReader["PRServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["PRServerId"]);
                            replication.SCR.DRServerId = Convert.IsDBNull(myDatabaseReader["DRServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["DRServerId"]);
                            replication.SCR.PRInstallationPath = Convert.IsDBNull(myDatabaseReader["PRInstallationPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRInstallationPath"]);
                            replication.SCR.DRInstallationPath = Convert.IsDBNull(myDatabaseReader["DRInstallationPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRInstallationPath"]);
                            replication.SCR.PRNewMailboxPath = Convert.IsDBNull(myDatabaseReader["PRNewMailboxPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRNewMailboxPath"]);
                            replication.SCR.DRNewMailboxPath = Convert.IsDBNull(myDatabaseReader["DRNewMailboxPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRNewMailboxPath"]);
                            replication.SCR.PRComponentPath = Convert.IsDBNull(myDatabaseReader["PRComponentPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRComponentPath"]);
                            replication.SCR.DRComponentPath = Convert.IsDBNull(myDatabaseReader["DRComponentPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRComponentPath"]);
                            replication.SCR.ExeFileName = Convert.IsDBNull(myDatabaseReader["ExeFileName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ExeFileName"]);
                            replication.SCR.ReplayLagTime = Convert.IsDBNull(myDatabaseReader["ReplayLagTime"]) ? string.Empty : Convert.ToString(myDatabaseReader["ReplayLagTime"]);

                            return replication;

                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildExchangeDagEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("EXCDAG_REP_GETBYREPID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            replication.ExchangeDAG.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.ExchangeDAG.DAGName = Convert.IsDBNull(reader["DAGName"])
                                ? string.Empty
                                : Convert.ToString(reader["DAGName"]);

                            replication.ExchangeDAG.IPAddress = Convert.IsDBNull(reader["IPAddress"])
                                ? string.Empty
                                : Convert.ToString(reader["IPAddress"]);

                            replication.ExchangeDAG.WitnessServer = Convert.IsDBNull(reader["WitnessServer"])
                                ? string.Empty
                                : Convert.ToString(reader["WitnessServer"]);
                            replication.ExchangeDAG.WitnessDirectory = Convert.IsDBNull(reader["WitnessDirectory"])
                                ? string.Empty
                                : Convert.ToString(reader["WitnessDirectory"]);

                            return replication;

                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildSqlNative2008Entity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("SqlNative2008_Rep_GetByRepID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            replication.SqlNative2008.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.SqlNative2008.BackupJobName = Convert.IsDBNull(reader["BackupJobName"])
                                ? string.Empty
                                : Convert.ToString(reader["BackupJobName"]);

                            replication.SqlNative2008.CopyJobName = Convert.IsDBNull(reader["CopyJobName"])
                                ? string.Empty
                                : Convert.ToString(reader["CopyJobName"]);

                            replication.SqlNative2008.RestoreJobName = Convert.IsDBNull(reader["RestoreJobName"])
                                ? string.Empty
                                : Convert.ToString(reader["RestoreJobName"]);

                            replication.SqlNative2008.SQLCmdPath = Convert.IsDBNull(reader["SQLCmdPath"])
                                ? string.Empty
                                : Convert.ToString(reader["SQLCmdPath"]);

                            replication.SqlNative2008.PRLocalPath = Convert.IsDBNull(reader["PRLocalPath"])
                                ? string.Empty
                                : Convert.ToString(reader["PRLocalPath"]);
                            replication.SqlNative2008.DRLocalPath = Convert.IsDBNull(reader["DRLocalPath"])
                                ? string.Empty
                                : Convert.ToString(reader["DRLocalPath"]);

                            replication.SqlNative2008.PRNetworkPath = Convert.IsDBNull(reader["PRNetworkPath"])
                               ? string.Empty
                               : Convert.ToString(reader["PRNetworkPath"]);
                            replication.SqlNative2008.DRNetworkPath = Convert.IsDBNull(reader["DRNetworkPath"])
                              ? string.Empty
                              : Convert.ToString(reader["DRNetworkPath"]);

                            return replication;

                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        public static ReplicationBase BuildMssqlAlwaysonEntity(int replicationid)
        {
            var replication = new ReplicationBase();
            try
            {
                using (var dbCommand = Database.GetStoredProcCommand("MssqlAlwayson_Rep_GetByRepID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            replication.Mssqlalwayson.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.Mssqlalwayson.GroupName = Convert.IsDBNull(reader["GroupName"])
                                ? string.Empty
                                : Convert.ToString(reader["GroupName"]);

                            replication.SqlNative2008.CopyJobName = Convert.IsDBNull(reader["GroupRole"])
                                ? string.Empty
                                : Convert.ToString(reader["GroupRole"]);



                            return replication;

                        }
                    }
                }
            }
            catch (Exception exc)
            {

            }
            return replication;
        }

        private static ReplicationBase BuildFastCopyEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("FastCopy_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {

                            replication.FastCopy.Id = Convert.IsDBNull(myDatabaseReader["Id"]) ? 0 : Convert.ToInt32(myDatabaseReader["Id"]);
                            //  replication.FastCopy.IsCompression = !myDatabaseReader.IsDBNull(2) && Convert.ToBoolean(myDatabaseReader[2].ToString());

                            if (!Convert.IsDBNull(myDatabaseReader["IsCompression"]))
                                replication.FastCopy.IsCompression = Convert.ToBoolean(myDatabaseReader["IsCompression"]);

                            //replication.FastCopy.IsFilter = !myDatabaseReader.IsDBNull(3) && Convert.ToBoolean(myDatabaseReader[3].ToString());

                            if (!Convert.IsDBNull(myDatabaseReader["IsFilter"]))
                                replication.FastCopy.IsFilter = Convert.ToBoolean(myDatabaseReader["IsFilter"]);

                            replication.FastCopy.Wildcard = Convert.IsDBNull(myDatabaseReader["WildCard"]) ? string.Empty : Convert.ToString(myDatabaseReader["WildCard"]);
                            replication.FastCopy.ProcessCode = Convert.IsDBNull(myDatabaseReader["ProcessCode"]) ? string.Empty : Convert.ToString(myDatabaseReader["ProcessCode"]);
                            replication.FastCopy.LocalDirectory = Convert.IsDBNull(myDatabaseReader["LocalDirectory"]) ? string.Empty : Convert.ToString(myDatabaseReader["LocalDirectory"]);
                            replication.FastCopy.RemoteDirectory = Convert.IsDBNull(myDatabaseReader["RemoteDirectory"]) ? string.Empty : Convert.ToString(myDatabaseReader["RemoteDirectory"]);
                            replication.FastCopy.OSPlatform = Convert.IsDBNull(myDatabaseReader["OSPlatform"]) ? string.Empty : Convert.ToString(myDatabaseReader["OSPlatform"]);
                            replication.FastCopy.ScheduleTime = Convert.IsDBNull(myDatabaseReader["ScheduleTime"]) ? string.Empty : Convert.ToString(myDatabaseReader["ScheduleTime"]);
                            replication.FastCopy.LastReplicationCount = Convert.IsDBNull(myDatabaseReader["LastReplicationCount"]) ? 0 : Convert.ToInt32(myDatabaseReader["LastReplicationCount"]);
                            replication.FastCopy.ModeType = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? FastCopyMode.Undefined : (FastCopyMode)Enum.Parse(typeof(FastCopyMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                            replication.FastCopy.DataSyncPath = Convert.IsDBNull(myDatabaseReader["DataSyncPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DataSyncPath"]);
                            replication.FastCopy.JrePath = Convert.IsDBNull(myDatabaseReader["DataSyncJREPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DataSyncJREPath"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildGlobalMirrorEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("GlobalMirror_GetByRepId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.GlobalMirror.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.GlobalMirror.DSCLIServerId = Convert.IsDBNull(myDatabaseReader["DSCLIServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["DSCLIServerId"]);
                            replication.GlobalMirror.HMCPRServerId = Convert.IsDBNull(myDatabaseReader["HMCPRServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["HMCPRServerId"]);
                            replication.GlobalMirror.HMCDRServerId = Convert.IsDBNull(myDatabaseReader["HMCDRServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["HMCDRServerId"]);
                            replication.GlobalMirror.PRLSSID = Convert.IsDBNull(myDatabaseReader["PRLSSID"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRLSSID"]);
                            replication.GlobalMirror.LSSIDRange = Convert.IsDBNull(myDatabaseReader["LSSIDRange"]) ? string.Empty : Convert.ToString(myDatabaseReader["LSSIDRange"]);
                            replication.GlobalMirror.SessionId = Convert.IsDBNull(myDatabaseReader["SessionId"]) ? string.Empty : Convert.ToString(myDatabaseReader["SessionId"]);
                            replication.GlobalMirror.PRStorageImageId = Convert.IsDBNull(myDatabaseReader["PRStorageImageId"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRStorageImageId"]);
                            replication.GlobalMirror.DRStorageImageId = Convert.IsDBNull(myDatabaseReader["DRStorageImageId"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRStorageImageId"]);
                            replication.GlobalMirror.PRWWN = Convert.IsDBNull(myDatabaseReader["PRWWN"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRWWN"]);
                            replication.GlobalMirror.DRWWN = Convert.IsDBNull(myDatabaseReader["DRWWN"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRWWN"]);
                            // replication.GlobalMirror.IsManual =Convert.ToBoolean( myDatabaseReader[12].ToString());
                            if (!Convert.IsDBNull(myDatabaseReader["IsManual"]))
                                replication.GlobalMirror.IsManual = Convert.ToBoolean(myDatabaseReader["IsManual"]);
                            replication.GlobalMirror.DSCLIPath = Convert.IsDBNull(myDatabaseReader["DSCLIPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["DSCLIPath"]);
                            replication.GlobalMirror.ReverseSessionId = Convert.IsDBNull(myDatabaseReader["ReverseSessionId"]) ? string.Empty : Convert.ToString(myDatabaseReader["ReverseSessionId"]);
                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildEMCSRDFEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("EMCSRDF_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.EMCSRDF.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.EMCSRDF.ServerId = Convert.IsDBNull(myDatabaseReader["ServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ServerId"]);
                            replication.EMCSRDF.DGroupName = Convert.IsDBNull(myDatabaseReader["DGroupName"]) ? string.Empty : Convert.ToString(myDatabaseReader["DGroupName"]);
                            replication.EMCSRDF.DGType = Convert.IsDBNull(myDatabaseReader["DGType"]) ? string.Empty : Convert.ToString(myDatabaseReader["DGType"]);
                            replication.EMCSRDF.DGSummetrixID = Convert.IsDBNull(myDatabaseReader["DGSummetrixID"]) ? string.Empty : Convert.ToString(myDatabaseReader["DGSummetrixID"]);
                            replication.EMCSRDF.RemoteSymmetrixID = Convert.IsDBNull(myDatabaseReader["RemoteSymmetrixID"]) ? string.Empty : Convert.ToString(myDatabaseReader["RemoteSymmetrixID"]);
                            replication.EMCSRDF.RdfRaGroupNumber = Convert.IsDBNull(myDatabaseReader["RDF_RA_GroupNumber"]) ? string.Empty : Convert.ToString(myDatabaseReader["RDF_RA_GroupNumber"]);
                            replication.EMCSRDF.State = Convert.IsDBNull(myDatabaseReader["State"]) ? string.Empty : Convert.ToString(myDatabaseReader["State"]);
                            replication.EMCSRDF.PendingTracks = Convert.IsDBNull(myDatabaseReader["PendingTracks"]) ? string.Empty : Convert.ToString(myDatabaseReader["PendingTracks"]);
                            replication.EMCSRDF.DataLag = Convert.IsDBNull(myDatabaseReader["DataLag"]) ? string.Empty : Convert.ToString(myDatabaseReader["DataLag"]);
                            replication.EMCSRDF.R2ImageCaptureTime = Convert.IsDBNull(myDatabaseReader["R2_IMAGE_CAPTURE_TIME"]) ? string.Empty : Convert.ToString(myDatabaseReader["R2_IMAGE_CAPTURE_TIME"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildHITACHIHUREntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("HitachiUr_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.HitachiUrReplication.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.HitachiUrReplication.PrServerId = Convert.IsDBNull(myDatabaseReader["PrServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["PrServerId"]);
                            replication.HitachiUrReplication.DrServerId = Convert.IsDBNull(myDatabaseReader["DrServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["DrServerId"]);
                            replication.HitachiUrReplication.PrStorageId = Convert.IsDBNull(myDatabaseReader["PrStorageId"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrStorageId"]);
                            replication.HitachiUrReplication.DrStorageId = Convert.IsDBNull(myDatabaseReader["DrStorageId"]) ? string.Empty : Convert.ToString(myDatabaseReader["DrStorageId"]);
                            replication.HitachiUrReplication.PrHorcomInstance = Convert.IsDBNull(myDatabaseReader["PrHORCOMInstance"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrHORCOMInstance"]);
                            replication.HitachiUrReplication.DrHorcomInstance = Convert.IsDBNull(myDatabaseReader["DrHORCOMInstance"]) ? string.Empty : Convert.ToString(myDatabaseReader["DrHORCOMInstance"]);
                            replication.HitachiUrReplication.PrHorcomDeviceGroups = Convert.IsDBNull(myDatabaseReader["PrHORCOMDeviceGroups"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrHORCOMDeviceGroups"]);
                            replication.HitachiUrReplication.DrHorcomDeviceGroups = Convert.IsDBNull(myDatabaseReader["DrHORCOMDeviceGroups"]) ? string.Empty : Convert.ToString(myDatabaseReader["DrHORCOMDeviceGroups"]);
                            replication.HitachiUrReplication.PrJournalVolume = Convert.IsDBNull(myDatabaseReader["PrJournalVolume"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrJournalVolume"]);
                            replication.HitachiUrReplication.DrJournalVolume = Convert.IsDBNull(myDatabaseReader["DrJournalVolume"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrJournalVolume"]);
                            replication.HitachiUrReplication.PrCommandDevice = Convert.IsDBNull(myDatabaseReader["PrCommandDevice"]) ? string.Empty : Convert.ToString(myDatabaseReader["PrCommandDevice"]);
                            replication.HitachiUrReplication.DrCommandDevice = Convert.IsDBNull(myDatabaseReader["DrCommandDevice"]) ? string.Empty : Convert.ToString(myDatabaseReader["DrCommandDevice"]);



                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildEC2S3DataSyncEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("EC2S3DataSync_REP_GETBYREPID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {

                            replication.EC2S3Repl.Id = Convert.IsDBNull(myDatabaseReader["Id"]) ? 0 : Convert.ToInt32(myDatabaseReader["Id"]);
                            replication.EC2S3Repl.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.EC2S3Repl.AWSKey = Convert.IsDBNull(myDatabaseReader["AWSKey"]) ? string.Empty : Convert.ToString(myDatabaseReader["AWSKey"]);
                            replication.EC2S3Repl.AWSSecretKey = Convert.IsDBNull(myDatabaseReader["AWSSecretKey"]) ? string.Empty : Convert.ToString(myDatabaseReader["AWSSecretKey"]);
                            replication.EC2S3Repl.EC2InstanceId = Convert.IsDBNull(myDatabaseReader["EC2InstanceId"]) ? string.Empty : Convert.ToString(myDatabaseReader["EC2InstanceId"]);
                            replication.EC2S3Repl.RegionEndPoint = Convert.IsDBNull(myDatabaseReader["RegionEndPoint"]) ? string.Empty : Convert.ToString(myDatabaseReader["RegionEndPoint"]);
                            replication.EC2S3Repl.S3BucketName = Convert.IsDBNull(myDatabaseReader["S3BucketName"]) ? string.Empty : Convert.ToString(myDatabaseReader["S3BucketName"]);
                            replication.EC2S3Repl.SourceDataPath = Convert.IsDBNull(myDatabaseReader["SourceDataPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["SourceDataPath"]);
                            replication.EC2S3Repl.IsPullPushMechanism = Convert.IsDBNull(myDatabaseReader["IsPullPushMechanism"]) ? false : Convert.ToBoolean(myDatabaseReader["IsPullPushMechanism"]);
                            replication.EC2S3Repl.IsActive = Convert.IsDBNull(myDatabaseReader["IsActive"]) ? false : Convert.ToBoolean(myDatabaseReader["IsActive"]);
                            replication.EC2S3Repl.CreatorId = Convert.IsDBNull(myDatabaseReader["CreatorId"]) ? 0 : Convert.ToInt32(myDatabaseReader["CreatorId"]);
                            replication.EC2S3Repl.CreateDate = Convert.IsDBNull(myDatabaseReader["CreatedDate"]) ? System.DateTime.MinValue : Convert.ToDateTime(myDatabaseReader["CreatedDate"]);
                            replication.EC2S3Repl.UpdatorId = Convert.IsDBNull(myDatabaseReader["UpdatorId"]) ? 0 : Convert.ToInt32(myDatabaseReader["UpdatorId"]);
                            replication.EC2S3Repl.UpdateDate = Convert.IsDBNull(myDatabaseReader["UpdatedDate"]) ? System.DateTime.MinValue : Convert.ToDateTime(myDatabaseReader["UpdatedDate"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildMssqlDoubleTakeEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("MSSqlDoubleRepli_GtByRepliId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            replication.doubletekRep.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.doubletekRep.JobName = Convert.IsDBNull(reader["JobName"])
                                ? string.Empty
                                : Convert.ToString(reader["JobName"]);

                            replication.doubletekRep.JobType = Convert.IsDBNull(reader["JobType"])
                                ? string.Empty
                                : Convert.ToString(reader["JobType"]);


                            return replication;

                        }
                    }
                }
            }






            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildMSSqlNetAppSnapMirrorEntity(int replicationId)
        {
            var replication = new ReplicationBase();
            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("SnapMirror_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.SnapMirror.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.SnapMirror.MCPRServerId = Convert.IsDBNull(reader["MCPRServerId"])
                ? 0
                : Convert.ToInt32(reader["MCPRServerId"]);

                            replication.SnapMirror.MCDRServerId = Convert.IsDBNull(reader["MCDRServerId"])
                ? 0
                : Convert.ToInt32(reader["MCDRServerId"]);
                            replication.SnapMirror.PRStorageId = Convert.IsDBNull(reader["PRStorageId"])
                                ? string.Empty
                                : Convert.ToString(reader["PRStorageId"]);
                            replication.SnapMirror.DRStorageId = Convert.IsDBNull(reader["DRStorageId"])
                                ? string.Empty
                                : Convert.ToString(reader["DRStorageId"]);
                            replication.SnapMirror.Mode = Convert.IsDBNull(reader["ModeType"]) ? 0 : Convert.ToInt32(reader["ModeType"]);
                            replication.SnapMirror.PRVolume = Convert.IsDBNull(reader["PRVolume"])
                                ? string.Empty
                                : Convert.ToString(reader["PRVolume"]);
                            replication.SnapMirror.DRVolume = Convert.IsDBNull(reader["DRVolume"])
                                ? string.Empty
                                : Convert.ToString(reader["DRVolume"]);
                            replication.SnapMirror.PRLunPath = Convert.IsDBNull(reader["PRLunPath"])
                               ? string.Empty
                               : Convert.ToString(reader["PRLunPath"]);
                            replication.SnapMirror.DRLunPath = Convert.IsDBNull(reader["DRLunPath"])
                             ? string.Empty
                             : Convert.ToString(reader["DRLunPath"]);
                            replication.SnapMirror.PRLunmapId = Convert.IsDBNull(reader["PRLunmapId"])
                             ? string.Empty
                             : Convert.ToString(reader["PRLunmapId"]);
                            replication.SnapMirror.DRLunmapId = Convert.IsDBNull(reader["DRLunmapId"])
                            ? string.Empty
                            : Convert.ToString(reader["DRLunmapId"]);
                            replication.SnapMirror.PRLunSerialNo = Convert.IsDBNull(reader["PRLunSerialNo"])
                          ? string.Empty
                          : Convert.ToString(reader["PRLunSerialNo"]);
                            replication.SnapMirror.DRLunSerialNo = Convert.IsDBNull(reader["DRLunSerialNo"])
                         ? string.Empty
                         : Convert.ToString(reader["DRLunSerialNo"]);


                            return replication;

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationId, exc);
            }
            return replication;

        }

        private static ReplicationBase BuildMSSqlAlwaysOnEntity(int replicationId)
        {
            var replication = new ReplicationBase();
            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("MssqlAlwaysOn_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.Mssqlalwayson.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.Mssqlalwayson.GroupName = Convert.IsDBNull(reader["GropuName"])
                ? string.Empty
                : Convert.ToString(reader["GropuName"]);

                            replication.Mssqlalwayson.GroupRole = Convert.IsDBNull(reader["GroupRole"])
                ? string.Empty
                : Convert.ToString(reader["GroupRole"]);

                            return replication;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationId, exc);
            }
            return replication;

        }

        public static int FastCopyIdGetByReplicationId(int id)
        {
            var fastCopyId = 0;

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("FastCopy_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            return myDatabaseReader.IsDBNull(0) ? 0 : Convert.ToInt32(myDatabaseReader[0]);

                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get FastCopyBy Replication Id information By Id - " + id, exc);
            }
            return fastCopyId;

        }

        public static ReplicationBase BuildDMysqlNativeReplicationEntity(int replicationId)
        {
            var replication = new ReplicationBase();
            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("MySqlNativRepli_GetByRepliId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.MysqlNativeRepli.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.MysqlNativeRepli.PRConnectState = Convert.IsDBNull(reader["PRConnectState"])
                           ? string.Empty
                           : Convert.ToString(reader["PRConnectState"]);

                            replication.MysqlNativeRepli.DRConnectState = Convert.IsDBNull(reader["DRConnectState"])
                         ? string.Empty
                         : Convert.ToString(reader["DRConnectState"]);

                            return replication;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationId, exc);
            }
            return replication;

        }


        private static ReplicationBase BuildSVCGlobalMirrorEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("SVCGlobalM_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.SVCGlobalMirrorReplication.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.SVCGlobalMirrorReplication.PRServerId = Convert.IsDBNull(myDatabaseReader["PrServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["PrServerId"]);
                            replication.SVCGlobalMirrorReplication.DRServerId = Convert.IsDBNull(myDatabaseReader["DrServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["DrServerId"]);
                            replication.SVCGlobalMirrorReplication.PRIPADDRESS = Convert.IsDBNull(myDatabaseReader["PRIPADDRESS"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRIPADDRESS"]);
                            replication.SVCGlobalMirrorReplication.DRIPADDRESS = Convert.IsDBNull(myDatabaseReader["DRIPADDRESS"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRIPADDRESS"]);
                            replication.SVCGlobalMirrorReplication.PRProductID = Convert.IsDBNull(myDatabaseReader["PRProductID"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRProductID"]);
                            replication.SVCGlobalMirrorReplication.DRProductID = Convert.IsDBNull(myDatabaseReader["DRProductID"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRProductID"]);
                            replication.SVCGlobalMirrorReplication.PRRELEASE = Convert.IsDBNull(myDatabaseReader["PRRELEASE"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRRELEASE"]);
                            replication.SVCGlobalMirrorReplication.DRRELEASE = Convert.IsDBNull(myDatabaseReader["DRRELEASE"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRRELEASE"]);
                            replication.SVCGlobalMirrorReplication.PRLOGIN = Convert.IsDBNull(myDatabaseReader["PRLOGIN"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRLOGIN"]);
                            replication.SVCGlobalMirrorReplication.DRLOGIN = Convert.IsDBNull(myDatabaseReader["DRLOGIN"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRLOGIN"]);
                            replication.SVCGlobalMirrorReplication.PRCONSISTENTGROUPID = Convert.IsDBNull(myDatabaseReader["PRCONSISTENTGROUPID"]) ? 0 : Convert.ToInt32(myDatabaseReader["PRCONSISTENTGROUPID"]);
                            replication.SVCGlobalMirrorReplication.DRCONSISTENTGROUPID = Convert.IsDBNull(myDatabaseReader["DRCONSISTENTGROUPID"]) ? 0 : Convert.ToInt32(myDatabaseReader["DRCONSISTENTGROUPID"]);
                            replication.SVCGlobalMirrorReplication.PRRC_REL_NAME = Convert.IsDBNull(myDatabaseReader["PRRC_REL_NAME"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRRC_REL_NAME"]);
                            replication.SVCGlobalMirrorReplication.DRRC_REL_NAME = Convert.IsDBNull(myDatabaseReader["DRRC_REL_NAME"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRRC_REL_NAME"]);
                            replication.SVCGlobalMirrorReplication.PRDISKCONTROLLERID = Convert.IsDBNull(myDatabaseReader["PRDISKCONTROLLERID"]) ? 0 : Convert.ToInt32(myDatabaseReader["PRDISKCONTROLLERID"]);
                            replication.SVCGlobalMirrorReplication.DRDISKCONTROLLERID = Convert.IsDBNull(myDatabaseReader["DRDISKCONTROLLERID"]) ? 0 : Convert.ToInt32(myDatabaseReader["DRDISKCONTROLLERID"]);
                            replication.SVCGlobalMirrorReplication.PRCLUSTEREDSYSNODENAME = Convert.IsDBNull(myDatabaseReader["PRCLUSTEREDSYSNODENAME"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRCLUSTEREDSYSNODENAME"]);
                            replication.SVCGlobalMirrorReplication.DRCLUSTEREDSYSNODENAME = Convert.IsDBNull(myDatabaseReader["DRCLUSTEREDSYSNODENAME"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRCLUSTEREDSYSNODENAME"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildSybaseWithSRSEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("SybaseWithSRS_GetByReplica"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.SVCGlobalMirrorReplication.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.SybaseWithSRSReplication.ReplicationServerId = Convert.IsDBNull(myDatabaseReader["ServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ServerId"]);
                            replication.SybaseWithSRSReplication.replicationDataServerName = Convert.IsDBNull(myDatabaseReader["ReplicationServerName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ReplicationServerName"]);
                            replication.SybaseWithSRSReplication.replicationDataServerUserName = Convert.IsDBNull(myDatabaseReader["ReplicationServerLogin"]) ? string.Empty : Convert.ToString(myDatabaseReader["ReplicationServerLogin"]);
                            replication.SybaseWithSRSReplication.replicationDataServerPassword = Convert.IsDBNull(myDatabaseReader["ReplicationServerPassword"]) ? string.Empty : Convert.ToString(myDatabaseReader["ReplicationServerPassword"]);

                            replication.SybaseWithSRSReplication.RSSDPassword = Convert.IsDBNull(myDatabaseReader["RSSDLoginPassword"]) ? string.Empty : Convert.ToString(myDatabaseReader["RSSDLoginPassword"]);
                            replication.SybaseWithSRSReplication.logicalConnectionName = Convert.IsDBNull(myDatabaseReader["LogicalConnectionName"]) ? string.Empty : Convert.ToString(myDatabaseReader["LogicalConnectionName"]);
                           
                            replication.SybaseWithSRSReplication.RSSDName = Convert.IsDBNull(myDatabaseReader["RSSDName"]) ? string.Empty : Convert.ToString(myDatabaseReader["RSSDName"]);
                            replication.SybaseWithSRSReplication.RSSDDSName = Convert.IsDBNull(myDatabaseReader["RSSD_DS_Name"]) ? string.Empty : Convert.ToString(myDatabaseReader["RSSD_DS_Name"]);
                            replication.SybaseWithSRSReplication.RSSDLogin = Convert.IsDBNull(myDatabaseReader["RSSDLoginName"]) ? string.Empty : Convert.ToString(myDatabaseReader["RSSDLoginName"]);
                            replication.SybaseWithSRSReplication.replicationSybaseEvnPath = Convert.IsDBNull(myDatabaseReader["SybaseEnvPath"]) ? string.Empty : Convert.ToString(myDatabaseReader["SybaseEnvPath"]);
                            replication.SybaseWithSRSReplication.ActiveConnectionName = Convert.IsDBNull(myDatabaseReader["ActiveConnectionName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ActiveConnectionName"]);
                            replication.SybaseWithSRSReplication.StandbyConnectionName = Convert.IsDBNull(myDatabaseReader["StandbyConnectionName"]) ? string.Empty : Convert.ToString(myDatabaseReader["StandbyConnectionName"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }

        private static ReplicationBase BuildDRNETEntity(int replicationId)
        {
            var replication = new ReplicationBase();
            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("DRNetConfig_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            replication.drnetRep.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            replication.drnetRep.ProcessName = Convert.IsDBNull(reader["ProcessName"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessName"]);
                            replication.drnetRep.AuditFileName = Convert.IsDBNull(reader["AuditFileName"])
                                ? string.Empty
                                : Convert.ToString(reader["AuditFileName"]);

                            return replication;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get DRNET Replication information By Replication Id - " + replicationId, exc);
            }
            return replication;

        }

        // RecoverPoint
        private static ReplicationBase BuildRecoverPointEntity(int replicationid)
        {
            var replication = new ReplicationBase();

            try
            {

                using (var dbCommand = Database.GetStoredProcCommand("recoveryPoint_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, replicationid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            replication.recoveryPoint.ReplicationId = Convert.IsDBNull(myDatabaseReader["ReplicationId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ReplicationId"]);
                            replication.recoveryPoint.ServerId = Convert.IsDBNull(myDatabaseReader["ServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ServerId"]);
                            replication.recoveryPoint.DGroupName = Convert.IsDBNull(myDatabaseReader["GroupName"]) ? string.Empty : Convert.ToString(myDatabaseReader["GroupName"]);
                            replication.recoveryPoint.PRClusterNodeName = Convert.IsDBNull(myDatabaseReader["PRClusterNodeName"]) ? string.Empty : Convert.ToString(myDatabaseReader["PRClusterNodeName"]);
                            replication.recoveryPoint.DRClusterNodeName = Convert.IsDBNull(myDatabaseReader["DRClusterNodeName"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRClusterNodeName"]);

                            replication.recoveryPoint.ProductionSiteName = Convert.IsDBNull(myDatabaseReader["ProductionSiteName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ProductionSiteName"]);
                            replication.recoveryPoint.DRSiteName = Convert.IsDBNull(myDatabaseReader["DRSiteName"]) ? string.Empty : Convert.ToString(myDatabaseReader["DRSiteName"]);
                            replication.recoveryPoint.NearDRSiteName = Convert.IsDBNull(myDatabaseReader["NearDRsiteName"]) ? string.Empty : Convert.ToString(myDatabaseReader["NearDRsiteName"]);

                            return replication;
                        }
                    }
                }
            }



            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Replication information By Replication Id - " + replicationid, exc);
            }
            return replication;
        }
    }
}

