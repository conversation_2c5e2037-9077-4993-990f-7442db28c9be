﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using System.Collections.Generic;

namespace Bcms.DataAccess
{
    public class HyperVDataAccess : BaseDataAccess
    {

        public static HyperVDetails GetById(int id)
        {

            var HyperVDetailsRepplication = new HyperVDetails();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("HyperVRepli_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            //HyperVDetailsRepplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            //HyperVDetailsRepplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            //HyperVDetailsRepplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            HyperVDetailsRepplication.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            HyperVDetailsRepplication.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            HyperVDetailsRepplication.PRVMName = Convert.IsDBNull(reader["PRVMName"]) ? string.Empty : Convert.ToString(reader["PRVMName"]);
                            HyperVDetailsRepplication.DRVMName = Convert.IsDBNull(reader["DRVMName"]) ? string.Empty : Convert.ToString(reader["DRVMName"]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Hyper V by  Replication Id - " + id, exc);
            }

            return HyperVDetailsRepplication;
        }

        public static IList<HyperVDetails> ReplicationGetAll()
        {
            var HyperVDetailsRepplication = new List<HyperVDetails>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("HyperVRepli_GetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var HyperVDetailsAdd = new HyperVDetails();

                            HyperVDetailsAdd.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            HyperVDetailsAdd.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            HyperVDetailsAdd.PRVMName = Convert.IsDBNull(reader["PRVMName"]) ? string.Empty : Convert.ToString(reader["PRVMName"]);
                            HyperVDetailsAdd.DRVMName = Convert.IsDBNull(reader["DRVMName"]) ? string.Empty : Convert.ToString(reader["DRVMName"]);
                            HyperVDetailsRepplication.Add(HyperVDetailsAdd);
                        }
                    }
                }


            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Hyper V  -All Replcation ", exc);
            }
            return HyperVDetailsRepplication;
        }

        public static HyperVDetails GetByReplicationId(int id)
        {
            var HyperVDetailsAdd = new HyperVDetails();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("HyperVRepli_GtByRepliId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            //HyperVDetailsRepplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            //HyperVDetailsRepplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            //HyperVDetailsRepplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            HyperVDetailsAdd.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            HyperVDetailsAdd.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            HyperVDetailsAdd.PRVMName = Convert.IsDBNull(reader["PRVMName"]) ? string.Empty : Convert.ToString(reader["PRVMName"]);
                            HyperVDetailsAdd.DRVMName = Convert.IsDBNull(reader["DRVMName"]) ? string.Empty : Convert.ToString(reader["DRVMName"]);

                            HyperVDetailsAdd.PRReplicaBrokerName = Convert.IsDBNull(reader["PRReplicaBrokerName"]) ? string.Empty : Convert.ToString(reader["PRReplicaBrokerName"]);
                            HyperVDetailsAdd.DRReplicaBrokerName = Convert.IsDBNull(reader["DRReplicaBrokerName"]) ? string.Empty : Convert.ToString(reader["DRReplicaBrokerName"]);
                            HyperVDetailsAdd.PRReplicaBrokerPort = Convert.IsDBNull(reader["PRReplicaBrokerPort"]) ? string.Empty : Convert.ToString(reader["PRReplicaBrokerPort"]);
                            HyperVDetailsAdd.DRReplicaBrokerPort = Convert.IsDBNull(reader["DRReplicaBrokerPort"]) ? string.Empty : Convert.ToString(reader["DRReplicaBrokerPort"]);
                            HyperVDetailsAdd.PRAuthenticationType = Convert.IsDBNull(reader["PRAuthenticationType"]) ? string.Empty : Convert.ToString(reader["PRAuthenticationType"]);
                            HyperVDetailsAdd.DRAuthenticationType = Convert.IsDBNull(reader["DRAuthenticationType"]) ? string.Empty : Convert.ToString(reader["DRAuthenticationType"]);



                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Hyper V by  Replication Id - " + id, exc);
            }


            return HyperVDetailsAdd;

        }


        public static IList<HyperVDetails> ReplicationGetByCompanyId(int companyId, int isParent)
        {
            var HyperVDetailsRepplicationCompanyID = new List<HyperVDetails>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("HyperVRepli_GetByCompanyId"))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(dbCommand, Dbstring + "iisParent", DbType.Int32, isParent);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var HyperVDetailsAddcmp = new HyperVDetails();

                            HyperVDetailsAddcmp.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            HyperVDetailsAddcmp.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            HyperVDetailsAddcmp.PRVMName = Convert.IsDBNull(reader["PRVMName"]) ? string.Empty : Convert.ToString(reader["PRVMName"]);
                            HyperVDetailsAddcmp.DRVMName = Convert.IsDBNull(reader["DRVMName"]) ? string.Empty : Convert.ToString(reader["DRVMName"]);
                            HyperVDetailsRepplicationCompanyID.Add(HyperVDetailsAddcmp);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Hyper V  -All Replcation ", exc);
            }
            return HyperVDetailsRepplicationCompanyID;
        }


        public static bool AddHyperVDetailsStatus(HyperVDetails HyperVDetails)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("Hyperv_Status_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.AnsiString, HyperVDetails.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRVMState", DbType.AnsiString, HyperVDetails.PRVMState);
                    Database.AddInParameter(cmd, Dbstring + "iDRVMState", DbType.AnsiString, HyperVDetails.DRVMState);
                    Database.AddInParameter(cmd, Dbstring + "iPRVMClustered", DbType.AnsiString, HyperVDetails.PRVMClustered);
                    Database.AddInParameter(cmd, Dbstring + "iDRVMClustered", DbType.AnsiString, HyperVDetails.DRVMClustered);
                    Database.AddInParameter(cmd, Dbstring + "iPRVMIPAdress", DbType.AnsiString, HyperVDetails.PRVMIPAdress);
                    Database.AddInParameter(cmd, Dbstring + "iDRVMIPAdress", DbType.AnsiString, HyperVDetails.DRVMIPAdress);
                    Database.AddInParameter(cmd, Dbstring + "iPRVMNetworkStatus", DbType.AnsiString, HyperVDetails.PRVMNetworkStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRVMNetworkStatus", DbType.AnsiString, HyperVDetails.DRVMNetworkStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationState", DbType.AnsiString, HyperVDetails.PRReplicationState);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationState", DbType.AnsiString, HyperVDetails.DRReplicationState);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationMode", DbType.AnsiString, HyperVDetails.PRReplicationMode);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationMode", DbType.AnsiString, HyperVDetails.DRReplicationMode);
                    Database.AddInParameter(cmd, Dbstring + "iPRCurrentPrimaryserver", DbType.AnsiString, HyperVDetails.PRCurrentPrimaryServer);
                    Database.AddInParameter(cmd, Dbstring + "iDRCurrentPrimaryServer", DbType.AnsiString, HyperVDetails.DRCurrentPrimaryServer);
                    Database.AddInParameter(cmd, Dbstring + "iPRCurrentReplicaServer", DbType.AnsiString, HyperVDetails.PRCurrentReplicaServer);
                    Database.AddInParameter(cmd, Dbstring + "iDRCurrentReplicaServer", DbType.AnsiString, HyperVDetails.DRCurrentReplicaServer);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationHealth", DbType.AnsiString, HyperVDetails.PRReplicationHealth);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationHealth", DbType.AnsiString, HyperVDetails.DRReplicationHealth);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicaPort", DbType.AnsiString, HyperVDetails.PRReplicaPort);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicaPort", DbType.AnsiString, HyperVDetails.DRReplicaPort);
                    Database.AddInParameter(cmd, Dbstring + "iPRAuthType", DbType.AnsiString, HyperVDetails.PRAuthType);
                    Database.AddInParameter(cmd, Dbstring + "iDRAuthType", DbType.AnsiString, HyperVDetails.DRAuthType);
                    Database.AddInParameter(cmd, Dbstring + "iPRSizeReplicated", DbType.AnsiString, HyperVDetails.PRSizeReplicated);
                    Database.AddInParameter(cmd, Dbstring + "iDRSizeReplicated", DbType.AnsiString, HyperVDetails.DRSizeReplicated);
                    Database.AddInParameter(cmd, Dbstring + "iLastSynchronized", DbType.AnsiString, HyperVDetails.LastSynchronized);
                    Database.AddInParameter(cmd, Dbstring + "iDatalag", DbType.AnsiString, HyperVDetails.HyperVDatalag);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Hyper V Details", exc);

            }
        }


        public static bool Addhypervclustersummary(hypervclustersummary hypervclustersummary)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("hypervclustsumry_Log_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.AnsiString, hypervclustersummary.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRClusterName", DbType.AnsiString, hypervclustersummary.PRClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iDRClusterName", DbType.AnsiString, hypervclustersummary.DRClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iPROwnerIPAddress", DbType.AnsiString, hypervclustersummary.PROwnerIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDROwnerIPAddress", DbType.AnsiString, hypervclustersummary.DROwnerIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iPROwnerNode", DbType.AnsiString, hypervclustersummary.PROwnerNode);
                    Database.AddInParameter(cmd, Dbstring + "iDROwnerNode", DbType.AnsiString, hypervclustersummary.DROwnerNode);
                    Database.AddInParameter(cmd, Dbstring + "iPRBrokerName", DbType.AnsiString, hypervclustersummary.PRBrokerName);
                    Database.AddInParameter(cmd, Dbstring + "iDRBrokerName", DbType.AnsiString, hypervclustersummary.DRBrokerName);
                    Database.AddInParameter(cmd, Dbstring + "iPRBrokerState", DbType.AnsiString, hypervclustersummary.PRBrokerState);
                    Database.AddInParameter(cmd, Dbstring + "iDRBrokerState", DbType.AnsiString, hypervclustersummary.DRBrokerState);
                    Database.AddInParameter(cmd, Dbstring + "iPRBrokerIPAddress", DbType.AnsiString, hypervclustersummary.PRBrokerIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDRBrokerIPAddress", DbType.AnsiString, hypervclustersummary.DRBrokerIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iPRBrokerNode", DbType.AnsiString, hypervclustersummary.PRBrokerNode);
                    Database.AddInParameter(cmd, Dbstring + "iDRBrokerNode", DbType.AnsiString, hypervclustersummary.DRBrokerNode);
                    Database.AddInParameter(cmd, Dbstring + "iPROwnerNodeList", DbType.AnsiString, hypervclustersummary.PROwnerNodeList);
                    Database.AddInParameter(cmd, Dbstring + "iDROwnerNodeList", DbType.AnsiString, hypervclustersummary.DROwnerNodeList);

                    Database.AddInParameter(cmd, Dbstring + "iPRNetworkIPAddress", DbType.AnsiString, hypervclustersummary.PRNetworkIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDRNetworkIPAddress", DbType.AnsiString, hypervclustersummary.DRNetworkIPAddress);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add hyperv cluster summary Details", exc);

            }
        }


        public static bool Addhypervclusternode(hypervclusternode hypervclusternode)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("hypervclustnode_Log_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.AnsiString, hypervclusternode.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRClusterID", DbType.AnsiString, hypervclusternode.PRClusterID);
                    Database.AddInParameter(cmd, Dbstring + "iDRClusterID", DbType.AnsiString, hypervclusternode.DRClusterID);
                    Database.AddInParameter(cmd, Dbstring + "iPRClusterName", DbType.AnsiString, hypervclusternode.PRClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iDRClusterName", DbType.AnsiString, hypervclusternode.DRClusterName);
                    Database.AddInParameter(cmd, Dbstring + "iPRClusterState", DbType.AnsiString, hypervclusternode.PRClusterState);
                    Database.AddInParameter(cmd, Dbstring + "iDRClusterState", DbType.AnsiString, hypervclusternode.DRClusterState);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add hyperv cluster summary Details", exc);

            }
        }

    }
}
