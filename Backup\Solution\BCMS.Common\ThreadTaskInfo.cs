﻿using System;
using System.Threading;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    public class ThreadTaskInfo<T>
    {
        private readonly object _synchLock = new object();

        /// <summary>
        /// Initializes a new instance of the <see cref="ThreadTaskInfo&lt;T&gt;"/> class.
        /// </summary>
        /// <param name="task">The task.</param>
        public ThreadTaskInfo(T task)
        {
            Task = task;

            // Task is created, but has not started running yet
            State = ThreadTaskStateType.Created;

            // Task is not set to completed
            TaskComplete = new ManualResetEvent(false);
        }

        /// <summary>
        /// Gets or sets the task that is running on the thread
        /// </summary>
        /// <value>The task.</value>
        public T Task { get; private set; }

        /// <summary>
        /// Gets or sets the task complete signal (Wait Handle).
        /// </summary>
        /// <value>The task complete.</value>
        public ManualResetEvent TaskComplete { get; private set; }

        /// <summary>
        /// Gets or sets an exception if it is thrown by the task.
        /// </summary>
        /// <value>The exception.</value>
        public Exception Exception { get; set; }

        /// <summary>
        /// Gets or sets the state of currently running threaded task.
        /// </summary>
        /// <value>The state.</value>
        public ThreadTaskStateType State { get; private set; }

        /// <summary>
        /// Set the state of the task.
        /// </summary>
        /// <param name="state">The state.</param>
        public void SetState(ThreadTaskStateType state)
        {
            // Lock the writing of the State property
            lock (_synchLock)
            {
                switch (state)
                {
                    case ThreadTaskStateType.Created:
                        throw new Exception("State cannot be set to created");

                    case ThreadTaskStateType.Started:
                        if (State != ThreadTaskStateType.Created)
                        {
                            return;
                        }
                        State = state;
                        return;

                    case ThreadTaskStateType.Completed:
                        if (State != ThreadTaskStateType.Started)
                        {
                            return;
                        }
                        State = state;
                        return;

                    case ThreadTaskStateType.Failed:
                        if (State == ThreadTaskStateType.Started || State == ThreadTaskStateType.Completed)
                        {
                            State = state;
                        }
                        return;

                    case ThreadTaskStateType.FailedTimeout:
                        if (State == ThreadTaskStateType.Started || State == ThreadTaskStateType.Completed)
                        {
                            State = state;
                        }
                        return;
                }
            }
        }
    }
}
