﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DataLag", Namespace = "http://www.Bcms.com/types")]
    public class DataLag : BaseEntity
    {
        #region Properties

        [DataMember]
        public string LogFileName
        {
            get;
            set;
        }

        [DataMember]
        public string PRSequenceNo
        {
            get;
            set;
        }

        [DataMember]
        public string DRSequenceNo
        {
            get;
            set;
        }


        [DataMember]
        public string PRTransId
        {
            get;
            set;
        }

        [DataMember]
        public string DRTransId
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogDate
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogDate
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogTime
        {
            get;
            set;
        }   
     
          [DataMember]
        public string PRLogDateTime
        {
            get;
            set;
        }
          [DataMember]
          public string DRLogDateTime
          {
              get;
              set;
          } 
        [DataMember]
          public int InfraObjectId
        {
            get;
            set;
        }
        [DataMember]
        public int GroupDatabaseNodeId
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessServiceId
        {
            get;
            set;
        }
        [DataMember]
        public int BuinsessFunctionId
        {
            get;
            set;
        }

        [DataMember]
        public int Health
        {
            get;
            set;
        }

        [DataMember]
        public string GroupName
        {
            get;
            set;
        }


        [DataMember]
        public string CurrentDataLag
        {
            get;
            set;
        }    

        #endregion

        #region Constructor

        public DataLag()
            : base()
        {
        }

        #endregion
    }
}
