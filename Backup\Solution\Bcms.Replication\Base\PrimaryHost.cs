﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Text;
using Bcms.ExceptionHandler;
using Bcms.Replication.Entity;
using Bcms.Replication.Shared;
using Jscape.Ssh;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Oracle.DataAccess.Client;
using PGlobalMirror;
using System.IO;

namespace Bcms.Replication.Base
{
    public static class CustomDatabaseFactory
    {
        static readonly DbProviderFactory DbProviderFactory = DbProviderFactories.GetFactory("Oracle.DataAccess.Client");

        public static Database CreateDatabase(string connectionString)
        {
            return new GenericDatabase(connectionString, DbProviderFactory);
        }
    }

    public class PrimaryHost : Host
    {
        public SshSession PrimarySession { get; set; }

        private SecureConnection _primaryConnection;

        private readonly object _lockObject = new object();

        public SecureConnection PrimaryConnection
        {
            get
            {
                return _primaryConnection ?? (_primaryConnection = new SecureConnection());
            }
            set
            {
                _primaryConnection = value;
            }
        }

        public PrimaryHost(string hostName, string userName, string password)
        {
            if(string.IsNullOrEmpty(hostName))
            {
                throw new ArgumentException("HostName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(userName))
            {
                throw new ArgumentException("UserName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(password))
            {
                throw new ArgumentException("Password cannot be null or empty");
            }
            HostName = hostName;
            UserName = userName;
            Password = password;
            PrivateKey = string.Empty;
            PrivateKeyPasspharase = string.Empty;
        }

        public PrimaryHost(string hostName, string userName, string privatekey, string privateKeyPasspharase)
        {
            HostName = hostName;
            UserName = userName;
            Password = string.Empty;
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
        }

        internal override bool Connect(bool isConnectDatabase)
        {
            try
            {
                lock (_lockObject)
                {
                    if (string.IsNullOrEmpty(Password) && !string.IsNullOrEmpty(PrivateKey))
                    {
                        SshParameters parameters = new SshParameters(HostName, UserName, Password);

                        parameters.SetPrivateKey(new FileInfo(PrivateKey), PrivateKeyPasspharase);

                        PrimarySession = new SshSession(parameters);

                    }
                    else
                    {
                        PrimarySession = new SshSession(HostName, UserName, Password);

                    }

                    PrimarySession.LicenseKey = Constants.QueryConstants.LicenseKey;
                    PrimarySession.SetShellPrompt("\\$|#|>", true);
                    PrimarySession.Connect();

                    if (isConnectDatabase)
                    {
                        return ConnectToDatabase(PrimarySession, DatabaseName);

                    }

                    return true;
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting Primary host - " + HostName, sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occured while connecting Primary host - " + HostName, exc);
            }
        }

        //internal override bool ConnectWithDatabaseName(string databaseName)
        //{
        //    try
        //    {
        //        PrimarySession = new SshSession(HostName, UserName, Password) { LicenseKey = Constants.QueryConstants.LicenseKey };
        //        PrimarySession.SetShellPrompt("\\$|#|>", true);
        //        PrimarySession.Connect();

        //        return ConnectToDatabase(PrimarySession, databaseName);
        //    }
        //    catch (SshAuthenticationException sshAuthentication)
        //    {
        //        throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
        //    }
        //    catch (SshException sshException)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting Primary host - " + HostName, sshException);
        //    }
        //    catch (BcmsException)
        //    {
        //        throw;
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.CommonUnhandled,
        //                                "Exception occured while connecting Primary host - " + HostName, exc);
        //    }
        //}

        internal override void Disconnect()
        {
            if (PrimarySession != null)
            {
                LogHelper.LogMessage("PRIMARY CONNECTION DISCONNECT");
                PrimarySession.Disconnect();
            }
        }

        internal override bool VerifyDatabaseMode()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGVerifyDatabaseMode();
                }
                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE COMMAND : ", Constants.QueryConstants.DatabaseModeAndRole);

                var primaryRole = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DatabaseModeAndRole, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE OUTPUT : ", primaryRole);

                return primaryRole.Contains("primary") && primaryRole.Contains("read write");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while verify primary database mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while verify primary database mode", exc);
            }

           
        }
        
        internal override bool VerifyUserStatus()
        {
            try
            {
                if(IsWindows)
                {
                    string usercounts = WinDGVerifyUserStatus();

                    LogHelper.LogHeaderAndMessage("PRIMARY DATABASE USER COUNT : ", usercounts);

                    if (Convert.ToInt32(usercounts) > 0)
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound, usercounts + "Active user (" + usercounts + ") found logged in status in Primary Server (" + HostName + ")");

                    }
                    return true;
                    
                }

                LogHelper.LogHeaderAndMessage("PRIMARY USER STATUS COMMAND : ", Constants.QueryConstants.ActiveUserCount);

                var usercount = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.ActiveUserCount,Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE USER COUNT :", usercount);

                if (usercount.Contains("count(*)") && usercount.Contains("-") && usercount.Contains("sql>"))
                {
                    usercount = usercount.Replace("\r\n", "");
                    usercount = usercount.Replace("\t", "");
                    usercount = usercount.Replace("count(*)", "");
                    usercount = usercount.Replace("-", "");
                    usercount = usercount.Replace("sql>", "").Trim();

                    if(Convert.ToInt32(usercount) > 0)
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound, usercount + "Active user (" + usercount + ") found logged in status in Primary Server (" + HostName + ")");
                        
                    }
                    return true;

                }

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS AUTHENTICATION ERROR",sshAuthentication);  

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS SSH EXCEPTION ERROR", sshException);  

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while get Primary Active user count", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS EXCEPTION ERROR", exc);  

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,"Exception occured while get Primary Active user count", exc);
            }
            return false;
        }

        internal override string GetMaxSequenceNumber()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGGetMaxSequenceNumber();
                }

                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE MAX SEQUENCE COMMAND : ", Constants.QueryConstants.MaxSequenceNo);

                string output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.MaxSequenceNo, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MAX SEQUENCE OUTPUT :", output);

                return output;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while Primary get Max Sequnce No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while get Primary Max Sequnce No", exc);
            }
           
        }
       
        internal override bool Exit()
        {
            try
            {
                ExitPromt(PrimarySession);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal override bool MoveFiles(string sourcePath, string fileName, string targetPath)
        {
            var moveResult = MoveFiles(PrimarySession, sourcePath, fileName, targetPath);

            return moveResult == null || !moveResult.Contains("cannot move");
        }

        internal override void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt)
        {
            MoveFiles(PrimarySession, sourcePath, targetPath, true);
        }

        internal bool ConnectServer()
        {
            return IsWindows ? SSHHelper.Ping(HostName) : SSHHelper.Connect(HostName, UserName, Password);
        }

        internal bool IsDatabaseRunning()
        {
            if(IsWindows)
            {
                return IsWinDatabaseRunning();
            }
            return OracleDB.DBRunning(new SSHInfo(HostName, UserName, Password),SudoUser,DatabaseName);
        }

        internal bool IsListnerRunning()
        {
            if (IsWindows)
            {
                return true;
                //return IsWinDatabaseListnerRunning();
            }
            return OracleDB.IsListnerRunning(new SSHInfo(HostName, UserName, Password), SudoUser);
        }


        internal bool SwitchDatabaseToStandbyMode()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGSwitchDatabaseToStandbyMode();
                }

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE COMMAND : ", Constants.QueryConstants.SwitchToStandbyMode);

                var switchresult = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.SwitchToStandbyMode, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE TO STANDBY MODE OUTPUT:", switchresult);

                return switchresult.Contains("database altered");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while Swith database Primary to standby mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while Swith database Primary to standby mode", exc);
            }
           
        }

        internal bool ShutdownPrimaryDatabase()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGShutdownPrimaryDatabase();
                }
                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE COMMAND : ", Constants.QueryConstants.ShutdownPrimary);

                var shutdownresult = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.ShutdownPrimary, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE OUTPUT :", shutdownresult);

                return shutdownresult.Contains("instance shut down") || shutdownresult.Contains("instance terminated");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while shutdown Primary Database", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while shutdown Primary Database", exc);
            }


           
        }

        internal bool MountStandby()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGMountStandby();
                }

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY COMMAND : ", Constants.QueryConstants.MountStandby);

                var standbyresult = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.MountStandby, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY OUTPUT :", standbyresult);

                return standbyresult.Contains("database mounted");

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while mount standby", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while  mount standby" +
                                                                           "" +
                                                                           "", exc);
            }


            
        }

        internal bool StartStandbyRecovery()
        {
            try
            {
                if (IsWindows)
                {
                    return WinDGStartStandbyRecovery();
                }

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY COMMAND : ", Constants.QueryConstants.StandbyRecovery);

                var startStandbyresult = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.StandbyRecovery, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY :", startStandbyresult);

                return startStandbyresult.Contains("database altered");
                
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while start standby recovery", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while while start standby recovery", exc);
            }
        }

        internal bool VerifyPrimaryProcess()
        {
            try
            {
                if(IsWindows)
                {
                    var processCounts = WinDGVerifyPrimaryProcess();

                    if(!processCounts)
                    {
                        KillAllWindowsPrimaryProcess();
                    }
                    return true;
                }

                var processCount = IsPrimaryHavingProcess(PrimarySession);

                if (!processCount)
                {
                    IsKillAllPrimaryProcess(PrimarySession);
                }

                return true;

            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while verify primary process", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while while verify primary process", exc);
            }
          
        }

        internal void GetDataFileCount()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE COUNT");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileCount, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileCount);

                LogHelper.LogHeaderAndMessage("DATA FILE COUNT OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DATA FILE COUNT ERROR", exc.Message);
            }
            
        }

        internal void CheckDataFileError()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE ERROR");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileError, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileError);

                LogHelper.LogHeaderAndMessage("DATA FILE ERROR OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("DATA FILE ERROR ERROR", exc.Message);
            }
        }

        internal void CheckRecoveryModeCount()
        {
            try
            {
                LogHelper.Header("CHECK RECOVERY MODE COUNT");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInRecoveryMode, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInRecoveryMode);

                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT ERROR", exc.Message);
            }
        }

        internal void CheckBackupMode()
        {
            try
            {
                LogHelper.Header("CHECK BACKUP MODE");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInBackupMode, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInBackupMode);

                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE ERROR", exc.Message);
            }
        }

        internal void CheckDataFileStatus()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE STATUS");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileStatus, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileStatus);

                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS ERROR", exc.Message);
            }
        }

        internal void CheckDataFileMode()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE MODE");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInReadMode, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInReadMode);

                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE ERROR", exc.Message);
            }
        }

        internal void IsCorruptedDataFile()
        {
            try
            {
                LogHelper.Header("IS CORRUPTED DATA FILE");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInCorrupted, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInCorrupted);

                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE ERROR", exc.Message);
            }
        }

        internal void CheckPointChange()
        {
            try
            {
                LogHelper.Header("CHECK POINT CHANGE");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckPointChange, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CheckPointChange);

                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE ERROR", exc.Message);
            }
        }

        internal void CheckCurrentSCN()
        {
            try
            {
                LogHelper.Header("CHECK CURRENT SCN");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.CurrentSCNNumber, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CurrentSCNNumber);

                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN ERROR", exc.Message);
            }
        }

        internal void CheckNologgingOperation()
        {
            try
            {
                LogHelper.Header("CHECK NOLOGGING OPERATION");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.NoLoggingOperation, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.NoLoggingOperation);

                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION ERROR", exc.Message);
            }
        }

        internal void CheckPrDataGuardStatus()
        {
            try
            {
                LogHelper.Header("CHECK PR DATAGUARD STATUS");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.PRDataGuardStatus);

                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS ERROR", exc.Message);
            }
        }

        internal void CheckForceLogging()
        {
            try
            {
                LogHelper.Header("CHECK FORCE LOGGING");

                var output = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckForceLogging, Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CheckForceLogging);

                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING OUTPUT", output);

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING ERROR", exc.Message);
            }
        }

        internal void VerifyPrimaryDatabaseLogCount()
        {
            int dbcountResult = VerifyPrimaryLogCount(PrimarySession);

            for (int i = 0; i <= 2 + dbcountResult; i++)
            {
                RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.AlterLogfiles,
                                       Constants.QueryConstants.GreaterThan);
            }
        }

        internal string VerifyRecoveryMode()
        {
            return RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.DataGuardInRecovery, Constants.QueryConstants.GreaterThan).ToLower();

        }

        internal string VerifyArachiveLogGap()
        {
            return  RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.VerifyArchiveGap, Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string CheckPRDataGuard()
        {
            string result;

            var prdgResult = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus,
                                                    Constants.QueryConstants.GreaterThan).ToLower();


            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.PRDataGuardStatus);

            LogHelper.LogHeaderAndMessage("COMMAND Outpu: ", prdgResult);

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive"; 
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }


            return result;
        }

        internal string StartFastCopy(string hostname,string username,string password, string localDir,string remoteDir,string processcode)
        {

            var sb = new StringBuilder();

            var commands = new List<string>
                                        {
                                            Constants.QueryConstants.ChangeFastCopyDirectory,

                                            string.Format("{0} \"{1}_{2}_{3}_{4}_{5}\"",
                                                          Constants.QueryConstants.StartFastCopyReplication,
                                            processcode,
                                            hostname,
                                            username,
                                            localDir,
                                            remoteDir),
                                            password
                                        };

            foreach (var command in commands)
            {
                sb.Append(RunSshCommands(PrimarySession, command));
            }
            var connectResult = sb.ToString().ToLower();

            return connectResult;
        }

        internal string CheckPRDataGuardWindows()
        {
            string result;

            var prdgResult = WinDGGetPrimaryDataGuardStatus().ToLower();

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive";
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }


            return result;
        }

        private bool IsWinDatabaseRunning()
        {
            try
            {
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    return true;
                }              
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB RUNNING EXCEPTION : ", exc);

                return false;
            }
            
        }

        private bool IsWinDatabaseListnerRunning()
        {
            try
            {
                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" + DatabaseName;
               
                using (var db = new OracleDatabase(constring))
                {
                    return true;
                } 
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB LISTNER RUNNING EXCEPTION : ", exc);

                return false;
            }
        }


        private string WinDGGetPrimaryDataGuardStatus()
        {
            try
            {
                string output = string.Empty;

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsPRDataGuardStatus))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                           return  myDatabaseReader[0].ToString();
                            
                        }
                    }
                }

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATAGUARD STATUS : ", exc);

                return "INVALID";
            }
        }

        private bool WinDGVerifyDatabaseMode()
        {
            try
            {
               LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsDatabaseModeAndRole);

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDatabaseModeAndRole))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            var mode = myDatabaseReader[1].ToString();
                            var role = myDatabaseReader[2].ToString();

                            if (mode == "READ WRITE" && role == "PRIMARY")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION : ", exc);
                return false;
            }

            return false;
        }

        private string WinDGVerifyUserStatus()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsActiveUserCount);
                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsActiveUserCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();

                        }
                    }
                }
                return "0";
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY USER STATUS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGVerifyPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsProcessCount);
                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsProcessCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string count = myDatabaseReader[1].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY PROCESS COUNT :", count);

                            return Convert.ToInt32(count) == 0;
                        }
                    }
                }
                return false;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY PROCESS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool KillAllWindowsPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsKillProcess);

                LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS ", string.Empty);

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsKillProcess))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string output = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS OUTPUT:", output);
                            
                            if (output == "")
                            {
                                return true;
                            }
                            return false;

                        }
                    }
                }
                LogHelper.LogHeaderAndMessage("KILLED ALL PRIMARY PROCESS ", string.Empty);
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("KILL PRIMARY PROCESS EXCEPTION : ", exc);

                throw;
            }
        }

        private string WinDGGetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMaxSequenceNo);

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsMaxSequenceNo))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string squenceno = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY MAX SEQUNCE NO OUTPUT : ", squenceno);
                            
                            return squenceno;
                        }
                    }
                }

                return string.Empty;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY MAX SEQUNCE NO EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGSwitchDatabaseToStandbyMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsSwitchToStandbyMode);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsSwitchToStandbyMode);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY DATABASE TO STANDBY MODE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGShutdownPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.ShutdownPrimary);
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.Shutdown(OracleDBShutdownMode.Immediate,true);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGMountStandby()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMountStandby);

                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" + DatabaseName;

                using (var db = new OracleDatabase(constring))
                {
                    db.Startup(OracleDBStartupMode.NoRestriction, null, false);
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsMountStandby);
                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGStartStandbyRecovery()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsStandbyRecovery);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsStandbyRecovery);

                    db.Dispose();
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION : ", exc);

                throw;
            }
        }


        internal List<string> GetPrimaryDRMaxLogSequnce(string sequnce)
        {
            try
            {
                var prsequnce=new List<string>();

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinDRLogLatestSeqInPrimary + sequnce))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                             
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY DR LATEST LOG SEQUENCE EXCEPTION : ", exc);

                throw;
            }
        }


        internal List<string> GetPrimaryMaxLogSequnce()
        {
            try
            {
                var prsequnce = new List<string>();

                var db = CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabasePassword));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinPRLatestLogSeq))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY LATEST LOG SEQUENCE EXCEPTION : ", exc);

                throw;
            }
        }
    }
}