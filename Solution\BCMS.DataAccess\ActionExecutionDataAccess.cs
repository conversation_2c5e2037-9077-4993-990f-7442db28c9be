﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ActionExecutionDataAccess : BaseDataAccess
    {

        public static ActionExecution GetPendingAction()
        {

            ActionExecution actionExecution = new ActionExecution();

            const string SP = "ActionExecution_GetPendingAction";

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                using (IDataReader myActionDataReader = db.ExecuteReader(cmd))
                {
                    if (myActionDataReader.Read())
                    {
                        const int FLD_ID = 0;
                        const int FLD_STARTTIME = 1;
                        const int FLD_ENDTIME = 2;
                        const int FLD_STATUS = 3;
                        const int FLD_ACTIONID = 4;
                        const int FLD_GROUPID = 5;
                        const int FLD_CREATORID = 6;
                        const int FLD_CREATEDATE = 7;

                        actionExecution.Id = myActionDataReader.IsDBNull(FLD_ID) ? 0 : myActionDataReader.GetInt32(FLD_ID);
                        actionExecution.StartTime = myActionDataReader.IsDBNull(FLD_STARTTIME) ? DateTime.MinValue : myActionDataReader.GetDateTime(FLD_STARTTIME);
                        actionExecution.EndTime = myActionDataReader.IsDBNull(FLD_ENDTIME) ? DateTime.MinValue : myActionDataReader.GetDateTime(FLD_ENDTIME);
                        actionExecution.Status = myActionDataReader.IsDBNull(FLD_STATUS) ? string.Empty : myActionDataReader.GetString(FLD_STATUS);
                        actionExecution.ActionId = myActionDataReader.IsDBNull(FLD_ACTIONID) ? 0 : myActionDataReader.GetInt32(FLD_ACTIONID);
                        actionExecution.GroupId = myActionDataReader.IsDBNull(FLD_GROUPID) ? 0 : myActionDataReader.GetInt32(FLD_GROUPID);
                        actionExecution.CreatorId = myActionDataReader.IsDBNull(FLD_CREATORID) ? 0 : myActionDataReader.GetInt32(FLD_CREATORID);
                        actionExecution.CreateDate = myActionDataReader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : myActionDataReader.GetDateTime(FLD_CREATEDATE);

                        return actionExecution;

                    }
                   
                }
                return null;
            }
        }


        public static bool UpdateActionExecutionByStatus(int id,string status)
        {
            const string SP = "ActionExecution_UpdateByStatus";

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                db.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                db.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, status);

                var returnCode = db.ExecuteNonQuery(cmd);

                return returnCode > 0;

            }
        }
    }
}