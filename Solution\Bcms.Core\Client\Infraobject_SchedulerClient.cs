﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PDTake;
using PMSSQLNative;
using Bcms.Core.Workflows;

namespace Bcms.Core.Client
{
    public class Infraobject_SchedulerClient : IDisposable
    {
        public Infraobject_SchedulerClient(int Schedulewfid)
        {
            ScheduleInfraobjectwfid = Schedulewfid;
        }
        public int ScheduleInfraobjectwfid { get; set; }

        public InfraObject CurrentInfraObject { get; set; }

        WorkflowManager wrkflowMgr = new WorkflowManager();
        BcmsClient bcmsclientobj = new BcmsClient();

        public bool _isDisposed;

        private static readonly ILog Logger = LogManager.GetLogger(typeof(Infraobject_SchedulerClient));


        public void ExecuteInfraobjectScheduleWorkflow()
        {
            if (ScheduleInfraobjectwfid > 0)
            {
                try
                {
                    var ScheduleWorkflowdetails = Infraobject_SchedulerDataAccess.GelInfraobjectSchedulerById(ScheduleInfraobjectwfid);
                    if (ScheduleWorkflowdetails != null)
                    {
                        CurrentInfraObject = InfraObjectDataAccess.GetInfraObjectById(ScheduleWorkflowdetails.InfraobjectId);
                        if (ScheduleWorkflowdetails.WorkFlowId > 0)
                        {
                            var workflowdetails = WorkflowDataAccess.GetWorkflowById(ScheduleWorkflowdetails.WorkFlowId);

                            ApplicationMonitorPerformWorkflow(workflowdetails, ScheduleWorkflowdetails.ScheduleType);
                        }
                        else
                        {
                            Logger.InfoFormat("Scheduler Workflow Id is Null for Infraobject :" + CurrentInfraObject.Name);
                        }
                    }
                    else
                    {
                        Logger.InfoFormat("Infraobject Scheduler  is Null for ID " + ScheduleInfraobjectwfid);
                    }
                }
                catch (Exception ex)
                {
                    if (ex.InnerException != null)
                    {
                        Logger.ErrorFormat("Error While Execute Schedule Workflow " + ex.InnerException);
                    }
                    else
                    {
                        Logger.ErrorFormat("Error While Execute Schedule Workflow " + ex.Message);
                    }
                }
            }
            else
            {
                Logger.InfoFormat("Infraobject Scheduler Workflow Info for Id is Null");
            }
        }

        private void ApplicationMonitorPerformWorkflow(Workflow workflow, int ScheduleType)
        {
            if (workflow != null)
            {
                string[] workflowActions = bcmsclientobj.GetWorkFlowActionFromXml(workflow.Xml);

                workflowActions = wrkflowMgr.RemoveArrayFirstElement(workflowActions);

                var _workflowStatus = false;

                var count = 0;

                wrkflowMgr.CurrentInfraObject = CurrentInfraObject;
                wrkflowMgr.CurrentWorkflow = workflow;
                wrkflowMgr._ScheduleId = ScheduleInfraobjectwfid;
                wrkflowMgr._SchStartTime = DateTime.Now;
                wrkflowMgr._SchEndTime = DateTime.Now;
                foreach (var action in workflowActions)
                {
                    try
                    {
                        bcmsclientobj._workflowActionId = Convert.ToInt32(action.Split('@')[0]);
                        wrkflowMgr._workflowActionId = Convert.ToInt32(action.Split('@')[0]);
                        
                        var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(wrkflowMgr._workflowActionId);

                        if (workflowAction != null)
                        {
                            if (!bcmsclientobj._isAbort)
                            {
                                wrkflowMgr.BindWorkflowActionProperties(workflowAction);

                                bcmsclientobj._drOperationResult = null;

                                bcmsclientobj.CurrentWorkflowAction = null;

                                bcmsclientobj.CurrentWorkflowAction = workflowAction;

                                bcmsclientobj.CurrentWorkflowActionName = workflowAction.Name;

                                bcmsclientobj.CurrentWorkflowActionId = workflowAction.Id;

                                bcmsclientobj.CurrentWorkflowActionType = (WorkflowActionType)workflowAction.ActionType;

                                bcmsclientobj.CurrentActionStartTime = DateTime.Now;

                                bcmsclientobj.CurrentActionEndTime = DateTime.Now;

                                wrkflowMgr.CurrentWorkflowAction = workflowAction;

                                wrkflowMgr.CurrentWorkflowActionId = workflowAction.Id;
                                try
                                {
                                    wrkflowMgr.PerformAction(bcmsclientobj.CurrentWorkflowActionType, false, false,false);

                                    Logger.Info("Schedule Workflow Name :" + workflow.Name + " for Infraobject " + CurrentInfraObject.Name + " Action :" + workflowAction.Name + " Executed Successfully..!");
                                    if (count == 0)
                                    {
                                        wrkflowMgr.BindAddInfraobjectSchedularWorkflowStatusUI(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Running", workflowAction.Id);
                                    }
                                    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                                    wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Running", workflowAction.Id, workflowdetails.Id);
                                    count++;
                                }
                                catch (Exception ex)
                                {
                                    Logger.ErrorFormat("Error While Execute Schedule Workflow " + CurrentInfraObject.Name);
                                    if (count == 0)
                                    {
                                        wrkflowMgr.BindAddInfraobjectSchedularWorkflowStatusUI(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", workflowAction.Id);
                                    }
                                    FailedActionExcution(workflow, CurrentInfraObject, ScheduleInfraobjectwfid, bcmsclientobj.CurrentWorkflowActionName, workflowAction.Id, ex.Message);
                                    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                                    if (workflowdetails != null)
                                    {
                                        wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", workflowAction.Id, workflowdetails.Id);

                                        if (workflowdetails.Status == "Running")
                                        {
                                            wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, count + 1);
                                        }
                                    }
                                    else
                                    {
                                        var workflowdetails1 = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Error");
                                        if (workflowdetails1 != null)
                                            wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", workflowAction.Id, workflowdetails1.Id);


                                    }
                                    return;
                                }
                            }

                        }
                    }
                    catch (BcmsException ex)
                    {
                        _workflowStatus = false;
                        if (count == 0)
                        {
                            wrkflowMgr.BindAddInfraobjectSchedularWorkflowStatusUI(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId);
                        }
                        FailedActionExcution(workflow, CurrentInfraObject, ScheduleInfraobjectwfid, bcmsclientobj.CurrentWorkflowActionName, bcmsclientobj.CurrentWorkflowActionId, ex.Message);
                        Logger.ErrorFormat("Error While Execute Schedule PerformWorkflow for Infraobject " + CurrentInfraObject.Name);
                        var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                        if (workflowdetails != null)
                        {
                            wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, workflowdetails.Id);
                            Logger.ErrorFormat("Schedule Workflow : " + workflow.Name + "is Stopped for infraobject :" + CurrentInfraObject.Name);

                            if (workflowdetails.Status == "Running")
                            {
                                wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, count);
                            }
                        }
                        else
                        {
                            var workflowdetails1 = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Error");
                            if (workflowdetails1 != null)
                                wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, workflowdetails1.Id);


                        }
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _workflowStatus = false;
                        if (count == 0)
                        {
                            wrkflowMgr.BindAddInfraobjectSchedularWorkflowStatusUI(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId);
                        }
                        var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                        if (workflowdetails != null)
                        {
                            wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, workflowdetails.Id);
                            Logger.ErrorFormat("Schedule Workflow : " + workflow.Name + "is Stopped for infraobject :" + CurrentInfraObject.Name);
                            Logger.ErrorFormat("Error While Execute Schedule PerformWorkflow for Infraobject " + CurrentInfraObject.Name);

                            if (workflowdetails.Status == "Running")
                            {
                                wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, count);
                            }
                        }
                        else
                        {
                            var workflowdetails1 = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Error");
                            if (workflowdetails1 != null)
                                wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Error", bcmsclientobj.CurrentWorkflowActionId, workflowdetails1.Id);


                        }
                        throw;
                    }

                }
                if (workflowActions.Count() == count)
                {

                    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                    if (workflowdetails != null)
                    {
                        wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Completed", bcmsclientobj.CurrentWorkflowActionId, workflowdetails.Id);
                        Logger.ErrorFormat("Schedule Workflow : " + workflow.Name + "is Running for infraobject :" + CurrentInfraObject.Name);
                        if (workflowdetails.Status == "Running")
                        {
                            wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Completed", bcmsclientobj.CurrentWorkflowActionId, count);
                        }
                    }
                    else
                    {
                        var workflowdetails1 = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Error");
                        if (workflowdetails1 != null)
                        {
                            wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Completed", bcmsclientobj.CurrentWorkflowActionId, workflowdetails1.Id);
                            Logger.ErrorFormat("Schedule Workflow : " + workflow.Name + "is Error for infraobject :" + CurrentInfraObject.Name);
                            if (workflowdetails1.Status == "Error")
                            {
                                wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails1.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Completed", bcmsclientobj.CurrentWorkflowActionId, count);
                            }
                        }
                    }
                }
                else
                {
                    Logger.ErrorFormat("Schedule Workflow : " + workflow.Name + "is Stopped for infraobject :" + CurrentInfraObject.Name);
                    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                    if (workflowdetails != null)
                    {
                        wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Stopped", bcmsclientobj.CurrentWorkflowActionId, workflowdetails.Id);
                        //    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                        if (workflowdetails.Status == "Running")
                        {
                            wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Stopped", bcmsclientobj.CurrentWorkflowActionId, count);
                        }

                    }
                    else
                    {
                        var workflowdetails1 = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Error");
                        if (workflowdetails1 != null)
                        {
                            wrkflowMgr.BindInfraobjectSchedularStatus(workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Stopped", bcmsclientobj.CurrentWorkflowActionId, workflowdetails1.Id);
                            //    var workflowdetails = InfraobjectSchedularWorkflowStatusDataAccess.GetScheduleworkflowById(ScheduleInfraobjectwfid, Convert.ToString(ScheduleType), "2", "Running");
                            if (workflowdetails1.Status == "Error")
                            {
                                wrkflowMgr.BindUpdateInfraobjectSchedularWorkflowStatusUI(workflowdetails1.Id, workflow.Id, workflow.Name, ScheduleInfraobjectwfid, ScheduleType, "Stopped", bcmsclientobj.CurrentWorkflowActionId, count);
                            }
                        }
                    }
                }
            }
        }

        private void FailedActionExcution(Workflow workflow, InfraObject _InfraObject, int ScheduleInfraobjectwfid, string workflowAction_Name, int ActionID,string Exception)
        {
            string[] workflowActions = bcmsclientobj.GetWorkFlowActionFromXml(workflow.Xml);

            workflowActions = wrkflowMgr.RemoveArrayFirstElement(workflowActions);

            foreach (var item in workflowActions)
            {

                var _workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32(item.Split('@')[0]));
                if (_workflowAction.ActionType == (int)WorkflowActionType.DRReady)
                {
                    DRReady _obj = new DRReady();
                    _obj.INFRAOBJECTID = _InfraObject.Id;
                    _obj.WORKFLOWID = workflow.Id;
                    _obj.LOGSCHEDULERID = ScheduleInfraobjectwfid;
                    _obj.ISDRREADY = 0;
                    _obj.REASON = Exception;
                    _obj.CreatorId = workflow.UpdatorId;
                    _obj.StartTime = wrkflowMgr._SchStartTime;
                    _obj.EndTime = DateTime.Now;
                    _obj.ActionID = ActionID;
                    bool isDRReady = DRReadyDataAccess.AddDRReady(_obj);
                }
            }

        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~Infraobject_SchedulerClient()
        {
            Dispose(false);
        }
    }
}
