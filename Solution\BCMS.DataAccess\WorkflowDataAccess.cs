﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using System.Collections.Generic;

namespace Bcms.DataAccess
{
    public class WorkflowDataAccess:BaseDataAccess
    {

        public static Workflow GetWorkflowByGroupIdAndType(int infraObjectId, int type)
        {
            var workflow = new Workflow();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOW_GETBYGROUPIDANDTYPE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring + "iType", DbType.Int32, type);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myworkflowReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myworkflowReader.Read())
                        {
                            workflow.Id = Convert.IsDBNull(myworkflowReader["Id"]) ? 0 : Convert.ToInt32(myworkflowReader["Id"]);
                            workflow.Name = Convert.IsDBNull(myworkflowReader["Name"]) ? string.Empty : Convert.ToString(myworkflowReader["Name"]);
                            workflow.Xml = Convert.IsDBNull(myworkflowReader["Xml"]) ? string.Empty : Convert.ToString(myworkflowReader["Xml"]);


                            //workflow.Id = Convert.ToInt32(myworkflowReader[0]);
                            //workflow.Name = myworkflowReader[1].ToString();
                            //workflow.Xml = myworkflowReader[2].ToString();
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by InfraObject Id - " + infraObjectId, exc);

            }
            return workflow;
        }

        public static List<Workflow> GetAllWorkflow()
        {
            List<Workflow> lstWorkflow = new List<Workflow>();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("BIAGetAllWorkflow"))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myworkflowReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myworkflowReader.Read())
                        {
                            var workflow = new Workflow
                            {
                                Id = Convert.IsDBNull(myworkflowReader["Id"]) ? 0 : Convert.ToInt32(myworkflowReader["Id"]),
                                Xml = Convert.IsDBNull(myworkflowReader["Xml"]) ? string.Empty : Convert.ToString(myworkflowReader["Xml"]),
                                CreateDate = Convert.IsDBNull(myworkflowReader["Createdate"]) ? DateTime.MinValue : Convert.ToDateTime(myworkflowReader["Createdate"]),
                                UpdateDate = Convert.IsDBNull(myworkflowReader["Updatedate"]) ? DateTime.MinValue : Convert.ToDateTime(myworkflowReader["Updatedate"]),

                            };
                            lstWorkflow.Add(workflow);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Geting GetAllWorkflow- ", exc);

            }
            return lstWorkflow;
        }

        public static List<Workflow> GetAllUpdatedWorkflow_BIA()
        {
            List<Workflow> lstWorkflow = new List<Workflow>();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Biagetallupdatedworkflow"))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myworkflowReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myworkflowReader.Read())
                        {
                            var workflow = new Workflow
                            {
                                Id = Convert.IsDBNull(myworkflowReader["Id"]) ? 0 : Convert.ToInt32(myworkflowReader["Id"]),
                                Xml = Convert.IsDBNull(myworkflowReader["Xml"]) ? string.Empty : Convert.ToString(myworkflowReader["Xml"]),
                                CreateDate = Convert.IsDBNull(myworkflowReader["Createdate"]) ? DateTime.MinValue : Convert.ToDateTime(myworkflowReader["Createdate"]),
                                UpdateDate = Convert.IsDBNull(myworkflowReader["Updatedate"]) ? DateTime.MinValue : Convert.ToDateTime(myworkflowReader["Updatedate"]),

                            };
                            lstWorkflow.Add(workflow);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Geting GetAllWorkflow- ", exc);

            }
            return lstWorkflow;
        }

        public static Workflow GetWorkflowByGroupIdAndStatus(int infraObjectId)
        {
            var workflow = new Workflow();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOW_GETBYINFRAIDANDSTATUS"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader myworkflowReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myworkflowReader.Read())
                        {
                            workflow.Id = Convert.IsDBNull(myworkflowReader["Id"]) ? 0 : Convert.ToInt32(myworkflowReader["Id"]);
                            workflow.Name = Convert.IsDBNull(myworkflowReader["Name"]) ? string.Empty : Convert.ToString(myworkflowReader["Name"]);
                            workflow.Xml = Convert.IsDBNull(myworkflowReader["Xml"]) ? string.Empty : Convert.ToString(myworkflowReader["Xml"]);


                            //workflow.Id = Convert.ToInt32(myworkflowReader[0]);
                            //workflow.Name = myworkflowReader[1].ToString();
                            //workflow.Xml = myworkflowReader[2].ToString();
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by InfraObject Id - " + infraObjectId, exc);

            }
            return workflow;
        }

        public static Workflow GetWorkflowById(int id)
        {
            var workflow = new Workflow();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Workflow_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader myworkflowReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myworkflowReader.Read())
                        {
                            workflow.Id = Convert.IsDBNull(myworkflowReader["Id"]) ? 0 : Convert.ToInt32(myworkflowReader["Id"]);
                            workflow.Name = Convert.IsDBNull(myworkflowReader["Name"]) ? string.Empty : Convert.ToString(myworkflowReader["Name"]);
                            workflow.Xml = Convert.IsDBNull(myworkflowReader["Xml"]) ? string.Empty : Convert.ToString(myworkflowReader["Xml"]);


                            //workflow.Id = Convert.ToInt32(myworkflowReader[0]);
                            //workflow.Name = myworkflowReader[1].ToString();
                            //workflow.Xml = myworkflowReader[2].ToString();
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Getting workflow by workflow Id - " + id, exc);

            }
            return workflow;
        }



    }
}