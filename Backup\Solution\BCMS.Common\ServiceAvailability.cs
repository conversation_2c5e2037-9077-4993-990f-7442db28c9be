﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ServiceAvailability", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ServiceAvailability : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string Status { get; set; }

        #endregion
    }
}
