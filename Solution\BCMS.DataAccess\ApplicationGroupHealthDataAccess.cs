﻿using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ApplicationGroupHealthDataAccess : BaseDataAccess
    {

        public static bool AddApplicationGroupHealth(ApplicationGroupHealth applicationGroup)
        {
            const string sp = "ApplicationGroupHealth_Create";

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(sp))
            {
                db.AddInParameter(cmd, Dbstring + "iApplicationGroupId", DbType.Int32, applicationGroup.ApplicationId);
                db.AddInParameter(cmd, Dbstring + "iApplicationGroupName", DbType.AnsiString, applicationGroup.ApplicationName);
                db.AddInParameter(cmd, Dbstring + "iHealth", DbType.Int32, applicationGroup.Health);
                db.AddInParameter(cmd, Dbstring + "iHealthUp", DbType.Int32, applicationGroup.HealthUp);
                db.AddInParameter(cmd, Dbstring + "iHealthDown", DbType.Int32, applicationGroup.HealthDown);
                db.AddInParameter(cmd, Dbstring + "iAvailability", DbType.AnsiString, applicationGroup.Availability);
                db.AddInParameter(cmd, Dbstring + "iCreatedate", DbType.DateTime, applicationGroup.CreateDate);

                int isuccess = db.ExecuteNonQuery(cmd);
                return isuccess > 0;

            }
        }
    }


}