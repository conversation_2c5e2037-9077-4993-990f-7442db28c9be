﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class eBDRProfile : BaseEntity
    {

        #region Properties


        public string Profile_Name { get; set; }

        public string migration_details { get; set; }

        public int InfraobjectId { get; set; }

        public string Source_Host { get; set; }

        public string Target_Host { get; set; }

        public string MachinName { get; set; }

        public string OsType { get; set; }

        public string Status { get; set; }

        public string Health { get; set; }

        public string RPO { get; set; }

        public string CalculatedRPO { get; set; }

        public string Datalag { get; set; }



        #endregion Properties



    }
}
