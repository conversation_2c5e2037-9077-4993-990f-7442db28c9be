﻿using System;
using log4net;

namespace Bcms.Replication.Shared
{
    public class LogHelper
    {
        private static readonly ILog Logger = LogManager.GetLogger("BcmsMonitor");

        public  static void LogHeaderAndMessageWithNewLine(string header,string message)
        {
            Logger.InfoFormat(header + Environment.NewLine + "---------------------------------" + Environment.NewLine + message);
        }

        public static void LogHeaderAndMessage(string header, string message)
        {
            Logger.InfoFormat(Environment.NewLine + header + message + Environment.NewLine);
        }

        public static void LogHeaderAndErrorMessage(string header, string message)
        {
            Logger.ErrorFormat(Environment.NewLine +Environment.NewLine + header + message + Environment.NewLine);
        }
        public static void LogHeaderAndErrorMessage(string header, Exception exception)
        {
            string message = exception.InnerException!=null ? exception.InnerException.Message : exception.Message;

            Logger.ErrorFormat(Environment.NewLine + Environment.NewLine + header + message + Environment.NewLine);
        }

        public static void LogMessage(string message)
        {
            Logger.Info(message);
        }

        public static void Header(string header)
        {
            Logger.InfoFormat(Environment.NewLine + Environment.NewLine + header + Environment.NewLine + "======================================" + Environment.NewLine);
        }
         
    }
}