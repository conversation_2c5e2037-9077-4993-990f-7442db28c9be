﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using System.Collections.Generic;

namespace Bcms.DataAccess
{
    public class InfraobjectSchedularStatusDataAccess : BaseDataAccess
    {
        public static bool AddInfraobjectSchedularStatus(InfraobjectSchedularStatus InfraobjectSchedular)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("InfraobjectSchStatus_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectName", DbType.AnsiString, InfraobjectSchedular.InfraObjectName);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.AnsiString, InfraobjectSchedular.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowName", DbType.AnsiString, InfraobjectSchedular.WorkflowName);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowID", DbType.AnsiString, InfraobjectSchedular.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionName", DbType.AnsiString, InfraobjectSchedular.CurrentActionName);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionID", DbType.AnsiString, InfraobjectSchedular.CurrentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iScheduleId", DbType.AnsiString, InfraobjectSchedular.ScheduleId);
                    Database.AddInParameter(cmd, Dbstring + "iScheduleType", DbType.AnsiString, InfraobjectSchedular.ScheduleType);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, InfraobjectSchedular.Status);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, string.Empty);
                    Database.AddInParameter(cmd, Dbstring + "iInfraschuWfID", DbType.AnsiString, InfraobjectSchedular.InfraschuWfID);
                    //Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.AnsiString, InfraobjectSchedular.StartTime);
                    //Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.AnsiString, InfraobjectSchedular.EndTime);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Infraobject Schedular Status Details", exc);

            }
        }

    }
}
