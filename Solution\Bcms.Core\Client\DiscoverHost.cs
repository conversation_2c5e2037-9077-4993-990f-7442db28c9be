﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using Bcms.Common;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.Core.Client
{
    public class DiscoverHostClient
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(DiscoverHostClient));

        public void PerformHostDiscover()
        {
            try
            {
                 IList<DiscoverHost> discoverList = DiscoverHostDataAccess.GetAllActiveDiscoverHost();

                if (discoverList != null && discoverList.Count > 0)
                {
                    foreach (var hostItem in discoverList)
                    {
                        DiscoverHostAddress(hostItem.Id,hostItem.RangeFrom,hostItem.RangeTo);
                    }
                }
            }
            catch (Exception exc)
            {
                Logger.Error("Exception occurred while discovering host address ." + exc.Message);
            }
        }

        public void DiscoverHostAddress(int discoverId, string rangeFrom, string rangeTo)
        {
            string range = GetRange(rangeFrom, rangeTo);

            var list = new IPRange(range);

            var ipList = list.GetAllIP();

            int scanId = DiscoverHostDataAccess.AddDiscoveryScan(discoverId, range);
            
            IList<string> reachableList =
                    (from ip in ipList where IsHostAccessible(ip.ToString()) select ip.ToString()).ToList();

                if (reachableList.Count > 0)
                {
                    int status = 0;

                    if (scanId < 2)
                    {
                        status = 1;

                        foreach (var host in reachableList)
                        {
                            Alert alert = new Alert();

                            alert.Type = "DiscoverNewHost";
                            alert.Severity = "Information";
                            alert.SystemMessage = "New Host (" + status + ") is discovered";
                            alert.UserMessage = "New Host (" + status + ") is discovered";
                            alert.JobName = "DiscoverHost";
                            alert.InfraObjectId = 62;
                            ExceptionManager.AddAlert(alert);

                            DiscoverHostDataAccess.AddDiscoverHostLogs(scanId, host, status);
                        }

                    }
                    else
                    {
                        IList<DiscoverHostLogs> discoveryLogList = DiscoverHostDataAccess.GetLastDiscoveryLogList();

                        foreach (var host in reachableList)
                          {
                              status = 1;

                              if (discoveryLogList.Any(log => host.Equals(log.ReachableHost)))
                              {
                                  status = 0;
                              }
                              if (status == 1)
                              {

                                  Alert alert = new Alert();
                                  alert.Type = "DiscoverNewHost";
                                  alert.Severity = "Information";
                                  alert.SystemMessage = "New Host (" + host + ") is discovered";
                                  alert.UserMessage = "New Host (" + host + ") is discovered";
                                  alert.JobName = "DiscoverHost";
                                  alert.InfraObjectId = 62;
                                  ExceptionManager.AddAlert(alert);
                              }
                              DiscoverHostDataAccess.AddDiscoverHostLogs(scanId, host, status);
                          }
                    }
                }
        }


        public static bool IsHostAccessible(string ipAddress)
        {
            try
            {
                var ping = new Ping();
                PingReply reply = ping.Send(ipAddress, 100);
                return reply != null && reply.Status == IPStatus.Success;
            }
            catch (Exception )
            {
                return false;
            }
        }





        //private PingReply[] PingRange(string rangeFrom, string rangeTo)
        //{
        //   var tasks = new List<Task<PingReply>>();

        //   string range = GetRange(rangeFrom, rangeTo);

        //   var list = new IPRange(range);

        //   var ipList = list.GetAllIP();

        //   foreach (var ip in ipList)
        //   {
        //       Task<PingReply> pingTask = PingIPAsync(ip);

        //       tasks.Add(pingTask);
        //   }
            
        //    //Wait for the tasks to finish
 
        //    Task.WaitAll(tasks.ToArray());
 
        //    //Return the ping replies
 
        //    return tasks.Select(x => x.Result).ToArray();
        //}


        //public static Task<PingReply> PingIPAsync(IPAddress ip)
        //{

        //    TaskCompletionSource<PingReply> tcs = new TaskCompletionSource<PingReply>();

        //    Ping ping = new Ping();

        //    ping.PingCompleted += (sender, pingCompletedEventArgs) =>
        //    {
        //        //The task completes when the TaskCompletionSource's result has been set!

        //        tcs.SetResult(pingCompletedEventArgs.Reply);
        //    };

        //    ping.SendAsync(ip, new object());
        //    return tcs.Task;
        //}

        private string GetRange(string rangeFrom, string rangeTo)
        {
            var sb = new StringBuilder();

            string[] fromArray = rangeFrom.Split('.');

            string[] toArray = rangeTo.Split('.');


            if (fromArray[0] == toArray[0])
            {
                sb.Append(fromArray[0]);
                sb.Append(".");
            }
            else
            {
                sb.Append(fromArray[0]+"-"+toArray[0]);
                sb.Append(".");
            }

            if (fromArray[1] == toArray[1])
            {
                sb.Append(fromArray[1]);
                sb.Append(".");
            }
            else
            {
                sb.Append(fromArray[1] + "-" + toArray[1]);
                sb.Append(".");
            }

            if (fromArray[2] == toArray[2])
            {
                sb.Append(fromArray[2]);
                sb.Append(".");
            }
            else
            {
                sb.Append(fromArray[2] + "-" + toArray[2]);
                sb.Append(".");
            }
            if (fromArray[3] == toArray[3])
            {
                sb.Append(fromArray[3]);
            }
            else
            {
                sb.Append(fromArray[3] + "-" + toArray[3]);
            }

            return sb.ToString();
        }

    }
}