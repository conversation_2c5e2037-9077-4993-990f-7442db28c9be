﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "BusinessService", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessService : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public bool DRReadyness { get; set; }

        [DataMember]
        public int CompanyId { get; set; }

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public string ConfiguredRPO { get; set; }

        [DataMember]
        public string ConfiguredRTO { get; set; }

        [DataMember]
        public string ConfiguredMAO { get; set; }

        [DataMember]
        public string Availabilty { get; set; }

        [DataMember]
        public string Health { get; set; }

        [DataMember]
        public int Priority { get; set; }

        #endregion
    }
}
