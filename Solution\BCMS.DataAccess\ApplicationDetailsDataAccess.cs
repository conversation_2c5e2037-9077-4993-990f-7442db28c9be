﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ApplicationDetailsDataAccess : BaseDataAccess
    {
        public static bool AddApplicationDetails(ApplicationDetail applicationDetail)
        {
            try
            {

                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("ApplicationDetail_Create"))
                {
                    db.AddInParameter(dbCommand, Dbstring + "iApplicationName", DbType.AnsiString, applicationDetail.ApplicationName);
                    db.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.Int32, applicationDetail.Status);
                    db.AddInParameter(dbCommand, Dbstring + "iSynStatus", DbType.AnsiString, applicationDetail.SyncStatus);
                    db.AddInParameter(dbCommand, Dbstring + "iApplicationGroupId", DbType.Int32, applicationDetail.ApplicationGroupId);
                    db.AddInParameter(dbCommand, Dbstring + "iApplicationId", DbType.Int32, applicationDetail.ApplicationId);
                    db.AddInParameter(dbCommand, Dbstring + "iDataLag", DbType.AnsiString, applicationDetail.Datalag);
                    db.AddInParameter(dbCommand, Dbstring + "iHealth", DbType.Int32, applicationDetail.Health);
                    int value = db.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ApplicationDetails information", exc);
            }
            return false;
        }

    }
}