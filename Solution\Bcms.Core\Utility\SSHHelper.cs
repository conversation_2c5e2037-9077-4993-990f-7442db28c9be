using System;
using Bcms.ExceptionHandler;
using Jscape.Ssh;
using log4net;
using Bcms.Common.Shared;
using Bcms.Common;
using System.IO;
using Bcms.DataAccess;
using Rebex.TerminalEmulation;
using Jscape.Ssh.Transport;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Parameters;
//using PSSHConn;
using System.Configuration;
using System.Text.RegularExpressions;
using PGlobalMirror;
using PSSHConnMutex;
using System.Threading;

namespace BCMS.Core.Utility
{

    public class SshUtil
    {
        public static SshConfiguration GetSshConfig()
        {
            SshConfiguration sshConfig = new SshConfiguration();
            sshConfig.ConnectionConfig.TransportConfiguration.RemoveAllCiphers();
            sshConfig.ConnectionConfig.TransportConfiguration.AddCipher(new BouncyCastleCipher.Creator("AES", "ctr", "aes128-ctr", 16, 16));
            return sshConfig;
        }
    }

    public class BouncyCastleCipher : ICipher
    {
        private readonly IBufferedCipher cipher;

        public BouncyCastleCipher(IBufferedCipher cipher)
        {
            this.cipher = cipher;
        }

        /// <summary>
        /// The cipher block length.
        /// </summary>
        public int BlockLength
        {
            get { return this.cipher.GetBlockSize(); }
        }

        /// <summary>
        /// Processes the specified data.
        /// </summary>
        /// <param name="data">the data to process</param>
        /// <returns>the processed data</returns>
        public byte[] Process(byte[] data)
        {
            return this.cipher.ProcessBytes(data);
        }

        public class Creator : ICipherCreator
        {
            private readonly string algorithmName;
            private readonly string mode;
            private readonly string sshAlgorithmName;
            private readonly int keyLength;
            private readonly int blockLength;

            public Creator(string algorithmName, string mode, string sshAlgorithmName, int keyLength, int blockLength)
            {

                this.algorithmName = algorithmName;
                this.mode = mode;
                this.sshAlgorithmName = sshAlgorithmName;
                this.keyLength = keyLength;
                this.blockLength = blockLength;
            }

            /// <summary>
            /// The cipher key length.
            /// </summary>
            public int KeyLength
            {
                get { return this.keyLength; }
            }

            /// <summary>
            /// The cipher block length.
            /// </summary>
            public int BlockLength
            {
                get { return this.blockLength; }
            }

            /// <summary>
            /// Creates a new encipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created encipher</returns>
            public ICipher CreateEncipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, true);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// Creates a new decipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created decipher</returns>
            public ICipher CreateDecipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, false);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// The creator algorithm name.
            /// </summary>
            public string Name
            {
                get { return this.sshAlgorithmName; }
            }

            private IBufferedCipher CipherFor(byte[] keyData, byte[] ivData, bool encryption)
            {
                string cipherAlgorithm = this.algorithmName + "/" + this.mode + "/NoPadding";

                IBufferedCipher cipher = CipherUtilities.GetCipher(cipherAlgorithm);
                KeyParameter keyParameter = ParameterUtilities.CreateKeyParameter(this.algorithmName, keyData);
                ParametersWithIV parameters = new ParametersWithIV(keyParameter, ivData);
                cipher.Init(encryption, parameters);

                return cipher;
            }

        }

    }

    public  class SSHHelper : IDisposable
    {

        #region Variable

        private const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(SSHHelper));

        private delegate void ConnectionSSHDelegate(Server server, string type);

        public bool _isDisposed;
        public int SSHConnCount;

        public static string IpAddress = "";
        //public static ILogWritter LogWritter { get; set; }


        #endregion

        //public static void BeginConnect(Server server,string type)
        //{
        //    var connection = new ConnectionSSHDelegate(Connect);

        //    connection.BeginInvoke(server,type, null, null);
        //}



        public  string Connect(Server server)
        {
            bool isconnect = false;
            string connect = "";
            bool removeConn = false;
            PSSHConnMutex.SSHServerInfo objSSHServer = null;
            dynamic client = null;

            objSSHServer = new PSSHConnMutex.SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);

            using (PSSHConnMutex.PSSHMutex psshMutex = new PSSHConnMutex.PSSHMutex())
            {
                try
                {
                    client = psshMutex.CreateSSHSession(objSSHServer);
                    Logger.InfoFormat("{0} :({1}) Connection Successfully established ", server.InfraObjectName, objSSHServer.SSHHost);
                    isconnect = true;
                }
                catch (Rebex.Net.SshException sshEx)
                {
                    string msg = sshEx.InnerException != null ? sshEx.InnerException.Message : "";
                    if (sshEx.ProtocolCode == "13")
                    {
                        Logger.ErrorFormat("Authentication Failed when connecting Server: {0} {1} for User:  Detailed Exception : {2}", objSSHServer.SSHHost, objSSHServer.SSHUser, Environment.NewLine, sshEx.Message + msg);
                        connect += "Authentication Failed";
                    }
                    else if (sshEx.ProtocolCode == "10")
                    {
                        Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
                        connect += "Host Not Reachable";
                    }
                    else
                    {
                        Logger.ErrorFormat("Error Occured while connecting PR Server: {0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
                        connect += "Error Occured while Connecting";
                    }
                }
                catch (SshAuthenticationException authexc)
                {
                    Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, authexc.Message);
                    connect += "Authentication Failed";
                }
                catch (SshTaskTimeoutException timeoutexe)
                {
                    Logger.ErrorFormat("No Response From Server: {0} {2} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, timeoutexe.Message);
                    connect += "No Response From Server";
                }
                catch (SshException sshexc)
                {
                    Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshexc.Message);
                    connect += "Host Not Reachable";
                }
                catch (Exception exc)
                {
                    string msg = exc.InnerException != null ? exc.InnerException.Message : "";

                    // Check for socket error 10055 (No buffer space available)
                    if (exc.Message.Contains("Socket error 10055") || (exc.InnerException != null && exc.InnerException.Message.Contains("Socket error 10055")))
                    {
                        Logger.ErrorFormat("Socket error 10055 (No buffer space available) while connecting to Server:{0} {1} This indicates network resource exhaustion. Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, exc.Message + " " + msg);
                        connect += "Network Resource Exhaustion - Socket Error 10055";
                    }
                    else
                    {
                        Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, exc.Message);
                        connect += "Error Occured while Connecting";
                    }
                }
                finally
                {
                    // Use helper method to safely dispose SSH session with proper null checking
                    removeConn = SafeDisconnectSSHSession(psshMutex, client, objSSHServer?.SSHHost);
                }
            }
            return isconnect + ":" + connect;
        }       

        public  string TestConnect(Server server)
        {
            bool isconnect = false;
            string connect = "";
            bool removeConn = false;
            PSSHConnMutex.SSHServerInfo objSSHServer = null;
            dynamic client = null;

            objSSHServer = new PSSHConnMutex.SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);
            using (PSSHConnMutex.PSSHMutex psshMutex = new PSSHConnMutex.PSSHMutex())
            {
                try
                {
                    client = psshMutex.CreateSSHSession(objSSHServer);
                    Logger.InfoFormat("{0} :({1}) Connection Successfully established ", server.InfraObjectName, objSSHServer.SSHHost);
                    isconnect = true;
                }
                catch (Rebex.Net.SshException sshEx)
                {
                    string msg = sshEx.InnerException != null ? sshEx.InnerException.Message : "";
                    if (sshEx.ProtocolCode == "13")
                    {
                        Logger.ErrorFormat("Authentication Failed when connecting Server: {0} {1} for User:  Detailed Exception : {2}", objSSHServer.SSHHost, objSSHServer.SSHUser, Environment.NewLine, sshEx.Message + msg);
                        connect += "Authentication Failed";
                    }
                    else if (sshEx.ProtocolCode == "10")
                    {
                        Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
                        connect += "Host Not Reachable";
                    }
                    else
                    {
                        Logger.ErrorFormat("Error Occured while connecting PR Server: {0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
                        connect += "Error Occured while Connecting";
                    }
                }
                catch (SshAuthenticationException authexc)
                {
                    Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, authexc.Message);
                    connect += "Authentication Failed";
                }
                catch (SshTaskTimeoutException timeoutexe)
                {
                    Logger.ErrorFormat("No Response From Server: {0} {2} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, timeoutexe.Message);
                    connect += "No Response From Server";
                }
                catch (SshException sshexc)
                {
                    Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshexc.Message);
                    connect += "Host Not Reachable";
                }
                catch (Exception exc)
                {
                    string msg = exc.InnerException != null ? exc.InnerException.Message : "";

                    // Check for socket error 10055 (No buffer space available)
                    if (exc.Message.Contains("Socket error 10055") || (exc.InnerException != null && exc.InnerException.Message.Contains("Socket error 10055")))
                    {
                        Logger.ErrorFormat("Socket error 10055 (No buffer space available) while connecting to Server:{0} {1} This indicates network resource exhaustion. Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, exc.Message + " " + msg);
                        connect += "Network Resource Exhaustion - Socket Error 10055";
                    }
                    else
                    {
                        Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, exc.Message);
                        connect += "Error Occured while Connecting";
                    }
                }
                finally
                {
                    // Use helper method to safely dispose SSH session with proper null checking
                    removeConn = SafeDisconnectSSHSession(psshMutex, client, objSSHServer?.SSHHost);
                }
            }
            return isconnect + ":" + connect;
        }


        //public static bool Connect(Server server, string type)
        //{

        //    bool isconnect = false;

        //    SshParameters sshParams = type == Constants.UIConstants.PrimaryServer ? new SshParameters(server.PRIPAddress, server.PRPort, server.PRUserName, server.PRPassword) : new SshParameters(server.DRIPAddress, server.DRPort, server.DRUserName, server.DRPassword);

        //    Ssh ssh = null;
        //    try
        //    {
        //        if (server.PRIsUseSShkeyAuth == 1)
        //        {
        //            if (type == Constants.UIConstants.PrimaryServer)
        //            { sshParams.SetPrivateKey(new FileInfo(server.PRSSHKeyPath), server.PRSSHKeyPassword); }
        //            else
        //            { sshParams.SetPrivateKey(new FileInfo(server.DRSSHKeyPath), server.DRSSHKeyPassword); }

        //        }

        //        ssh = new Ssh(sshParams,SshUtil.GetSshConfig())
        //        {
        //            LicenseKey = LicenseKey
        //        };


        //        ssh.Connect();

        //        if (ssh.Connected)
        //        {
        //            Logger.InfoFormat("{0} : {1}({2}) Connection Successfully established ", server.InfraObjectName, type, sshParams.Hostname);

        //            ssh.Disconnect();

        //            isconnect = true;
        //        }
        //    }
        //    catch (SshAuthenticationException authexc)
        //    {
        //        Logger.ErrorFormat("{0} : Authentication Failed when connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, authexc.Message);

        //        ExceptionManager.SendAlertsToUsers("Authentication Failed for host - " + sshParams.Hostname, server.InfraObjectName, "Server Monitor");
        //        // throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc);
        //        //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc));
        //        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
        //        isconnect = false;
        //    }
        //    catch (SshTaskTimeoutException timeoutexe)
        //    {
        //        Logger.ErrorFormat("{0} : No Response From Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, timeoutexe.Message);

        //    }
        //    catch (SshException sshexc)
        //    {
        //        Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detatiled Exception : {4}", server.InfraObjectName, type, sshParams.Hostname, Environment.NewLine, sshexc.Message);

        //        // throw new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.GroupName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc);
        //        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.InfraObjectName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
        //        isconnect = false;
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat("{0} : Error Occured while connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, exc.Message);
        //        ServerDataAccess.UpdateServerByStatus(true ? Convert.ToInt32(server.PRId) : Convert.ToInt32(server.DRId), ServerStatus.Down);
        //        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, exc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
        //        // throw;
        //        isconnect = false;
        //    }
        //    finally
        //    {
        //        if(ssh != null)
        //        if (ssh.Connected)
        //        {
        //            ssh.Disconnect();
        //        }
        //    }

        //    return isconnect;
        //}

        public  bool IsConnectRebex(Server server, string type)
        {
            bool isconnect = false;
            Rebex.Net.Ssh DRSessionRebex = new Rebex.Net.Ssh();
            VirtualTerminal terminal = new VirtualTerminal(80, 500);
            try
            {
                if (type == Constants.UIConstants.PrimaryServer)
                {
                    DRSessionRebex.Connect(server.PRIPAddress, server.PRPort);
                    DRSessionRebex.Login(server.PRUserName, server.PRPassword);
                }
                else if (type == Constants.UIConstants.DRServer)
                {
                    DRSessionRebex.Connect(server.DRIPAddress, server.DRPort);
                    DRSessionRebex.Login(server.DRUserName, server.DRPassword);
                }
                if (DRSessionRebex.IsConnected)
                {
                    isconnect = true;
                }
            }
            catch (Rebex.Net.SshException authexc)
            {
                Logger.ErrorFormat("{0} : Authentication Failed when connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, server.PRIPAddress, Environment.NewLine, authexc.Message);
                ExceptionManager.SendAlertsToUsers("Authentication Failed for host - " + server.PRIPAddress, server.InfraObjectName, "Server Monitor");
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.PRIPAddress, authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                isconnect = false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("{0} : Error Occured while connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, server.PRIPAddress, Environment.NewLine, exc.Message);
                ServerDataAccess.UpdateServerByStatus(true ? Convert.ToInt32(server.PRId) : Convert.ToInt32(server.DRId), ServerStatus.Down);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.PRIPAddress, exc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);

                isconnect = false;
            }
            finally
            {
                if (DRSessionRebex.IsConnected)
                {
                    DRSessionRebex.Disconnect();
                }
            }
            return isconnect;
        }

        public  bool ConnectWithSingle(Server server, string type)
        {

            bool isconnect = false;

            SshParameters sshParams = new SshParameters(server.PRIPAddress, server.PRPort, server.PRUserName, server.PRPassword);

            Ssh ssh = null;
            try
            {
                if (server.PRIsUseSShkeyAuth == 1)
                {
                    sshParams.SetPrivateKey(new FileInfo(server.PRSSHKeyPath), server.PRSSHKeyPassword);

                }

                ssh = new Ssh(sshParams,SshUtil.GetSshConfig())
                {
                    LicenseKey = LicenseKey
                };


                ssh.Connect();

                if (ssh.Connected)
                {
                    Logger.InfoFormat("{0} : {1}({2}) Connection Successfully established ", server.InfraObjectName, type, sshParams.Hostname);

                    ssh.Disconnect();

                    isconnect = true;
                }
            }
            catch (SshAuthenticationException authexc)
            {
                Logger.ErrorFormat("{0} : Authentication Failed when connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, authexc.Message);
                ExceptionManager.SendAlertsToUsers("Authentication Failed for host - " + sshParams.Hostname, server.InfraObjectName, "Server Monitor");
                //throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                isconnect = false;
            }
            catch (SshTaskTimeoutException timeoutexe)
            {
                Logger.ErrorFormat("{0} : No Response From Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, timeoutexe.Message);

            }
            catch (SshException sshexc)
            {
                Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detatiled Exception : {4}", server.InfraObjectName, type, sshParams.Hostname, Environment.NewLine, sshexc.Message);

                // throw new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.GroupName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.InfraObjectName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                isconnect = false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("{0} : Error Occured while connecting Server:{1} {2} Detatiled Exception : {3}", server.InfraObjectName, sshParams.Hostname, Environment.NewLine, exc.Message);
                // throw;
                isconnect = false;
            }
            finally
            {
                if (ssh.Connected)
                {
                    ssh.Disconnect();
                }
            }    
            return isconnect;
        }

        //Suriya.B
        public bool _ConnectServerviaservice(Server server, string type)
        {
            //SSHConnFlag sshConnOpt = (SSHConnFlag)Enum.Parse(typeof(SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);
            bool isconnect = false;
            bool removeConn = false;
            bool passwd_exp = false;
            PSSHConnMutex.SSHServerInfo objSSHServer = null;
            string serverType = (type == Constants.UIConstants.PrimaryServer) ? "PR" : "DR";
            dynamic client = null;

            //if (serverType == "PR")
            //{
            objSSHServer = new PSSHConnMutex.SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);
            //}
            //else if (serverType == "DR")
            //{
            //    objSSHServer = new SSHServerInfo(server.DRIPAddress, server.DRUserName, server.DRPassword, (server.DRIsUseSShkeyAuth == 1), server.DRSSHKeyPath, server.DRSSHKeyPassword, server.DRPort);
            //}
            using (PSSHConnMutex.PSSHMutex psshMutex = new PSSHConnMutex.PSSHMutex())
            {
                try
                {
                    using (PSSHHelper psshHelper = new PSSHHelper())
                    {
                        client = psshHelper.CreateSSHHelperSession(objSSHServer);
                    }
                    //client = psshMutex.CreateSSHSession(objSSHServer);
                    //isconnect = true;
                    //Suriya.B
                    if (!passwd_exp)
                    {
                        string output = psshMutex.ExecuteOSCmmand(objSSHServer, "#", "sudo chage -l " + objSSHServer.SSHUser);
                        if (output.Contains("passwd: Authentication token manipulation error"))
                        {
                            Logger.ErrorFormat("Your password has expired.You must change your password now and login again!: {0} {1} for User: Detailed Exception : {2}", objSSHServer.SSHHost, objSSHServer.SSHUser, output);
                            isconnect = false;
                        }
                        else
                        {
                            Logger.InfoFormat("{0} :({1}) Connection Successfully established ", server.InfraObjectName, objSSHServer.SSHHost);
                            isconnect = true;
                        }
                    }
                    //  isconnect = true;
                }
                catch (Rebex.Net.SshException sshEx)
                {
                    if (type == "Primary Server")
                    {
                        ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                        if (sshEx.ProtocolCode == "13")
                        {
                            Logger.ErrorFormat("{0} : Authentication Failed when connecting PR Server: {1} {2} for User:  Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, server.PRUserName, Environment.NewLine, sshEx.Message);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.PRIPAddress + " for User: " + server.PRUserName, sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                        }
                        else if (sshEx.ProtocolCode == "10")
                        {
                            Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception : {4}", server.InfraObjectName, type, server.PRName, Environment.NewLine, sshEx);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " Server: " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")  is not reachable.", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                        }
                        else
                        {
                            Logger.ErrorFormat("{0} : Error Occured while connecting PR Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, Environment.NewLine, sshEx.Message);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " - Error while connecting host - " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.PRIPAddress);
                        }
                    }
                    else
                    {
                        //ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Down);
                        ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                        if (sshEx.ProtocolCode == "13")
                        {
                            Logger.ErrorFormat("{0} : Authentication Failed when connecting DR Server: {1} {2} for User: Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, server.PRUserName, Environment.NewLine, sshEx.Message);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.PRIPAddress + " for User: " + server.PRUserName, sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                        }
                        else if (sshEx.ProtocolCode == "10")
                        {
                            Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception: {4}", server.InfraObjectName, type, server.PRName, Environment.NewLine, sshEx);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " Server: " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")  is not reachable.", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                        }
                        else
                        {
                            Logger.ErrorFormat("{0} : Error Occured while connecting DR Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, Environment.NewLine, sshEx.Message);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " - Error while connecting host - " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.PRIPAddress);
                        }
                    }
                }
                catch (SshAuthenticationException authexc)
                {
                    Logger.ErrorFormat("{0} : Authentication Failed when connecting {4} Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, objSSHServer.SSHHost, Environment.NewLine, authexc.Message, serverType);
                    ExceptionManager.SendAlertsToUsers("Authentication Failed for host - " + objSSHServer.SSHHost, server.InfraObjectName, "Server Monitor");
                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + objSSHServer.SSHHost, authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                }
                catch (SshTaskTimeoutException timeoutexe)
                {
                    Logger.ErrorFormat("{0} : No Response From {4} Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, objSSHServer.SSHHost, Environment.NewLine, timeoutexe.Message, serverType);
                }
                catch (Jscape.Ssh.SshException sshexc)
                {
                    Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception : {4}", server.InfraObjectName, type, objSSHServer.SSHHost, Environment.NewLine, sshexc.Message);
                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, " InfraObject Name: " + server.InfraObjectName + " Server: " + objSSHServer.SSHHost + " is not reachable.", sshexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                }
                catch (Exception exc)
                {
                    if (type == "Primary Server")
                    {
                        ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                        Logger.ErrorFormat("{0} : Exception raised while connecting to {1} : {2} {3} Detailed Exception : {4}", server.InfraObjectName, type, server.PRIPAddress, Environment.NewLine, exc.Message);
                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "Error while connecting host - " + server.PRIPAddress, exc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.PRIPAddress);
                    }
                    else
                    {
                        ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                        Logger.ErrorFormat("{0} : Exception raised while connecting to {1} : {2} {3} Detailed Exception : {4}", server.InfraObjectName, type, server.PRIPAddress, Environment.NewLine, exc.Message);
                        ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "Error while connecting host - " + server.PRIPAddress, exc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.PRIPAddress);
                    }
                }
                finally
                {
                    using (PSSHHelper psshHelper = new PSSHHelper())
                    {
                        removeConn = psshHelper.DisposeSSHHelperSession(client);
                    }
                }
            }
            return isconnect;
        }


        public  bool Connect(string hostname, int port, string username, string password, string applicationName, string type)
        {

            bool isconnect = false;

            SshParameters sshParams = new SshParameters(hostname, port, username, password);


            var ssh = new Ssh(sshParams,SshUtil.GetSshConfig())
            {
                LicenseKey = LicenseKey
            };
            try
            {

                ssh.Connect();

                if (ssh.Connected)
                {
                    Logger.InfoFormat("{0} : {1}) Connection Successfully established ", hostname, type);

                    ssh.Disconnect();

                    isconnect = true;
                }
            }
            catch (SshAuthenticationException authexc)
            {
                Logger.ErrorFormat("{0} : Authentication Failed when connecting Server:{1} {2} Detailed Exception : {3}", applicationName, sshParams.Hostname, Environment.NewLine, authexc.Message);

                // throw new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc));
                isconnect = false;
            }
            catch (SshTaskTimeoutException timeoutexe)
            {
                Logger.ErrorFormat("No Response From Server:{0} {2} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, timeoutexe.Message);

            }
            catch (SshException sshexc)
            {
                Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception : {4}", applicationName, type, sshParams.Hostname, Environment.NewLine, sshexc.Message);

                // throw new BcmsException(BcmsExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc));
                isconnect = false;
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("Error Occurred while connecting Server:{0} {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, exc.Message);
                //throw new BcmsException(BcmsExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", exc);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", exc));
                isconnect = false;
            }
            finally
            {
                if (ssh.Connected)
                {
                    ssh.Disconnect();
                }
            }

            return isconnect;
        }

        public  string Connect(string hostname, int port, string username, string password)
        {
            bool isconnect = false;
            string connect = "";
            SshParameters sshParams = new SshParameters(hostname, port, username, password);

            var ssh = new Ssh(sshParams,SshUtil.GetSshConfig())
            {
                LicenseKey = LicenseKey
            };
            try
            {
                ssh.Connect();

                if (ssh.Connected)
                {
                    Logger.InfoFormat("{0} : Connection Successfully established ", hostname);

                    connect = "Connection Successfully established";

                    ssh.Disconnect();

                    isconnect = true;
                }
            }
            catch (SshAuthenticationException authexc)
            {
                Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, authexc.Message);

                //throw new CpException(CpExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc);
                connect += "Authentication Failed";
            }
            catch (SshTaskTimeoutException timeoutexe)
            {
                Logger.ErrorFormat("No Response From Server:{0} {2} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, timeoutexe.Message);
                connect += "No Response From Server";
            }
            catch (SshException sshexc)
            {
                Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, sshexc.Message);
                connect += "Host Not Reachable";
                //throw new CpException(CpExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc);
            }
            catch (Exception exc)
            {
                Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, exc.Message);
                //throw new CpException(CpExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", exc);
                connect += "Error Occured while Connecting";
            }
            finally
            {
                if (isconnect)
                {
                    ssh.Disconnect();
                }
            }

            return isconnect + ":" + connect;
        }

        //public static string Connect(Server server)
        //{
        //    bool isconnect = false;
        //    string connect = "";
        //    SshParameters sshParams = new SshParameters(server.PRIPAddress, server.PRPort, server.PRUserName, server.PRPassword);

        //    Ssh ssh = null;
        //    try
        //    {
        //        if (server.PRIsUseSShkeyAuth == 1)
        //        {
        //            { sshParams.SetPrivateKey(new FileInfo(server.PRSSHKeyPath), server.PRSSHKeyPassword); }
        //        }

        //        ssh = new Ssh(sshParams,SshUtil.GetSshConfig())
        //        {
        //            LicenseKey = LicenseKey
        //        };


        //        ssh.Connect();

        //        if (ssh.Connected)
        //        {
        //            Logger.InfoFormat("{0} :({1}) Connection Successfully established ", server.InfraObjectName, sshParams.Hostname);

        //            ssh.Disconnect();

        //            isconnect = true;
        //        }
        //    }

        //    catch (SshAuthenticationException authexc)
        //    {
        //        Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, authexc.Message);

        //        //throw new CpException(CpExceptionType.AuthenticationFailed, "Authentication Failed for host - " + sshParams.Hostname, authexc);
        //        connect += "Authentication Failed";
        //    }
        //    catch (SshTaskTimeoutException timeoutexe)
        //    {
        //        Logger.ErrorFormat("No Response From Server:{0} {2} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, timeoutexe.Message);
        //        connect += "No Response From Server";
        //    }
        //    catch (SshException sshexc)
        //    {
        //        Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, sshexc.Message);
        //        connect += "Host Not Reachable";
        //        //throw new CpException(CpExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", sshexc);
        //    }
        //    catch (Exception exc)
        //    {
        //        Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", sshParams.Hostname, Environment.NewLine, exc.Message);
        //        //throw new CpException(CpExceptionType.NRHostNotReachable, "Application Name : " + applicationName + " Server : " + sshParams.Hostname + " is not reachable.", exc);
        //        connect += "Error Occured while Connecting";
        //    }
        //    finally
        //    {
        //        if (isconnect)
        //        {
        //            ssh.Disconnect();
        //        }
        //    }

        //    return isconnect + ":" + connect;
        //}


        public  bool Connect(Server server, string type)
         {
              try
            {
            //var businessInfo = BusinessServiceDataAccess.GetById(server.BusinessServiceId);
            bool isconnect = false;

            bool removeConn = false;
            PSSHConnMutex.SSHServerInfo objSSHServer = null;
            string serverType = (type == Constants.UIConstants.PrimaryServer) ? "PR" : "DR";
            dynamic client = null;

            if (serverType == "PR")
            {
                objSSHServer = new PSSHConnMutex.SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);
            }
            else if (serverType == "DR")
            {
                objSSHServer = new PSSHConnMutex.SSHServerInfo(server.DRIPAddress, server.DRUserName, server.DRPassword, (server.DRIsUseSShkeyAuth == 1), server.DRSSHKeyPath, server.DRSSHKeyPassword, server.DRPort);
            }
          
                using (PSSHConnMutex.PSSHMutex psshMutex = new PSSHConnMutex.PSSHMutex())
                {
                    try
                    {
                        client = psshMutex.CreateSSHSession(objSSHServer);
                        isconnect = true;
                    }
                    catch (SshAuthenticationException authexc)
                    {
                        Logger.ErrorFormat("{0} : Authentication Failed when connecting Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, objSSHServer.SSHHost, Environment.NewLine, authexc.Message);
                        ExceptionManager.SendAlertsToUsers("Authentication Failed for host - " + objSSHServer.SSHHost, server.InfraObjectName, "Server Monitor");

                        if (type.Equals("Primary Server"))
                        {
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR = false;
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR1 = true;

                            // ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + objSSHServer.SSHHost + " on host (" + server.PRHostName + ")" + " for BS : (" + businessInfo.Name + ")", authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                            isconnect = false;
                        }
                        else if (type.Equals("DR Server"))
                        {
                            ////Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR = false;
                            ////Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR1 = true;

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + objSSHServer.SSHHost + " on host (" + server.DRHostName + ")" + " for BS : (" + businessInfo.Name + ")", authexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                            isconnect = false;
                        }
                    }
                    catch (Rebex.Net.SshException sshEx)
                    {
                        if (type == "Primary Server")
                        {
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR = false;
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR1 = true;

                            ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                            if (sshEx.ProtocolCode == "13")
                            {
                                Logger.ErrorFormat("{0} : Authentication Failed when connecting PR Server: {1} {2} for User:  Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, server.PRUserName, Environment.NewLine, sshEx.Message);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.PRIPAddress + " for User: " + server.PRUserName, sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            }
                            else if (sshEx.ProtocolCode == "10")
                            {
                                Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception : {4}", server.InfraObjectName, type, server.PRName, Environment.NewLine, sshEx);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " Server: " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")  is not reachable.", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            }
                            else
                            {
                                Logger.ErrorFormat("{0} : Error Occured while connecting PR Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, server.PRIPAddress, Environment.NewLine, sshEx.Message);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " - Error while connecting host - " + server.PRName + "  " + type + ": (" + server.PRIPAddress + ")", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.PRIPAddress);
                            }
                        }
                        else
                        {
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR = false;
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR1 = true;

                            ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Down);
                            if (sshEx.ProtocolCode == "13")
                            {
                                Logger.ErrorFormat("{0} : Authentication Failed when connecting PR Server: {1} {2} for User:  Detailed Exception : {3}", server.InfraObjectName, server.DRIPAddress, server.DRUserName, Environment.NewLine, sshEx.Message);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host - " + server.DRIPAddress + " for User: " + server.DRUserName, sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            }
                            else if (sshEx.ProtocolCode == "10")
                            {
                                Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception: {4}", server.InfraObjectName, type, server.DRName, Environment.NewLine, sshEx);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " Server: " + server.DRName + "  " + type + ": (" + server.DRIPAddress + ")  is not reachable.", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            }
                            else
                            {
                                Logger.ErrorFormat("{0} : Error Occured while connecting DR Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, server.DRIPAddress, Environment.NewLine, sshEx.Message);
                                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name: " + server.InfraObjectName + " - Error while connecting host - " + server.DRName + "  " + type + ": (" + server.DRIPAddress + ")", sshEx), "MonitorServer", server.InfraObjectId, server.InfraObjectName, server.DRIPAddress);
                            }
                        }
                    }
                    catch (SshTaskTimeoutException timeoutexe)
                    {
                        Logger.ErrorFormat("{0} : No Response From {4} Server: {1} {2} Detailed Exception : {3}", server.InfraObjectName, objSSHServer.SSHHost, Environment.NewLine, timeoutexe.Message, serverType);
                    }
                    catch (SshException sshexc)
                    {
                        Logger.ErrorFormat("{0} : {1} {2} is Not Reachable {3} Detailed Exception : {4}", server.InfraObjectName, type, objSSHServer.SSHHost, Environment.NewLine, sshexc.Message);

                        if (type.Equals("Primary Server"))
                        {
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR = false;
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_PR1 = true;

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.InfraObjectName + " PR Server: " + objSSHServer.SSHHost + " is not reachable on host (" + server.PRHostName + ") for BS : (" + businessInfo.Name + ")", sshexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                            isconnect = false;
                        }
                        else if (type.Equals("DR Server"))
                        {
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR1 = false;
                            //Bcms.Core.Client.OracleDatabaseMonitor.ODG_variable_DR = true;

                            // ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, " InfraObject Name: " + server.InfraObjectName + " DR Server: " + objSSHServer.SSHHost + " is not reachable on host (" + server.DRHostName + ") for BS : (" + businessInfo.Name + ")", sshexc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                            isconnect = false;
                        }
                    }
                    catch (Exception exc)
                    {
                        Logger.ErrorFormat("{0} : Error Occured while connecting Server: {1} {2} Detailed Exception: {3}", server.InfraObjectName, objSSHServer.SSHHost, Environment.NewLine, exc.Message);
                        ServerDataAccess.UpdateServerByStatus(serverType == "PR" ? Convert.ToInt32(server.PRId) : Convert.ToInt32(server.DRId), ServerStatus.Down);
                        // ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "Error while connecting host - " + objSSHServer.SSHHost + " on host (" + server.DRHostName + ") for BS : (" + businessInfo.Name + ")", exc), "MonitorServer", server.InfraObjectId, server.InfraObjectName, objSSHServer.SSHHost);
                    }
                    finally
                    {
                        // it will connect target server and clear all zombie process present there
                        // ConnectServer method in PSSHConnMutex library will internally call another method - CheckZombieAndClear(...)
                        try
                        {
                            removeConn = psshMutex.ConnectServer(objSSHServer);
                        }
                        catch (Exception connectEx)
                        {
                            Logger.ErrorFormat("Error connecting to server for cleanup {0}: {1}", objSSHServer?.SSHHost ?? "Unknown", connectEx.Message);
                        }

                        // Use helper method to safely dispose SSH session with proper null checking
                        removeConn = SafeDisconnectSSHSession(psshMutex, client, objSSHServer?.SSHHost);

                        //client.Process();
                        //client.SendToServer("exit" + "\r\n");
                        //Thread.Sleep(1000);

                        // removeConn = PSSH.DisconnectAndRemoveSSHSession(client);
                    }
                }
                return isconnect;
            }
            catch (Exception exc)
            {
                return false;
            }

        }
        public  bool ConnectWindows(string hostname, string username, string password)
        {
            try
            {
                string status = PWindows.WinRemote.CheckWMIConnection(new PWindows.WindowsInstance(hostname, username, password));
                if (!string.IsNullOrEmpty(status))
                {
                    var output = status.ToString().Split(':');
                    if (output.Length > 0)
                    {

                        return output[0] == "TRUE" ? true : false;

                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                Logger.ErrorFormat("Error in ConnectWindows method " + ex.Message);
                return false;
            }
        }

        public  bool ConnectWindows(Server server, string hostname, string username, string password, string type)
        {
            try
            {
                string status = PWindows.WinRemote.CheckWMIConnection(new PWindows.WindowsInstance(hostname, username, password));

                Logger.ErrorFormat("Check Status MSExchange :" + status + "===");

                if (!string.IsNullOrEmpty(status))
                {
                    var output = status.ToString().Split(':');
                    if (output.Length > 0)
                    {
                        var IsVerified = output[0] == "TRUE" ? true : false;
                        if (!IsVerified)
                        {
                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.InfraObjectName + " Server : " + hostname + " is not reachable."), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host: " + hostname), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            if (type == "PR")
                            {
                                if (server.PRStatus == "Pending")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                                }
                                if (server.PRStatus == "Up")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                                }
                                if (server.PRStatus == "Down")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Down);
                                }
                            }
                            if (type == "DR")
                            {
                                if (server.DRStatus == "Pending")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Down);
                                }
                                if (server.DRStatus == "Up")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Down);
                                }
                                if (server.DRStatus == "Down")
                                {
                                    ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Down);
                                }
                            }
                        }
                        if (IsVerified)
                        {
                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable, "InfraObject Name : " + server.InfraObjectName + " Server (" + hostname + ") is connected."), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                            if (type == "PR")
                            {
                                //if (server.PRStatus == "Pending")
                                //{
                                //    ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Up);
                                //}

                                if (server.PRStatus != "UP")
                                    ServerDataAccess.UpdateServerByStatus(server.PRId, ServerStatus.Up);


                            }
                            if (type == "DR")
                            {
                                //if (server.DRStatus == "Pending")
                                //{
                                //    ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Up);
                                //}
                                if (server.DRStatus != "UP")
                                    ServerDataAccess.UpdateServerByStatus(server.DRId, ServerStatus.Up);

                            }
                        }
                        return IsVerified;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                Logger.ErrorFormat("Error in ConnectWindows method " + ex.Message);
                ExceptionManager.Manage(new BcmsException(BcmsExceptionType.AuthenticationFailed, "Authentication Failed for host: " + hostname), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + server.InfraObjectName + " Server : " + username + " is not reachable.", ex), "MonitorServer", server.InfraObjectId, server.InfraObjectName);
                return false;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        public bool TestConnect1(PSSHConnMutex.SSHServerInfo val)
        {
            {
                bool isconnect = false;
                bool passwd_exp = false;

                bool removeConn = false;
               // PSSHConnMutex.SSHServerInfo objSSHServer = null;
                dynamic client = null;

                //objSSHServer = new SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);
                using (PSSHConnMutex.PSSHMutex psshMutex = new PSSHConnMutex.PSSHMutex())
                {
                    try
                    {


                        client = psshMutex.CreateSSHSession(val);
                        if (!passwd_exp)
                        {
                            string output = psshMutex.ExecuteOSCmmand(val, "#", "sudo chage -l " + val.SSHUser);
                            if (output.Contains("passwd: Authentication token manipulation error"))
                            {
                                Logger.ErrorFormat("Your password has expired.You must change your password now and login again!: {0} {1} for User: Detailed Exception : {2}", val.SSHHost, val.SSHUser, output);

                                isconnect = false;
                            }
                            else
                            {
                                Logger.InfoFormat("{0} :({1}) Connection Successfully established ", val.SSHHost);
                                isconnect = true;
                            }
                        }
                    }
                    catch (Rebex.Net.SshException sshEx)
                    {
                        string msg = sshEx.InnerException != null ? sshEx.InnerException.Message : "";
                        if (sshEx.ProtocolCode == "13")
                        {
                            Logger.ErrorFormat("Authentication Failed when connecting Server: {0} {1} for User:  Detailed Exception : {2}", val.SSHHost, val.SSHUser, Environment.NewLine, sshEx.Message + msg);
                            //connect += "Authentication Failed";
                        }
                        else if (sshEx.ProtocolCode == "10")
                        {
                            Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, sshEx.Message + msg);
                            //connect += "Host Not Reachable";
                        }
                        else
                        {
                            Logger.ErrorFormat("Error Occured while connecting PR Server: {0} {1} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, sshEx.Message + msg);
                            //  connect += "Error Occured while Connecting";
                        }
                    }
                    catch (SshAuthenticationException authexc)
                    {
                        Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, authexc.Message);
                        // connect += "Authentication Failed";
                    }
                    catch (SshTaskTimeoutException timeoutexe)
                    {
                        Logger.ErrorFormat("No Response From Server: {0} {2} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, timeoutexe.Message);
                        // connect += "No Response From Server";
                    }
                    catch (SshException sshexc)
                    {
                        Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, sshexc.Message);
                        // connect += "Host Not Reachable";
                    }
                    catch (Exception exc)
                    {
                        string msg = exc.InnerException != null ? exc.InnerException.Message : "";

                        // Check for socket error 10055 (No buffer space available)
                        if (IsSocketError10055(exc))
                        {
                            Logger.ErrorFormat("Socket error 10055 (No buffer space available) while connecting to Server:{0} {1} This indicates network resource exhaustion. Detailed Exception : {2}", val.SSHHost, Environment.NewLine, exc.Message + " " + msg);
                        }
                        else
                        {
                            Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", val.SSHHost, Environment.NewLine, exc.Message);
                        }
                        // connect += "Error Occured while Connecting";
                    }
                    finally
                    {
                        // Use helper method to safely dispose SSH session with proper null checking
                        removeConn = SafeDisconnectSSHSession(psshMutex, client, val?.SSHHost);
                    }
                }
                return isconnect;
            }
        }

        //internal bool CheckPasswordExpired(SSHInfo wFSSHServer)
        //{
        //    {

        //        bool isconnect = false;
        //        string connect = "";
        //        bool removeConn = false;
        //        PSSHConn.SSHServerInfo objSSHServer = null;
        //        dynamic client = null;

        //        objSSHServer = new SSHServerInfo(server.PRIPAddress, server.PRUserName, server.PRPassword, (server.PRIsUseSShkeyAuth == 1), server.PRSSHKeyPath, server.PRSSHKeyPassword, server.PRPort);

        //        try
        //        {


        //            client = PSSH.CreateSSHSession(objSSHServer);
        //            if (!passwd_exp)
        //            {
        //                string output = PSSH.ExecuteOSCmmand(objSSHServer, "#", "sudo chage -l " + objSSHServer.SSHUser);
        //                if (output.Contains("passwd: Authentication token manipulation error"))
        //                {
        //                    Logger.ErrorFormat("Your password has expired.You must change your password now and login again!: {0} {1} for User: Detailed Exception : {2}", objSSHServer.SSHHost, objSSHServer.SSHUser, output);
        //                    connect += "Password Expired";
        //                    isconnect = false;
        //                }
        //                else
        //                {
        //                    Logger.InfoFormat("{0} :({1}) Connection Successfully established ", server.InfraObjectName, objSSHServer.SSHHost);
        //                    isconnect = true;
        //                }
        //            }
        //        }
        //        catch (Rebex.Net.SshException sshEx)
        //        {
        //            string msg = sshEx.InnerException != null ? sshEx.InnerException.Message : "";
        //            if (sshEx.ProtocolCode == "13")
        //            {
        //                Logger.ErrorFormat("Authentication Failed when connecting Server: {0} {1} for User:  Detailed Exception : {2}", objSSHServer.SSHHost, objSSHServer.SSHUser, Environment.NewLine, sshEx.Message + msg);
        //                connect += "Authentication Failed";
        //            }
        //            else if (sshEx.ProtocolCode == "10")
        //            {
        //                Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
        //                connect += "Host Not Reachable";
        //            }
        //            else
        //            {
        //                Logger.ErrorFormat("Error Occured while connecting PR Server: {0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshEx.Message + msg);
        //                connect += "Error Occured while Connecting";
        //            }
        //        }
        //        catch (SshAuthenticationException authexc)
        //        {
        //            Logger.ErrorFormat("Authentication Failed when connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, authexc.Message);
        //            connect += "Authentication Failed";
        //        }
        //        catch (SshTaskTimeoutException timeoutexe)
        //        {
        //            Logger.ErrorFormat("No Response From Server: {0} {2} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, timeoutexe.Message);
        //            connect += "No Response From Server";
        //        }
        //        catch (SshException sshexc)
        //        {
        //            Logger.ErrorFormat("{0} is Not Reachable {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, sshexc.Message);
        //            connect += "Host Not Reachable";
        //        }
        //        catch (Exception exc)
        //        {
        //            string msg = exc.InnerException != null ? exc.InnerException.Message : "";
        //            Logger.ErrorFormat("Error Occured while connecting Server:{0} {1} Detailed Exception : {2}", objSSHServer.SSHHost, Environment.NewLine, exc.Message);
        //            connect += "Error Occured while Connecting";
        //        }
        //        finally
        //        {
        //            removeConn = PSSH.DisconnectAndRemoveSSHSession(client);
        //        }

        //        return isconnect + ":" + connect;
        //    }
        //}
    }

    public class PSSHHelper : IDisposable
    {
        #region Variables

        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsPSSHHelperLog");
        public bool _isDisposed;
        public static string shellPrompt = "\\$|#|]|>";
        public static int expectTime = 120000;
        public static Regex _reg = new Regex(shellPrompt, RegexOptions.RightToLeft);
        private readonly object sshLock = new object();
        public static PSSHConnMutex.SSHConnFlag sshConnOpt = (PSSHConnMutex.SSHConnFlag)Enum.Parse(typeof(PSSHConnMutex.SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);
        public static string IpAddress = "";

        #endregion Variables

        private Jscape.Ssh.SshParameters BindSSHParameters(PSSHConnMutex.SSHServerInfo _remoteServer)
        {
            string keyPass = string.Empty;
            Jscape.Ssh.SshParameters _sp = new Jscape.Ssh.SshParameters();
            _sp.Hostname = _remoteServer.SSHHost;
            _sp.Username = _remoteServer.SSHUser;
            _sp.Port = _remoteServer.SSHPort;
            if (_remoteServer.AuthKey)
            {
                FileInfo _pvtKey = new FileInfo(_remoteServer.SSHKeyPath);
                if (!string.IsNullOrEmpty(_remoteServer.SSHKeyPassword))
                {
                    keyPass = _remoteServer.SSHKeyPassword;
                }
                _sp.SetPrivateKey(_pvtKey, keyPass);
                Enginelog.Info("PSSHHelper: JSSH Parameters with SSHKey created sucessfully..." + _remoteServer.SSHHost);
            }
            else
            {
                _sp.Password = _remoteServer.SSHPass;
                Enginelog.Info("PSSHHelper: JSSH Parameters created sucessfully..." + _remoteServer.SSHHost);
            }

            return _sp;
        }

        public dynamic CreateSSHHelperSession(PSSHConnMutex.SSHServerInfo objSshInfo)
        {

            string strOut = string.Empty;
            dynamic client = null;
            VirtualTerminal terminal = null;
            if (objSshInfo != null)
            {
                try
                {
                    switch (sshConnOpt)
                    {
                        case PSSHConnMutex.SSHConnFlag.JSession:
                            client = new Jscape.Ssh.Ssh();
                            break;
                        case PSSHConnMutex.SSHConnFlag.RSession:
                            client = new Rebex.Net.Ssh();
                            Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                            client.Timeout = 3600000;
                            break;
                    }

                    Enginelog.Info("PSSHHelper: Creating SSH Session... " + objSshInfo.SSHHost);
                    if (sshConnOpt == PSSHConnMutex.SSHConnFlag.JSession)
                    {

                        client = new Jscape.Ssh.SshSession(BindSSHParameters(objSshInfo), SshUtil.GetSshConfig());
                        client.SetShellPrompt(shellPrompt, true);

                        client.LicenseKey =
                            "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                        client.Connect(300000);
                        Enginelog.Info("PSSHHelper: JSession created sucessfully..." + objSshInfo.SSHHost);
                    }
                    else if (sshConnOpt == PSSHConnMutex.SSHConnFlag.RSession)
                    {
                        Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                        client.Timeout = 3600000;
                        if (objSshInfo.SSHPort != 0)
                        {
                            client.Connect(objSshInfo.SSHHost, objSshInfo.SSHPort);
                        }
                        else
                        {
                            client.Connect(objSshInfo.SSHHost);
                        }

                        if (objSshInfo.AuthKey)
                        {
                            Rebex.Net.SshPrivateKey _pvtkey1 = new Rebex.Net.SshPrivateKey(objSshInfo.SSHKeyPath, objSshInfo.SSHKeyPassword);
                            client.Login(objSshInfo.SSHUser, _pvtkey1);
                        }
                        else
                        {
                            client.Login(objSshInfo.SSHUser, objSshInfo.SSHPass);
                        }

                        IpAddress = client.LocalEndPoint.Address.ToString();
                        terminal = new VirtualTerminal(400, 800);
                        terminal.Bind(client);
                        terminal.Expect(_reg, 5000);
                        string con_op = terminal.ReceivedData;
                        if (con_op.ToLower().Contains("your password has expired."))
                        {
                            Enginelog.Info("PSSH: RSession connection Check" + con_op);

                            throw new Exception("Server Password is Expired...");
                        }
                        client = terminal;
                        Enginelog.Info("PSSHHelper: RSession created sucessfully..." + objSshInfo.SSHHost);
                    }
                }
                catch (Jscape.Ssh.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("JPSSHHelper: INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + " : Messege: " + SshEx.InnerException.Message);

                    Enginelog.Error("JPSSHHelper: SSHException: While connecting to server: " + objSshInfo.SSHHost + " : Messege: " + SshEx.Message);

                    throw SshEx;
                }
                catch (Rebex.Net.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("RPSSHHelper: INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + SshEx.InnerException.Message);

                    Enginelog.Error("RPSSHHelper: SSHException: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + SshEx.Message);

                    throw SshEx;
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        Enginelog.Error("PSSHHelper: INNER_Exception: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + exc.InnerException.Message);

                    Enginelog.Error("PSSHHelper: Exception: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + exc.Message);

                    throw exc;
                }
            }
            else
            {
                Enginelog.Error("PSSHHelper: SSHSERVER NULL While Create SSH Session for SSHFlag: " + sshConnOpt.ToString());
            }
            return client;

        }

        public bool DisposeSSHHelperSession(dynamic client)
        {
            bool result = false;

            if (client != null)
            {
                try
                {
                    if (sshConnOpt == PSSHConnMutex.SSHConnFlag.RSession)
                    {
                        // client.SendToServer("exit" + "\n");
                        if (!client.IsDisposed)
                        {
                            if (client != null)
                            {
                                client.Unbind();
                            }
                            client.Dispose();
                        }
                    }
                    else
                    {
                        client.Send("exit");
                    }
                    result = true;
                }
                catch (Jscape.Ssh.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("PSSHHelper: INNER_SSHException: on DisposeSSHHelperSession: Jscape SshEx InnerException: " + SshEx.InnerException.Message);

                    Enginelog.Error("PSSHHelper: SSHException: on DisposeSSHHelperSession: Jscape SshEx Exception: " + SshEx.Message);
                }
                catch (Rebex.Net.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("PSSHHelper: INNER_SSHException: on DisposeSSHHelperSession: Rebex SshEx InnerException: " + SshEx.InnerException.Message);

                    Enginelog.Error("PSSHHelper: SSHException: on DisposeSSHHelperSession: Rebex SshEx Exception: " + SshEx.Message);
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        Enginelog.Error("PSSHHelper: INNER_Exception: on DisposeSSHHelperSession:  InnerException: " + exc.InnerException.Message);

                    Enginelog.Error("PSSHHelper: Exception: on DisposeSSHHelperSession: Exception: " + exc.Message);
                }
            }
            else
            {
                Enginelog.Error("PSSHHelper: client is null on DisposeSSHHelperSession for: " + sshConnOpt.ToString());
            }
            return result;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~PSSHHelper()
        {
            Dispose(false);
        }

        /// <summary>
        /// Helper method to check if an exception is a socket error 10055 (No buffer space available)
        /// </summary>
        /// <param name="exception">The exception to check</param>
        /// <returns>True if it's a socket error 10055, false otherwise</returns>
        private static bool IsSocketError10055(Exception exception)
        {
            if (exception == null) return false;

            string message = exception.Message ?? "";
            string innerMessage = exception.InnerException?.Message ?? "";

            return message.Contains("Socket error 10055") ||
                   message.Contains("No buffer space available") ||
                   innerMessage.Contains("Socket error 10055") ||
                   innerMessage.Contains("No buffer space available");
        }

        /// <summary>
        /// Helper method to safely dispose SSH session with proper null checking and error handling
        /// </summary>
        /// <param name="psshMutex">The PSSHMutex instance</param>
        /// <param name="client">The SSH client to dispose</param>
        /// <param name="hostName">The hostname for logging purposes</param>
        /// <returns>True if disposal was successful, false otherwise</returns>
        private static bool SafeDisconnectSSHSession(PSSHConnMutex.PSSHMutex psshMutex, dynamic client, string hostName)
        {
            if (client == null)
            {
                Logger.InfoFormat("SSH client was null for {0}, no cleanup needed", hostName ?? "Unknown");
                return true;
            }

            try
            {
                return psshMutex.DisconnectAndRemoveSSHSession(client);
            }
            catch (Exception disposeEx)
            {
                Logger.ErrorFormat("Error disposing SSH session for {0}: {1}", hostName ?? "Unknown", disposeEx.Message);
                return false;
            }
        }

    }
}