﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "BusinessServiceRPOInfo", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessServiceRPOInfo : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public int BusinessFunctionId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string CurrentRPO { get; set; }


        #endregion
    }
}
