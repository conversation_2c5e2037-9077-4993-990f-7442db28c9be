﻿using System;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using log4net;

namespace Bcms.Core.Job
{
    public class PostgreMonitoringJob : IJob
    {
        public const string InfraObject = "infraObject";

        private readonly ILog _logger = LogManager.GetLogger(typeof(PostgreMonitoringJob));

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var infraobject = data.Get(InfraObject) as InfraObject;
            if (infraobject != null)
            {

                InfraObject infraObjectItem = InfraObjectDataAccess.GetInfraObjectById(infraobject.Id);


                if (infraObjectItem != null)
                {
                    if (infraObjectItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        return;
                    }

                   PostgreLogDetails(infraObjectItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " Infra Object  Having null values While Perform Monitor Server Job ");
            }
        }


        public void PostgreLogDetails(InfraObject infraobject)
        {
            using (var client = new BcmsClient(infraobject))
            {
                _logger.Info("===== EnterPriseDB Monitoring started ======");

                client.PostgreSqlMonitoring();
                client.PostgredbReplication();

                _logger.Info("=====EnterPriseDB Monitoring completed ======");
            }
        }
    }
}
