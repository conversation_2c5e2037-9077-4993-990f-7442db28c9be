﻿using System;
using System.Runtime.Serialization;
using BCMS.Common;
using Bcms.Common;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ReplicationBase", Namespace = "http://www.BCMS.com/types")]

    public class ReplicationBase : BaseEntity
    {
        #region Member Variables

        DataGuard _dataGuard = new DataGuard();

        GlobalMirror _globalMirror = new GlobalMirror();

        SnapMirror _snapMirror = new SnapMirror();

        SCR _scr = new SCR();

        FastCopy _fastCopy = new FastCopy();

        EMCSRDF _emcsrdf = new EMCSRDF();
        
        HitachiUrReplication _hitachiUrReplication =new HitachiUrReplication();
   

        #endregion

        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public ReplicationType Type
        {
            get;
            set;
        }


        public int SiteId
        {
            get;
            set;
        }


        [DataMember]
        public DataGuard DataGuard
        {
            get
            {
                return _dataGuard;
            }
            set
            {
                _dataGuard = value;
            }
        }

        [DataMember]
        public GlobalMirror GlobalMirror
        {
            get
            {
                return _globalMirror;
            }
            set
            {
                _globalMirror = value;
            }
        }

        [DataMember]
        public SnapMirror SnapMirror
        {
            get
            {
                return _snapMirror;
            }
            set
            {
                _snapMirror = value;
            }
        }

        [DataMember]
        public SCR SCR
        {
            get
            {
                return _scr;
            }
            set
            {
                _scr = value;
            }
        }

        [DataMember]
        public FastCopy FastCopy
        {
            get
            {
                return _fastCopy;
            }
            set
            {
                _fastCopy = value;
            }
        }

        [DataMember]
        public EMCSRDF EMCSRDF
        {
            get
            {
                return _emcsrdf;
            }
            set
            {
                _emcsrdf = value;
            }
        }
        [DataMember]
        public HitachiUrReplication HitachiUrReplication
        {
            get
            {
                return _hitachiUrReplication;
            }
            set
            {
                _hitachiUrReplication = value;
            }
        }
        #endregion

        #region Constructor
        public ReplicationBase()
            : base()
        {

        }
        #endregion
         
    }
}