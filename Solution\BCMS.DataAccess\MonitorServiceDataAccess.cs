﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class MonitorServiceDataAccess : BaseDataAccess
    {
        /// <Modified> Mridul - CP 4.0v - 24-06-2014 for MonitorService</Modified>
        public static bool AddMonitorServiceLogs(MonitorService oracleData)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("MonitorServiceLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, oracleData.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring+"iServiceId", DbType.Int32, oracleData.ServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, oracleData.Type);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowId", DbType.Int32, oracleData.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, oracleData.Status);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Monitor Service Logs InfraObject Id - " + oracleData.InfraobjectId, exc);

            }
           
            return false;
        }       
 
       /// <Modified> Mridul - CP 4.0v - 30-06-2014 for MonitorService</Modified>
        public static MonitorService GetMonitorServiceByInfraobjectId(int infraobjectId)
        {
            var dgStatus = new MonitorService();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("MONITORSERVICE_GETBYINFRAID"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataguardReader = Database.ExecuteReader(cmd))
                    {
                        while (myDataguardReader.Read())
                        {

                            dgStatus.Id = Convert.IsDBNull(myDataguardReader["Id"]) ? 0 : Convert.ToInt32(myDataguardReader["Id"]);
                            dgStatus.InfraobjectId = Convert.IsDBNull(myDataguardReader["InfraobjectId"]) ? 0 : Convert.ToInt32(myDataguardReader["InfraobjectId"]);
                            dgStatus.ServerId = Convert.IsDBNull(myDataguardReader["ServerId"]) ? 0 : Convert.ToInt32(myDataguardReader["ServerId"]);
                            dgStatus.Type = Convert.IsDBNull(myDataguardReader["Type"]) ? 0 : Convert.ToInt32(myDataguardReader["Type"]);
                            dgStatus.WorkflowId = Convert.IsDBNull(myDataguardReader["WorkflowId"]) ? 0 : Convert.ToInt32(myDataguardReader["WorkflowId"]);
                            dgStatus.ServicePath = Convert.IsDBNull(myDataguardReader["ServicePath"]) ? string.Empty : Convert.ToString(myDataguardReader["ServicePath"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while GetMonitorServiceByInfraobjectId Monitor Status Details by infraobjectId - " + infraobjectId, exc);
            }

            return dgStatus;
        }

        public static IList<MonitorService> GetAllMonitorService()
        {
            var monitorServices = new List<MonitorService>();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("MonitorService_GetAll"))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataguardReader = Database.ExecuteReader(cmd))
                    {
                        while (myDataguardReader.Read())
                        {
                            var dgStatus = new MonitorService();

                            dgStatus.Id = Convert.IsDBNull(myDataguardReader["Id"]) ? 0: Convert.ToInt32(myDataguardReader["Id"]);
                            dgStatus.InfraobjectId = Convert.IsDBNull(myDataguardReader["InfraobjectId"]) ? 0 : Convert.ToInt32(myDataguardReader["InfraobjectId"]);
                            dgStatus.ServerId = Convert.IsDBNull(myDataguardReader["ServerId"]) ? 0 : Convert.ToInt32(myDataguardReader["ServerId"]);
                            dgStatus.Type = Convert.IsDBNull(myDataguardReader["Type"]) ? 0 : Convert.ToInt32(myDataguardReader["Type"]);
                            dgStatus.WorkflowId = Convert.IsDBNull(myDataguardReader["WorkflowId"]) ? 0 : Convert.ToInt32(myDataguardReader["WorkflowId"]);
                            dgStatus.ServicePath = Convert.IsDBNull(myDataguardReader["ServicePath"]) ? string.Empty : Convert.ToString(myDataguardReader["ServicePath"]);
                            monitorServices.Add(dgStatus);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get  all monitor services Details ", exc);
            }

            return monitorServices;
        }


    }
}