﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Base;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;


namespace Bcms.DataAccess
{
    public class GroupDatabaseNodesDataAccess : BaseDataAccess
    {      

        #region  Methods

        public static GroupDatabaseNodes GroupDatabaseNodesGetById(int id)
       {
           GroupDatabaseNodes groupDatabaseNodes = new GroupDatabaseNodes();
            try
            {
               // var db = DatabaseFactory.CreateDatabase();


                const string sp = "InfraobjectDbNode_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("InfraDBNode_Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            groupDatabaseNodes.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            groupDatabaseNodes.GroupId = Convert.IsDBNull(reader["InfraobjectId"])
                                                             ? 0
                                                             : Convert.ToInt32(reader["InfraobjectId"]);
                            ;
                            groupDatabaseNodes.PrServerId = Convert.IsDBNull(reader["PrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["PrServerId"]);
                            ;
                            groupDatabaseNodes.DrServerId = Convert.IsDBNull(reader["DrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["DrServerId"]);
                            ;
                            groupDatabaseNodes.PrDbseId = Convert.IsDBNull(reader["PrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrDbseId"]);
                            ;
                            groupDatabaseNodes.DrDbseId = Convert.IsDBNull(reader["DrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrDbseId"]);
                            ;
                            groupDatabaseNodes.PrNodeId = Convert.IsDBNull(reader["PrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrNodeId"]);
                            ;
                            groupDatabaseNodes.DrNodeId = Convert.IsDBNull(reader["DrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrNodeId"]);
                            ;
                            //groupDatabaseNodes.IsActive = Convert.ToBoolean(reader["IsActive"].ToString());
                        }
                    }

                }
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,  "Error In DAL While Executing Function Signature GroupDatabaseNodesGetById(" + id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return groupDatabaseNodes;
        }

        public static IList<GroupDatabaseNodes> GroupDatabaseNodesGetByGroupId(int groupId)
        {
            IList<GroupDatabaseNodes> groupDatabaseNodeslist = new List<GroupDatabaseNodes>();
            try
            {

                const string sp = "INFRAOBJDBNODE_GETINFRAID";
                //var db = DatabaseFactory.CreateDatabase();
              

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraobjectId", DbType.Int32, groupId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("InfraDBNode_Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        { 
                            GroupDatabaseNodes groupDatabaseNodes = new GroupDatabaseNodes();

                            groupDatabaseNodes.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            groupDatabaseNodes.GroupId = Convert.IsDBNull(reader["InfraobjectId"])
                                                             ? 0
                                                             : Convert.ToInt32(reader["InfraobjectId"]);
                            ;
                            groupDatabaseNodes.PrServerId = Convert.IsDBNull(reader["PrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["PrServerId"]);
                            ;
                            groupDatabaseNodes.DrServerId = Convert.IsDBNull(reader["DrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["DrServerId"]);
                            ;
                            groupDatabaseNodes.PrDbseId = Convert.IsDBNull(reader["PrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrDbseId"]);
                            ;
                            groupDatabaseNodes.DrDbseId = Convert.IsDBNull(reader["DrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrDbseId"]);
                            ;
                            groupDatabaseNodes.PrNodeId = Convert.IsDBNull(reader["PrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrNodeId"]);
                            ;
                            groupDatabaseNodes.DrNodeId = Convert.IsDBNull(reader["DrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrNodeId"]);
                            ;
                           
                            groupDatabaseNodeslist.Add(groupDatabaseNodes);
                        }
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,  "Error In DAL While Executing Function Signature IGroupDatabaseNodesDataAccess.GetByGroupId(" + groupId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return groupDatabaseNodeslist;
        }

        public static IList<GroupDatabaseNodes> GroupDatabaseNodesGetByGroupIdandDBId(int groupId, int PRDBId, int DRDBId)
        {
            IList<GroupDatabaseNodes> groupDatabaseNodeslist = new List<GroupDatabaseNodes>();
            try
            {
                const string sp = "GroupDbNode_GetGroupIdDBId";

                //var db = DatabaseFactory.CreateDatabase();
               

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"igroupId", DbType.Int32, groupId);
                    Database.AddInParameter(cmd, Dbstring+"iPRDBId", DbType.Int32, PRDBId);
                    Database.AddInParameter(cmd, Dbstring+"iDRDBId", DbType.Int32, DRDBId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            GroupDatabaseNodes groupDatabaseNodes = new GroupDatabaseNodes();
                            groupDatabaseNodes.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            groupDatabaseNodes.GroupId = Convert.IsDBNull(reader["GroupId"])
                                                             ? 0
                                                             : Convert.ToInt32(reader["GroupId"]);
                            ;
                            groupDatabaseNodes.PrServerId = Convert.IsDBNull(reader["PrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["PrServerId"]);
                            ;
                            groupDatabaseNodes.DrServerId = Convert.IsDBNull(reader["DrServerId"])
                                                                ? 0
                                                                : Convert.ToInt32(reader["DrServerId"]);
                            ;
                            groupDatabaseNodes.PrDbseId = Convert.IsDBNull(reader["PrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrDbseId"]);
                            ;
                            groupDatabaseNodes.DrDbseId = Convert.IsDBNull(reader["DrDbseId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrDbseId"]);
                            ;
                            groupDatabaseNodes.PrNodeId = Convert.IsDBNull(reader["PrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["PrNodeId"]);
                            ;
                            groupDatabaseNodes.DrNodeId = Convert.IsDBNull(reader["DrNodeId"])
                                                              ? 0
                                                              : Convert.ToInt32(reader["DrNodeId"]);
                            ;
                          
                            groupDatabaseNodeslist.Add(groupDatabaseNodes);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Error In DAL While Executing Function Signature IGroupDatabaseNodesDataAccess.GetAll" + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return groupDatabaseNodeslist;
        }

        #endregion       
    }
}
