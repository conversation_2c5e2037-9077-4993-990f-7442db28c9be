# Database provider configuration data
# Core information taken from Spring Framework .NET - All credits to their great work!


# SQL SERVER
bcms.dbprovider.SqlServer-11.productName=Microsoft SQL Server, provider V1.0.5000.0 in framework .NET V1.1
bcms.dbprovider.SqlServer-11.assemblyName=System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.connectionType=System.Data.SqlClient.SqlConnection, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.commandType=System.Data.SqlClient.SqlCommand, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.parameterType=System.Data.SqlClient.SqlParameter, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.SqlServer-11.dataAdapterType=System.Data.SqlClient.SqlDataAdapter, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.commandBuilderType=System.Data.SqlClient.SqlCommandBuilder, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.SqlServer-11.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.SqlServer-11.parameterDbType=System.Data.SqlDbType, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.parameterDbTypePropertyName=SqlDbType
#bcms.dbprovider.SqlServer-11.parameterIsNullableProperty=IsNullable
bcms.dbprovider.SqlServer-11.parameterNamePrefix=@
bcms.dbprovider.SqlServer-11.exceptionType=System.Data.SqlClient.SqlException, System.Data, Version=1.0.3300.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-11.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.SqlServer-11.bindByName=true
bcms.dbprovider.SqlServer-11.dbBinaryTypeName=Image

bcms.dbprovider.SqlServer-20.productName=Microsoft SQL Server, provider V2.0.0.0 in framework .NET V2.0
bcms.dbprovider.SqlServer-20.assemblyName=System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.connectionType=System.Data.SqlClient.SqlConnection, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.commandType=System.Data.SqlClient.SqlCommand, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.parameterType=System.Data.SqlClient.SqlParameter, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.SqlServer-20.dataAdapterType=System.Data.SqlClient.SqlDataAdapter, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.commandBuilderType=System.Data.SqlClient.SqlCommandBuilder, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.SqlServer-20.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.SqlServer-20.parameterDbType=System.Data.SqlDbType, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.parameterDbTypePropertyName=SqlDbType
#bcms.dbprovider.SqlServer-20.parameterIsNullableProperty=IsNullable				
bcms.dbprovider.SqlServer-20.parameterNamePrefix=@
bcms.dbprovider.SqlServer-20.exceptionType=System.Data.SqlClient.SqlException, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServer-20.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.SqlServer-20.bindByName=true
bcms.dbprovider.SqlServer-20.dbBinaryTypeName=Image

# SQL Server CE

bcms.dbprovider.SqlServerCe-351.productName=Microsoft SQL Server Compact Edition, provider V3.5.1.0 in framework .NET V2.0
bcms.dbprovider.SqlServerCe-351.assemblyName=System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
bcms.dbprovider.SqlServerCe-351.connectionType=System.Data.SqlServerCe.SqlCeConnection, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
bcms.dbprovider.SqlServerCe-351.commandType=System.Data.SqlServerCe.SqlCeCommand, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
bcms.dbprovider.SqlServerCe-351.parameterType=System.Data.SqlServerCe.SqlCeParameter, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
#bcms.dbprovider.SqlServerCe-351.dataAdapterType=System.Data.SqlServerCe.SqlCeDataAdapter, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
bcms.dbprovider.SqlServerCe-351.commandBuilderType=System.Data.SqlServerCe.SqlCeCommandBuilder, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
#bcms.dbprovider.SqlServerCe-351.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.SqlServerCe-351.parameterDbType=System.Data.SqlDbType, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.SqlServerCe-351.parameterDbTypePropertyName=SqlDbType
#bcms.dbprovider.SqlServerCe-351.parameterIsNullableProperty=IsNullable				
bcms.dbprovider.SqlServerCe-351.parameterNamePrefix=@
bcms.dbprovider.SqlServerCe-351.exceptionType=System.Data.SqlServerCe.SqlCeException, System.Data.SqlServerCe, Version=3.5.1.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
bcms.dbprovider.SqlServerCe-351.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.SqlServerCe-351.bindByName=true
bcms.dbprovider.SqlServerCe-351.dbBinaryTypeName=Image


# ORACLE 
  
bcms.dbprovider.OracleClient-20.productName=Oracle, Microsoft provider V2.0.0.0
bcms.dbprovider.OracleClient-20.assemblyName=System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.connectionType=System.Data.OracleClient.OracleConnection, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.commandType=System.Data.OracleClient.OracleCommand, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.parameterType=System.Data.OracleClient.OracleParameter, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.OracleClient-20.dataAdapterType=System.Data.OracleClient.OracleDataAdapter, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.commandBuilderType=System.Data.OracleClient.OracleCommandBuilder, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
#bcms.dbprovider.OracleClient-20.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.OracleClient-20.parameterDbType=System.Data.OracleClient.OracleType, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.parameterDbTypePropertyName=OracleType
#bcms.dbprovider.OracleClient-20.parameterIsNullableProperty=IsNullable
bcms.dbprovider.OracleClient-20.parameterNamePrefix=:
bcms.dbprovider.OracleClient-20.exceptionType=System.Data.OracleClient.OracleException, System.Data.OracleClient, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
bcms.dbprovider.OracleClient-20.useParameterNamePrefixInParameterCollection=false
bcms.dbprovider.OracleClient-20.bindByName=true
bcms.dbprovider.OracleClient-20.dbBinaryTypeName=Blob

bcms.dbprovider.OracleODP-20.productName=Oracle, Oracle provider V2.102.2.20"  />
bcms.dbprovider.OracleODP-20.assemblyName=Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.connectionType=Oracle.DataAccess.Client.OracleConnection, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.commandType=Oracle.DataAccess.Client.OracleCommand, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.parameterType=Oracle.DataAccess.Client.OracleParameter, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
#bcms.dbprovider.OracleODP-20.dataAdapterType=Oracle.DataAccess.Client.OracleDataAdapter, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.commandBuilderType=Oracle.DataAccess.Client.OracleCommandBuilder, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
#bcms.dbprovider.OracleODP-20.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.OracleODP-20.parameterDbType=Oracle.DataAccess.Client.OracleDbType, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.parameterDbTypePropertyName=OracleDbType
#bcms.dbprovider.OracleODP-20.parameterIsNullableProperty=IsNullable
bcms.dbprovider.OracleODP-20.parameterNamePrefix=:
bcms.dbprovider.OracleODP-20.exceptionType=Oracle.DataAccess.Client.OracleException, Oracle.DataAccess, Version=2.102.2.20, Culture=neutral, PublicKeyToken=89b483f429c47342
bcms.dbprovider.OracleODP-20.useParameterNamePrefixInParameterCollection=false
bcms.dbprovider.OracleODP-20.bindByName=true
bcms.dbprovider.OracleODP-20.dbBinaryTypeName=Blob


# MySQL

# Connector/NET 1.0 
  
bcms.dbprovider.MySql-10.productName=MySQL, MySQL provider 1.0.10.1
bcms.dbprovider.MySql-10.assemblyName=MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.connectionType=MySql.Data.MySqlClient.MySqlConnection, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.commandType=MySql.Data.MySqlClient.MySqlCommand, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.parameterType=MySql.Data.MySqlClient.MySqlParameter, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-10.dataAdapterType=MySql.Data.MySqlClient.MySqlDataAdapter, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.commandBuilderType=MySql.Data.MySqlClient.MySqlCommandBuilder, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-10.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.MySql-10.parameterDbType=MySql.Data.MySqlClient.MySqlDbType, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.parameterDbTypePropertyName=MySqlDbType
#bcms.dbprovider.MySql-10.parameterIsNullableProperty=IsNullable
bcms.dbprovider.MySql-10.parameterNamePrefix=?
bcms.dbprovider.MySql-10.exceptionType=MySql.Data.MySqlClient.MySqlException, MySql.Data, Version=1.0.10.1, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-10.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.MySql-10.bindByName=true
bcms.dbprovider.MySql-10.dbBinaryTypeName=Blob
		
bcms.dbprovider.MySql-109.productName=MySQL, MySQL provider 1.0.9.0
bcms.dbprovider.MySql-109.assemblyName=MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.connectionType=MySql.Data.MySqlClient.MySqlConnection, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.commandType=MySql.Data.MySqlClient.MySqlCommand, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.parameterType=MySql.Data.MySqlClient.MySqlParameter, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-109.dataAdapterType=MySql.Data.MySqlClient.MySqlDataAdapter, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.commandBuilderType=MySql.Data.MySqlClient.MySqlCommandBuilder, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-109.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.MySql-109.parameterDbType=MySql.Data.MySqlClient.MySqlDbType, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.parameterDbTypePropertyName=MySqlDbType
#bcms.dbprovider.MySql-109.parameterIsNullableProperty=IsNullable
bcms.dbprovider.MySql-109.parameterNamePrefix=?
bcms.dbprovider.MySql-109.exceptionType=MySql.Data.MySqlClient.MySqlException, MySql.Data, Version=1.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-109.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.MySql-109.bindByName=true
bcms.dbprovider.MySql-109.dbBinaryTypeName=Blob

# Connector/NET 5.0 (.NET 2.0 only)

bcms.dbprovider.MySql-50.productName=MySQL, MySQL provider 5.0.9.0
bcms.dbprovider.MySql-50.assemblyName=MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.connectionType=MySql.Data.MySqlClient.MySqlConnection, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.commandType=MySql.Data.MySqlClient.MySqlCommand, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.parameterType=MySql.Data.MySqlClient.MySqlParameter, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-50.dataAdapterType=MySql.Data.MySqlClient.MySqlDataAdapter, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.commandBuilderType=MySql.Data.MySqlClient.MySqlCommandBuilder, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-50.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.MySql-50.parameterDbType=MySql.Data.MySqlClient.MySqlDbType, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.parameterDbTypePropertyName=MySqlDbType
#bcms.dbprovider.MySql-50.parameterIsNullableProperty=IsNullable
bcms.dbprovider.MySql-50.parameterNamePrefix=?
bcms.dbprovider.MySql-50.exceptionType=MySql.Data.MySqlClient.MySqlException, MySql.Data, Version=5.0.9.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-50.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.MySql-50.bindByName=true
bcms.dbprovider.MySql-50.dbBinaryTypeName=Blob

# Connector/NET 5.1 (.NET 2.0 only) -->

bcms.dbprovider.MySql-51.productName=MySQL, MySQL provider 5.1.6.0
bcms.dbprovider.MySql-51.assemblyName=MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d																								   
bcms.dbprovider.MySql-51.connectionType=MySql.Data.MySqlClient.MySqlConnection, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-51.commandType=MySql.Data.MySqlClient.MySqlCommand, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-51.parameterType=MySql.Data.MySqlClient.MySqlParameter, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-51.dataAdapterType=MySql.Data.MySqlClient.MySqlDataAdapter, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-51.commandBuilderType=MySql.Data.MySqlClient.MySqlCommandBuilder, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-51.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.MySql-51.parameterDbType=MySql.Data.MySqlClient.MySqlDbType, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-51.parameterDbTypePropertyName=MySqlDbType
#bcms.dbprovider.MySql-51.parameterIsNullableProperty=IsNullable
bcms.dbprovider.MySql-51.parameterNamePrefix=?
bcms.dbprovider.MySql-51.exceptionType=MySql.Data.MySqlClient.MySqlException, MySql.Data, Version=5.1.6.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-51.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.MySql-51.bindByName=true
bcms.dbprovider.MySql-51.dbBinaryTypeName=Blob		




# Connector/NET 5.2 (.NET 2.0 only) -->

bcms.dbprovider.MySql-52.productName=MySQL, MySQL provider 5.2.3.0
bcms.dbprovider.MySql-52.assemblyName=MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.connectionType=MySql.Data.MySqlClient.MySqlConnection, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.commandType=MySql.Data.MySqlClient.MySqlCommand, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.parameterType=MySql.Data.MySqlClient.MySqlParameter, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-52.dataAdapterType=MySql.Data.MySqlClient.MySqlDataAdapter, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.commandBuilderType=MySql.Data.MySqlClient.MySqlCommandBuilder, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
#bcms.dbprovider.MySql-52.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.MySql-52.parameterDbType=MySql.Data.MySqlClient.MySqlDbType, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.parameterDbTypePropertyName=MySqlDbType
#bcms.dbprovider.MySql-52.parameterIsNullableProperty=IsNullable
bcms.dbprovider.MySql-52.parameterNamePrefix=?
bcms.dbprovider.MySql-52.exceptionType=MySql.Data.MySqlClient.MySqlException, MySql.Data, Version=5.2.3.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d
bcms.dbprovider.MySql-52.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.MySql-52.bindByName=true
bcms.dbprovider.MySql-52.dbBinaryTypeName=Blob		




# PostgreSQL

bcms.dbprovider.Npgsql-10.productName=Npgsql
bcms.dbprovider.Npgsql-10.assemblyName=Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.connectionType=Npgsql.NpgsqlConnection, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.commandType=Npgsql.NpgsqlCommand, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.parameterType=Npgsql.NpgsqlParameter, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
#bcms.dbprovider.Npgsql-10.dataAdapterType=Npgsql.NpgsqlDataAdapter, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.commandBuilderType=Npgsql.NpgsqlCommandBuilder, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
#bcms.dbprovider.Npgsql-10.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.Npgsql-10.parameterDbType=NpgsqlTypes.NpgsqlDbType, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.parameterDbTypePropertyName=NpgsqlDbType
#bcms.dbprovider.Npgsql-10.parameterIsNullableProperty=IsNullable
bcms.dbprovider.Npgsql-10.parameterNamePrefix=@
bcms.dbprovider.Npgsql-10.exceptionType=Npgsql.NpgsqlException, Npgsql, Version=1.0.0.0, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7
bcms.dbprovider.Npgsql-10.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.Npgsql-10.bindByName=true


# Sqlite + ADO Net provider for sqlite
# Driver can be found at : http://sourceforge.net/projects/sqlite-dotnet2
bcms.dbprovider.SQLite-10.assemblyName=System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.connectionType=System.Data.SQLite.SQLiteConnection, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.commandType=System.Data.SQLite.SQLiteCommand, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.parameterType=System.Data.SQLite.SQLiteParameter, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
#bcms.dbprovider.SQLite-10.dataAdapterType=System.Data.SQLite.SQLiteDataAdapter , System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.commandBuilderType=System.Data.SQLite.SQLiteCommandBuilder, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
#bcms.dbprovider.SQLite-10.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.SQLite-10.parameterDbType=System.Data.SQLite.TypeAffinity, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.parameterDbTypePropertyName=DbType
#bcms.dbprovider.SQLite-10.parameterIsNullableProperty=IsNullable
bcms.dbprovider.SQLite-10.parameterNamePrefix=@
bcms.dbprovider.SQLite-10.exceptionType=System.Data.SQLite.SQLiteException, System.Data.SQLite, Version=1.0.56.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139
bcms.dbprovider.SQLite-10.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.SQLite-10.bindByName=true

# FireBird 2.0.1
bcms.dbprovider.Firebird-201.assemblyName=FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.connectionType=FirebirdSql.Data.FirebirdClient.FbConnection, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.commandType=FirebirdSql.Data.FirebirdClient.FbCommand, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.parameterType=FirebirdSql.Data.FirebirdClient.FbParameter, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
#bcms.dbprovider.Firebird-201.dataAdapterType=FirebirdSql.Data.FirebirdClient.FbDataAdapter, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.commandBuilderType=FirebirdSql.Data.FirebirdClient.FbCommandBuilder, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
#bcms.dbprovider.Firebird-201.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.Firebird-201.parameterDbType=FirebirdSql.Data.FirebirdClient.FbDbType, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.parameterDbTypePropertyName=DbType
#bcms.dbprovider.Firebird-201.parameterIsNullableProperty=IsNullable
bcms.dbprovider.Firebird-201.parameterNamePrefix=@
bcms.dbprovider.Firebird-201.exceptionType=FirebirdSql.Data.FirebirdClient.FbException, FirebirdSql.Data.FirebirdClient, Version=2.0.1.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-201.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.Firebird-201.bindByName=true

# FireBird 2.1.0
bcms.dbprovider.Firebird-210.assemblyName=FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.connectionType=FirebirdSql.Data.FirebirdClient.FbConnection, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.commandType=FirebirdSql.Data.FirebirdClient.FbCommand, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.parameterType=FirebirdSql.Data.FirebirdClient.FbParameter, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
#bcms.dbprovider.Firebird-210.dataAdapterType=FirebirdSql.Data.FirebirdClient.FbDataAdapter, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.commandBuilderType=FirebirdSql.Data.FirebirdClient.FbCommandBuilder, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
#bcms.dbprovider.Firebird-210.commandBuilderDeriveParametersMethod=DeriveParameters
bcms.dbprovider.Firebird-210.parameterDbType=FirebirdSql.Data.FirebirdClient.FbDbType, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.parameterDbTypePropertyName=DbType
#bcms.dbprovider.Firebird-210.parameterIsNullableProperty=IsNullable
bcms.dbprovider.Firebird-210.parameterNamePrefix=@
bcms.dbprovider.Firebird-210.exceptionType=FirebirdSql.Data.FirebirdClient.FbException, FirebirdSql.Data.FirebirdClient, Version=2.1.0.0, Culture=neutral, PublicKeyToken=3750abcc3150b00c
bcms.dbprovider.Firebird-210.useParameterNamePrefixInParameterCollection=true
bcms.dbprovider.Firebird-210.bindByName=true

        
!END End processing of this file for now...
		
# SQL SERVER COMPACT EDITION 3.1 -->

  <object id="SqlServerCe-3.1" type="Spring.Data.Common.DbProvider, Spring.Data" singleton="false">
    dbMetaData">
      <object type="Spring.Data.Common.DbMetadata">
        productName=Microsoft SQL Server Compact Edition, provider V9.0.242.0
        assemblyName=System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        connectionType=System.Data.SqlServerCe.SqlCeConnection, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        commandType=System.Data.SqlServerCe.SqlCeCommand, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        parameterType=System.Data.SqlServerCe.SqlCeParameter, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        dataAdapterType=System.Data.SqlServerCe.SqlCeDataAdapter, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        commandBuilderType=System.Data.SqlServerCe.SqlCeCommandBuilder, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=System.Data.SqlDbType, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        parameterDbTypeProperty=SqlDbType
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=@
        exceptionType=System.Data.SqlServerCe.SqlCeException, System.Data.SqlServerCe, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91
        useParameterNamePrefixInParameterCollection=true
        bindByName=true
        <!-- this is only true for .net 1.1 kept it here just in case we want to revert back to this strategy for
             obtaining error codes-->
        errorCodeExceptionExpression=Errors[0].NativeError.ToString()
        <!-- TODO select form system db all errors that have 'incorrect syntax' at the start of the error string-->
        <property name="ErrorCodes.BadSqlGrammarCodes">
          <value></value>
        </property>
        <property name="ErrorCodes.PermissionDeniedCodes">
          <value></value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value></value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value></value>
        </property>
      </object>
    </constructor-arg>
  </object>

  <alias name="SqlServerCe-3.1" alias="System.Data.SqlServerCe

  
  <!-- OLE DB -->

  <object id="OleDb-1.1" type="Spring.Data.Common.DbProvider, Spring.Data" singleton="false">
    dbMetaData">
      <object type="Spring.Data.Common.DbMetadata">
        productName=OleDb, provider V1.0.5000.0 in framework .NET V1.1
        assemblyName=System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        connectionType=System.Data.OleDb.OleDbConnection, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        commandType=System.Data.OleDb.OleDbCommand, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        parameterType=System.Data.OleDb.OleDbParameter, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        dataAdapterType=System.Data.OleDb.OleDbDataAdapter, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        commandBuilderType=System.Data.OleDb.OleDbCommandBuilder, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=System.Data.OleDb.OleDbType, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        parameterDbTypeProperty=OleDbType
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=?
        exceptionType=System.Data.OleDb.OleDbException, System.Data, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        useParameterNamePrefixInParameterCollection=false
        bindByName=false
        <!-- TODO should parse out high/low values in the native error...yuk!-->
        errorCodeExceptionExpression=Errors[0].NativeError.ToString()

        <!-- TODO where is the definitive list of the error.  Anyway, until parse out high low error there isn't any
             exception mapping yet for this provider. 
         -->
        <property name="ErrorCodes.BadSqlGrammarCodes">
          <value>156,170,207,208</value>
        </property>
        <property name="ErrorCodes.PermissionDeniedCodes">
          <value>229</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>2627,8114,8115</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>1205</value>
        </property>
      </object>
    </constructor-arg>
  </object>
  
  <object id="OleDb-2.0" type="Spring.Data.Common.DbProvider, Spring.Data" singleton="false">
    dbMetaData">
      <object type="Spring.Data.Common.DbMetadata">
        productName=OleDb, provider V2.0.0.0 in framework .NET V2
        assemblyName=System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        connectionType=System.Data.OleDb.OleDbConnection, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089        
        commandType=System.Data.OleDb.OleDbCommand, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        parameterType=System.Data.OleDb.OleDbParameter, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        dataAdapterType=System.Data.OleDb.OleDbDataAdapter, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        commandBuilderType=System.Data.OleDb.OleDbCommandBuilder, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=System.Data.OleDb.OleDbType, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        parameterDbTypeProperty=OleDbType
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=?
        exceptionType=System.Data.OleDb.OleDbException, System.Data, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089
        useParameterNamePrefixInParameterCollection=false
        bindByName=false
        <!-- TODO should parse out high/low values in the native error...yuk!-->
        errorCodeExceptionExpression=Errors[0].NativeError.ToString()

        <!-- TODO where is the definitive list of the error.  Anyway, until parse out high low error there isn't any
             exception mapping yet for this provider. 
         -->
        <property name="ErrorCodes.BadSqlGrammarCodes">
          <value>156,170,207,208</value>
        </property>
        <property name="ErrorCodes.PermissionDeniedCodes">
          <value>229</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>2627,8114,8115</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>1205</value>
        </property>
      </object>
    </constructor-arg>

  </object>





  
  <!-- DB2 9.0.0 for .NET 1.1 -->
  <object id="DB2-9.0.0-1.1" type="Spring.Data.Common.DbProvider, Spring.Data"  singleton="false">
    DbMetaData">
      <object type="Spring.Data.Common.DbMetadata, Spring.Data">
        productName=IBM.Data.DB2 .NET 1.1 Version 9.0.0.1
        assemblyName=IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        connectionType=IBM.Data.DB2.DB2Connection, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandType=IBM.Data.DB2.DB2Command, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterType=IBM.Data.DB2.DB2Parameter, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        dataAdapterType=IBM.Data.DB2.DB2DataAdapter, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderType=IBM.Data.DB2.DB2CommandBuilder, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=IBM.Data.DB2.DB2Type, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterDbTypeProperty=DB2Type
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=@
        exceptionType=IBM.Data.DB2.DB2Exception, IBM.Data.DB2, Version=9.0.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        useParameterNamePrefixInParameterCollection=true
        bindByName=true
        <!-- this is only true for .net 1.1 kept it here just in case we want to revert back to this strategy for
             obtaining error codes-->
        errorCodeExceptionExpression=Errors[0].NativeError
        <!-- error codes taken from http://publib.boulder.ibm.com/infocenter/iseries/v5r3/index.jsp?topic=/rzala/rzalaco.htm and 
             from Spring.Java sql-error-codes.xml -->

        <property name="ErrorCodes.badSqlGrammarCodes">
          <value>-204,-206,-301,-408</value>
        </property>
        <property name="ErrorCodes.DataAccessResourceFailureCodes">
          <value>-904</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>-803</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>-911,-913</value>
        </property>
      </object>
    </constructor-arg>
  </object>

  <!-- DB2 9.0.0 for .NET 2.0 -->
  <object id="DB2-9.0.0-2.0" type="Spring.Data.Common.DbProvider, Spring.Data"  singleton="false">
    DbMetaData">
      <object type="Spring.Data.Common.DbMetadata, Spring.Data">
        productName=IBM.Data.DB2
        assemblyName=IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        connectionType=IBM.Data.DB2.DB2Connection, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandType=IBM.Data.DB2.DB2Command, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterType=IBM.Data.DB2.DB2Parameter, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        dataAdapterType=IBM.Data.DB2.DB2DataAdapter, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderType=IBM.Data.DB2.DB2CommandBuilder, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=IBM.Data.DB2.DB2Type, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterDbTypeProperty=DB2Type
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=@
        exceptionType=IBM.Data.DB2.DB2Exception, IBM.Data.DB2, Version=9.0.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        useParameterNamePrefixInParameterCollection=true
        bindByName=true
        <!-- this is only true for .net 1.1 kept it here just in case we want to revert back to this strategy for
             obtaining error codes-->
        errorCodeExceptionExpression=Errors[0].NativeError
        <!-- error codes taken from http://publib.boulder.ibm.com/infocenter/iseries/v5r3/index.jsp?topic=/rzala/rzalaco.htm and 
             from Spring.Java sql-error-codes.xml -->

        <property name="ErrorCodes.badSqlGrammarCodes">
          <value>-204,-206,-301,-408</value>
        </property>
        <property name="ErrorCodes.DataAccessResourceFailureCodes">
          <value>-904</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>-803</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>-911,-913</value>
        </property>
      </object>
    </constructor-arg>
  </object>

  <alias name="DB2-9.0.0-2.0" alias="IBM.Data.DB2

  <!-- DB2 9.1.0 for .NET 1.1 -->
  <object id="DB2-9.1.0-1.1" type="Spring.Data.Common.DbProvider, Spring.Data"  singleton="false">
    DbMetaData">
      <object type="Spring.Data.Common.DbMetadata, Spring.Data">
        productName=IBM.Data.DB2 .NET 1.1 Version 9.1.0.1
        assemblyName=IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        connectionType=IBM.Data.DB2.DB2Connection, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandType=IBM.Data.DB2.DB2Command, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterType=IBM.Data.DB2.DB2Parameter, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        dataAdapterType=IBM.Data.DB2.DB2DataAdapter, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderType=IBM.Data.DB2.DB2CommandBuilder, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=IBM.Data.DB2.DB2Type, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterDbTypeProperty=DB2Type
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=@
        exceptionType=IBM.Data.DB2.DB2Exception, IBM.Data.DB2, Version=9.1.0.1, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        useParameterNamePrefixInParameterCollection=true
        bindByName=true
        <!-- this is only true for .net 1.1 kept it here just in case we want to revert back to this strategy for
             obtaining error codes-->
        errorCodeExceptionExpression=Errors[0].NativeError
        <!-- error codes taken from http://publib.boulder.ibm.com/infocenter/iseries/v5r3/index.jsp?topic=/rzala/rzalaco.htm and 
             from Spring.Java sql-error-codes.xml -->

        <property name="ErrorCodes.badSqlGrammarCodes">
          <value>-204,-206,-301,-408</value>
        </property>
        <property name="ErrorCodes.DataAccessResourceFailureCodes">
          <value>-904</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>-803</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>-911,-913</value>
        </property>
      </object>
    </constructor-arg>
  </object>

  <!-- DB2 9.1.0 for .NET 2.0 -->
  <object id="DB2-9.1.0.2" type="Spring.Data.Common.DbProvider, Spring.Data"  singleton="false">
    DbMetaData">
      <object type="Spring.Data.Common.DbMetadata, Spring.Data">
        productName=IBM.Data.DB2
        assemblyName=IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        connectionType=IBM.Data.DB2.DB2Connection, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandType=IBM.Data.DB2.DB2Command, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterType=IBM.Data.DB2.DB2Parameter, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        dataAdapterType=IBM.Data.DB2.DB2DataAdapter, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderType=IBM.Data.DB2.DB2CommandBuilder, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        commandBuilderDeriveParametersMethod=DeriveParameters
        parameterDbType=IBM.Data.DB2.DB2Type, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        parameterDbTypeProperty=DB2Type
        parameterIsNullableProperty=IsNullable
        parameterNamePrefix=@
        exceptionType=IBM.Data.DB2.DB2Exception, IBM.Data.DB2, Version=9.1.0.2, Culture=neutral, PublicKeyToken=7c307b91aa13d208
        useParameterNamePrefixInParameterCollection=true
        bindByName=true
        <!-- this is only true for .net 1.1 kept it here just in case we want to revert back to this strategy for
             obtaining error codes-->
        errorCodeExceptionExpression=Errors[0].NativeError
        <!-- error codes taken from http://publib.boulder.ibm.com/infocenter/iseries/v5r3/index.jsp?topic=/rzala/rzalaco.htm and 
             from Spring.Java sql-error-codes.xml -->

        <property name="ErrorCodes.badSqlGrammarCodes">
          <value>-204,-206,-301,-408</value>
        </property>
        <property name="ErrorCodes.DataAccessResourceFailureCodes">
          <value>-904</value>
        </property>
        <property name="ErrorCodes.DataIntegrityViolationCodes">
          <value>-803</value>
        </property>
        <property name="ErrorCodes.DeadlockLoserCodes">
          <value>-911,-913</value>
        </property>
      </object>
    </constructor-arg>
  </object>

  <alias name="DB2-9.1.0.2" alias="IBM.Data.DB2.9.1.0



</objects>

