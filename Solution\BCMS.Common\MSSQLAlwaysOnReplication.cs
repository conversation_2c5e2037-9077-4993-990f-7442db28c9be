﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    public class MSSQLAlwaysOnReplication : BaseEntity
    {
        //private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string GroupName { get; set; }

        [DataMember]
        public string GroupRole { get; set; }


        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get { return _replicationBase; }
        //    set { _replicationBase = value; }
        //}

        #endregion Properties
    }
}
