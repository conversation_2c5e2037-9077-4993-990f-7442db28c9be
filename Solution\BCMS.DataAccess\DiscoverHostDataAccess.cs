﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.DataAccess
{
    public class DiscoverHostDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(DiscoverHostDataAccess));

        public static int AddDiscoveryScan(int discoveryId,string range)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DiscoverScan_Create"))
                {
                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring+"iDiscoveryId", DbType.Int32, discoveryId);

                    DataAccessBase.Database.AddInParameter(dbCommand, Dbstring+"iRange", DbType.AnsiString, range);

                    var scanId = DataAccessBase.Database.ExecuteScalar(dbCommand);

                    return Convert.ToInt32(scanId);

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert Scan information", exc);
            }
        }

        public static IList<DiscoverHost> GetAllActiveDiscoverHost()
        {
            IList<DiscoverHost> hostList = new List<DiscoverHost>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DiscoverHost_GetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var host = new DiscoverHost();

                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.RangeFrom = Convert.ToString(myHostReader["RangeFrom"]);
                            host.RangeTo = Convert.ToString(myHostReader["RangeTo"]);
                            hostList.Add(host);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get Infra Object List", exc);
            }

            return hostList;
        }

        public static IList<DiscoverHostLogs> GetLastDiscoveryLogList()
        {
            IList<DiscoverHostLogs> hostList = new List<DiscoverHostLogs>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DiscoverHostLogs_GetLast"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var host = new DiscoverHostLogs();

                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.DiscoverScanId = Convert.ToInt32(myHostReader["DiscoverScanId"]);
                            host.ReachableHost = Convert.ToString(myHostReader["ReachableHost"]);
                            host.Status = Convert.ToInt32(myHostReader["Status"]);
                            hostList.Add(host);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get DiscoverHostLogs Last", exc);
            }

            return hostList;
        }

        public static bool AddDiscoverHostLogs(int discoverScanId,string host,int status)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("DiscoverHostLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iDiscoverScanId", DbType.Int32, discoverScanId);
                    Database.AddInParameter(cmd, Dbstring+"iReachableHost", DbType.AnsiString, host);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, status);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }

    }
}
