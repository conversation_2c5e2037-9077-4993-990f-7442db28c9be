﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SCR", Namespace = "http://www.BCMS.com/types")]
    public class SCR : BaseEntity
    {
        //ReplicationBase _replicationBase = new ReplicationBase();
        public SCR()
            : base()
        {
        }
        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }
        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }
        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PRServerName
        {
            get;
            set;
        }
        [DataMember]
        public string DRServerName
        {
            get;
            set;
        }

        [DataMember]
        public string PRInstallationPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRInstallationPath
        {
            get;
            set;
        }
        [DataMember]
        public string PRNewMailboxPath
        {
            get;
            set;
        }
        [DataMember]
        public string DRNewMailboxPath
        {
            get;
            set;
        }
        [DataMember]
        public string PRComponentPath
        {
            get;
            set;
        }
        [DataMember]
        public string DRComponentPath
        {
            get;
            set;
        }
        [DataMember]
        public string ExeFileName
        {
            get;
            set;
        }
        [DataMember]
        public string ReplayLagTime
        {
            get;
            set;
        }
        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get
        //    {
        //        return _replicationBase;
        //    }
        //    set
        //    {
        //        _replicationBase = value;
        //    }
        //}

        #endregion
    }
}
