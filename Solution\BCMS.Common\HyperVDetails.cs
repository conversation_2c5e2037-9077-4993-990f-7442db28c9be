﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{

    [DataContract(Name = "HyperVDetails", Namespace = "http://www.BCMS.com/types")]
    public class HyperVDetails : BaseEntity
    {
        // private ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRVMName { get; set; }

        [DataMember]
        public string DRVMName { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string PRVMState { get; set; }

        [DataMember]
        public string DRVMState { get; set; }

        [DataMember]
        public string PRVMClustered { get; set; }

        [DataMember]
        public string DRVMClustered { get; set; }

        [DataMember]
        public string PRVMIPAdress { get; set; }

        [DataMember]
        public string DRVMIPAdress { get; set; }

        [DataMember]
        public string PRVMNetworkStatus { get; set; }

        [DataMember]
        public string DRVMNetworkStatus { get; set; }

        [DataMember]
        public string PRReplicationState { get; set; }

        [DataMember]
        public string DRReplicationState { get; set; }

        [DataMember]
        public string PRReplicationMode { get; set; }

        [DataMember]
        public string DRReplicationMode { get; set; }

        [DataMember]
        public string PRCurrentPrimaryServer { get; set; }

        [DataMember]
        public string DRCurrentPrimaryServer { get; set; }

        [DataMember]
        public string PRCurrentReplicaServer { get; set; }

        [DataMember]
        public string DRCurrentReplicaServer { get; set; }

        [DataMember]
        public string PRReplicationHealth { get; set; }

        [DataMember]
        public string DRReplicationHealth { get; set; }

        [DataMember]
        public string PRReplicaPort { get; set; }

        [DataMember]
        public string DRReplicatPort { get; set; }

        [DataMember]
        public string DRReplicaPort { get; set; }

        [DataMember]
        public string PRAuthType { get; set; }

        [DataMember]
        public string DRAuthType { get; set; }

        [DataMember]
        public string PRSizeReplicated { get; set; }

        [DataMember]
        public string DRSizeReplicated { get; set; }

        [DataMember]
        public string LastSynchronized { get; set; }

        [DataMember]
        public string HyperVDatalag { get; set; }


        [DataMember]
        public string PRReplicaBrokerName { get; set; }

        [DataMember]
        public string DRReplicaBrokerName { get; set; }

        [DataMember]
        public string PRReplicaBrokerPort { get; set; }

        [DataMember]
        public string DRReplicaBrokerPort { get; set; }

        [DataMember]
        public string PRAuthenticationType { get; set; }

        [DataMember]
        public string DRAuthenticationType { get; set; }

        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get { return _replicationBase; }
        //    set { _replicationBase = value; }
        //}

        #endregion Properties
    }
}
