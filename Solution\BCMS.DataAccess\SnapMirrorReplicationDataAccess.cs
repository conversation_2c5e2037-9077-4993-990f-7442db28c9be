﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class SnapMirrorReplicationDataAccess : BaseDataAccess
    {
        public static bool AddSnapMirrorReplication(SnapMirrorReplication snapreplication)
        {
            //var db = DatabaseFactory.CreateDatabase();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SnapMirrorMonitor_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iSource", DbType.AnsiString, snapreplication.Source);
                    Database.AddInParameter(cmd, Dbstring+"iDestination", DbType.AnsiString, snapreplication.Destination);
                    Database.AddInParameter(cmd, Dbstring+"iState", DbType.AnsiString, snapreplication.State);
                    Database.AddInParameter(cmd, Dbstring+"iLag", DbType.AnsiString, snapreplication.Lag);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, snapreplication.Status);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, snapreplication.GroupId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Snap Mirror Replication information", exc);
            }

        }

        public static bool AddSnapMirrorLogs(SnapMirrorReplication snapreplication)
        {
            //var db = DatabaseFactory.CreateDatabase();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SnapMirrorMonitorLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iSource", DbType.AnsiString, snapreplication.Source);
                    Database.AddInParameter(cmd, Dbstring+"iDestination", DbType.AnsiString, snapreplication.Destination);
                    Database.AddInParameter(cmd, Dbstring+"iState", DbType.AnsiString, snapreplication.State);
                    Database.AddInParameter(cmd, Dbstring+"iLag", DbType.AnsiString, snapreplication.Lag);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, snapreplication.Status);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, snapreplication.GroupId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Snap Mirror Replication information", exc);
            }

        }

        public static bool AddSnapMirrorStatus(SnapMirrorReplication snapreplication)
        {
            //var db = DatabaseFactory.CreateDatabase();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SnapMirrorMonitorStatus_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iSource", DbType.AnsiString, snapreplication.Source);
                    Database.AddInParameter(cmd, Dbstring+"iDestination", DbType.AnsiString, snapreplication.Destination);
                    Database.AddInParameter(cmd, Dbstring+"iState", DbType.AnsiString, snapreplication.State);
                    Database.AddInParameter(cmd, Dbstring+"iLag", DbType.AnsiString, snapreplication.Lag);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, snapreplication.Status);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, snapreplication.GroupId);

//#if ORACLE
//                    cmd.Parameters.Add("cur");
//#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Snap Mirror Replication information", exc);
            }

        }

         
    }
}