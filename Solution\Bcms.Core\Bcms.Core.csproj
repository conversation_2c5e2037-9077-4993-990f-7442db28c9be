﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{440DFC03-8DE7-408B-8BC3-CE3ECAE05050}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Bcms.Core</RootNamespace>
    <AssemblyName>Bcms.Core</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;MSSQL</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AWSSDK">
      <HintPath>..\..\Thirdparty\Amazon\AWSSDK.dll</HintPath>
    </Reference>
    <Reference Include="BCMSExchangeClass, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\ExchangeSCR\BCMSExchangeClass.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>..\..\Thirdparty\JSCAPE\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="CPDiscovery">
      <HintPath>..\..\Thirdparty\CPDiscovery.dll</HintPath>
    </Reference>
    <Reference Include="DNSManagement, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\DNSManagement\DNSManagement.dll</HintPath>
    </Reference>
    <Reference Include="EM653032">
      <HintPath>..\..\Thirdparty\Base24\EM653032.dll</HintPath>
    </Reference>
    <Reference Include="HyperVCluster">
      <HintPath>..\..\Thirdparty\HyperV\HyperVCluster.dll</HintPath>
    </Reference>
    <Reference Include="Interop.VixCOM, Version=*******, Culture=neutral, PublicKeyToken=1a2e7e25925ba452, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\Interop.VixCOM.dll</HintPath>
    </Reference>
    <Reference Include="Jscape.Ssh, Version=*******, Culture=neutral, PublicKeyToken=504a4aa00214df6f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\JSCAPE\Jscape.Ssh.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=*******, Culture=neutral, PublicKeyToken=b32731d11ce58905">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Log4Net\log4net.dll</HintPath>
    </Reference>
    <Reference Include="LogShippingNative, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Logshipping\LogShippingNative.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="MssqlCluster">
      <HintPath>..\..\Thirdparty\MssqlDBMirroring\MssqlCluster.dll</HintPath>
    </Reference>
    <Reference Include="MSSQLHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\MSSQLHelper\MSSQLHelper.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\MySql\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="MySqlBackup">
      <HintPath>..\..\Thirdparty\MySql\MySqlBackup.dll</HintPath>
    </Reference>
    <Reference Include="MySqlSolApp, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\MySqlNative\MySqlSolApp.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=2.6.0.12051, Culture=neutral, PublicKeyToken=96d09a1eb7f44a77, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="OracleDbSwitchoverwithDG, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\OracleSwitchoverwithDG\OracleDbSwitchoverwithDG.dll</HintPath>
    </Reference>
    <Reference Include="PADManager">
      <HintPath>..\..\Thirdparty\PADManager\PADManager.dll</HintPath>
    </Reference>
    <Reference Include="PAws">
      <HintPath>..\..\Thirdparty\Amazon\PAws.dll</HintPath>
    </Reference>
    <Reference Include="PAzure">
      <HintPath>..\..\Thirdparty\Azure\PAzure.dll</HintPath>
    </Reference>
    <Reference Include="PCPL, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Global\PCPL.dll</HintPath>
    </Reference>
    <Reference Include="PCPSL">
      <HintPath>..\..\Thirdparty\Global\PCPSL.dll</HintPath>
    </Reference>
    <Reference Include="PDB2HADR, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\HADR\PDB2HADR.dll</HintPath>
    </Reference>
    <Reference Include="PDeleteOracleArchiveLog">
      <HintPath>..\..\Thirdparty\DeleteArchiveLogs\PDeleteOracleArchiveLog.dll</HintPath>
    </Reference>
    <Reference Include="PDSStorage">
      <HintPath>..\..\Thirdparty\Global\PDSStorage.dll</HintPath>
    </Reference>
    <Reference Include="PDTake">
      <HintPath>..\..\Thirdparty\DoubleTake\PDTake.dll</HintPath>
    </Reference>
    <Reference Include="PEMCSRDF, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\EMC\PEMCSRDF.dll</HintPath>
    </Reference>
    <Reference Include="PExchangeDAG">
      <HintPath>..\..\..\..\ExchangeDagDomainWork\PExchangeDagCPROOT4.5SVN21092016\Exchange.F4\PExchangeDAG\bin\Release\PExchangeDAG.dll</HintPath>
    </Reference>
    <Reference Include="PFailoverClusters">
      <HintPath>..\..\Thirdparty\FailedOverClusterHyperV\PFailoverClusters.dll</HintPath>
    </Reference>
    <Reference Include="PGlobalMirror, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Global\PGlobalMirror.dll</HintPath>
    </Reference>
    <Reference Include="PHitachi, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\DLL\Phitachi\AUF\PHitachi\bin\Release\PHitachi.dll</HintPath>
    </Reference>
    <Reference Include="PHPUNIX, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\HPUNIX\PHPUNIX.dll</HintPath>
    </Reference>
    <Reference Include="PHyperV, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\HyperV\PHyperV.dll</HintPath>
    </Reference>
    <Reference Include="PHyperVVMAutomation">
      <HintPath>..\..\Thirdparty\HyperV\PHyperVVMAutomation.dll</HintPath>
    </Reference>
    <Reference Include="PILOManagement">
      <HintPath>..\..\Thirdparty\PILOM\PILOManagement.dll</HintPath>
    </Reference>
    <Reference Include="PInfoblox">
      <HintPath>..\..\Thirdparty\INFOBLOX\PInfoblox.dll</HintPath>
    </Reference>
    <Reference Include="PMainframe">
      <HintPath>..\..\Thirdparty\MainFrame\PMainframe.dll</HintPath>
    </Reference>
    <Reference Include="PMSSQLDBMirroring">
      <HintPath>..\..\Thirdparty\MssqlDBMirroring\PMSSQLDBMirroring.dll</HintPath>
    </Reference>
    <Reference Include="PMSSQLNative, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Dll\New folder\pmsqlnative\PMSSQLNative\bin\Release\PMSSQLNative.dll</HintPath>
    </Reference>
    <Reference Include="PODGBrokerLib">
      <HintPath>..\..\Thirdparty\ODGBroker\PODGBrokerLib.dll</HintPath>
    </Reference>
    <Reference Include="POracle, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\POracle\POracle.dll</HintPath>
    </Reference>
    <Reference Include="POracleDatabase, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\WindowsOraclewithoutDG\POracleDatabase.dll</HintPath>
    </Reference>
    <Reference Include="POracleMutex, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\POracle\POracleMutex.dll</HintPath>
    </Reference>
    <Reference Include="POracleOpsCeneter">
      <HintPath>..\..\Thirdparty\Global\POracleOpsCeneter.dll</HintPath>
    </Reference>
    <Reference Include="POracleWin_SSH, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\OracleWinSSH\POracleWin_SSH.dll</HintPath>
    </Reference>
    <Reference Include="Postgre3.5">
      <HintPath>..\..\Thirdparty\Postgre3.5\Postgre3.5.dll</HintPath>
    </Reference>
    <Reference Include="Postgres">
      <HintPath>..\..\Thirdparty\Postgres9x\Postgres.dll</HintPath>
    </Reference>
    <Reference Include="PRecoverpoint, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PRecoverPoint\PRecoverpoint.dll</HintPath>
    </Reference>
    <Reference Include="PSMSEmail">
      <HintPath>..\..\Thirdparty\Email\PSMSEmail.dll</HintPath>
    </Reference>
    <Reference Include="PSnapMirror, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\June-16\sanpmirror\PSnapMirror\bin\Release\PSnapMirror.dll</HintPath>
    </Reference>
    <Reference Include="PSoftLayer">
      <HintPath>..\..\Thirdparty\PSoftLayer\PSoftLayer.dll</HintPath>
    </Reference>
    <Reference Include="PSRM, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PSRM\PSRM.dll</HintPath>
    </Reference>
    <Reference Include="PSSHConn, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PSSH\PSSHConn.dll</HintPath>
    </Reference>
    <Reference Include="PSSHMutexConn, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PSSH\PSSHMutexConn.dll</HintPath>
    </Reference>
    <Reference Include="PvCenterSRM">
      <HintPath>..\..\Thirdparty\PvCenterSRM\PvCenterSRM.dll</HintPath>
    </Reference>
    <Reference Include="PVMOperations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\PVMOperations.dll</HintPath>
    </Reference>
    <Reference Include="PVMProvision, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PVMProvision\PVMProvision.dll</HintPath>
    </Reference>
    <Reference Include="PVMWare, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\PVMWare.dll</HintPath>
    </Reference>
    <Reference Include="PVxVM, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\PVxVM.dll</HintPath>
    </Reference>
    <Reference Include="PWebHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\PWebHelper\PWebHelper.dll</HintPath>
    </Reference>
    <Reference Include="PWinCluster, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\WinCluster\PWinCluster.dll</HintPath>
    </Reference>
    <Reference Include="PWindows, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\windows\PWindows.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <HintPath>..\..\Thirdparty\Quartz\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Castle, Version=*******, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Castle.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Common">
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Common.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Curve25519, Version=*******, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Curve25519.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Ed25519, Version=*******, Culture=neutral, PublicKeyToken=1c4638788972655d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Ed25519.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Networking">
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Networking.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Sftp">
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Sftp.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.SshShell">
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.SshShell.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Terminal">
      <HintPath>..\..\Thirdparty\Rebex\Rebex_Latest\Rebex.Terminal.dll</HintPath>
    </Reference>
    <Reference Include="ReportsLib, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\ReportLibrary\ReportsLib.dll</HintPath>
    </Reference>
    <Reference Include="Serilog">
      <HintPath>..\..\Thirdparty\Serilogger\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Thread">
      <HintPath>..\..\Thirdparty\Serilogger\Serilog.Enrichers.Thread.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File">
      <HintPath>..\..\Thirdparty\Serilogger\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Map">
      <HintPath>..\..\Thirdparty\Serilogger\Serilog.Sinks.Map.dll</HintPath>
    </Reference>
    <Reference Include="SpreadsheetGear, Version=6.0.3.222, Culture=neutral, PublicKeyToken=39c186f5904944ec, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\DLLs\SpreadsheetGear.dll</HintPath>
    </Reference>
    <Reference Include="SqlServer2000dll, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\SqlLogFastCopy\SqlServer2000dll.dll</HintPath>
    </Reference>
    <Reference Include="SrmService2008">
      <HintPath>..\..\Thirdparty\SRM\SrmService2008.dll</HintPath>
    </Reference>
    <Reference Include="SrmService2008.XmlSerializers">
      <HintPath>..\..\Thirdparty\SRM\SrmService2008.XmlSerializers.dll</HintPath>
    </Reference>
    <Reference Include="Sybase">
      <HintPath>..\..\Thirdparty\Sybase\Sybase.dll</HintPath>
    </Reference>
    <Reference Include="SybaseWithSRS">
      <HintPath>..\..\Thirdparty\SybaseWithSRS\SybaseWithSRS.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Management.Automation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Azure\System.Management.Automation.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="TPCR">
      <HintPath>..\..\Thirdparty\TPCR\TPCR.dll</HintPath>
    </Reference>
    <Reference Include="Vestris.VMWareLib, Version=1.6.3950.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\Vestris.VMWareLib.dll</HintPath>
    </Reference>
    <Reference Include="Vestris.VMWareTools, Version=1.6.3950.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Vmware\Vestris.VMWareTools.dll</HintPath>
    </Reference>
    <Reference Include="WebURLHelper">
      <HintPath>..\..\Thirdparty\PWebHelper\WebURLHelper.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BcmsCore.cs" />
    <Compile Include="BcmsServerFactory.cs" />
    <Compile Include="Client\ApplicationeBDRClient.cs" />
    <Compile Include="Client\BcmsClient.cs" />
    <Compile Include="Client\DiscoverHost.cs" />
    <Compile Include="Client\doubleTakeClient.cs" />
    <Compile Include="Client\HyperVClient.cs" />
    <Compile Include="Client\Infraobject_SchedulerClient.cs" />
    <Compile Include="Client\IPList.cs" />
    <Compile Include="Client\MimixClient.cs" />
    <Compile Include="Client\MSSQLAlwaysONClient.cs" />
    <Compile Include="Client\MSSQLDBMirroringClient.cs" />
    <Compile Include="Client\MSSQLMirroringClient.cs" />
    <Compile Include="Client\MSSqlNative2008Client.cs" />
    <Compile Include="Client\MySqlNativeClient.cs" />
    <Compile Include="Client\OracleDatabaseMonitor.cs" />
    <Compile Include="Client\Postgres9XClient.cs" />
    <Compile Include="Client\SqlServerDatabaseMonitor.cs" />
    <Compile Include="Client\SybaseClient.cs" />
    <Compile Include="Client\SybaseWithSRSClient.cs" />
    <Compile Include="Client\VCenterClient.cs" />
    <Compile Include="Client\XIVClientDetails.cs" />
    <Compile Include="Configuration\Configuration.cs" />
    <Compile Include="FastCopy\FastCopy.cs" />
    <Compile Include="FastCopy\Sql2000Migration.cs" />
    <Compile Include="Interface\IBcmsBase.cs" />
    <Compile Include="IBcmsCore.cs" />
    <Compile Include="Job\ActiveODGJobs.cs" />
    <Compile Include="Job\ApplicationDeleteFilesJob.cs" />
    <Compile Include="Job\ApplicationDTJob.cs" />
    <Compile Include="Job\ApplicationeBDRJob.cs" />
    <Compile Include="Job\ApplicationMimixJob.cs" />
    <Compile Include="Job\ApplicationReplicationJob.cs" />
    <Compile Include="Job\ApplicationReplicationUploadCFLJob.cs" />
    <Compile Include="Job\ApplyArchiveLogsJob.cs" />
    <Compile Include="Job\ArchivedLogDeleteJob.cs" />
    <Compile Include="Job\ArchivedLogReplicationJob.cs" />
    <Compile Include="Job\BIAActionAnalyticsJob.cs" />
    <Compile Include="Job\BIAActionTrendDataJob.cs" />
    <Compile Include="Job\BIAAlertAnalyticsJob.cs" />
    <Compile Include="Job\BIAAnalyticsJob.cs" />
    <Compile Include="Job\BIATrendDatajob.cs" />
    <Compile Include="Job\CryptographyHelper.cs" />
    <Compile Include="Job\DRNetMonitorJob.cs" />
    <Compile Include="Job\InfraobjectDiskMonitorJob.cs" />
    <Compile Include="Job\Infraobject_SchedulerJob.cs" />
    <Compile Include="Job\MonitorHitachiUrMSSQLJob.cs" />
    <Compile Include="Job\MonitorHyperVJob.cs" />
    <Compile Include="Job\MonitorMAXDBJob.cs" />
    <Compile Include="Job\MonitorMySqlDBJob.cs" />
    <Compile Include="Job\MonitorRecoverPointJob.cs" />
    <Compile Include="Job\MonitorSVCGlobalMirrorJob.cs" />
    <Compile Include="Job\MonitorTPCRJob.cs" />
    <Compile Include="Job\MSSqlAlwaysONJob.cs" />
    <Compile Include="Job\MssqldoubleTakeJob.cs" />
    <Compile Include="Job\MSSqlNetAppSnapMirror.cs" />
    <Compile Include="Job\EC2S3DataSyncReplicationJob.cs" />
    <Compile Include="Job\EC2S3DataSyncJob.cs" />
    <Compile Include="Job\FastCopyOracleSyncJob.cs" />
    <Compile Include="Job\GenerateReportJob.cs" />
    <Compile Include="Job\MonintorExchangeJob.cs" />
    <Compile Include="Job\MonitorApplicationServiceJob.cs" />
    <Compile Include="Job\MonitorApplicationWorkflowJob.cs" />
    <Compile Include="Job\MonitorExchangeDagJob.cs" />
    <Compile Include="Job\MonitorFileChangeJob.cs" />
    <Compile Include="Job\MonitorHADRJob.cs" />
    <Compile Include="Job\MonitorHitachiHURJob.cs" />
    <Compile Include="Job\MonitoringDataGurdGapAndModeJob.cs" />
    <Compile Include="Job\MonitorOracleDgJob.cs" />
    <Compile Include="Job\MonitorPasswordChangeJob.cs" />
    <Compile Include="Job\MonitorSRDFDataLagJob.cs" />
    <Compile Include="Job\MonitorSRDFJob.cs" />
    <Compile Include="Job\MonitorsServiesJob.cs" />
    <Compile Include="Job\MonitorVmWareJob.cs" />
    <Compile Include="Job\MSSqlEmcSrdfFullDbJob.cs" />
    <Compile Include="Job\MSSQLMirroringMonitoringJob.cs" />
    <Compile Include="Job\MSSqlNative2008Job.cs" />
    <Compile Include="Job\MSSqlNativeEnableLogshipping.cs" />
    <Compile Include="Job\MSSqlNativeLogjob.cs" />
    <Compile Include="Job\MSSqlServer2000Job.cs" />
    <Compile Include="Job\MssqlSVCFullDbJob.cs" />
    <Compile Include="Job\MySqlNativeJob.cs" />
    <Compile Include="Job\ParallelWorkflowJob.cs" />
    <Compile Include="Job\MonitorBusinessServiceAvailbility.cs" />
    <Compile Include="Job\DatabaseBackupJob.cs" />
    <Compile Include="Job\GlobalMirrorJob.cs" />
    <Compile Include="Job\MonitorApplicationJob.cs" />
    <Compile Include="Job\MonitorDataGuardJob.cs" />
    <Compile Include="Job\MonitorMountPointJob.cs" />
    <Compile Include="Job\MonitorReplicateLogVolumeJob.cs" />
    <Compile Include="Job\MonitorComponentJob.cs" />
    <Compile Include="Job\DataSynchronizationJob.cs" />
    <Compile Include="Job\PingJob.cs" />
    <Compile Include="Job\Postgre9XJob.cs" />
    <Compile Include="Job\PostgreMonitoringJob.cs" />
    <Compile Include="Job\Report24HrsStatusJob.cs" />
    <Compile Include="Job\SnapMirrorJob.cs" />
    <Compile Include="Job\SqlDBMirroringJob.cs" />
    <Compile Include="Job\SRMVMwareMonitorJob.cs" />
    <Compile Include="Job\SybaseMonitoringJob.cs" />
    <Compile Include="Job\SybaseWithSRSMonitoringJob.cs" />
    <Compile Include="Job\XIVJobdetails.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Utility\SSHHelper.cs" />
    <Compile Include="Utility\StringHelper.cs" />
    <Compile Include="Utility\ThreadHelper.cs" />
    <Compile Include="Workflows\NewWorkflowVars.cs" />
    <Compile Include="Workflows\WorkflowClient.cs" />
    <Compile Include="Workflows\WorkflowController.cs" />
    <Compile Include="Workflows\WorkflowManager.cs" />
    <Compile Include="Workflows\WorkflowResumeClient.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Base\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BCMS.Common\Bcms.Common.csproj">
      <Project>{4f12b107-29b7-4e7a-98a2-e4219142261f}</Project>
      <Name>Bcms.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.DataAccess\Bcms.DataAccess.csproj">
      <Project>{e8cb1405-a5f6-4a2c-af32-46301ed4df0c}</Project>
      <Name>Bcms.DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.ExceptionHandler\Bcms.ExceptionHandler.csproj">
      <Project>{0dfb8da8-a766-4c68-9d49-0511764793aa}</Project>
      <Name>Bcms.ExceptionHandler</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.Helper\Bcms.Helper.csproj">
      <Project>{a3af28b6-66cb-4b8c-bb83-2952977967af}</Project>
      <Name>Bcms.Helper</Name>
    </ProjectReference>
    <ProjectReference Include="..\Bcms.Replication\Bcms.Replication.csproj">
      <Project>{5c5ceed3-44cc-419f-8efd-c853e06636ed}</Project>
      <Name>Bcms.Replication</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.ScheduleEngine\Bcms.ScheduleEngine.csproj">
      <Project>{4ea537a2-0da2-4253-a646-e5189037ad55}</Project>
      <Name>Bcms.ScheduleEngine</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>