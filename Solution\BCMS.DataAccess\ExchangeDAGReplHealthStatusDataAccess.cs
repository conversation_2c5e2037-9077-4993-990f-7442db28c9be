﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using Bcms.DataAccess.Base;
using Bcms.Common.Base;
using BCMS.Common;

namespace Bcms.DataAccess
{
    public class ExchangeDAGReplHealthStatusDataAccess : BaseDataAccess
    {
        #region Constructors
        public ExchangeDAGReplHealthStatusDataAccess()
            : base()
        {
        }       

        #endregion


        #region IExchangeDAGReplHealthStatusDataAccess Members

        public static bool AddDAGReplHealthStatus(ExchangeDAGReplHealthStatus exchangeDAGReplHealthStatus)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand(DbRoleName + "EXCDAG_REPLHTSTATUS_CREATE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLICATIONID", DbType.Int32, exchangeDAGReplHealthStatus.ReplicationId);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, exchangeDAGReplHealthStatus.InfraobjectId);                  
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERSERVICE_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterService_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERSERVICE_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterService_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERSERVICE_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterService_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERSERVICE_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterService_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLAYSERVICE_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ReplayService_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLAYSERVICE_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ReplayService_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLAYSERVICE_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ReplayService_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLAYSERVICE_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ReplayService_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iACTIVEMANAGER_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ActiveManager_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iACTIVEMANAGER_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ActiveManager_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iACTIVEMANAGER_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ActiveManager_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iACTIVEMANAGER_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ActiveManager_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iTASKSRPCLISTENER_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.TasksRPCListener_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iTASKSRPCLISTENER_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.TasksRPCListener_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iTASKSRPCLISTENER_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.TasksRPCListener_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iTASKSRPCLISTENER_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.TasksRPCListener_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iTCPLISTENER_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.TCPListener_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iTCPLISTENER_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.TCPListener_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iTCPLISTENER_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.TCPListener_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iTCPLISTENER_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.TCPListener_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iDAGMEMBERSUP_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DAGMembersUp_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDAGMEMBERSUP_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DAGMembersUp_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iDAGMEMBERSUP_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DAGMembersUp_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDAGMEMBERSUP_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DAGMembersUp_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERNETWORK_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterNetwork_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERNETWORK_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterNetwork_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERNETWORK_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterNetwork_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iCLUSTERNETWORK_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ClusterNetwork_Dr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iQUORUMGROUP_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.QuorumGroup_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iQUORUMGROUP_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.QuorumGroup_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iQUORUMGROUP_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.QuorumGroup_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iQUORUMGROUP_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.QuorumGroup_Dr_Error);

                    //Inserting ServerLocatorService Values in DBCOPYSUSPENDED     
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYSUSPENDED_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ServerLocatorService_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYSUSPENDED_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ServerLocatorService_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYSUSPENDED_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.ServerLocatorService_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYSUSPENDED_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.ServerLocatorService_Dr_Error);
                    //Inserting "DatabaseRedundancyCheck" Values in DBCOPYFAILED    
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYFAILED_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DbRedundancyCheck_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYFAILED_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DbRedundancyCheck_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYFAILED_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DbRedundancyCheck_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBCOPYFAILED_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DbRedundancyCheck_Dr_Error);
                    //Inserting "DatabaseAvailabilityCheck" Values in DBINITIALIZING  
                    Database.AddInParameter(dbCommand, Dbstring+"iDBINITIALIZING_PR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DbAvailabilityCheck_Pr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBINITIALIZING_PR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DbAvailabilityCheck_Pr_Error);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBINITIALIZING_DR_RESULT", DbType.AnsiString, exchangeDAGReplHealthStatus.DbAvailabilityCheck_Dr_Result);
                    Database.AddInParameter(dbCommand, Dbstring+"iDBINITIALIZING_DR_ERROR", DbType.AnsiString, exchangeDAGReplHealthStatus.DbAvailabilityCheck_Dr_Error);
                    
                    Database.AddInParameter(dbCommand, Dbstring+"iDBDISCONNECTED_PR_RESULT", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBDisconnected_Pr_Result
                    Database.AddInParameter(dbCommand, Dbstring+"iDBDISCONNECTED_PR_ERROR", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBDisconnected_Pr_Error
                    Database.AddInParameter(dbCommand, Dbstring+"iDBDISCONNECTED_DR_RESULT", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBDisconnected_Dr_Result
                    Database.AddInParameter(dbCommand, Dbstring+"iDBDISCONNECTED_DR_ERROR", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBDisconnected_Dr_Error

                    Database.AddInParameter(dbCommand, Dbstring+"iDBLOGCOPYKEEPINGUP_PR_RESULT", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBLogCopyKeepingUp_Pr_Result
                    Database.AddInParameter(dbCommand, Dbstring+"iDBLOGCOPYKEEPINGUP_PR_ERROR", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBLogCopyKeepingUp_Pr_Error
                    Database.AddInParameter(dbCommand, Dbstring+"iDBLOGCOPYKEEPINGUP_DR_RESULT", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBLogCopyKeepingUp_Dr_Result
                    Database.AddInParameter(dbCommand, Dbstring+"iDBLOGCOPYKEEPINGUP_DR_ERROR", DbType.AnsiString, "N/A");//exchangeDAGReplHealthStatus.DBLogCopyKeepingUp_Dr_Error

                    Database.AddInParameter(dbCommand, Dbstring+"iServerId", DbType.Int32, exchangeDAGReplHealthStatus.ServerId);
                    Database.AddInParameter(dbCommand, Dbstring+"iDatabaseId", DbType.Int32, exchangeDAGReplHealthStatus.DatabaseId);

                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occured while insert AddDAGReplHealthStatus information", exc);
            }
            return false;
        }

        #endregion

    }
}
