﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class NetworkIPDataAccess : BaseDataAccess
    {

        public static IList<NetworkIP> GetAllNetworkIP()
        {
            IList<NetworkIP> networkIps = new List<NetworkIP>();
            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetStoredProcCommand("NetworkIp_GetAll"))
                {
                    using (IDataReader myGroupReader = db.ExecuteReader(dbCommand))
                    {
                        while (myGroupReader.Read())
                        {
                            var networkip = new NetworkIP();
                            networkip.Id = Convert.IsDBNull(myGroupReader["Id"]) ? 0 : Convert.ToInt32(myGroupReader["Id"]);
                            networkip.Circle = Convert.IsDBNull(myGroupReader["Circle"]) ? string.Empty : Convert.ToString(myGroupReader["Circle"]);
                            networkip.IP = Convert.IsDBNull(myGroupReader["IP"]) ? string.Empty : Convert.ToString(myGroupReader["IP"]);
                            networkip.Switch = Convert.IsDBNull(myGroupReader["Switch"]) ? string.Empty : Convert.ToString(myGroupReader["Switch"]);
                            networkip.Status = Convert.IsDBNull(myGroupReader["Status"]) ? 0 : Convert.ToInt32(myGroupReader["Status"]);
                            //networkip.Id = Convert.ToInt32(myGroupReader[0]);
                            //networkip.IP = myGroupReader[2].ToString();
                            //networkip.Switch = myGroupReader[3].ToString();
                            //networkip.Status = Convert.ToInt32(myGroupReader[4]);

                            networkIps.Add(networkip);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,"Exception occurred while Get NetworkIp Details ", exc);
                
            }
            return networkIps;
        }

        public static bool UpdateNetworkIpByStatus(int status, string ip)
        {
            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetStoredProcCommand("NetworkIP_UpdateByStatus"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"istatus", DbType.Int32, status);
                    db.AddInParameter(dbCommand, Dbstring+"iIP", DbType.AnsiString, ip);
                    int isuccess = db.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Error occurred while updating Network ip status -" + ip, exc);
            }
        }
    }
}
