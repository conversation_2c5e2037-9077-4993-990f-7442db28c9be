﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class NodeSubstituteAuthentication : BaseEntity
    {
        #region Properties

        [DataMember]
        public Int32 Id { get; set; }

        [DataMember]
        public Int32 BaseDatabaseId { get; set; }

        [DataMember]
        public string Sqlplus { get; set; }

        [DataMember]
        public int Credential { get; set; }

        [DataMember]
        public string Role { get; set; }

        [DataMember]
        public string DBConnectionCmd { get; set; }


        [DataMember]
        public Int32 IsSubstituteAuth { get; set; }



        #endregion
    }
}
