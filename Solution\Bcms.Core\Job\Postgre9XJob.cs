﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Core;
using Bcms.Core.Client;
using log4net;

namespace Bcms.Core.Job
{
   public class Postgre9XJob : IJob
    {
       public const string InfraObject = "InfraObject";

       private static readonly ILog Logger = LogManager.GetLogger(typeof(Postgre9XJob));

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var infraObject = data.Get(InfraObject) as InfraObject;

            if (infraObject != null)
            {
                InfraObject InfraObjectItem = InfraObjectDataAccess.GetInfraObjectById(infraObject.Id);

                if (InfraObjectItem != null)
                {
                    if (InfraObjectItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : InfraObject is not in active mode, skip the monitor components job cycle", InfraObjectItem.Name);

                        return;
                    }

                    MonitorPostgreComponent(InfraObjectItem);

                   // Postgres9XClient client = new Client.Postgres9XClient();

                 //   client.CurrentInfraObject = InfraObjectItem;

                    //client.MonitorPostgre9XComponent();
                    
                }

            }
            else
            {
               
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor Server Job ");
            }
        }
        public void MonitorPostgreComponent(InfraObject infraObject)
        {
            try
            {
                using (var client = new Postgres9XClient(infraObject))
                {
                    Logger.Info("Postgres9XClient InfraObject Name:" + infraObject.Name.ToString() +
                                    " Started Time" + DateTime.Now.ToString());
                    client.MonitorPostgre9XComponent();
                    Logger.Info("MonitorPostgre9XComponent Completed");
                    Logger.Info("Postgres9XClient InfraObject Name:" + infraObject.Name.ToString() +
                                " Completed Time" + DateTime.Now.ToString());

                }

            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorPostgreComponent" + infraObject.Name.ToString() + exc.Message);
                ExceptionManager.Manage(exc, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
            catch (Exception exc)
            {
                Logger.Info("Exception:MonitorPostgreComponent" + infraObject.Name.ToString() + exc.Message);
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MonitorComponent.ToString(), infraObject.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
        }
    }
}
