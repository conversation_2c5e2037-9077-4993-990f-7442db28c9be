﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Alert", Namespace = "http://www.cp.com/types")]
    public class FastCopyMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int ScndFilesCount { get; set; }

        [DataMember]
        public int ReplicatedFilesCount { get; set; }

        [DataMember]
        public int UnchangedFilesCount { get; set; }

        [DataMember]
        public int RepFailedCount { get; set; }

        [DataMember]
        public int JobId { get; set; }

        [DataMember]
        public string SourceIP { get; set; }

        [DataMember]
        public string DestinationIP { get; set; }

        [DataMember]
        public string SourcePath { get; set; }

        [DataMember]
        public string DestinationPath { get; set; }

        [DataMember]
        public string LastFileName { get; set; }

        [DataMember]
        public string LastFileSize { get; set; }

        [DataMember]
        public string TotalFilesCount { get; set; }

        [DataMember]
        public string TotalFilesSize { get; set; }

        [DataMember]
        public string LockFilesCount { get; set; }

        [DataMember]
        public string IncrementalFilesCount { get; set; }

        [DataMember]
        public string SkippedFilesCount { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public bool IsInfraObject { get; set; }

        [DataMember]
        public string StartTime { get; set; }

        [DataMember]
        public string EndTime { get; set; }

        [DataMember]
        public string LastSuccessfullReplTime
        {
            get;
            set;
        }

        #endregion Properties
    }
}
