﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MonitorInfo", Namespace = "http://www.Bcms.com/types")]
    public class MonitorInfo
    {
        #region Properties

        public ServerStatus PRServerStatus
        {
            get;
            set;
        }

        public ServerStatus DRServerStatus
        {
            get;
            set;
        }

        public DatabaseMode PRDatabaseMode
        {
            get;
            set;
        }

        public String PRServerErrorMessage
        {
            get;
            set;
        }

        public String DRServerErrorMessage
        {
            get;
            set;
        }

        public String PRDatabaseErrorMessage
        {
            get;
            set;
        }

        public String DRDatabaseErrorMessage
        {
            get;
            set;
        }

        public ListnerStatus PRListnerStatus
        {
            get;
            set;
        }

        public ListnerStatus DRListnerStatus
        {
            get;
            set;
        }

        public String PRListnerErrorMessage
        {
            get;
            set;
        }

        public String DRListnerErrorMessage
        {
            get;
            set;
        }

        public DatabaseMode DRDatabaseMode
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        #endregion Properties
    }
}