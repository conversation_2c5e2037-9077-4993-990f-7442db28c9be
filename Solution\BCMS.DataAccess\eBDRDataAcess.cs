﻿using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using log4net;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;

namespace Bcms.DataAccess
{
    public class eBDRDataAcess
    {

        private static readonly ILog Logger = LogManager.GetLogger(typeof(eBDRDataAcess));

        protected static string GetConnectionDB(string Connection)
        {
            try
            {
                if (Connection.Equals("event"))
                {
                    string encrypt = ConfigurationManager.ConnectionStrings["EBDRConnectionEvent"].ConnectionString;

                    if (!String.IsNullOrEmpty(encrypt))
                    {
                        return (CryptographyHelper.Md5Decrypt(encrypt));
                    }
                }
                else
                {

                    string encrypt = ConfigurationManager.ConnectionStrings["EBDRConnectionReport"].ConnectionString;

                    if (!String.IsNullOrEmpty(encrypt))
                    {
                        return (CryptographyHelper.Md5Decrypt(encrypt));
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }

        public static string GetPostgressconnectionstringfromDB(string conntype)
        {
            try
            {

                string Conn = string.Empty;
                var ********sdatabase = DatabaseBaseDataAccess.GetByDatabaseType(DatabaseType.Postgres9x);

                if (********sdatabase != null)
                {
                    foreach (var item in ********sdatabase)
                    {
                        if (conntype == "event")
                        {
                            if ((item.DatabasePostgres9x.PRDatabaseName.ToLower()).Contains("event"))
                            {
                                //SERVER=localhost;Database=src_reports_db;User name=********;Password=********
                                var eventserver = ServerDataAccess.GetServersById(item.PRServerId);

                                Conn = "SERVER=" + eventserver.PRIPAddress + ";Database=" + item.DatabasePostgres9x.PRDatabaseName + ";Port=5432;User name=" + item.DatabasePostgres9x.PRUserName + ";Password=" + item.DatabasePostgres9x.PRPassword;

                                return Conn;
                            }
                        }
                        else if (conntype == "report")
                        {
                            if ((item.DatabasePostgres9x.PRDatabaseName.ToLower()).Contains("report"))
                            {
                                //SERVER=localhost;Database=src_reports_db;User name=********;Password=********
                                var reportserver = ServerDataAccess.GetServersById(item.PRServerId);

                                Conn = "SERVER=" + reportserver.PRIPAddress + ";Database=" + item.DatabasePostgres9x.PRDatabaseName + ";Port=5432;User name=" + item.DatabasePostgres9x.PRUserName + ";Password=" + item.DatabasePostgres9x.PRPassword;


                                return Conn;
                            }
                        }
                        else
                        {

                            Logger.Info("No Database Configure for Postgress.");

                            return Conn = string.Empty;

                        }


                    }
                    return "";
                }
                else
                {

                    Logger.Info("No Database Configure for Postgress.");

                    return "";

                }

            }
            catch (Exception ex)
            {
                return "";
            }

        }


        #region Workflow

        public static IList<eBDRProfile> LoadAllData()
        {
            try
            {
                IList<eBDRProfile> _EBDRProfileDetails = new List<eBDRProfile>();

                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";

                //     string ss = GetConnectionDB("report");

                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for LoadAllData is :" + ss);


                //  string ss = "SERVER=*************;Database=src_reports_db;User name=********;Password=********;SSL=false;SslMode=Disable";

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0";

                var cmmmd = new NpgsqlCommand(Query, cn);
                cn.Open();
                dr = cmmmd.ExecuteReader();
                //    dr.Read();

                while (dr.Read())
                {
                    var infraObject = new eBDRProfile();

                    //infraObject.Id = Convert.ToInt32(dr[0].ToString());
                    infraObject.Profile_Name = dr[0].ToString();
                    string Source = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32((dr[1].ToString())));

                    infraObject.Source_Host = Source;

                    string Target = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32(dr[2].ToString()));

                    infraObject.Target_Host = Target;
                    // infraObject.MachinName = (dr[3].ToString());

                    infraObject.OsType = (dr[4].ToString());

                    string stringValue = Enum.GetName(typeof(ActionTypeeBDR), Convert.ToInt32((dr[5].ToString())));
                    //   infraObject.Status = stringValue;

                    if ((dr[5].ToString()) == "36")
                    {
                        infraObject.Status = "PAUSED";
                        infraObject.Health = "UP";
                    }
                    else if ((dr[5].ToString()) == "17" || (dr[5].ToString()) == "4")
                    {
                        infraObject.Status = "Failed";
                        infraObject.Health = "Down";
                    }
                    else
                    {
                        infraObject.Status = "Running";
                        infraObject.Health = "UP";
                    }

                    infraObject.RPO = "00:00:00";

                    _EBDRProfileDetails.Add(infraObject);

                }
                return _EBDRProfileDetails;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in LoadAllData :" + ex.InnerException);
                else
                    Logger.Error("Exception in LoadAllData :" + ex.Message);
                return new List<eBDRProfile>();
            }

        }

        public static bool StarteBDRReplication(string Name)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";
                //     cn.ConnectionString = GetConnectionDB("event");  // eventDB

                //    string ss = GetPostgressconnectionstringfromDB("event");

                string ss = GetPostgressconnectionstringfromDB("event");

                Logger.Info("Connection string for StarteBDRReplication is :" + ss);

                cn.ConnectionString = ss;


                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                string query = "UPDATE status SET event=1,is_trigger=" + true + " WHERE migration_name='" + Name + "'";

                var cmmmd = new NpgsqlCommand(query, cn);

                //  var cmmmd = new NpgsqlCommand("SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports", cn);
                cn.Open();
                int isuccess = cmmmd.ExecuteNonQuery();
                cn.Close();

                return true;

            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in StarteBDRReplication :" + ex.InnerException);
                else
                    Logger.Error("Exception in StarteBDRReplication :" + ex.Message);
                return false;
            }

        }


        public static bool StopeBDRReplication(string Name)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //      cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";
                //   cn.ConnectionString = GetConnectionDB("report"); //txtreportcon.Text;

                //   cn.ConnectionString = GetPostgressconnectionstringfromDB("report");

                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for StopeBDRReplication is :" + ss);

                cn.ConnectionString = ss;


                string query = "UPDATE migration_reports SET islaunched=" + true + " WHERE migration_name='" + Name + "'";

                var cmmmd1 = new NpgsqlCommand(query, cn);

                cn.Open();
                int isuccess = cmmmd1.ExecuteNonQuery();
                cn.Close();

                return true;

            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in StopeBDRReplication :" + ex.InnerException);
                else
                    Logger.Error("Exception in StopeBDRReplication :" + ex.Message);
                return false;
            }

        }

        public static bool PauseeBDRReplication(string Name)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //    cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = GetConnectionDB("event");// txteventcon.Text;


                //       cn.ConnectionString = GetPostgressconnectionstringfromDB("event");

                string ss = GetPostgressconnectionstringfromDB("event");

                Logger.Info("Connection string for PauseeBDRReplication is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;
                //string chk = CheckPerformPause(para[i]);

                //if (!string.IsNullOrEmpty(chk))
                //{
                string query = "UPDATE status SET event=32 ,is_trigger=" + true + " WHERE migration_name='" + Name + "'";

                var cmmmd = new NpgsqlCommand(query, cn);
                cn.Open();
                int isuccess = cmmmd.ExecuteNonQuery();
                cn.Close();


                //   }
                //else
                //{
                //    MessageBox.Show("Profile : " + para[i] + " Can not Pause profile");
                //}

                return true;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in PauseeBDRReplication :" + ex.InnerException);
                else
                    Logger.Error("Exception in PauseeBDRReplication :" + ex.Message);
                return false;
            }
        }


        public static bool ResumeeBDRReplication(string Name)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //     cn.ConnectionString = GetConnectionDB("event");//txteventcon.Text;

                string ss = GetPostgressconnectionstringfromDB("event");

                Logger.Info("Connection string for ResumeeBDRReplication is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;


                int Actiondetails = CheckPerformResume(Name);


                if (Actiondetails == 36)
                {
                    string query = "UPDATE status SET event=35 ,is_trigger=" + true + " WHERE migration_name='" + Name + "'";

                    var cmmmd1 = new NpgsqlCommand(query, cn);
                    cn.Open();
                    int isuccess = cmmmd1.ExecuteNonQuery();
                    cn.Close();

                    return true;

                }
                else
                {
                    return false;
                }

            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in ResumeeBDRReplication :" + ex.InnerException);
                else
                    Logger.Error("Exception in ResumeeBDRReplication :" + ex.Message);
                return false;
            }

        }


        public static int CheckPerformResume(string para)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //   cn.ConnectionString = GetConnectionDB("report");//txtreportcon.Text;


                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for CheckPerformResume is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                int Action = 0;

                string ChkQuery = "select action FROM migration_reports where migration_name='" + para + "' and ismd=" + true + ";";
                var cmmmd = new NpgsqlCommand(ChkQuery, cn);

                cn.Open();
                dr = cmmmd.ExecuteReader();

                while (dr.Read())
                {
                    Action = Convert.ToInt32(dr[0].ToString());
                }
                cn.Close();


                return Action;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in CheckPerformResume :" + ex.InnerException);
                else
                    Logger.Error("Exception in CheckPerformResume :" + ex.Message);
                return 0;
            }

        }


        public static bool CancleeBDRReplication(string Name)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //   cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                // cn.ConnectionString = GetConnectionDB("event");//txteventcon.Text;

                string ss = GetPostgressconnectionstringfromDB("event");

                Logger.Info("Connection string for CancleeBDRReplication is :" + ss);

                cn.ConnectionString = ss;


                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                string query = "UPDATE status SET event=29 ,is_trigger=" + true + " WHERE migration_name='" + Name + "'";

                var cmmmd = new NpgsqlCommand(query, cn);

                cn.Open();
                int isuccess = cmmmd.ExecuteNonQuery();
                cn.Close();

                return true;

            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in CancleeBDRReplication :" + ex.InnerException);
                else
                    Logger.Error("Exception in CancleeBDRReplication :" + ex.Message);
                return false;
            }
        }


        #endregion

        #region Monitoring

        public static IList<eBDRProfile> GetAlleBDRProfileInfobyIPAddress(string SourceIP, string TargetIP)
        {
            try
            {
                IList<eBDRProfile> _EBDRProfileDetails = new List<eBDRProfile>();

                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";

                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for GetAlleBDRProfileInfobyIPAddress is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                //       string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0 and src_host='" + SourceIP + "' and tgt_host='" + TargetIP + "'";

                string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action,configured_rpo,last_migration_time FROM migration_reports where ismd=" + false + " and rep_id=0 and src_host='" + SourceIP + "' and tgt_host='" + TargetIP + "'";


                Logger.Info("eBDR Query for GetData from IP :" + Query);


                var cmmmd = new NpgsqlCommand(Query, cn);
                cn.Open();
                dr = cmmmd.ExecuteReader();
                //    dr.Read();

                while (dr.Read())
                {
                    var infraObject = new eBDRProfile();

                    //infraObject.Id = Convert.ToInt32(dr[0].ToString());
                    infraObject.Profile_Name = dr[0].ToString();
                    string Source = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32((dr[1].ToString())));

                    infraObject.Source_Host = Source;

                    string Target = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32(dr[2].ToString()));

                    infraObject.Target_Host = Target;
                    // infraObject.MachinName = (dr[3].ToString());

                    infraObject.OsType = (dr[4].ToString());

                    infraObject.MachinName = (dr[3].ToString());

                    string stringValue = Enum.GetName(typeof(ActionTypeeBDR), Convert.ToInt32((dr[5].ToString())));
                    infraObject.Status = stringValue;
                    if ((dr[5].ToString()) == "6" || (dr[5].ToString()) == "11" || (dr[5].ToString()) == "15" || (dr[5].ToString()) == "18")
                        infraObject.Health = "0";
                    else
                        infraObject.Health = "1";

                    //if ((dr[5].ToString()) == "36")
                    //{
                    //    infraObject.Status = "PAUSED";
                    //    infraObject.Health = "1";
                    //}
                    //else if ((dr[5].ToString()) == "17" || (dr[5].ToString()) == "4")
                    //{
                    //    infraObject.Status = "Failed";
                    //    infraObject.Health = "0";
                    //}
                    //else
                    //{
                    //    infraObject.Status = "Running";
                    //    infraObject.Health = "1";
                    //}

                    infraObject.RPO = "10";

                    string confiRPO = dr[6].ToString();
                    try
                    {

                        string CalculatedRPO = Convert.ToString(dr[7].ToString());

                        //TimeSpan t1 = new TimeSpan(0, CalculatedRPO, 0);
                        //string CalculatedRPO = "23:04:2016,09:15";


                        var date = CalculatedRPO.Split(',');

                        var getdetailsdate = date[0].Split(':');

                        var gettimedetails = date[1].Split(':');

                        DateTime dt = new DateTime(Convert.ToInt32(getdetailsdate[2]), Convert.ToInt32(getdetailsdate[1]), Convert.ToInt32(getdetailsdate[0]), Convert.ToInt32(gettimedetails[0]), 0, Convert.ToInt32(gettimedetails[1]));

                        TimeSpan ActualDatalag = (DateTime.Now - Convert.ToDateTime(dt));


                        var ActualDatalaginString = Convert.ToString(ActualDatalag);

                        if (!ActualDatalaginString.Contains("-"))
                        {
                            if (ActualDatalaginString.Contains("."))
                            {
                                if (ActualDatalag.Days == 0)
                                {
                                    var finalDatalag = ActualDatalaginString.Split('.');
                                    infraObject.Datalag = Convert.ToString(finalDatalag[0]);
                                }
                                else
                                {
                                    var finalDatalag = ActualDatalaginString.Split('.');
                                    infraObject.Datalag = Convert.ToString("" + finalDatalag[0] + ":" + finalDatalag[1] + "");
                                }
                            }
                            else
                            {
                                infraObject.Datalag = Convert.ToString(ActualDatalag);
                            }
                        }
                        else
                        {
                            infraObject.Datalag = "00:00:00";
                        }

                    }
                    catch (Exception ex) { infraObject.Datalag = "00:00:00"; }

                    _EBDRProfileDetails.Add(infraObject);

                }
                return _EBDRProfileDetails;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.InnerException);
                else
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.Message);
            }

            return new List<eBDRProfile>();

        }



        public static IList<eBDRProfile> GetAlleBDRProfileInfobyName(string Name)
        {
            try
            {
                IList<eBDRProfile> _EBDRProfileDetails = new List<eBDRProfile>();

                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";

                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for GetAlleBDRProfileInfobyIPAddress is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                //       string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0 and src_host='" + SourceIP + "' and tgt_host='" + TargetIP + "'";

                //string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action,configured_rpo,last_migration_time FROM migration_reports where ismd=" + false + " and rep_id=0 and migration_name='" + Name + "'";

                string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0 and migration_name='" + Name + "'";


                Logger.Info("eBDR Query for GetData from IP :" + Query);


                var cmmmd = new NpgsqlCommand(Query, cn);
                cn.Open();
                dr = cmmmd.ExecuteReader();
                //    dr.Read();

                while (dr.Read())
                {
                    var infraObject = new eBDRProfile();

                    //infraObject.Id = Convert.ToInt32(dr[0].ToString());
                    infraObject.Profile_Name = dr[0].ToString();
                    string Source = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32((dr[1].ToString())));

                    infraObject.Source_Host = Source;

                    string Target = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32(dr[2].ToString()));

                    infraObject.Target_Host = Target;
                    // infraObject.MachinName = (dr[3].ToString());

                    infraObject.OsType = (dr[4].ToString());

                    infraObject.MachinName = (dr[3].ToString());

                    string stringValue = Enum.GetName(typeof(ActionTypeeBDR), Convert.ToInt32((dr[5].ToString())));
                    infraObject.Status = stringValue;
                    if ((dr[5].ToString()) == "6" || (dr[5].ToString()) == "11" || (dr[5].ToString()) == "15" || (dr[5].ToString()) == "18")
                        infraObject.Health = "0";
                    else
                        infraObject.Health = "1";

                    //if ((dr[5].ToString()) == "36")
                    //{
                    //    infraObject.Status = "PAUSED";
                    //    infraObject.Health = "1";
                    //}
                    //else if ((dr[5].ToString()) == "17" || (dr[5].ToString()) == "4")
                    //{
                    //    infraObject.Status = "Failed";
                    //    infraObject.Health = "0";
                    //}
                    //else
                    //{
                    //    infraObject.Status = "Running";
                    //    infraObject.Health = "1";
                    //}

                    infraObject.RPO = "10";

                    //string confiRPO = dr[6].ToString();
                    //try
                    //{

                    //    string CalculatedRPO = Convert.ToString(dr[7].ToString());

                    //    //TimeSpan t1 = new TimeSpan(0, CalculatedRPO, 0);
                    //    //string CalculatedRPO = "23:04:2016,09:15";


                    //    var date = CalculatedRPO.Split(',');

                    //    var getdetailsdate = date[0].Split(':');

                    //    var gettimedetails = date[1].Split(':');

                    //    DateTime dt = new DateTime(Convert.ToInt32(getdetailsdate[2]), Convert.ToInt32(getdetailsdate[1]), Convert.ToInt32(getdetailsdate[0]), Convert.ToInt32(gettimedetails[0]), 0, Convert.ToInt32(gettimedetails[1]));

                    //    TimeSpan ActualDatalag = (DateTime.Now - Convert.ToDateTime(dt));


                    //    var ActualDatalaginString = Convert.ToString(ActualDatalag);

                    //    if (!ActualDatalaginString.Contains("-"))
                    //    {
                    //        if (ActualDatalaginString.Contains("."))
                    //        {
                    //            if (ActualDatalag.Days == 0)
                    //            {
                    //                var finalDatalag = ActualDatalaginString.Split('.');
                    //                infraObject.Datalag = Convert.ToString(finalDatalag[0]);
                    //            }
                    //            else
                    //            {
                    //                var finalDatalag = ActualDatalaginString.Split('.');
                    //                infraObject.Datalag = Convert.ToString("" + finalDatalag[0] + ":" + finalDatalag[1] + "");
                    //            }
                    //        }
                    //        else
                    //        {
                    //            infraObject.Datalag = Convert.ToString(ActualDatalag);
                    //        }
                    //    }
                    //    else
                    //    {
                    //        infraObject.Datalag = "00:00:00";
                    //    }

                    //}
                    //catch (Exception ex) { infraObject.Datalag = "00:00:00"; }

                    infraObject.Datalag = "00:00:00";

                    _EBDRProfileDetails.Add(infraObject);

                }
                return _EBDRProfileDetails;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.InnerException);
                else
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.Message);
            }

            return new List<eBDRProfile>();

        }


        public static IList<eBDRProfile> GetAlleBDRProfileData()
        {
            try
            {
                IList<eBDRProfile> _EBDRProfileDetails = new List<eBDRProfile>();

                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";

                string ss = GetPostgressconnectionstringfromDB("report");

                Logger.Info("Connection string for GetAlleBDRProfileInfobyIPAddress is :" + ss);

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                //       string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0 and src_host='" + SourceIP + "' and tgt_host='" + TargetIP + "'";

                string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action,configured_rpo,last_migration_time,migration_details FROM migration_reports where ismd=" + false + " and rep_id=0 ";


                Logger.Info("eBDR Query for GetData from IP :" + Query);


                var cmmmd = new NpgsqlCommand(Query, cn);
                cn.Open();
                dr = cmmmd.ExecuteReader();
                //    dr.Read();

                while (dr.Read())
                {
                    var infraObject = new eBDRProfile();

                    //infraObject.Id = Convert.ToInt32(dr[0].ToString());
                    infraObject.Profile_Name = dr[0].ToString();
                    string Source = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32((dr[1].ToString())));

                    infraObject.Source_Host = Source;

                    string Target = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32(dr[2].ToString()));

                    infraObject.Target_Host = Target;
                    // infraObject.MachinName = (dr[3].ToString());

                    infraObject.OsType = (dr[4].ToString());

                    infraObject.MachinName = (dr[3].ToString());

                    string stringValue = Enum.GetName(typeof(ActionTypeeBDR), Convert.ToInt32((dr[5].ToString())));
                    infraObject.Status = stringValue;
                    if ((dr[5].ToString()) == "6" || (dr[5].ToString()) == "11" || (dr[5].ToString()) == "15" || (dr[5].ToString()) == "18")
                        infraObject.Health = "0";
                    else
                        infraObject.Health = "1";

                    //if ((dr[5].ToString()) == "36")
                    //{
                    //    infraObject.Status = "PAUSED";
                    //    infraObject.Health = "1";
                    //}
                    //else if ((dr[5].ToString()) == "17" || (dr[5].ToString()) == "4")
                    //{
                    //    infraObject.Status = "Failed";
                    //    infraObject.Health = "0";
                    //}
                    //else
                    //{
                    //    infraObject.Status = "Running";
                    //    infraObject.Health = "1";
                    //}

                    infraObject.RPO = "10";

                    string confiRPO = dr[6].ToString();

                    infraObject.migration_details = Convert.ToString(dr[8].ToString());
                    //try
                    //{

                    //    string CalculatedRPO = Convert.ToString(dr[7].ToString());

                    //    //TimeSpan t1 = new TimeSpan(0, CalculatedRPO, 0);
                    //    //string CalculatedRPO = "23:04:2016,09:15";


                    //    var date = CalculatedRPO.Split(',');

                    //    var getdetailsdate = date[0].Split(':');

                    //    var gettimedetails = date[1].Split(':');

                    //    DateTime dt = new DateTime(Convert.ToInt32(getdetailsdate[2]), Convert.ToInt32(getdetailsdate[1]), Convert.ToInt32(getdetailsdate[0]), Convert.ToInt32(gettimedetails[0]), 0, Convert.ToInt32(gettimedetails[1]));

                    //    TimeSpan ActualDatalag = (DateTime.Now - Convert.ToDateTime(dt));


                    //    var ActualDatalaginString = Convert.ToString(ActualDatalag);

                    //    if (!ActualDatalaginString.Contains("-"))
                    //    {
                    //        if (ActualDatalaginString.Contains("."))
                    //        {
                    //            if (ActualDatalag.Days == 0)
                    //            {
                    //                var finalDatalag = ActualDatalaginString.Split('.');
                    //                infraObject.Datalag = Convert.ToString(finalDatalag[0]);
                    //            }
                    //            else
                    //            {
                    //                var finalDatalag = ActualDatalaginString.Split('.');
                    //                infraObject.Datalag = Convert.ToString("" + finalDatalag[0] + ":" + finalDatalag[1] + "");
                    //            }
                    //        }
                    //        else
                    //        {
                    //            infraObject.Datalag = Convert.ToString(ActualDatalag);
                    //        }
                    //    }
                    //    else
                    //    {
                    //        infraObject.Datalag = "00:00:00";
                    //    }

                    //}
                    //catch (Exception ex) { infraObject.Datalag = "00:00:00"; }

                    _EBDRProfileDetails.Add(infraObject);

                }
                return _EBDRProfileDetails;
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.InnerException);
                else
                    Logger.Error("Exception in GetAlleBDRProfileInfobyIPAddress :" + ex.Message);
            }

            return new List<eBDRProfile>();

        }


        #endregion

    }
}
