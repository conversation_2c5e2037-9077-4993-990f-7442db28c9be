﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAActionAnalyaticDataAccess : BaseDataAccess
    {
        /// <summary>
        /// Retrive data from live table to insert into BIA table 
        /// </summary>
        /// <returns></returns>
        public static IList<BIAActionAnalytic> GetAllBIAParraleWorkfActionResultsAction()
        {
            IList<BIAActionAnalytic> BIAActionAnalytic = new List<BIAActionAnalytic>();

            const string sp = "BIAParaWorkActionRes_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {
                    //dbCommand.CommandTimeout=100000;
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));

                    
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var _BIAActionAnalytic = new BIAActionAnalytic();


                            _BIAActionAnalytic.ActionId = Convert.IsDBNull(reader["ACTIONID"]) ? 0 : Convert.ToInt32(reader["ACTIONID"]);
                            _BIAActionAnalytic.ActionName = Convert.IsDBNull(reader["ACTIONNAME"]) ? string.Empty : Convert.ToString(reader["ACTIONNAME"]);
                            _BIAActionAnalytic.ActionType = Convert.IsDBNull(reader["ACTIONTYPE"]) ? 0 : Convert.ToInt32(reader["ACTIONTYPE"]);
                            _BIAActionAnalytic.Total = Convert.IsDBNull(reader["TOTAL"]) ? 0 : Convert.ToInt32(reader["TOTAL"]);
                            _BIAActionAnalytic.Skip = Convert.IsDBNull(reader["SKIP1"]) ? 0 : Convert.ToInt32(reader["SKIP1"]);
                            _BIAActionAnalytic.Retry = Convert.IsDBNull(reader["RETRY"]) ? 0 : Convert.ToInt32(reader["RETRY"]);
                            _BIAActionAnalytic.Abort = Convert.IsDBNull(reader["ABORT1"]) ? 0 : Convert.ToInt32(reader["ABORT1"]);
                            _BIAActionAnalytic.Success = Convert.IsDBNull(reader["SUCCESS"]) ? 0 : Convert.ToInt32(reader["SUCCESS"]);
                            _BIAActionAnalytic.ActionConfigRTO = Convert.IsDBNull(reader["ActionConfigRTO"]) ? 0 : Convert.ToInt32(reader["ActionConfigRTO"]);
                            //_BIAActionAnalytic.Error = Convert.IsDBNull(reader["Error1"]) ? 0 : Convert.ToInt32(reader["Error1"]);
                            _BIAActionAnalytic.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                            _BIAActionAnalytic.CompletedOutOFrto = Convert.IsDBNull(reader["CompletedOutOFrto"]) ? 0 : Convert.ToInt32(reader["CompletedOutOFrto"]);
                            _BIAActionAnalytic.Completedwithinrto = Convert.IsDBNull(reader["Completedwithinrto"]) ? 0 : Convert.ToInt32(reader["Completedwithinrto"]);
                            _BIAActionAnalytic.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);

                            BIAActionAnalytic.Add(_BIAActionAnalytic);


                        }
                    }
                }
                return BIAActionAnalytic;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Action Analytic in GetAllBIAParraleWorkfActionResultsAction", exc);

            }
        }

        public static IList<BIAActionAnalytic> GetAllBIAActionAnalyatic()
        {
            IList<BIAActionAnalytic> BIAActionAnalytic = new List<BIAActionAnalytic>();

            const string sp = "ActionAnalytic_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var _BIAActionAnalytic = new BIAActionAnalytic();

                            _BIAActionAnalytic.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["ID"]);
                            _BIAActionAnalytic.ActionId = Convert.IsDBNull(reader["ACTIONID"]) ? 0 : Convert.ToInt32(reader["ACTIONID"]);
                            _BIAActionAnalytic.ActionName = Convert.IsDBNull(reader["ACTIONNAME"]) ? string.Empty : Convert.ToString(reader["ACTIONNAME"]);
                            _BIAActionAnalytic.ActionType = Convert.IsDBNull(reader["ACTIONTYPE"]) ? 0 : Convert.ToInt32(reader["ACTIONTYPE"]);
                            _BIAActionAnalytic.Total = Convert.IsDBNull(reader["TOTAL"]) ? 0 : Convert.ToInt32(reader["TOTAL"]);
                            _BIAActionAnalytic.Skip = Convert.IsDBNull(reader["SKIP"]) ? 0 : Convert.ToInt32(reader["SKIP"]);
                            _BIAActionAnalytic.Retry = Convert.IsDBNull(reader["RETRY"]) ? 0 : Convert.ToInt32(reader["RETRY"]);
                            _BIAActionAnalytic.Abort = Convert.IsDBNull(reader["ABORT"]) ? 0 : Convert.ToInt32(reader["ABORT"]);
                            _BIAActionAnalytic.Success = Convert.IsDBNull(reader["SUCCESS"]) ? 0 : Convert.ToInt32(reader["SUCCESS"]);
                            _BIAActionAnalytic.Error = Convert.IsDBNull(reader["Error"]) ? 0 : Convert.ToInt32(reader["Error"]);
                            _BIAActionAnalytic.ActionConfigRTO = Convert.IsDBNull(reader["ActionConfigRTO"]) ? 0 : Convert.ToInt32(reader["ActionConfigRTO"]);
                            _BIAActionAnalytic.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                            _BIAActionAnalytic.CompletedOutOFrto = Convert.IsDBNull(reader["CompletedOutOFrto"]) ? 0 : Convert.ToInt32(reader["CompletedOutOFrto"]);
                            _BIAActionAnalytic.Completedwithinrto = Convert.IsDBNull(reader["Completedwithinrto"]) ? 0 : Convert.ToInt32(reader["Completedwithinrto"]);
                            _BIAActionAnalytic.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                            BIAActionAnalytic.Add(_BIAActionAnalytic);
                        }
                    }
                }
                return BIAActionAnalytic;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }

        // for update  IsAnalyze Status in Parallel Workflow Action Result table
        public static void UpdateAnalyzeStatusParallelWorkflowActionResult()
        {

            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("BIAParWrkActRes_update"))
                {
                    int isuccess = Database.ExecuteNonQuery(cmd);
                }
            }

            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Updateting the BIA IsAnalyzed Status of parallelWorkflow Action result in UpdateAnalyzeStatusParallelWorkflowActionResult", exc);

            }


        }

        // insert records into  Action_analytic BIA table
        public static void AddActionAnalytic(IList<BIAActionAnalytic> AllActionAnalytic)
        {
            try
            {
                foreach (var Action_Analytic in AllActionAnalytic)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("BIAActionAnalytic_Create"))
                    {
                        Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, Action_Analytic.ActionId);
                        Database.AddInParameter(cmd, Dbstring + "iActionName", DbType.AnsiString, Action_Analytic.ActionName == null ? string.Empty : Action_Analytic.ActionName);
                        Database.AddInParameter(cmd, Dbstring + "iActionType", DbType.Int32, Action_Analytic.ActionType);
                        Database.AddInParameter(cmd, Dbstring + "iActionConfigRTO", DbType.Int32, Action_Analytic.ActionConfigRTO);
                        Database.AddInParameter(cmd, Dbstring + "iAbort", DbType.Int32, Action_Analytic.Abort);
                        Database.AddInParameter(cmd, Dbstring + "iSkip", DbType.Int32, Action_Analytic.Skip);
                        Database.AddInParameter(cmd, Dbstring + "iRetry", DbType.Int32, Action_Analytic.Retry);
                        Database.AddInParameter(cmd, Dbstring + "iSuccess", DbType.Int32, Action_Analytic.Success);
                        Database.AddInParameter(cmd, Dbstring + "iTotal", DbType.Int32, Action_Analytic.Total);
                        //Database.AddInParameter(cmd, Dbstring + "iError", DbType.Int32, Action_Analytic.Error);
                        Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, Action_Analytic.Type);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedOutOFrto", DbType.Int32, Action_Analytic.CompletedOutOFrto);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedwithinrto", DbType.Int32, Action_Analytic.Completedwithinrto);
                        Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, Action_Analytic.InfraobjectId);

                        int value = Database.ExecuteNonQuery(cmd);

                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }


        }

        /// <summary>
        /// Update Action_Analytic BIA table.
        /// </summary>
        /// <param name="lstActionAnaUpdate"></param>
        public static void UpdateActionAnalytic(List<BIAActionAnalytic> lstActionAnaUpdate)
        {
            try
            {

                foreach (var Action_Analytic in lstActionAnaUpdate)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("BIAActionAnalytic_Update"))
                    {

                        Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, Action_Analytic.ActionId);
                        Database.AddInParameter(cmd, Dbstring + "iTotal", DbType.Int32, Action_Analytic.Total);
                        Database.AddInParameter(cmd, Dbstring + "iSuccess", DbType.Int32, Action_Analytic.Success);
                        //Database.AddInParameter(cmd, Dbstring + "iError", DbType.Int32, Action_Analytic.Error);
                        Database.AddInParameter(cmd, Dbstring + "iAbort", DbType.Int32, Action_Analytic.Abort);
                        Database.AddInParameter(cmd, Dbstring + "iSkip", DbType.Int32, Action_Analytic.Skip);
                        Database.AddInParameter(cmd, Dbstring + "iRetry", DbType.Int32, Action_Analytic.Retry);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedOutOFrto", DbType.Int32, Action_Analytic.CompletedOutOFrto);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedwithinrto", DbType.Int32, Action_Analytic.Completedwithinrto);

                        int value = Database.ExecuteNonQuery(cmd);

                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Updateting the BIA IsAnalyzed Status in ParalleGroupWorkflow in UpdateAnalyzeStatusParallelGroupWorkflow", exc);

            }
        }
    }
}
