﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Core;
using Bcms.Core.Client;
using log4net;
using BCMS.Common;

namespace Bcms.Core.Job
{
    public class SybaseMonitoringJob : IJob
    {
        public const string InfraObject = "InfraObject";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(SybaseMonitoringJob));

        #region IJob Members

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var infraObject = data.Get(InfraObject) as InfraObject;
            if (infraObject != null)
            {
                InfraObject InfraObjectItem = InfraObjectDataAccess.GetInfraObjectById(infraObject.Id);

                if (InfraObjectItem != null)
                {
                    if (InfraObjectItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : InfraObject is not in active mode, skip the monitor components job cycle ", InfraObjectItem.Name);

                        return;
                    }
                    MonitorSybase(InfraObjectItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor Server Job ");
            }
        }

        public void MonitorSybase(InfraObject infraObject)
        {
            try
            {
                using (var client = new SybaseClient(infraObject))
                {
                    Logger.Info("Sybase InfraObject Name: " + infraObject.Name.ToString() +
                                   " Started Time " + DateTime.Now.ToString());
                    SyBaseMonitor _syBaseGetMonitorDetailsForCheckGen = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                    if (_syBaseGetMonitorDetailsForCheckGen.IsApplied == 3 || _syBaseGetMonitorDetailsForCheckGen.IsApplied == 0)
                    {
                        client.SybaseGenerateLog();

                        //SyBaseMonitor _syBaseGetMonitorDetails = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                        //if (_syBaseGetMonitorDetails.IsApplied == 1)
                        //{
                        //    client.SybaseDBReplication();
                        //    SyBaseMonitor _syBaseGetMonitorDetailsForCheck = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                        //    if (_syBaseGetMonitorDetailsForCheck.IsApplied == 2)
                        //    {
                        //        client.SybaseRestoreLog();
                        //    }
                        //}
                    }
                    SyBaseMonitor _syBaseGetMonitorDetails = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                    if (_syBaseGetMonitorDetails.IsApplied == 1)
                    {
                        client.SybaseDBReplication();
                    }
                    SyBaseMonitor _syBaseGetMonitorDetailsForCheck = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                    if (_syBaseGetMonitorDetailsForCheck.IsApplied == 2)
                    {
                        client.SybaseRestoreLog();
                    }
                    Logger.Info("MonitorSybaseComponent Completed");
                    Logger.Info("SybaseClient InfraObject Name: " + infraObject.Name.ToString() +
                                " Completed Time " + DateTime.Now.ToString());
                }
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorSybaseMirroringComponent " + infraObject.Name.ToString() + exc.Message);
                ExceptionManager.Manage(exc, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
            catch (Exception exc)
            {
                Logger.Info("Exception:MonitorSybaseMirroringComponent " + infraObject.Name.ToString() + exc.Message);
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MonitorComponent.ToString(), infraObject.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
        }

        #endregion
    }
}
