﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E8CB1405-A5F6-4A2C-AF32-46301ED4DF0C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Bcms.DataAccess</RootNamespace>
    <AssemblyName>Bcms.DataAccess</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;MSSQL</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <GenerateSerializationAssemblies>Auto</GenerateSerializationAssemblies>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Bcms.Common">
      <HintPath>..\BCMS.Common\bin\Release\Bcms.Common.dll</HintPath>
    </Reference>
    <Reference Include="Bcms.ExceptionHandler">
      <HintPath>..\BCMS.ExceptionHandler\bin\Release\Bcms.ExceptionHandler.dll</HintPath>
    </Reference>
    <Reference Include="Bcms.Helper">
      <HintPath>..\BCMS.Helper\bin\Release\Bcms.Helper.dll</HintPath>
    </Reference>
    <Reference Include="CMDExec">
      <HintPath>..\..\Thirdparty\TPAM\CMDExec.dll</HintPath>
    </Reference>
    <Reference Include="CyberArkLib">
      <HintPath>..\..\Thirdparty\CyberArk\CyberArkLib.dll</HintPath>
    </Reference>
    <Reference Include="Devart.Data">
      <HintPath>..\..\Thirdparty\Devart\Devart.Data.dll</HintPath>
    </Reference>
    <Reference Include="Devart.Data.Oracle">
      <HintPath>..\..\Thirdparty\Devart\Devart.Data.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=1.2.9.0, Culture=neutral, PublicKeyToken=b32731d11ce58905">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Log4Net\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=4.1.0.0, Culture=neutral, PublicKeyToken=e44a2bc38ed2c13c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data, Version=3.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ObjectBuilder">
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.ObjectBuilder.dll</HintPath>
    </Reference>
    <Reference Include="NetPasswordSDK, Version=7.20.110.0, Culture=neutral, PublicKeyToken=40be1dbc8718670f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\CyberArk\NetPasswordSDK.dll</HintPath>
    </Reference>
    <Reference Include="npgsql">
      <HintPath>..\..\Thirdparty\Postgress\npgsql.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionExecutionDataAccess.cs" />
    <Compile Include="ActiveODGMonitorDataAccess.cs" />
    <Compile Include="AlertDataAccess.cs" />
    <Compile Include="ApplicationDataAccess.cs" />
    <Compile Include="ApplicationDetailsDataAccess.cs" />
    <Compile Include="ApplicationDiscoveryDataAccess.cs" />
    <Compile Include="ApplicationDiscoveryProfileDetailsDataAccess.cs" />
    <Compile Include="ApplicationGroupDataAccess.cs" />
    <Compile Include="ApplicationGroupHealthDataAccess.cs" />
    <Compile Include="ApplicationMonitorDataAccess.cs" />
    <Compile Include="ApplicationServiceDataAccess.cs" />
    <Compile Include="ARCOSAPI001.cs" />
    <Compile Include="Base\BaseDataAccess.cs" />
    <Compile Include="BIAActionAnalyaticDataAccess.cs" />
    <Compile Include="BIAActionTrendDataDataAccess.cs" />
    <Compile Include="BIAAlertAnalyticsDataAccess.cs" />
    <Compile Include="BIAWorkflowAnalyaticDataAccess.cs" />
    <Compile Include="BIAWorkflowAndActionIdDataAcces.cs" />
    <Compile Include="BIAWorkflowTrendDataDataAccess.cs" />
    <Compile Include="BPAutomationdDataAccess.cs" />
    <Compile Include="BusinessFunctionBIADataAccess.cs" />
    <Compile Include="BusinessFunctionDataAccess.cs" />
    <Compile Include="BusinessServiceDataAccess.cs" />
    <Compile Include="BusinessServiceRPOInfoDataAccess.cs" />
    <Compile Include="BusinessServiceRTOInfoDataAccess.cs" />
    <Compile Include="CPSLScriptDataAcess.cs" />
    <Compile Include="CPSL_schedulerDataAcess.cs" />
    <Compile Include="DRNetDataAccess.cs" />
    <Compile Include="DRReadyDataAccess.cs" />
    <Compile Include="eBDRDataAcess.cs" />
    <Compile Include="eBDRStatusLogs.cs" />
    <Compile Include="EC2S3DatasyncDataAccess.cs" />
    <Compile Include="DiscoverHostDataAccess.cs" />
    <Compile Include="EventManagementDataAccess.cs" />
    <Compile Include="ExchangeDAG2013DataAccess.cs" />
    <Compile Include="ExchangeDAGCompMonitorDataAccess.cs" />
    <Compile Include="ExchangeDAGReplcationMonitorDataAccess.cs" />
    <Compile Include="ExchangeDAGReplHealthStatusDataAccess.cs" />
    <Compile Include="FastCopyMonitorDataAccess.cs" />
    <Compile Include="GroupJobInfoDataAccess.cs" />
    <Compile Include="HyperVDataAccess.cs" />
    <Compile Include="IncidentManagementDataAccess.cs" />
    <Compile Include="InfraobjectDiskMonitorDataAccess.cs" />
    <Compile Include="InfraobjectSchedularStatusDataAccess.cs" />
    <Compile Include="InfraobjectSchedularWorkflowStatusDataAccess.cs" />
    <Compile Include="Infraobject_SchedulerDataAccess.cs" />
    <Compile Include="InfrastructureDataAccess.cs" />
    <Compile Include="MAXDBMonitoringDataAccess.cs" />
    <Compile Include="MimixMonitorDataAccess.cs" />
    <Compile Include="MSSQLAlwaysonMonitorDataAccess.cs" />
    <Compile Include="MSSQLDBMirroringDataAccess.cs" />
    <Compile Include="MSSqlDoubletekRepliMonitorDataAccess.cs" />
    <Compile Include="MSSQLEmcsrdfDataAccess.cs" />
    <Compile Include="DatabaseBackupInfoDataAccess.cs" />
    <Compile Include="DatabaseBackupOperationDataAccess.cs" />
    <Compile Include="DatabaseBaseDataAccess.cs" />
    <Compile Include="DatabaseNodesDataAccess.cs" />
    <Compile Include="DataLagDataAccess.cs" />
    <Compile Include="DROperationDataAccess.cs" />
    <Compile Include="DROperationResultDataAccess.cs" />
    <Compile Include="EmcReplicationDataAccess.cs" />
    <Compile Include="ExchageSCRServiceStatusDataAccess.cs" />
    <Compile Include="ExchangeHealthDataAccess.cs" />
    <Compile Include="ExchangeReplicationDataAccess.cs" />
    <Compile Include="ExchangeSCRStatusDataAccess.cs" />
    <Compile Include="FastCopyDataAccess.cs" />
    <Compile Include="FastCopyJobDataAccess.cs" />
    <Compile Include="GlobalMirrorDataAccess.cs" />
    <Compile Include="GlobalMirrorLunsDataAccess.cs" />
    <Compile Include="GlobalMirrorMonitorDataAccess.cs" />
    <Compile Include="GroupDatabaseNodesDataAccess.cs" />
    <Compile Include="GroupLunsDataAccess.cs" />
    <Compile Include="GroupWorkflowDataAccess.cs" />
    <Compile Include="HADRDataAccess.cs" />
    <Compile Include="HADRReplicationDataAccess.cs" />
    <Compile Include="HeatmapDataAccess.cs" />
    <Compile Include="HitachiURDeviceMonitoringStatusDataAccess.cs" />
    <Compile Include="HitachiURMonitoringStatusDataAccess.cs" />
    <Compile Include="ImpactAnalysisDataAccess.cs" />
    <Compile Include="InfraObjectDataAccess.cs" />
    <Compile Include="LogVolumeDataAccess.cs" />
    <Compile Include="MonitorServiceDataAccess.cs" />
    <Compile Include="MountInfoDataAccess.cs" />
    <Compile Include="MSSQLMirroringDataAccess.cs" />
    <Compile Include="MSsqlnativeDataAccess.cs" />
    <Compile Include="MySqlMonitorDataAccess.cs" />
    <Compile Include="MysqlNativeMonitorDataAccess.cs" />
    <Compile Include="NetworkIPDataAccess.cs" />
    <Compile Include="DataGuardMonitorDataAccess.cs" />
    <Compile Include="NodesDataAccess.cs" />
    <Compile Include="NodeSubstituteAuthenticationDataAccess.cs" />
    <Compile Include="ParallelDROperationDataAccess.cs" />
    <Compile Include="ParallelGroupWorkflowDataAccess.cs" />
    <Compile Include="ParallelServerDataAccess.cs" />
    <Compile Include="ParallelWorkflowActionResultDataAccess.cs" />
    <Compile Include="PCPLDataAccess.cs" />
    <Compile Include="Postgre9xDataAccess.cs" />
    <Compile Include="PostgreDataAccess.cs" />
    <Compile Include="RecoverPStatisticMonitorDataAccess.cs" />
    <Compile Include="RecoveryPointDataAccess.cs" />
    <Compile Include="RecoveryPStateMonitorDataAccess.cs" />
    <Compile Include="ReplicationBaseDataAccess.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RPOTimeSpanDataAccess.cs" />
    <Compile Include="ScheduleDiscoveryProfDetailsDataAccess.cs" />
    <Compile Include="ServerDataAccess.cs" />
    <Compile Include="ServiceAvailabilityDataAccess.cs" />
    <Compile Include="SingleSignOnDataAccess.cs" />
    <Compile Include="SnapMirrorDataAccess.cs" />
    <Compile Include="SnapMirrorReplicationDataAccess.cs" />
    <Compile Include="SqlLogDataAccess.cs" />
    <Compile Include="SqlLogNativeDataAccess.cs" />
    <Compile Include="SqlNative2008HealthDataAccess.cs" />
    <Compile Include="SqlNativeHealthDataAccess.cs" />
    <Compile Include="SqlNativeMonitorStatus2008DataAccess.cs" />
    <Compile Include="SqlNativeServiceDataAccess.cs" />
    <Compile Include="SRMVmwareMonitorDataAccess.cs" />
    <Compile Include="SVCControllerDataAccess.cs" />
    <Compile Include="SVCGlobalMirrorMonitorDataAccess.cs" />
    <Compile Include="SVCGlobalMirrorReplicationMonitorDataAccess.cs" />
    <Compile Include="SVCNodedetailedMonitorDataAccess.cs" />
    <Compile Include="SybaseStatusDataAccess.cs" />
    <Compile Include="SybaseWithSRSDataAccess.cs" />
    <Compile Include="TPCRMonitorDataAccess.cs" />
    <Compile Include="TPCRReplicationDataAccess.cs" />
    <Compile Include="Utility\StringHelper.cs" />
    <Compile Include="VcenterProfileDataAccess.cs" />
    <Compile Include="VmWareDataAccess.cs" />
    <Compile Include="Web References\ARCOSAPIOnline\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="WorkflowActionDataAccess.cs" />
    <Compile Include="WorkflowDataAccess.cs" />
    <Compile Include="XIVCGMonitoringDataAccess.cs" />
    <Compile Include="XIVCGVolumeMoniDataAccess.cs" />
    <Compile Include="XIVClustorGroupInfoDataAccess.cs" />
    <Compile Include="XIVMirrorStatisticsDataAccess.cs" />
    <Compile Include="XIVReplicationMoniDataAccess.cs" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://120.63.158.229:10185/ARCOSAPI001.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\ARCOSAPIOnline\</RelPath>
      <UpdateFromURL>http://120.63.158.229:10185/ARCOSAPI001.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>
      </CachedAppSettingsObjectName>
      <CachedSettingsPropName>
      </CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\ARCOSAPIOnline\ARCOSAPI001.disco" />
    <None Include="Web References\ARCOSAPIOnline\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>