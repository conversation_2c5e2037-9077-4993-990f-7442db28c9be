﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class DRReadyDataAccess : BaseDataAccess  
    {
        public static bool AddDRReady(DRReady DRReady)
        {
            try
            {
                const string sp = "INFRA_DRREADY_LOGSCREATE";
                // var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iINFRAOBJECTID", DbType.Int32, DRReady.INFRAOBJECTID);
                    Database.AddInParameter(dbCommand, Dbstring + "iWORKFLOWID", DbType.Int32, DRReady.WORKFLOWID);
                    Database.AddInParameter(dbCommand, Dbstring + "iLOGSCHEDULERID", DbType.Int32, DRReady.LOGSCHEDULERID);
                    Database.AddInParameter(dbCommand, Dbstring + "iISDRREADY", DbType.Int32, DRReady.ISDRREADY);
                    Database.AddInParameter(dbCommand, Dbstring + "iREASON", DbType.AnsiString, DRReady.REASON);
                    Database.AddInParameter(dbCommand, Dbstring + "iCREATORID", DbType.Int32, DRReady.CreatorId);
                    Database.AddInParameter(dbCommand, Dbstring + "iSTARTTIME", DbType.DateTime, DRReady.StartTime);
                    Database.AddInParameter(dbCommand, Dbstring + "iENDTIME", DbType.DateTime, DRReady.EndTime);
                    Database.AddInParameter(dbCommand, Dbstring + "iACTIONID", DbType.Int32, DRReady.ActionID);
                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while  Add DR Ready information", exc);
            }
            return false;
        }
    }
}
