﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ApplicationMonitorDataAccess : BaseDataAccess
    {
        public static bool AddApplicationMonitor(ApplicationMonitor applicationDetail)
        {
            try
            {

                //var db = DatabaseFactory.CreateDatabase();
                const string sp = "AppMonitorstatus_Create";

                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectName", DbType.AnsiString, applicationDetail.InfraobjectName);
                    Database.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.AnsiString, applicationDetail.Status);
                    Database.AddInParameter(dbCommand, Dbstring+"iBusinessServiceId", DbType.Int32, applicationDetail.BusinessServiceId);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, applicationDetail.InfraobjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iAppType",  DbType.AnsiString, applicationDetail.AppType);
                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ApplicationMonitorstatus information", exc);
            }
            return false;
        }
    }
}
