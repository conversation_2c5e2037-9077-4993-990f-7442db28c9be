﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GroupWorkflow", Namespace = "http://www.BCMS.com/types")]
    public class GroupWorkflow : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        [DataMember]
        public int WorkflowId
        {
            get;
            set;
        }
        [DataMember]
        public int ActionType
        {
            get;
            set;
        }

        [DataMember]
        public string TotalRTO
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public GroupWorkflow()
            : base()
        {
        }

        #endregion
    }
}