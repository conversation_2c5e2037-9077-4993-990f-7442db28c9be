﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "hypervclustersummary", Namespace = "http://www.BCMS.com/types")]
    public class hypervclustersummary : BaseEntity
    {
        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRClusterName
        {
            get;
            set;
        }

        [DataMember]
        public string DRClusterName
        {
            get;
            set;
        }

        [DataMember]
        public string PROwnerIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string DROwnerIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string PROwnerNode
        {
            get;
            set;
        }

        [DataMember]
        public string DROwnerNode
        {
            get;
            set;
        }

        [DataMember]
        public string PRBrokerName
        {
            get;
            set;
        }

        [DataMember]
        public string DRBrokerName
        {
            get;
            set;
        }

        [DataMember]
        public string PRBrokerState
        {
            get;
            set;
        }

        [DataMember]
        public string DRBrokerState
        {
            get;
            set;
        }


        [DataMember]
        public string PRBrokerIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string DRBrokerIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string PRBrokerNode
        {
            get;
            set;
        }

        [DataMember]
        public string DRBrokerNode
        {
            get;
            set;
        }

        [DataMember]
        public string PROwnerNodeList
        {
            get;
            set;
        }

        [DataMember]
        public string DROwnerNodeList
        {
            get;
            set;
        }

        [DataMember]
        public string PRNetworkIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string DRNetworkIPAddress
        {
            get;
            set;
        }


    }
}
