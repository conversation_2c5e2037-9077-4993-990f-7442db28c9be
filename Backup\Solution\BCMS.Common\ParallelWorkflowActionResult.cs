﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ParallelWorkflowActionResult", Namespace = "http://www.BCMS.com/types")]
    public class ParallelWorkflowActionResult : BaseEntity
    {
        #region Properties

        [DataMember]
        public string WorkflowActionName
        {
            get;
            set;
        }

        [DataMember]
        public DateTime StartTime
        {
            get;
            set;
        }

        [DataMember]
        public DateTime EndTime
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string Message
        {
            get;
            set;
        }

        [DataMember]
        public int ParallelDROperationId
        {
            get;
            set;
        }

        [DataMember]
        public int ParallelGroupWorkflowId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int ConditionActionId
        {
            get;
            set;
        }

        [DataMember]
        public bool SkipStep
        {
            get;
            set;
        }

        [DataMember]
        public string Direction
        {
            get;
            set;
        }

        [DataMember]
        public int ActionId
        {
            get;
            set;
        }
        #endregion

        #region Constructor

        public ParallelWorkflowActionResult()
            : base()
        {
        }

        #endregion
    }
}