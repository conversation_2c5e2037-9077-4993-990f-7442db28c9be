﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
  public class XIVMirrorStatisticsDataAccess : BaseDataAccess
    {
      public static bool AddMirrorStatisticsMoniLogDetails(XIVMirrorStatisticsLog XIVMirrorStatisticsLog)
      {
          try
          {
              using (DbCommand cmd = Database.GetStoredProcCommand("XIVStatistic_Log_CREATE"))
              {
                  Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, XIVMirrorStatisticsLog.InfraobjectId);
                  Database.AddInParameter(cmd, Dbstring + "iCGId", DbType.Int32, XIVMirrorStatisticsLog.CGId);
                  Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, XIVMirrorStatisticsLog.CGName);
                  Database.AddInParameter(cmd, Dbstring + "iCGCreatedate", DbType.AnsiString, XIVMirrorStatisticsLog.CGCreatedate);
                  Database.AddInParameter(cmd, Dbstring + "iCGStartedDate", DbType.AnsiString, XIVMirrorStatisticsLog.CGStartedDate);
                  Database.AddInParameter(cmd, Dbstring + "iCGFinishDate", DbType.AnsiString, XIVMirrorStatisticsLog.CGFinishDate);
                  Database.AddInParameter(cmd, Dbstring + "iJobSize", DbType.AnsiString, XIVMirrorStatisticsLog.JobSize);
                  Database.AddInParameter(cmd, Dbstring + "iJobDuration", DbType.AnsiString, XIVMirrorStatisticsLog.JobDuration);
                  Database.AddInParameter(cmd, Dbstring + "iAverageSyncRole", DbType.AnsiString, XIVMirrorStatisticsLog.AverageSyncRole);
                  Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, XIVMirrorStatisticsLog.DataLag);                


                  int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                  if (value > 0)
                  {
                      return true;
                  }

              }

          }
          catch (Exception exc)
          {
              throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Mirror Statistics Detailed Monitor Logs InfraObject Id - " + XIVMirrorStatisticsLog.InfraobjectId, exc);

          }

          return false;
      }
    }
}
