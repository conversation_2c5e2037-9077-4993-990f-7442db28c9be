﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Utility;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using BCMS.Common;

namespace Bcms.DataAccess
{
    public class SqlNativeMonitorStatus2008DataAccess : BaseDataAccess
    {
        public static bool AddSqlNativeLogDetails(SQLNative2008Monitor sqllognative)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEMONITORLOGS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqllognative.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRServer", DbType.AnsiString, sqllognative.PRServer);
                    Database.AddInParameter(cmd, Dbstring + "iDRServer", DbType.AnsiString, sqllognative.DRServer);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseName", DbType.AnsiString, sqllognative.PRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iLast_GenLog", DbType.AnsiString, sqllognative.LastGenLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_ApplyLog", DbType.AnsiString, sqllognative.LastApplyLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_CopiedLog", DbType.AnsiString, sqllognative.LastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_last_backupLog", DbType.AnsiString, sqllognative.LSNLastBackupLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_last_restoredLog", DbType.AnsiString, sqllognative.LSNLastRestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_Last_CopiedLog", DbType.AnsiString, sqllognative.LSNLastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Gen_Time", DbType.AnsiString, sqllognative.LastLogGenTime);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Appl_Time", DbType.AnsiString, sqllognative.LastLogApplTime);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Copy_Time", DbType.AnsiString, sqllognative.LastLogCopyTime);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, Convert.ToString(sqllognative.DataLag_HHMMSS));
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddSqlNativeLogDetails entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNativeLogStatusDetails(SQLNative2008Monitor sqllognative)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEMONITORSTATUS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqllognative.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRServer", DbType.AnsiString, sqllognative.PRServer);
                    Database.AddInParameter(cmd, Dbstring + "iDRServer", DbType.AnsiString, sqllognative.DRServer);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseName", DbType.AnsiString, sqllognative.PRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iLast_GenLog", DbType.AnsiString, sqllognative.LastGenLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_ApplyLog", DbType.AnsiString, sqllognative.LastApplyLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_CopiedLog", DbType.AnsiString, sqllognative.LastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_last_backupLog", DbType.AnsiString, sqllognative.LSNLastBackupLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_last_restoredLog", DbType.AnsiString, sqllognative.LSNLastRestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iLSN_Last_CopiedLog", DbType.AnsiString, sqllognative.LSNLastCopiedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Gen_Time", DbType.AnsiString, sqllognative.LastLogGenTime);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Appl_Time", DbType.AnsiString, sqllognative.LastLogApplTime);
                    Database.AddInParameter(cmd, Dbstring + "iLast_Log_Copy_Time", DbType.AnsiString, sqllognative.LastLogCopyTime);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString,Convert.ToString(sqllognative.DataLag_HHMMSS));
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting UpdateSqlNativeLogDetails entry ", exc);
            }
            return false;
        }

        public static string GetlogbyId(int groupId)
        {
            string logFileName = string.Empty;
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SQLNATIVE_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySqlLogReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySqlLogReader.Read())
                        {
                            logFileName = mySqlLogReader[0].ToString();
                        }
                    }
                }
                return logFileName;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while while executing function signature GetlogbyId for group Id(" + groupId + ")", exc);

            }
        }

    }
}