﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Nodes", Namespace = "http://www.Bcms.com/types")]
   public class Nodes : BaseEntity
    {
        public Nodes()
            : base()
        {
        }

        [DataMember]
        public string Name
        {
            get;
            set;
        }
        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        public string ArchieveLogPath
        {
            get;
            set;
        }
        [DataMember]
        public int IsPrimary
        {
            get;
            set;
        }
        [DataMember]
        public int IsConfigPath
        {
            get;
            set;
        }
        [DataMember]
        public string TargetArchieveLogPath
        {
            get;
            set;
        }
        [DataMember]
        public string OracleSID
        {
            get;
            set;
        }

        [DataMember]
        public string InstanceName
        {
            get;
            set;
        }

        [DataMember]
        public NodeStatus Status
        {
            get;
            set;
        }
       
    }
}
