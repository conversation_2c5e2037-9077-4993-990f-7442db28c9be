﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "SqlNative", Namespace = "http://www.BCMS.com/types")]

    public class SqlNative:BaseEntity
    {
        #region Properties

            [DataMember]
            public int GroupId
            {
                 get;
                 set;
            }
            
            [DataMember]
            public string BackupFolderName
            {
                get;
                set;
            }

            [DataMember]
            public string BackupFolderSharedName
            {
                get;
                set;
            }

            [DataMember]
            public string CopyRestoreName
            {
                get;
                set;
            }

            [DataMember]
            public string CopyRestoreSharedName
            {
                get;
                set;
            }

            [DataMember]
            public int BackupInterval
            {
                get;
                set;
            }

            [DataMember]
            public int CopyInterval
            {
                get;
                set;
            }

            [DataMember]
            public int RestoreInterval
            {
                get;
                set;
            }          

            #endregion

        #region Constructor

            public SqlNative()
                : base()
            {
            }

            #endregion
    }
}
