﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class AlertDataAccess : BaseDataAccess
    {

        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static bool AddAlert(Alert alert)
        {
            try
            {
                const string sp = "Alert_Create";
               // var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iType", DbType.AnsiString, alert.Type);
                    Database.AddInParameter(dbCommand, Dbstring+"iSeverity", DbType.AnsiString, alert.Severity);
                    Database.AddInParameter(dbCommand, Dbstring+"iSystemMessage", DbType.AnsiString, alert.SystemMessage);
                    Database.AddInParameter(dbCommand, Dbstring+"iUserMessage", DbType.AnsiString, alert.UserMessage);
                    Database.AddInParameter(dbCommand, Dbstring+"iJobName", DbType.AnsiString, alert.JobName);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, alert.InfraObjectId);

                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }
            return false;
        }


        //Start: Added by Neeraj for Alerts Isssue(19062012)
        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static bool UpdateAlertData(int iAlertCategoryId, int iAlertType, bool bNotified, int iAlertCategory)
        {
            try
            {
               // var db = DatabaseFactory.CreateDatabase();
               // DbCommand dbCommand = db.GetStoredProcCommand("Group_Alert_Update_Notification");
                //DbCommand dbCommand = db.GetStoredProcCommand("AlertNotification_UpdateByCategoryIdAndType");
                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertNoti_UpdtByCatIdAndTyp"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategoryId", DbType.Int32, iAlertCategoryId);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertType", DbType.Int32, iAlertType);
                    Database.AddInParameter(dbCommand, Dbstring+"iNotification", DbType.Int32, bNotified);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategory", DbType.Int32, iAlertCategory);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by Notification ", exc);
            }

        }

        public static int GetSentAlertCount(int iAlertCategoryId, int iAlertType, int iAlertCategory)
        {
            
            try
            {
                //using (DbCommand dbCommand = db.GetStoredProcCommand("Group_Alert_GetNotification"))
                //{
                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertNoti_GetByCateAndType"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategoryId", DbType.Int32, iAlertCategoryId);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertType", DbType.Int32, iAlertType);
                    Database.AddInParameter(dbCommand, Dbstring + "iAlertCategory", DbType.Int32, iAlertCategory);                   

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_alertnoti"));
#endif
                    var myScalar = Database.ExecuteScalar(dbCommand);

                    return Convert.ToInt32(myScalar);

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while checking alert notification count for a group", exc);
            }
        }
        //End:


        //Added By Vishal For CPSL Alert Dated on 25-11-2015
        public static AlertReceiver GetAlertReceiverByName(string Name)
        {
            AlertReceiver alertReceivers = null;
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertReceiver_GetByName"))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iName", DbType.AnsiString, Name);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader myAlertRecieverReader = Database.ExecuteReader(dbCommand))
                    {
                        alertReceivers = new AlertReceiver();

                        while (myAlertRecieverReader.Read())
                        {
                            alertReceivers.Id = Convert.IsDBNull(myAlertRecieverReader["Id"]) ? 0 : Convert.ToInt32(myAlertRecieverReader["Id"]);
                            alertReceivers.EmailAddress = Convert.IsDBNull(myAlertRecieverReader["EmailAddress"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["EmailAddress"]);
                            alertReceivers.Name = Convert.IsDBNull(myAlertRecieverReader["Name"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["Name"]);
                            alertReceivers.InfraObjectId = Convert.IsDBNull(myAlertRecieverReader["InfraObjectId"]) ? string.Empty : Convert.ToString(myAlertRecieverReader["InfraObjectId"]);
                            alertReceivers.IsActive = !Convert.IsDBNull(myAlertRecieverReader["IsActive"]) && Convert.ToBoolean(myAlertRecieverReader["IsActive"]);
                        }
                    }

                    return alertReceivers;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while getting alert Receiver information by Name", exc);
            }

        }
    }
}
