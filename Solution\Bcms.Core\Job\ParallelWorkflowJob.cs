﻿using System;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess;
using Bcms.Core.Client;
using Bcms.ExceptionHandler;
using log4net;
using Bcms.Core.Workflows;
using Quartz;
using System.Threading;
using System.Security.Permissions;

namespace Bcms.Core.Job
{
    public class ParallelWorkflowJob : Quartz.IInterruptableJob
    {

        public const string ParallelGroupWorkflow = "parallelgroupworkflow";
        //Added by <PERSON><PERSON><PERSON> for ITIT-10566
        public const string ParallelGroupWorkflowLogger = "parallelgroupworkflowlogger";

        private static readonly ILog _logger = LogManager.GetLogger(typeof(ParallelWorkflowJob));

        private Serilog.ILogger Logger;

        private CancellationTokenSource _CancellationTokenSource = new CancellationTokenSource();

        private Thread _currentThread;
        private string _workflowName;


        //private static readonly ILog Logger = LogManager.GetLogger(typeof(ParallelWorkflowJob));
        //Added by <PERSON><PERSON><PERSON> for ITIT-10566
        #region IJob Members
        //Added by <PERSON><PERSON><PERSON> B for ITIT-10566
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                _logger.Info(("Enter ParallelWorkflowJob Execute "));

                JobKey key = context.JobDetail.Key;

                Quartz.JobDataMap data = context.JobDetail.JobDataMap;

                var parallelGroup = data.Get(ParallelGroupWorkflow) as ParallelGroupWorkflow;

                Logger = data.Get(ParallelGroupWorkflowLogger) as Serilog.ILogger;

                // Logger = Serilog.Log.ForContext("FileName", parallelGroup.WorkflowName);

                var triggerState = context.Scheduler.GetTriggerState(new TriggerKey(context.Trigger.Key.Name));

                _logger.Info("Execute : trigger State: " + triggerState);


                _logger.Info("Enter Initailise parallel workflow execution started");

                _currentThread = Thread.CurrentThread;

                if (parallelGroup != null)
                {
                    Logger.Information("parallelGroup WorkflowName:" + parallelGroup.WorkflowName);
                    Logger.Information("{0} : Current Parallel Execution job Thread id {1} is ", parallelGroup.WorkflowName, _currentThread.ManagedThreadId);
                    _workflowName = parallelGroup.WorkflowName;
                    Logger.Information("{0} : Parallel Workflow Simple trigger started" + parallelGroup.WorkflowName);
                    parallelGroup = ParallelGroupWorkflowDataAccess.GetById(parallelGroup.Id);
                    Logger.Information("parallelGroup.IsResume Status in ParallelWorkflowJob" + parallelGroup.IsResume);
                    if (parallelGroup.IsResume == 2)
                    {
                        try
                        {
                            Logger.Information("RESUME : ParallelWorkflowJob - Initializing ExecuteResumeWorkflow ");
                            ParallelGroupWorkflowDataAccess.UpdateStatusById(parallelGroup.Id, "Running");
                            Logger.Information("RESUME : Update Running Status in GroupWorkflowTable  : " + parallelGroup.ParallelDROperationId);
                            ParallelGroupWorkflowDataAccess.UpdateResumeStatusGrpId(parallelGroup.Id, 0, 0);
                            ParallelGroupWorkflowDataAccess.UpdatePauseStatusGrpId(parallelGroup.Id, 0, 0);
                            Logger.Information("Update Status Running for Group workflow  : " + parallelGroup.WorkflowName + " Thread ID :" + Convert.ToInt32(Thread.CurrentThread.ManagedThreadId));
                            Logger.Information("Thread.CurrentThread.Name :" + Thread.CurrentThread.Name); Logger.Information("Thread.CurrentThread.ManagedThreadId :" + Thread.CurrentThread.ManagedThreadId);
                            Logger.Information("Progress Status get from context menu:" + parallelGroup.ProgressStatus);
                            parallelGroup = ParallelGroupWorkflowDataAccess.GetById(parallelGroup.Id);
                            Logger.Information("Progress Status get from GroupId:" + parallelGroup.ProgressStatus);
                            var objClient = new WorkflowResumeClient(Logger);
                            objClient.CancellationTokenSource = _CancellationTokenSource;
                            objClient.ExecuteResumeWorkflow(parallelGroup, Logger);
                        }
                        catch (Exception exc)
                        {
                            Logger.Error("Resume:Error in Executing IJob " + exc.Message);
                        }

                    }
                    else
                    {
                        try
                        {
                            InfraObject infraItem = InfraObjectDataAccess.GetInfraObjectById(parallelGroup.InfraObjectId);
                            if (infraItem != null)
                            {
                                Logger.Information(" Started ParallelGroupWorkflow : " + parallelGroup.WorkflowName + " Current ThreadId : " + Thread.CurrentThread.ManagedThreadId.ToString() + " Time : " + DateTime.Now);
                                if (infraItem.State == InfraObjectState.Maintenance.ToString())
                                {
                                    PerformParallelDROperation(parallelGroup, Logger);
                                }
                                else
                                {
                                    Logger.Error("{0} : InfraObject not in 'Maintenance' mode, skipped parallel execution"+ infraItem.Name);
                                }
                            }
                        }
                        catch (Exception exc)
                        {
                            Logger.Error("Error in Parallel Executing IJob " + exc.Message);
                        }
                    }
                }
                else
                {
                    Logger.Error(context.JobDetail.Key.Name + " InfraObject Having null values While Perform Normal Replication Job ");
                }
                if (context.Scheduler.DeleteJob(context.JobDetail.Key))
                {
                    Logger.Information("*********JOB " + context.JobDetail.Key.Name + " Completed Successfully *********");
                }
                else
                {
                    Logger.Error("*********JOB " + context.JobDetail.Key.Name + " cannot be clear *********");
                }
            }
            catch (Exception exc)
            {
                _logger.Error("**********Exception Occured while Execute jobs:***********" + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("**********Inner Exception Occured while Execute jobs  :***********" + exc.InnerException);
                }
            }
        }

        public void PerformParallelDROperation(ParallelGroupWorkflow parallelGroup, Serilog.ILogger logger)
        {

            Logger = logger;
            try
            {

                using (var client = new WorkflowClient(parallelGroup, _CancellationTokenSource, logger))
                {
                    client.TotalActionCount = 0;
                    client.PerformParallelWorkflow(logger);
                }

            }
            catch (BcmsException exc)
            {
                if (exc.ExceptionType != BcmsExceptionType.ThreadAborted)
                {
                    ExceptionManager.Manage(exc, JobName.ParallelWorkflowJob.ToString(), 0, string.Empty);
                }

            }
            catch (Exception exc)
            {
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.ParallelWorkflowJob.ToString(), string.Empty, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.ParallelWorkflowJob.ToString(), 0, string.Empty);
            }
        }

        public void Interrupt()
        {
            if (_currentThread != null)
            {
                KillTheThread();
            }
        }
        [SecurityPermissionAttribute(SecurityAction.Demand, ControlThread = true)]
        private void KillTheThread()
        {
            try
            {
                Logger.Error("Thread {0} is going to Abort", _currentThread.ManagedThreadId);

                if (_CancellationTokenSource != null)
                {
                    _CancellationTokenSource.Cancel();
                }

                Logger.Information("Workflow {0} is waiting for Abort because associated threads are currently running state ", _workflowName);
                _currentThread.Abort();

                Thread.Sleep(3000);
                Logger.Error("Workflow {0} is aborted:", _workflowName);
                Logger.Error("Thread current state is {0}", _currentThread.ThreadState.ToString());
                _currentThread = null;

                return;
            }
            catch (ThreadAbortException exc)
            {
                Logger.Error("Exception occured in KillTheThread: " + exc.Message);
            }

        }



        //Added by Karthick B for ITIT-10566

        #region Old
        //public void Execute(JobExecutionContext context)
        //{
        //    JobDataMap data = context.JobDetail.JobDataMap;

        //    var parallelGroup = data.Get(ParallelGroupWorkflow) as ParallelGroupWorkflow;

        //    if (parallelGroup != null)
        //    {
        //        Logger.InfoFormat("{0} : Parallel Workflow Simple trigger started", parallelGroup.WorkflowName);


        //        InfraObject infraItem = InfraObjectDataAccess.GetInfraObjectById(parallelGroup.InfraObjectId);

        //        if (infraItem != null)
        //        {
        //            if (infraItem.State == InfraObjectState.Maintenance.ToString())
        //            {

        //                PerformParallelDROperation(parallelGroup);
        //            }
        //            else
        //            {
        //                Logger.ErrorFormat("{0} : InfraObject not in 'Maintenance' mode, skipped parallel execution", infraItem.Name);
        //            }

        //        }
        //    }
        //    else
        //    {
        //        throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Normal Replication Job ");
        //    }
        //}

        //private void PerformParallelDROperation(ParallelGroupWorkflow parallelGroup)
        //{
        //    try
        //    {
        //        //using (var client = new BcmsClient(parallelGroup))
        //        //{
        //        //    client.PerformParallelWorkflow();
        //        //}

        //        using (var client = new WorkflowClient(parallelGroup))
        //        {
        //            client.TotalActionCount = 0;

        //            client.PerformParallelWorkflow();
        //        }

        //    }
        //    catch (BcmsException exc)
        //    {
        //        ExceptionManager.Manage(exc, JobName.ParallelWorkflowJob.ToString(), 0, string.Empty);
        //    }
        //    catch (Exception exc)
        //    {
        //        var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.ParallelWorkflowJob.ToString(), string.Empty, string.Empty, ExceptionType.UnHandled), exc);
        //        ExceptionManager.Manage(bcmsException, JobName.ParallelWorkflowJob.ToString(), 0, string.Empty);
        //    }
        //}
        #endregion
        #endregion
    }
}