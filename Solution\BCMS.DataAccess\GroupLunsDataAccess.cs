﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class GroupLunsDataAccess : BaseDataAccess
    {
        public static GroupLuns GetGroupLunsByGroupId(int groupId)
        {
            var groupLuns = new GroupLuns();

            try
            {
                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("GroupLuns_GetByGroupId"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iGroupId", DbType.Int32, groupId);
                    using (IDataReader myGroupLunsReader = db.ExecuteReader(dbCommand))
                    {
                        while (myGroupLunsReader.Read())
                        {
                            //groupLuns.AGroup = myGroupLunsReader[4].ToString();
                            //groupLuns.AMount = myGroupLunsReader[5].ToString();
                            groupLuns.AGroup = Convert.IsDBNull(myGroupLunsReader["AGroup"]) ? string.Empty : Convert.ToString(myGroupLunsReader["AGroup"]);
                            groupLuns.AMount = Convert.IsDBNull(myGroupLunsReader["AMount"]) ? string.Empty : Convert.ToString(myGroupLunsReader["AMount"]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
              throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,"Exception occurred while Get GroupLuns Details by InfraObject Id - "+groupId, exc);
               
            }

            return groupLuns;
        }
    }
}
