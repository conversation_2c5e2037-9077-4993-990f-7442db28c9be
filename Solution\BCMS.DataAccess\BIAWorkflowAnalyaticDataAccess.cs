﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAWorkflowAnalyaticDataAccess : BaseDataAccess
    {
        //retrive From Live Table to insert into BIA table
        public static IList<BIAWorkflowAnalyatic> GetAllBIAWorkflowAnalyaticAbort()
        {
            IList<BIAWorkflowAnalyatic> BIAWorkflowAnalyticAborted = new List<BIAWorkflowAnalyatic>();

            const string sp = "Biaworkflowanaabort_Getall";



            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAnalyticAbort = new BIAWorkflowAnalyatic();

                            BIAAnalyticAbort.ProfileName = Convert.IsDBNull(reader["Profilename"]) ? string.Empty : Convert.ToString(reader["Profilename"]);
                            BIAAnalyticAbort.ProfileId = Convert.IsDBNull(reader["profileID"]) ? 0 : Convert.ToInt32(reader["profileID"]);
                            BIAAnalyticAbort.WorkFlowId = Convert.IsDBNull(reader["Workflowid"]) ? 0 : Convert.ToInt32(reader["Workflowid"]);
                            BIAAnalyticAbort.WorkFlowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            BIAAnalyticAbort.BusinessFunction = Convert.IsDBNull(reader["Businessfunction"]) ? string.Empty : Convert.ToString(reader["Businessfunction"]);
                            BIAAnalyticAbort.BusinessService = Convert.IsDBNull(reader["Businessservice"]) ? string.Empty : Convert.ToString(reader["Businessservice"]);
                            BIAAnalyticAbort.InfraObjectId = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                            BIAAnalyticAbort.WorkFlowConfiguredRTO = Convert.IsDBNull(reader["Configuredrto"]) ? 0 : Convert.ToInt32(reader["Configuredrto"]);
                            BIAAnalyticAbort.WorkFlowType = Convert.IsDBNull(reader["workflowtype"]) ? 0 : Convert.ToInt32(reader["workflowtype"]);
                            BIAAnalyticAbort.Aborted = Convert.IsDBNull(reader["Abortedworkflowcount"]) ? 0 : Convert.ToInt32(reader["Abortedworkflowcount"]);
                            BIAAnalyticAbort.CompletedWithError = 0;
                            BIAAnalyticAbort.CompletedWithSuccess = 0;
                            BIAAnalyticAbort.TotalWorkFlow = 0;
                            BIAWorkflowAnalyticAborted.Add(BIAAnalyticAbort);
                        }
                    }
                }
                return BIAWorkflowAnalyticAborted;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }

        //retrive From Live Table to insert into BIA table
        public static IList<BIAWorkflowAnalyatic> GetAllBIAWorkflowAnalyaticCompleteWithSuccess()
        {
            IList<BIAWorkflowAnalyatic> BIAWorkflowAnalyticCompletedWithSuccess = new List<BIAWorkflowAnalyatic>();



            const string sp = "BiaworkflowAnaCompSucc_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAnalyticComSucc = new BIAWorkflowAnalyatic();
                            BIAAnalyticComSucc.ProfileName = Convert.IsDBNull(reader["Profilename"]) ? string.Empty : Convert.ToString(reader["Profilename"]);
                            BIAAnalyticComSucc.ProfileId = Convert.IsDBNull(reader["profileID"]) ? 0 : Convert.ToInt32(reader["profileID"]);
                            BIAAnalyticComSucc.WorkFlowId = Convert.IsDBNull(reader["Workflowid"]) ? 0 : Convert.ToInt32(reader["Workflowid"]);
                            BIAAnalyticComSucc.WorkFlowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            BIAAnalyticComSucc.BusinessFunction = Convert.IsDBNull(reader["Businessfunction"]) ? string.Empty : Convert.ToString(reader["Businessfunction"]);
                            BIAAnalyticComSucc.BusinessService = Convert.IsDBNull(reader["Businessservice"]) ? string.Empty : Convert.ToString(reader["Businessservice"]);
                            BIAAnalyticComSucc.InfraObjectId = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                            BIAAnalyticComSucc.WorkFlowConfiguredRTO = Convert.IsDBNull(reader["Configuredrto"]) ? 0 : Convert.ToInt32(reader["Configuredrto"]);
                            BIAAnalyticComSucc.WorkFlowType = Convert.IsDBNull(reader["workflowtype"]) ? 0 : Convert.ToInt32(reader["workflowtype"]);
                            BIAAnalyticComSucc.CompletedWithSuccess = Convert.IsDBNull(reader["CompletedSuccesworkflowcount"]) ? 0 : Convert.ToInt32(reader["CompletedSuccesworkflowcount"]);
                            BIAAnalyticComSucc.CompletedWithError = 0;
                            BIAAnalyticComSucc.Aborted = 0;
                            BIAAnalyticComSucc.TotalWorkFlow = 0;
                            BIAWorkflowAnalyticCompletedWithSuccess.Add(BIAAnalyticComSucc);
                        }
                    }
                }
                return BIAWorkflowAnalyticCompletedWithSuccess;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }

        //retrive From Live Table to insert into BIA table
        public static IList<BIAWorkflowAnalyatic> GetAllBIAWorkflowAnalyaticCompleteWithError()
        {
            IList<BIAWorkflowAnalyatic> BIAWorkflowAnalyticCompletedWithError = new List<BIAWorkflowAnalyatic>();



            const string sp = "BiaworkflowAnaCompErro_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAnalyticComError = new BIAWorkflowAnalyatic();
                            BIAAnalyticComError.ProfileName = Convert.IsDBNull(reader["Profilename"]) ? string.Empty : Convert.ToString(reader["Profilename"]);
                            BIAAnalyticComError.ProfileId = Convert.IsDBNull(reader["profileID"]) ? 0 : Convert.ToInt32(reader["profileID"]);
                            BIAAnalyticComError.WorkFlowId = Convert.IsDBNull(reader["Workflowid"]) ? 0 : Convert.ToInt32(reader["Workflowid"]);
                            BIAAnalyticComError.WorkFlowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            BIAAnalyticComError.BusinessFunction = Convert.IsDBNull(reader["Businessfunction"]) ? string.Empty : Convert.ToString(reader["Businessfunction"]);
                            BIAAnalyticComError.BusinessService = Convert.IsDBNull(reader["Businessservice"]) ? string.Empty : Convert.ToString(reader["Businessservice"]);
                            BIAAnalyticComError.InfraObjectId = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                            BIAAnalyticComError.WorkFlowConfiguredRTO = Convert.IsDBNull(reader["Configuredrto"]) ? 0 : Convert.ToInt32(reader["Configuredrto"]);
                            BIAAnalyticComError.WorkFlowType = Convert.IsDBNull(reader["workflowtype"]) ? 0 : Convert.ToInt32(reader["workflowtype"]);
                            BIAAnalyticComError.CompletedWithError = Convert.IsDBNull(reader["CompletedWithErrorWorkFloCnt"]) ? 0 : Convert.ToInt32(reader["CompletedWithErrorWorkFloCnt"]);
                            BIAAnalyticComError.Aborted = 0;
                            BIAAnalyticComError.CompletedWithSuccess = 0;
                            BIAAnalyticComError.TotalWorkFlow = 0;

                            BIAWorkflowAnalyticCompletedWithError.Add(BIAAnalyticComError);
                        }
                    }
                }
                return BIAWorkflowAnalyticCompletedWithError;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }

        //using this method to take all counts success,completed,aborted workflow
        //retrive From Live Table to insert into BIA table
        public static IList<BIAWorkflowAnalyatic> GetAllBIAWorkflowAnalyaticTotal()
        {
            IList<BIAWorkflowAnalyatic> BIAWorkflowAnalyticTotal = new List<BIAWorkflowAnalyatic>();



            const string sp = "BiaworkflowAnaTotal_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAnalyticWorkTotal = new BIAWorkflowAnalyatic();
                            BIAAnalyticWorkTotal.ProfileName = Convert.IsDBNull(reader["Profilename"]) ? string.Empty : Convert.ToString(reader["Profilename"]);
                            BIAAnalyticWorkTotal.ProfileId = Convert.IsDBNull(reader["profileID"]) ? 0 : Convert.ToInt32(reader["profileID"]);
                            BIAAnalyticWorkTotal.WorkFlowId = Convert.IsDBNull(reader["Workflowid"]) ? 0 : Convert.ToInt32(reader["Workflowid"]);
                            BIAAnalyticWorkTotal.WorkFlowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            BIAAnalyticWorkTotal.BusinessFunction = Convert.IsDBNull(reader["Businessfunction"]) ? string.Empty : Convert.ToString(reader["Businessfunction"]);
                            BIAAnalyticWorkTotal.BusinessService = Convert.IsDBNull(reader["Businessservice"]) ? string.Empty : Convert.ToString(reader["Businessservice"]);
                            BIAAnalyticWorkTotal.InfraObjectId = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                            BIAAnalyticWorkTotal.WorkFlowConfiguredRTO = Convert.IsDBNull(reader["Configuredrto"]) ? 0 : Convert.ToInt32(reader["Configuredrto"]);
                            BIAAnalyticWorkTotal.WorkFlowType = Convert.IsDBNull(reader["workflowtype"]) ? 0 : Convert.ToInt32(reader["workflowtype"]);
                            BIAAnalyticWorkTotal.TotalWorkFlow = Convert.IsDBNull(reader["Totalworkflowcount"]) ? 0 : Convert.ToInt32(reader["Totalworkflowcount"]);
                            BIAAnalyticWorkTotal.Aborted = Convert.IsDBNull(reader["Abortedworkflowcount"]) ? 0 : Convert.ToInt32(reader["Abortedworkflowcount"]);
                            BIAAnalyticWorkTotal.CompletedWithError = Convert.IsDBNull(reader["Completedwitherrorworkflocnt"]) ? 0 : Convert.ToInt32(reader["Completedwitherrorworkflocnt"]);
                            BIAAnalyticWorkTotal.CompletedWithSuccess = Convert.IsDBNull(reader["Completedsuccesworkflowcount"]) ? 0 : Convert.ToInt32(reader["Completedsuccesworkflowcount"]);
                            BIAAnalyticWorkTotal.CompletedWithinRTO = Convert.IsDBNull(reader["Completedwithrto"]) ? 0 : Convert.ToInt32(reader["Completedwithrto"]);
                            BIAAnalyticWorkTotal.CompletedOutOfRTO = Convert.IsDBNull(reader["CompletedoutOfrto"]) ? 0 : Convert.ToInt32(reader["CompletedoutOfrto"]);
                            BIAAnalyticWorkTotal.ParallelGroupworkflowId = Convert.IsDBNull(reader["parallelGroupworkflowid"]) ? 0 : Convert.ToInt32(reader["parallelGroupworkflowid"]);
                            BIAAnalyticWorkTotal.ProfileSuccess = Convert.IsDBNull(reader["ProfileSuccess"]) ? 0 : Convert.ToInt32(reader["ProfileSuccess"]);
                            BIAAnalyticWorkTotal.ProfileAborted = Convert.IsDBNull(reader["ProfileAborted"]) ? 0 : Convert.ToInt32(reader["ProfileAborted"]);
                            BIAAnalyticWorkTotal.ProfileError = Convert.IsDBNull(reader["ProfileError"]) ? 0 : Convert.ToInt32(reader["ProfileError"]);
                            BIAAnalyticWorkTotal.TotalProfileRun = Convert.IsDBNull(reader["TotalProfileRun"]) ? 0 : Convert.ToInt32(reader["TotalProfileRun"]);

                            BIAWorkflowAnalyticTotal.Add(BIAAnalyticWorkTotal);
                        }
                    }
                }
                return BIAWorkflowAnalyticTotal;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }

        // for update  IsAnalyze Status in ParallelGroupWorkflow table
        public static void UpdateAnalyzeStatusParallelGroupWorkflow()
        {

            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("BIAParallGrpWrkIsAnaLy_update"))
                {
                    int isuccess = Database.ExecuteNonQuery(cmd);
                }
            }

            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Updateting the BIA IsAnalyzed Status in ParalleGroupWorkflow in UpdateAnalyzeStatusParallelGroupWorkflow", exc);

            }


        }

        // insert records into  Workflow_analytic BIA table
        public static void Add_Analytic_Workflow(IList<BIAWorkflowAnalyatic> AllWorkflowTotal)
        {
            try
            {
                foreach (var workflow_Analytic in AllWorkflowTotal)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("workFlowAnalytic_CREATE"))
                    {

                        Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, workflow_Analytic.ProfileName == null ? string.Empty : workflow_Analytic.ProfileName);
                        Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, workflow_Analytic.ProfileId);
                        Database.AddInParameter(cmd, Dbstring + "iWorkFlowId", DbType.Int32, workflow_Analytic.WorkFlowId);
                        Database.AddInParameter(cmd, Dbstring + "iWorkFlowName", DbType.AnsiString, workflow_Analytic.WorkFlowName == null ? string.Empty : workflow_Analytic.WorkFlowName);
                        Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, workflow_Analytic.InfraObjectId);
                        Database.AddInParameter(cmd, Dbstring + "iBusinessFunction", DbType.AnsiString, workflow_Analytic.BusinessFunction == null ? string.Empty : workflow_Analytic.BusinessFunction);
                        Database.AddInParameter(cmd, Dbstring + "iBusinessService", DbType.AnsiString, workflow_Analytic.BusinessService == null ? string.Empty : workflow_Analytic.BusinessService);
                        Database.AddInParameter(cmd, Dbstring + "iTotalWorkFlow", DbType.Int32, workflow_Analytic.TotalWorkFlow);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithSuccess", DbType.Int32, workflow_Analytic.CompletedWithSuccess);
                        Database.AddInParameter(cmd, Dbstring + "iAborted", DbType.Int32, workflow_Analytic.Aborted);
                        Database.AddInParameter(cmd, Dbstring + "iWorkFlowConfiguredRTO", DbType.Int32, workflow_Analytic.WorkFlowConfiguredRTO);
                        Database.AddInParameter(cmd, Dbstring + "iWorkFlowType", DbType.Int32, workflow_Analytic.WorkFlowType);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithError", DbType.Int32, workflow_Analytic.CompletedWithError);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithinRTO", DbType.Int32, workflow_Analytic.CompletedWithinRTO);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedOutOfRTO", DbType.Int32, workflow_Analytic.CompletedOutOfRTO);
                        Database.AddInParameter(cmd, Dbstring + "iParallelGroupworkflowId", DbType.Int32, workflow_Analytic.ParallelGroupworkflowId);
                        Database.AddInParameter(cmd, Dbstring + "iErrorWorkflowCnt", DbType.Int32, workflow_Analytic.ErrorWorkflowCnt);
                        Database.AddInParameter(cmd, Dbstring + "iTotalProfileRun", DbType.Int32, workflow_Analytic.TotalProfileRun);
                        Database.AddInParameter(cmd, Dbstring + "iProfileSuccess", DbType.Int32, workflow_Analytic.ProfileSuccess);
                        Database.AddInParameter(cmd, Dbstring + "iProfileAborted", DbType.Int32, workflow_Analytic.ProfileAborted);
                        Database.AddInParameter(cmd, Dbstring + "iProfileError", DbType.Int32, workflow_Analytic.ProfileError);
                        int value = Database.ExecuteNonQuery(cmd);

                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }


        }

        /// <summary>
        /// Update Workflow_Analytic BIA table.
        /// </summary>
        /// <param name="lstWorkflowAnaUpdate"></param>
        public static void UpdateWorkflowAnalytic(List<BIAWorkflowAnalyatic> lstWorkflowAnaUpdate)
        {
            try
            {

                foreach (var workflow_Analytic in lstWorkflowAnaUpdate)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("workFlowAnalytic_Update"))
                    {

                        Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, workflow_Analytic.ProfileId);
                        Database.AddInParameter(cmd, Dbstring + "iWorkFlowId", DbType.Int32, workflow_Analytic.WorkFlowId);
                        Database.AddInParameter(cmd, Dbstring + "iTotalWorkFlow", DbType.Int32, workflow_Analytic.TotalWorkFlow);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithSuccess", DbType.Int32, workflow_Analytic.CompletedWithSuccess);
                        Database.AddInParameter(cmd, Dbstring + "iAborted", DbType.Int32, workflow_Analytic.Aborted);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithError", DbType.Int32, workflow_Analytic.CompletedWithError);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedWithinRTO", DbType.Int32, workflow_Analytic.CompletedWithinRTO);
                        Database.AddInParameter(cmd, Dbstring + "iCompletedOutOfRTO", DbType.Int32, workflow_Analytic.CompletedOutOfRTO);
                        Database.AddInParameter(cmd, Dbstring + "iTotalProfileRun", DbType.Int32, workflow_Analytic.TotalProfileRun);
                        Database.AddInParameter(cmd, Dbstring + "iProfileAborted", DbType.Int32, workflow_Analytic.ProfileAborted);
                        Database.AddInParameter(cmd, Dbstring + "iProfileError", DbType.Int32, workflow_Analytic.ProfileError);
                        Database.AddInParameter(cmd, Dbstring + "iProfileSuccess", DbType.Int32, workflow_Analytic.ProfileSuccess);

                        int value = Database.ExecuteNonQuery(cmd);

                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Updateting the BIA IsAnalyzed Status in ParalleGroupWorkflow in UpdateAnalyzeStatusParallelGroupWorkflow", exc);

            }
        }

        //retrive From  BIA table
        public static IList<BIAWorkflowAnalyatic> GetAllWorkflowAnalytic()
        {
            IList<BIAWorkflowAnalyatic> BIAWorkflowAnalytic = new List<BIAWorkflowAnalyatic>();

            const string sp = "BIAWORKFLOWANALYTIC";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAnalytic = new BIAWorkflowAnalyatic();

                            BIAAnalytic.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["ID"]);
                            BIAAnalytic.ProfileName = Convert.IsDBNull(reader["Profilename"]) ? string.Empty : Convert.ToString(reader["Profilename"]);
                            BIAAnalytic.ProfileId = Convert.IsDBNull(reader["profileID"]) ? 0 : Convert.ToInt32(reader["profileID"]);
                            BIAAnalytic.WorkFlowId = Convert.IsDBNull(reader["Workflowid"]) ? 0 : Convert.ToInt32(reader["Workflowid"]);
                            BIAAnalytic.WorkFlowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            BIAAnalytic.BusinessFunction = Convert.IsDBNull(reader["Businessfunction"]) ? string.Empty : Convert.ToString(reader["Businessfunction"]);
                            BIAAnalytic.BusinessService = Convert.IsDBNull(reader["Businessservice"]) ? string.Empty : Convert.ToString(reader["Businessservice"]);
                            BIAAnalytic.InfraObjectId = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                            BIAAnalytic.WorkFlowConfiguredRTO = Convert.IsDBNull(reader["WorkflowConfiguredRTO"]) ? 0 : Convert.ToInt32(reader["WorkflowConfiguredRTO"]);
                            BIAAnalytic.WorkFlowType = Convert.IsDBNull(reader["workflowtype"]) ? 0 : Convert.ToInt32(reader["workflowtype"]);
                            BIAAnalytic.CompletedWithSuccess = Convert.IsDBNull(reader["CompletedWithSuccess"]) ? 0 : Convert.ToInt32(reader["CompletedWithSuccess"]);
                            BIAAnalytic.CompletedWithError = Convert.IsDBNull(reader["CompletedWithError"]) ? 0 : Convert.ToInt32(reader["CompletedWithError"]);

                            BIAAnalytic.Aborted = Convert.IsDBNull(reader["Aborted"]) ? 0 : Convert.ToInt32(reader["Aborted"]);
                            BIAAnalytic.TotalWorkFlow = Convert.IsDBNull(reader["Totalworkflow"]) ? 0 : Convert.ToInt32(reader["Totalworkflow"]);
                            BIAAnalytic.CompletedWithinRTO = Convert.IsDBNull(reader["Completedwithinrto"]) ? 0 : Convert.ToInt32(reader["Completedwithinrto"]);
                            BIAAnalytic.CompletedOutOfRTO = Convert.IsDBNull(reader["CompletedOutOfRTO"]) ? 0 : Convert.ToInt32(reader["CompletedOutOfRTO"]);
                            BIAAnalytic.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            BIAAnalytic.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                            BIAWorkflowAnalytic.Add(BIAAnalytic);
                        }
                    }
                }
                return BIAWorkflowAnalytic;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Analytic aborted workflows in BIAWorkflowAnalyaticAbort", exc);

            }
        }
    }
}
