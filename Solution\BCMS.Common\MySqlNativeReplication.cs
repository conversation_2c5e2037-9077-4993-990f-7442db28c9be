﻿using Bcms.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Bcms.Common
{

    [Serializable]
    [DataContract(Name = "EC2S3Replication", Namespace = "http://www.BCMS.com/types")]
    public class MySqlNativeReplication
    {
        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public string PRConnectState
        {
            get;
            set;
        }

        [DataMember]
        public string DRConnectState
        {
            get;
            set;
        }

        #endregion Properties
    }
}
