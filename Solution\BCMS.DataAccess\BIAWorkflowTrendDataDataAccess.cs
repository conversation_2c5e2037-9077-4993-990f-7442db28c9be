﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAWorkflowTrendDataDataAccess : BaseDataAccess
    {
        public static void ExecuteWorkflowDBMigration()
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("BIAACTIONTRENDWFDATAPERMONTH"))
                {
                    int isuccess = Database.ExecuteNonQuery(cmd);
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while BIA DB migration for Workflow in ExecuteWorkflowDBMigration()", exc);

            }


        }

    }
}
