﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseBackupOperation", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseBackupOperation : BaseEntity
    {
        #region Properties

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string BackupFileName
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string ErrorDescription
        {
            get;
            set;
        }

        [DataMember]
        public bool IsBackupNow
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public DatabaseBackupOperation()
            : base()
        {
        }

        #endregion
    }
}
