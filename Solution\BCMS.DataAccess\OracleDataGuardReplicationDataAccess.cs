﻿using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class OracleDataGuardReplicationDataAccess
    {

        public static bool AddOracleDataGuardReplication(OracleDataGuardReplication oracleData)
        {
            const string sp = "OracleDataGuardReplication_Create";

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(sp))
            {
                db.AddInParameter(cmd, "?iPRStatus", DbType.AnsiString, oracleData.PRStatus);
                db.AddInParameter(cmd, "?iDRStatus", DbType.AnsiString, oracleData.DRStatus);
                db.AddInParameter(cmd, "?iPRErrorReason", DbType.AnsiString, oracleData.PRErrorReason);
                db.AddInParameter(cmd, "?iDRErrorReason", DbType.AnsiString, oracleData.DRErrorReason);
                db.AddInParameter(cmd, "?iGroupId", DbType.Int32, oracleData.GroupId);

                int value = db.ExecuteNonQuery(cmd);
                if (value > 0)
                {
                    return true;
                }

            }

            return false;
        }

    }
}