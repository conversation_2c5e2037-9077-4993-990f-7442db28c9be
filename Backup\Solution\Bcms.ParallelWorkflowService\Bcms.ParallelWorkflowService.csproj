﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="3.5" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A5A85384-784D-4933-9BE4-E8BD5E6DB44E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Bcms.ParallelWorkflowService</RootNamespace>
    <AssemblyName>Bcms.ParallelWorkflowService</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;ORACLE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=1.2.9.0, Culture=neutral, PublicKeyToken=b32731d11ce58905">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Log4Net\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BcmsParallelWorkflowService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="BcmsParallelWorkflowService.Designer.cs">
      <DependentUpon>BcmsParallelWorkflowService.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="ProjectInstaller.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProjectInstaller.Designer.cs">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ProjectInstaller.resx">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BCMS.Common\Bcms.Common.csproj">
      <Project>{4F12B107-29B7-4E7A-98A2-E4219142261F}</Project>
      <Name>Bcms.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Bcms.Core\Bcms.Core.csproj">
      <Project>{440DFC03-8DE7-408B-8BC3-CE3ECAE05050}</Project>
      <Name>Bcms.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.DataAccess\Bcms.DataAccess.csproj">
      <Project>{E8CB1405-A5F6-4A2C-AF32-46301ED4DF0C}</Project>
      <Name>Bcms.DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.ExceptionHandler\Bcms.ExceptionHandler.csproj">
      <Project>{0DFB8DA8-A766-4C68-9D49-0511764793AA}</Project>
      <Name>Bcms.ExceptionHandler</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.Helper\Bcms.Helper.csproj">
      <Project>{A3AF28B6-66CB-4B8C-BB83-2952977967AF}</Project>
      <Name>Bcms.Helper</Name>
    </ProjectReference>
    <ProjectReference Include="..\Bcms.Replication\Bcms.Replication.csproj">
      <Project>{5C5CEED3-44CC-419F-8EFD-C853E06636ED}</Project>
      <Name>Bcms.Replication</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.ScheduleEngine\Bcms.ScheduleEngine.csproj">
      <Project>{4EA537A2-0DA2-4253-A646-E5189037AD55}</Project>
      <Name>Bcms.ScheduleEngine</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>