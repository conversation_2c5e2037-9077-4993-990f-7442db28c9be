﻿﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class FastCopyJob : BaseEntity
    {
        [DataMember]
        public int FastCopyId
        {
            get;
            set;
        }
        [DataMember]
        public string SourceDirectory
        {
            get;
            set;
        }
        [DataMember]
        public string DestinationDirectory
        {
            get;
            set;
        }
        [DataMember]
        public string LastSuccessfullReplTime
        {
            get;
            set;
        }
        [DataMember]
        public string NextScheduleRepTime
        {
            get;
            set;
        }
        [DataMember]
        public int JobCount
        {
            get;
            set;
        }
        [DataMember]
        public int LastReplicationCount
        {
            get;
            set;
        }
        [DataMember]
        public string LastFileName
        {
            get;
            set;
        }
        [DataMember]
        public string LastFileSize
        {
            get;
            set;
        }

        [DataMember]
        public int Mode
        {
            get;
            set;
        }
        public FastCopyJob()
            : base()
        {
        }
         
    }
}
 