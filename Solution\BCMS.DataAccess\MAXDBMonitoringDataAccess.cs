﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;


namespace Bcms.DataAccess
{
    public class MAXDBMonitoringDataAccess : BaseDataAccess
    {
        public static bool AddMAXDBMonitorLogs(MAXDBMonitoring maxdbmonitoring)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("maxdb_logs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, maxdbmonitoring.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDBIPAddress", DbType.AnsiString, maxdbmonitoring.PRDBIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBIPAddress", DbType.AnsiString, maxdbmonitoring.DRDBIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iPRDBName", DbType.AnsiString, maxdbmonitoring.PRDBName);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBName", DbType.AnsiString, maxdbmonitoring.DRDBName);
                    Database.AddInParameter(cmd, Dbstring + "iPRDBState", DbType.AnsiString, maxdbmonitoring.PRDBState);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBState", DbType.AnsiString, maxdbmonitoring.DRDBState);                   
                    Database.AddInParameter(cmd, Dbstring + "iPRDBVersion", DbType.AnsiString, maxdbmonitoring.PRDBVersion);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBVersion", DbType.AnsiString, maxdbmonitoring.DRDBVersion);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }
    }

     public class MAXDBReplicationMonitoringDataAccess : BaseDataAccess
    {
        public static bool AddMAXDBReplMonitorLogs(MAXDBReplMonitoring maxdbreplmonitoring)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("maxdbrepl_logs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, maxdbreplmonitoring.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iLastGeneratedLogSeq", DbType.AnsiString, maxdbreplmonitoring.LastGeneratedLogSeq);
                    Database.AddInParameter(cmd, Dbstring + "iLastGeneratedLogTime", DbType.AnsiString, maxdbreplmonitoring.LastGeneratedLogTime);
                    Database.AddInParameter(cmd, Dbstring + "iLastAppliedLogSeq", DbType.AnsiString, maxdbreplmonitoring.LastappliedLogSeq);
                    Database.AddInParameter(cmd, Dbstring + "iLastTransLogSeq", DbType.AnsiString, maxdbreplmonitoring.LastTransLogSeq);
                    Database.AddInParameter(cmd, Dbstring + "iLastAppliedLogTime", DbType.AnsiString, maxdbreplmonitoring.LastappliedLogTime);
                    Database.AddInParameter(cmd, Dbstring + "iPRLastLogPage", DbType.AnsiString, maxdbreplmonitoring.PRLastLogPage);
                      Database.AddInParameter(cmd, Dbstring + "iDRLastLogPage", DbType.AnsiString, maxdbreplmonitoring.DRLastLogPage);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogLocation", DbType.AnsiString, maxdbreplmonitoring.PRLogLocation);
                      Database.AddInParameter(cmd, Dbstring + "iDRLogLocation", DbType.AnsiString, maxdbreplmonitoring.DRLogLocation);
                    Database.AddInParameter(cmd, Dbstring + "iDatalag", DbType.AnsiString, maxdbreplmonitoring.datalag);                    

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }
        public static bool AddMAXDBReplLogpage(string LastLogPageApplied, int InfraObjectId)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("maxdb_appl_logs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iLastLogPageApplied", DbType.AnsiString, LastLogPageApplied);
                   
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraObjectId);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }
    }

    
     

}
