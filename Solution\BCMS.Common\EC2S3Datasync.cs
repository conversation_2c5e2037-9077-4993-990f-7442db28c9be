﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "EC2S3Datasync", Namespace = "http://www.BCMS.com/types")]
    public class EC2S3Datasync : BaseEntity
    {

        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }      

        [DataMember]
        public string SourceDataPath
        {
            get;
            set;
        }

        [DataMember]
        public string Ec2InstanceId
        {
            get;
            set;
        }

        [DataMember]
        public string Ec2InstanceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string S3BucketLocation
        {
            get;
            set;
        }

        [DataMember]
        public string S3BucketTimeStamp
        {
            get;
            set;
        }

        #endregion Properties

    }
}