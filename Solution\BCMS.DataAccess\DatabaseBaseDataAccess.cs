﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;

namespace Bcms.DataAccess
{
    public class DatabaseBaseDataAccess : BaseDataAccess
    {
        public static Common.DatabaseBase GetDatabaseByInfraObjectId(int groupId, int prServerId, int drServerId)
        {
            var database = new Common.DatabaseBase();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBase_GetByInfraId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            if (Convert.ToInt32(myDatabaseReader["Type"]) == prServerId)
                            {
                                database.PRName = myDatabaseReader["Name"].ToString();

                                database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);

                                database.PRVersion = StringHelper.Md5Decrypt(myDatabaseReader["Version"].ToString());
                                database.Type = StringHelper.Md5Decrypt(myDatabaseReader["Type"].ToString());
                                database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);

                                if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                                    database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                                database.Mode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);

                            }
                            else if (Convert.ToInt32(myDatabaseReader["Type"]) == drServerId)
                            {
                                database.DRName = myDatabaseReader["Name"].ToString();
                                database.DRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                                database.DRVersion = StringHelper.Md5Decrypt(myDatabaseReader["Version"].ToString());
                                database.Type = StringHelper.Md5Decrypt(myDatabaseReader["Type"].ToString());
                                database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);

                                if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                                    database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

                                database.Mode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                            }

                            //if (Convert.ToInt32(myDatabaseReader[4]) == prServerId)
                            //{
                            //    database.PRDatabaseName = myDatabaseReader[3].ToString();
                            //    database.PRServerId = Convert.ToInt32(myDatabaseReader[4]);
                            //    database.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader[5].ToString());
                            //    database.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader[6].ToString());
                            //    database.Port = Convert.ToInt32(myDatabaseReader[7]);
                            //    database.PRBackupRestorePath = myDatabaseReader[18].ToString();
                            //    database.PRNetworkSharedPath = myDatabaseReader[19].ToString();
                            //}
                            //else if (Convert.ToInt32(myDatabaseReader[4]) == drServerId)
                            //{
                            //    database.DRDatabaseName = myDatabaseReader[3].ToString();
                            //    database.DRServerId = Convert.ToInt32(myDatabaseReader[4]);
                            //    database.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader[5].ToString());
                            //    database.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader[6].ToString());
                            //    database.Port = Convert.ToInt32(myDatabaseReader[7]);
                            //    database.DRBackupRestorePath = myDatabaseReader[18].ToString();
                            //    database.DRNetworkSharedPath = myDatabaseReader[19].ToString();
                            //}
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By InfraObject Id - " + groupId, exc);
            }
            return database;
        }

        //public static DatabaseBase GetDatabaseByGroupId(int groupId)
        //{
        //    var database = new DatabaseBase();

        //    try
        //    {
        //        var db = DatabaseFactory.CreateDatabase();

        //        using (var dbCommand = db.GetStoredProcCommand("DatabaseBase_GetByGroupIdAndDBtype"))
        //        {
        //            db.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, groupId);

        //            using (var mydbReader = db.ExecuteReader(dbCommand))
        //            {
        //                while (mydbReader.Read())
        //                {

        //                    var dbtype = (DatabaseType)Enum.Parse(typeof(DatabaseType), mydbReader[1].ToString(), true);
        //                    var dbKind = mydbReader[2].ToString();
        //                    switch (dbtype)
        //                    {
        //                        case DatabaseType.Oracle:

        //                            switch (dbKind)
        //                            {

        //                                case "PRDatabase":
        //                                    database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
        //                                    database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
        //                                    //  database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);


        //                                    database.PRDatabaseType = dbtype;
        //                                    database.PRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseOracle.PROracleSID = mydbReader["OracleSID"].ToString();
        //                                    database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
        //                                    database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
        //                                    database.DatabaseOracle.PRPort = Convert.ToInt32(mydbReader["Port"]);
        //                                    break;
        //                                case "DRDatabase":
        //                                    database.DRName = mydbReader["Name"].ToString();
        //                                    database.DRDatabaseType = dbtype;
        //                                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

        //                                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);

        //                                    database.DRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseOracle.DROracleSID = mydbReader["OracleSID"].ToString();
        //                                    database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
        //                                    database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
        //                                    database.DatabaseOracle.DRPort = Convert.ToInt32(mydbReader["Port"]);
        //                                    break;

        //                                //case "PRDatabase":
        //                                //    database.PRName = mydbReader[0].ToString();
        //                                //    database.PRServerId = Convert.ToInt32(mydbReader[3]);
        //                                //    database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                //    database.PRDatabaseType = dbtype;
        //                                //    database.PRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
        //                                //    database.DatabaseOracle.PROracleSID = mydbReader[6].ToString();
        //                                //    database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
        //                                //    database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
        //                                //    database.DatabaseOracle.PRPort = Convert.ToInt32(mydbReader[9]);
        //                                //    break;
        //                                //case "DRDatabase":
        //                                //    database.DRName = mydbReader[0].ToString();
        //                                //    database.DRDatabaseType = dbtype;
        //                                //    database.DRServerId = Convert.ToInt32(mydbReader[3]);
        //                                //    database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                //    database.DRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
        //                                //    database.DatabaseOracle.DROracleSID = mydbReader[6].ToString();
        //                                //    database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
        //                                //    database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
        //                                //    database.DatabaseOracle.DRPort = Convert.ToInt32(mydbReader[9]);
        //                                //    break;
        //                            }
        //                            break;

        //                        case DatabaseType.Sql:

        //                            switch (dbKind)
        //                            {
        //                                case "PRDatabase":
        //                                    database.PRName = mydbReader["Name"].ToString();
        //                                    database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    //database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);


        //                                    database.PRDatabaseType = dbtype;
        //                                    database.PRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseSql.PRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
        //                                    database.DatabaseSql.PRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
        //                                    database.DatabaseSql.PRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
        //                                    database.DatabaseSql.PRPort = Convert.IsDBNull(mydbReader["Port"]) ? 0 : Convert.ToInt32(mydbReader["Port"]);
        //                                    database.DatabaseSql.PRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
        //                                    database.DatabaseSql.PRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
        //                                    database.DatabaseSql.PRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
        //                                    database.DatabaseSql.PRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
        //                                    database.DatabaseSql.PRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
        //                                    database.DatabaseSql.PRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);
        //                                    break;
        //                                case "DRDatabase":
        //                                    database.DRName = mydbReader["Name"].ToString();
        //                                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

        //                                    database.DRDatabaseType = dbtype;
        //                                    database.DRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseSql.DRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
        //                                    database.DatabaseSql.DRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
        //                                    database.DatabaseSql.DRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
        //                                    database.DatabaseSql.DRPort = Convert.IsDBNull(mydbReader["Port"]) ? 0 : Convert.ToInt32(mydbReader["Port"]);
        //                                    database.DatabaseSql.DRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
        //                                    database.DatabaseSql.DRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
        //                                    database.DatabaseSql.DRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
        //                                    database.DatabaseSql.DRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
        //                                    database.DatabaseSql.DRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
        //                                    database.DatabaseSql.DRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);



        //                                    break;
        //                            }
        //                            break;

        //                        case DatabaseType.Exchange:

        //                            switch (dbKind)
        //                            {
        //                                case "PRDatabase":
        //                                    database.PRName = mydbReader["Name"].ToString();
        //                                    database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    // database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

        //                                    database.PRDatabaseType = dbtype;
        //                                    database.PRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseExchange.PRStorageGroupName = mydbReader["StorageGroupName"].ToString();
        //                                    database.DatabaseExchange.PRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
        //                                    break;
        //                                case "DRDatabase":
        //                                    database.DRName = mydbReader["Name"].ToString();
        //                                    database.DRDatabaseType = dbtype;
        //                                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

        //                                    database.DRMode = Convert.IsDBNull(mydbReader["Mode"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["Mode"]), true);
        //                                    database.DatabaseExchange.DRStorageGroupName = mydbReader["StorageGroupName"].ToString();
        //                                    database.DatabaseExchange.DRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
        //                                    break;
        //                            }
        //                            break;

        //                        case DatabaseType.OracleRac:

        //                            switch (dbKind)
        //                            {
        //                                case "PRDatabase":
        //                                    database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
        //                                    database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
        //                                    //  database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
        //                                    database.PRDatabaseType = dbtype;
        //                                    //database.PRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
        //                                    //database.DatabaseOracle.PROracleSID = mydbReader[6].ToString();
        //                                    //database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
        //                                    //database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
        //                                    //database.DatabaseOracle.PRPort = Convert.ToInt32(mydbReader[9]);
        //                                    break;
        //                                case "DRDatabase":
        //                                    database.DRName = mydbReader["Name"].ToString();
        //                                    database.DRDatabaseType = dbtype;
        //                                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
        //                                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
        //                                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

        //                                    //database.DRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
        //                                    //database.DatabaseOracle.DROracleSID = mydbReader[6].ToString();
        //                                    //database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
        //                                    //database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
        //                                    //database.DatabaseOracle.DRPort = Convert.ToInt32(mydbReader[9]);
        //                                    break;
        //                            }
        //                            break;
        //                    }
        //                }
        //            }
        //        }

        //    }
        //    catch (Exception exc)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By InfraObject Id - " + groupId, exc);
        //    }
        //    return database;
        //}

        public static DatabaseBase GetDatabaseByInfraObjectId(int infraObjectId)
        {
            var database = new DatabaseBase(); 

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand("DatabaseBase_GtByInfraIdnDBtyp"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var mydbReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mydbReader.Read())
                        {

                            var dbtype = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(mydbReader["DatabaseType"]), true);
                            var dbKind = Convert.ToString(mydbReader["Type"]);
                            switch (dbtype)
                            {
                                case DatabaseType.Oracle:

                                    switch (dbKind)
                                    {

                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            //  database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            database.PRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRDatabaseType = dbtype;
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseOracle.PROracleSID = mydbReader["OracleSID"].ToString();
                                            database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
                                            database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
                                            database.DatabaseOracle.PRPort = Convert.ToInt32(mydbReader["Port"]);

                                            database.DatabaseOracle.PRArchivePath = Convert.IsDBNull(mydbReader["Archive"]) ? string.Empty : Convert.ToString(mydbReader["Archive"].ToString());
                                            database.DatabaseOracle.PROracleHome = Convert.IsDBNull(mydbReader["Home"]) ? string.Empty : Convert.ToString(mydbReader["Home"].ToString());
                                            database.DatabaseOracle.PRRedoPath = Convert.IsDBNull(mydbReader["Redo"]) ? string.Empty : Convert.ToString(mydbReader["Redo"].ToString());
                                            database.DatabaseOracle.PRASMGridPath = Convert.IsDBNull(mydbReader["ASMGrid"]) ? string.Empty : Convert.ToString(mydbReader["ASMGrid"].ToString());
                                            database.DatabaseOracle.PRInstanceName = Convert.IsDBNull(mydbReader["InstanceName"]) ? string.Empty : Convert.ToString(mydbReader["InstanceName"].ToString());
                                            database.DatabaseOracle.IsAsm = Convert.IsDBNull(mydbReader["IsASM"]) ? 0: Convert.ToInt32(mydbReader["IsASM"].ToString());
                                            database.DatabaseOracle.PRAsmInstanceName = Convert.IsDBNull(mydbReader["ASMInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["ASMInstanceName"].ToString());
                                            database.DatabaseOracle.DatabaseConnectivity = Convert.IsDBNull(mydbReader["DatabaseConnectivity"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseConnectivity"].ToString());
                                            database.DatabaseOracle.PRASMPath = Convert.IsDBNull(mydbReader["ASMPath"]) ? string.Empty : Convert.ToString(mydbReader["ASMPath"].ToString());
                                            database.DatabaseOracle.PRASMUserName = Convert.IsDBNull(mydbReader["ASMUserName"]) ? string.Empty : Convert.ToString(mydbReader["ASMUserName"].ToString());
                                            database.DatabaseOracle.PRASMPassword = Convert.IsDBNull(mydbReader["ASMPassword"]) ? string.Empty : Convert.ToString(mydbReader["ASMPassword"].ToString());

                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            database.DRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseOracle.DROracleSID = mydbReader["OracleSID"].ToString();
                                            database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(mydbReader["UserName"].ToString());
                                            database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(mydbReader["Password"].ToString());
                                            database.DatabaseOracle.DRPort = Convert.ToInt32(mydbReader["Port"]);

                                            database.DatabaseOracle.DRArchivePath = Convert.IsDBNull(mydbReader["Archive"]) ? string.Empty : Convert.ToString(mydbReader["Archive"].ToString());
                                            database.DatabaseOracle.DROracleHome = Convert.IsDBNull(mydbReader["Home"]) ? string.Empty : Convert.ToString(mydbReader["Home"].ToString());
                                            database.DatabaseOracle.DRRedoPath = Convert.IsDBNull(mydbReader["Redo"]) ? string.Empty : Convert.ToString(mydbReader["Redo"].ToString());
                                            database.DatabaseOracle.DRASMGridPath = Convert.IsDBNull(mydbReader["ASMGrid"]) ? string.Empty : Convert.ToString(mydbReader["ASMGrid"].ToString());
                                            database.DatabaseOracle.DRInstanceName = Convert.IsDBNull(mydbReader["InstanceName"]) ? string.Empty : Convert.ToString(mydbReader["InstanceName"].ToString());
                                            database.DatabaseOracle.IsAsm = Convert.IsDBNull(mydbReader["IsASM"]) ? 0 : Convert.ToInt32(mydbReader["IsASM"].ToString());
                                            database.DatabaseOracle.DRAsmInstanceName = Convert.IsDBNull(mydbReader["ASMInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["ASMInstanceName"].ToString());
                                            database.DatabaseOracle.DRASMPath = Convert.IsDBNull(mydbReader["ASMPath"]) ? string.Empty : Convert.ToString(mydbReader["ASMPath"].ToString());
                                            database.DatabaseOracle.DRASMUserName = Convert.IsDBNull(mydbReader["ASMUserName"]) ? string.Empty : Convert.ToString(mydbReader["ASMUserName"].ToString());
                                            database.DatabaseOracle.DRASMPassword = Convert.IsDBNull(mydbReader["ASMPassword"]) ? string.Empty : Convert.ToString(mydbReader["ASMPassword"].ToString());
                                            break;


                                    }
                                    break;

                                case DatabaseType.Sql:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = mydbReader["Name"].ToString();
                                            database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            database.PRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            //database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRDatabaseType = dbtype;
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseSql.PRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
                                            database.DatabaseSql.PRUserName = StringHelper.Md5Decrypt(mydbReader["MSSqlUserName"].ToString());
                                            database.DatabaseSql.PRPassword = StringHelper.Md5Decrypt(mydbReader["MSSqlPassword"].ToString());
                                            database.DatabaseSql.PRPort = Convert.IsDBNull(mydbReader["MSSqlPort"]) ? 0 : Convert.ToInt32(mydbReader["MSSqlPort"]);
                                            database.DatabaseSql.PRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
                                            database.DatabaseSql.PRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
                                            database.DatabaseSql.PRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
                                            database.DatabaseSql.PRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
                                            database.DatabaseSql.PRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
                                            database.DatabaseSql.PRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);
                                            database.DatabaseSql.PRInstancename = Convert.IsDBNull(mydbReader["sqlInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["sqlInstanceName"]);
                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            database.DRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRDatabaseType = dbtype;
                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseSql.DRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
                                            database.DatabaseSql.DRUserName = StringHelper.Md5Decrypt(mydbReader["MSSqlUserName"].ToString());
                                            database.DatabaseSql.DRPassword = StringHelper.Md5Decrypt(mydbReader["MSSqlPassword"].ToString());
                                            database.DatabaseSql.DRPort = Convert.IsDBNull(mydbReader["MSSqlPort"]) ? 0 : Convert.ToInt32(mydbReader["MSSqlPort"]);
                                            database.DatabaseSql.DRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
                                            database.DatabaseSql.DRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
                                            database.DatabaseSql.DRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
                                            database.DatabaseSql.DRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
                                            database.DatabaseSql.DRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
                                            database.DatabaseSql.DRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);
                                            database.DatabaseSql.DRInstancename = Convert.IsDBNull(mydbReader["sqlInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["sqlInstanceName"]);

                                            break;
                                    }
                                    break;

                                case DatabaseType.Exchange:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = mydbReader["Name"].ToString();
                                            database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            // database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRDatabaseType = dbtype;
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseExchange.PRStorageGroupName = mydbReader["StorageGroupName"].ToString();
                                            database.DatabaseExchange.PRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseExchange.DRStorageGroupName = mydbReader["StorageGroupName"].ToString();
                                            database.DatabaseExchange.DRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
                                            break;
                                    }
                                    break;
                                case DatabaseType.ExchangeDag:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = mydbReader["Name"].ToString();
                                            database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            // database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRDatabaseType = dbtype;
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DataBaseExchangeDAG.PRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
                                            database.DataBaseExchangeDAG.PRAuthenticationType = mydbReader["AuthenticationType"].ToString();
                                            database.DataBaseExchangeDAG.PRProtocolType = mydbReader["ProtocolType"].ToString();

                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);

                                            database.DataBaseExchangeDAG.PRMailBoxDBName = mydbReader["MailBoxDBName"].ToString();
                                            database.DataBaseExchangeDAG.PRAuthenticationType = mydbReader["AuthenticationType"].ToString();
                                            database.DataBaseExchangeDAG.PRProtocolType = mydbReader["ProtocolType"].ToString();

                                            break;
                                    }
                                    break;

                                case DatabaseType.OracleRac:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            //  database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            database.PRDatabaseType = dbtype;
                                            database.PRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            //database.PRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
                                            //database.DatabaseOracle.PROracleSID = mydbReader[6].ToString();
                                            //database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
                                            //database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
                                            //database.DatabaseOracle.PRPort = Convert.ToInt32(mydbReader[9]);
                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            //database.DRMode = mydbReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), mydbReader.GetString(5), true);
                                            //database.DatabaseOracle.DROracleSID = mydbReader[6].ToString();
                                            //database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(mydbReader[7].ToString());
                                            //database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(mydbReader[8].ToString());
                                            //database.DatabaseOracle.DRPort = Convert.ToInt32(mydbReader[9]);
                                            break;
                                    }
                                    break;
                                case DatabaseType.Postgres9x:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DatabasePostgres9x.PRDatabaseName = Convert.IsDBNull(mydbReader["DataBaseName"]) ? string.Empty : Convert.ToString(mydbReader["DataBaseName"]);
                                            database.DatabasePostgres9x.PRUserName = Convert.IsDBNull(mydbReader["UserName"]) ? string.Empty : Convert.ToString(mydbReader["UserName"]);
                                            database.DatabasePostgres9x.PRPassword = Convert.IsDBNull(mydbReader["Password"]) ? string.Empty : Convert.ToString(mydbReader["Password"]);
                                            database.DatabasePostgres9x.PRPort = Convert.IsDBNull(mydbReader["Port"]) ? 0 : Convert.ToInt32(mydbReader["Port"]);
                                            database.DatabasePostgres9x.PRDBbinDirectory = Convert.IsDBNull(mydbReader["DBbinDirectory"]) ? string.Empty : Convert.ToString(mydbReader["DBbinDirectory"]);
                                            database.DatabasePostgres9x.PRDBDataDirectory = Convert.IsDBNull(mydbReader["DBDataDirectory"]) ? string.Empty : Convert.ToString(mydbReader["DBDataDirectory"]);
                                            database.DatabasePostgres9x.PRSULogin = Convert.IsDBNull(mydbReader["SULogin"]) ? string.Empty : Convert.ToString(mydbReader["SULogin"]);
                                            database.DatabasePostgres9x.PRServiceName = Convert.IsDBNull(mydbReader["ServiceName"]) ? string.Empty : Convert.ToString(mydbReader["ServiceName"]);
                                            database.PRDatabaseType = dbtype;
                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.DRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DatabasePostgres9x.DRDatabaseName = Convert.IsDBNull(mydbReader["DataBaseName"]) ? string.Empty : Convert.ToString(mydbReader["DataBaseName"]);
                                            database.DatabasePostgres9x.DRUserName = Convert.IsDBNull(mydbReader["UserName"]) ? string.Empty : Convert.ToString(mydbReader["UserName"]);
                                            database.DatabasePostgres9x.DRPassword = Convert.IsDBNull(mydbReader["Password"]) ? string.Empty : Convert.ToString(mydbReader["Password"]);
                                            database.DatabasePostgres9x.DRPort = Convert.IsDBNull(mydbReader["Port"]) ? 0 : Convert.ToInt32(mydbReader["Port"]);
                                            database.DatabasePostgres9x.DRDBbinDirectory = Convert.IsDBNull(mydbReader["DBbinDirectory"]) ? string.Empty : Convert.ToString(mydbReader["DBbinDirectory"]);
                                            database.DatabasePostgres9x.DRDBDataDirectory = Convert.IsDBNull(mydbReader["DBDataDirectory"]) ? string.Empty : Convert.ToString(mydbReader["DBDataDirectory"]);
                                            database.DatabasePostgres9x.DRSULogin = Convert.IsDBNull(mydbReader["SULogin"]) ? string.Empty : Convert.ToString(mydbReader["SULogin"]);
                                            database.DatabasePostgres9x.DRServiceName = Convert.IsDBNull(mydbReader["ServiceName"]) ? string.Empty : Convert.ToString(mydbReader["ServiceName"]);
                                            database.PRDatabaseType = dbtype;
                                            break;
                                    }
                                    break;

                                case DatabaseType.SQLNative2008:
                                    switch (dbKind)
                                    {
                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DatabaseSqlNative2008.PRDatabaseName = Convert.IsDBNull(mydbReader["SqlNativeDBName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeDBName"]);
                                            database.DatabaseSqlNative2008.PRAuthenticationMode = Convert.IsDBNull(mydbReader["SqlNativeAuthMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["SqlNativeAuthMode"]), true);
                                            database.PRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            if (database.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                            {
                                                database.DatabaseSqlNative2008.PRUserName = StringHelper.Md5Decrypt(Convert.IsDBNull(mydbReader["SqlNativeUserName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeUserName"]));
                                                database.DatabaseSqlNative2008.PRPassword = StringHelper.Md5Decrypt(Convert.IsDBNull(mydbReader["SqlNativePassword"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativePassword"]));
                                            }
                                            database.DatabaseSqlNative2008.PRPort = Convert.IsDBNull(mydbReader["SqlNativePort"]) ? 0 : Convert.ToInt32(mydbReader["SqlNativePort"]);
                                            // database.DatabaseSqlNative2008.PRAuthenticationMode = Convert.IsDBNull(mydbReader["sqlnativeauthmode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["sqlnativeauthmode"]), true);
                                            database.DatabaseSqlNative2008.PRInstancename = Convert.IsDBNull(mydbReader["SqlNativeInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeInstanceName"]);

                                            //    // Convert.IsDBNull(myDatabaseReader["AuthenticationMode"])
                                            //? SqlAuthenticateType.Undefined
                                            //: (SqlAuthenticateType)
                                            //  Enum.Parse(typeof(SqlAuthenticateType),
                                            //             Convert.ToString(myDatabaseReader["AuthenticationMode"]), true);

                                            database.PRDatabaseType = dbtype;
                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.DRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRVersion = Convert.IsDBNull(mydbReader["Version"]) ? string.Empty : Convert.ToString(mydbReader["Version"]);
                                            database.DatabaseSqlNative2008.DRDatabaseName = Convert.IsDBNull(mydbReader["SqlNativeDBName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeDBName"]);
                                            database.DatabaseSqlNative2008.DRAuthenticationMode = Convert.IsDBNull(mydbReader["SqlNativeAuthMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["SqlNativeAuthMode"]), true);
                                            if (database.DatabaseSqlNative2008.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                            {
                                                database.DatabaseSqlNative2008.DRUserName = StringHelper.Md5Decrypt(Convert.IsDBNull(mydbReader["SqlNativeUserName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeUserName"]));
                                                database.DatabaseSqlNative2008.DRPassword = StringHelper.Md5Decrypt(Convert.IsDBNull(mydbReader["SqlNativePassword"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativePassword"]));
                                            }
                                            database.DatabaseSqlNative2008.DRPort = Convert.IsDBNull(mydbReader["SqlNativePort"]) ? 0 : Convert.ToInt32(mydbReader["SqlNativePort"]);
                                            //dont delete instance changes please its IMP 
                                            database.DatabaseSqlNative2008.DRInstancename = Convert.IsDBNull(mydbReader["SqlNativeInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["SqlNativeInstanceName"]);
                                            //  database.DatabaseSqlNative2008.DRAuthenticationMode = Convert.IsDBNull(mydbReader["sqlnativeauthmode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["sqlnativeauthmode"]), true);
                                            database.PRDatabaseType = dbtype;
                                            break;

                                    }
                                    break;

                                case DatabaseType.MYSQL:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":

                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);

                                            database.PRDatabaseType = dbtype;
                                            database.PRMode = Convert.IsDBNull(mydbReader["MODETYPE"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["MODETYPE"]), true);
                                            database.DatabaseMysql.PRDatabaseSID = mydbReader["mysqldatabsesid"].ToString();
                                            //database.DatabaseMysql.PRDatabaseType = mydbReader["mysqldatabsestype"].ToString();
                                            database.DatabaseMysql.PRUserName = StringHelper.Md5Decrypt(mydbReader["mysqldbuser"].ToString());
                                            database.DatabaseMysql.PRPassword = StringHelper.Md5Decrypt(mydbReader["mysqluserpassword"].ToString());
                                            database.DatabaseMysql.PRPort = Convert.ToInt32(mydbReader["mysqlport"]);
                                            break;
                                        case "DRDatabase":
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRMode = Convert.IsDBNull(mydbReader["MODETYPE"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["MODETYPE"]), true);
                                            database.DatabaseMysql.DRDatabaseSID = mydbReader["mysqldatabsesid"].ToString();
                                            //database.DatabaseMysql.DRDatabaseType = mydbReader["mysqldatabsestype"].ToString();
                                            database.DatabaseMysql.DRUserName = StringHelper.Md5Decrypt(mydbReader["mysqldbuser"].ToString());
                                            database.DatabaseMysql.DRPassword = StringHelper.Md5Decrypt(mydbReader["mysqluserpassword"].ToString());
                                            database.DatabaseMysql.DRPort = Convert.ToInt32(mydbReader["mysqlport"]);
                                            break;

                                    }
                                    break;

                                case DatabaseType.Sybase:

                                    switch (dbKind)
                                    {
                                        case "PRDatabase":

                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);

                                            database.PRDatabaseType = dbtype;
                                            database.DataBaseSybase.PRDatabaseSID = mydbReader["sybaseDatabaseSID"].ToString();
                                            database.DataBaseSybase.PRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseUserName"].ToString());
                                            database.DataBaseSybase.PRPassword = StringHelper.Md5Decrypt(mydbReader["sybasePassword"].ToString());
                                            database.DataBaseSybase.PRPort = Convert.ToString(mydbReader["sybasePort"]);

                                            database.DataBaseSybase.PRTransactionFileLocation = mydbReader["TransactionFileLocation"].ToString();
                                            database.DataBaseSybase.EnvironmentPath = mydbReader["EnvironmentPath"].ToString();
                                            database.DataBaseSybase.PRSybaseDataServerName = mydbReader["SybaseDataServerName"].ToString();
                                            database.DataBaseSybase.PRSybaseBackupServer = mydbReader["SybaseBackupServer"].ToString();
                                            database.DataBaseSybase.IsStandByaccess = Convert.ToInt32(mydbReader["IsStandByaccess"]);
                                            break;
                                        case "DRDatabase":
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRDatabaseType = dbtype;
                                            database.DataBaseSybase.DRDatabaseSID = mydbReader["sybaseDatabaseSID"].ToString();
                                            database.DataBaseSybase.DRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseUserName"].ToString());
                                            database.DataBaseSybase.DRPassword = StringHelper.Md5Decrypt(mydbReader["sybasePassword"].ToString());
                                            database.DataBaseSybase.DRPort = Convert.ToString(mydbReader["sybasePort"]);

                                            database.DataBaseSybase.PRTransactionFileLocation = mydbReader["TransactionFileLocation"].ToString();
                                            database.DataBaseSybase.EnvironmentPath = mydbReader["EnvironmentPath"].ToString();
                                            database.DataBaseSybase.DRSybaseDataServerName = mydbReader["SybaseDataServerName"].ToString();
                                            database.DataBaseSybase.DRSybaseBackupServer = mydbReader["SybaseBackupServer"].ToString();
                                            database.DataBaseSybase.IsStandByaccess = Convert.ToInt32(mydbReader["IsStandByaccess"]);
                                            break;
                                    }
                                    break;

                                case DatabaseType.SybaseWithSRS:
                                    switch (dbKind)
                                    {
                                        case "PRDatabase":

                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);

                                            database.PRDatabaseType = dbtype;
                                            database.DataBaseSybaseWithSRS.PRDatabaseSID = mydbReader["sybaseSRSDatabaseSID"].ToString();
                                            database.DataBaseSybaseWithSRS.PRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseSRSUserName"].ToString());
                                            database.DataBaseSybaseWithSRS.PRPassword = StringHelper.Md5Decrypt(mydbReader["sybaseSRSPassword"].ToString());
                                            database.DataBaseSybaseWithSRS.PRPort = Convert.ToInt32(mydbReader["sybaseSRSPort"]);

                                            database.DataBaseSybaseWithSRS.PRSybaseEnv_Path = mydbReader["SybaseSRSEnv_Path"].ToString();
                                            database.DataBaseSybaseWithSRS.PRSybaseDataServerName = mydbReader["SybaseSRSDataServerName"].ToString();
                                            database.DataBaseSybaseWithSRS.PRSybaseBackupServer = mydbReader["SybaseSRSBackupServer"].ToString();

                                            break;
                                        case "DRDatabase":
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRDatabaseType = dbtype;
                                            database.DataBaseSybaseWithSRS.DRDatabaseSID = mydbReader["sybaseSRSDatabaseSID"].ToString();
                                            database.DataBaseSybaseWithSRS.DRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseSRSUserName"].ToString());
                                            database.DataBaseSybaseWithSRS.DRPassword = StringHelper.Md5Decrypt(mydbReader["sybaseSRSPassword"].ToString());
                                            database.DataBaseSybaseWithSRS.DRPort = Convert.ToInt32(mydbReader["sybaseSRSPort"]);

                                            database.DataBaseSybaseWithSRS.DRSybaseEnv_Path = mydbReader["SybaseSRSEnv_Path"].ToString();
                                            database.DataBaseSybaseWithSRS.DRSybaseDataServerName = mydbReader["SybaseSRSDataServerName"].ToString();
                                            database.DataBaseSybaseWithSRS.DRSybaseBackupServer = mydbReader["SybaseSRSBackupServer"].ToString();
                                            break;
                                    }
                                    break;
                                case DatabaseType.MaxDB:

                                    switch (dbKind)
                                    {

                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                                            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);
                                            //  database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            database.PRDatabaseType = dbtype;
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DataBaseMaxDB.PRDatabaseSID = mydbReader["maxDatabaseSID"].ToString();
                                            database.DataBaseMaxDB.PRUserName = StringHelper.Md5Decrypt(mydbReader["maxdbUserName"].ToString());
                                            database.DataBaseMaxDB.PRPassword = StringHelper.Md5Decrypt(mydbReader["maxdbPassword"].ToString());
                                            database.DataBaseMaxDB.PRPort = Convert.ToInt32(mydbReader["maxdbPort"]);

                                            database.DataBaseMaxDB.PRInstallationPath = mydbReader["maxdbInstallationPath"].ToString();
                                            database.DataBaseMaxDB.PRMediumName = mydbReader["maxdbMediumName"].ToString();
                                            database.DataBaseMaxDB.PRLogfileName = mydbReader["maxdbLogfileName"].ToString();
                                            database.DataBaseMaxDB.PRLogPath = mydbReader["maxdbLogPath"].ToString();
                                            database.DataBaseMaxDB.PRInstanceName = mydbReader["maxdbInstanceName"].ToString();

                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader["Name"].ToString();
                                            database.DRDatabaseType = dbtype;
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DataBaseMaxDB.DRDatabaseSID = mydbReader["maxDatabaseSID"].ToString();
                                            database.DataBaseMaxDB.DRUserName = StringHelper.Md5Decrypt(mydbReader["maxdbUserName"].ToString());
                                            database.DataBaseMaxDB.DRPassword = StringHelper.Md5Decrypt(mydbReader["maxdbPassword"].ToString());
                                            database.DataBaseMaxDB.DRPort = Convert.ToInt32(mydbReader["maxdbPort"]);

                                            database.DataBaseMaxDB.DRInstallationPath = mydbReader["maxdbInstallationPath"].ToString();
                                            database.DataBaseMaxDB.DRMediumName = mydbReader["maxdbMediumName"].ToString();
                                            database.DataBaseMaxDB.DRLogfileName = mydbReader["maxdbLogfileName"].ToString();
                                            database.DataBaseMaxDB.DRLogPath = mydbReader["maxdbLogPath"].ToString();
                                            database.DataBaseMaxDB.DRInstanceName = mydbReader["maxdbInstanceName"].ToString();
                                            break;


                                    }
                                    break;

                                case DatabaseType.DB2:

                                    switch (dbKind)
                                    {

                                        case "PRDatabase":
                                            database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.PRName = mydbReader[0].ToString();
                                            database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(mydbReader["DatabaseType"]), true);
                                            database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseDb2.DatabaseSID = mydbReader["DB2DatabaseSID"].ToString();
                                            database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(mydbReader["DB2UserName"].ToString());
                                            database.DatabaseDb2.Password = StringHelper.Md5Decrypt(mydbReader["DB2Password"].ToString());
                                            database.DatabaseDb2.Port = Convert.ToInt32(mydbReader["DB2Port"]);

                                            break;
                                        case "DRDatabase":
                                            database.DRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                                            database.DRName = mydbReader[0].ToString();
                                            database.DRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(mydbReader["DatabaseType"]), true);
                                            database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                                            database.Racdbnode = Convert.IsDBNull(mydbReader["Isracdbnode"]) ? 0 : Convert.ToInt32(mydbReader["Isracdbnode"]);
                                            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                                                database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                                            database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                                            database.DatabaseDb2.DatabaseSID = mydbReader["DB2DatabaseSID"].ToString();
                                            database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(mydbReader["DB2UserName"].ToString());
                                            database.DatabaseDb2.Password = StringHelper.Md5Decrypt(mydbReader["DB2Password"].ToString());
                                            database.DatabaseDb2.Port = Convert.ToInt32(mydbReader["DB2Port"]);
                                            break;


                                    }
                                    break;

                            }
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By InfraObject Id - " + infraObjectId, exc);
            }
            return database;
        }

        private static DatabaseBase BuildExchangeDatabaseEntity(IDataReader myDatabaseReader, DatabaseBase database)
        {
            switch ((string)myDatabaseReader[2])
            {
                case "PRDatabase":

                    database.PRName = myDatabaseReader["Name"].ToString();
                    database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DatabaseExchange.PRStorageGroupName = myDatabaseReader["StorageGroupName"].ToString();
                    database.DatabaseExchange.PRMailBoxDBName = myDatabaseReader["MailBoxDBName"].ToString();
                    break;
                case "DRDatabase":
                    database.DRName = myDatabaseReader["Name"].ToString();
                    database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    database.DRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

                    database.DRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DatabaseExchange.DRStorageGroupName = myDatabaseReader["StorageGroupName"].ToString();
                    database.DatabaseExchange.DRMailBoxDBName = myDatabaseReader["MailBoxDBName"].ToString();
                    break;
            }
            return database;
        }

        private static DatabaseBase BuildExchangeDagDatabaseEntity(IDataReader myDatabaseReader, DatabaseBase database)
        {
            switch ((string)myDatabaseReader[2])
            {
                case "PRDatabase":

                    database.PRName = myDatabaseReader["Name"].ToString();
                    database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DataBaseExchangeDAG.PRMailBoxDBName = myDatabaseReader["MailBoxDBName"].ToString();
                    database.DataBaseExchangeDAG.PRAuthenticationType = myDatabaseReader["AuthenticationType"].ToString();
                    database.DataBaseExchangeDAG.PRProtocolType = myDatabaseReader["ProtocolType"].ToString();

                    break;
                case "DRDatabase":
                    database.DRName = myDatabaseReader["Name"].ToString();
                    database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    database.DRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

                    database.DRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DataBaseExchangeDAG.DRMailBoxDBName = myDatabaseReader["MailBoxDBName"].ToString();
                    database.DataBaseExchangeDAG.DRAuthenticationType = myDatabaseReader["AuthenticationType"].ToString();
                    database.DataBaseExchangeDAG.DRProtocolType = myDatabaseReader["ProtocolType"].ToString();

                    break;
            }
            return database;
        }

        private static DatabaseBase BuildMSSqlNative2008DataBaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {

            database.PRName = myDatabaseReader["Name"].ToString();
            database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
            database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
            if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
            database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
            database.PRVersion = Convert.IsDBNull(myDatabaseReader["Version"]) ? string.Empty : Convert.ToString(myDatabaseReader["Version"]);
            database.DatabaseSqlNative2008.PRDatabaseName = Convert.IsDBNull(myDatabaseReader["SqlNativeDBName"]) ? string.Empty : Convert.ToString(myDatabaseReader["SqlNativeDBName"]);
            database.DatabaseSqlNative2008.PRAuthenticationMode = Convert.IsDBNull(myDatabaseReader["MSsqlnativeauthmode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(myDatabaseReader["MSsqlnativeauthmode"]), true);
            if (database.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
            {
                database.DatabaseSqlNative2008.PRUserName = StringHelper.Md5Decrypt(Convert.IsDBNull(myDatabaseReader["SqlNativeUserName"]) ? string.Empty : Convert.ToString(myDatabaseReader["SqlNativeUserName"]));
                database.DatabaseSqlNative2008.PRPassword = StringHelper.Md5Decrypt(Convert.IsDBNull(myDatabaseReader["SqlNativePassword"]) ? string.Empty : Convert.ToString(myDatabaseReader["SqlNativePassword"]));
            }
            database.DatabaseSqlNative2008.PRPort = Convert.IsDBNull(myDatabaseReader["SqlNativePort"]) ? 0 : Convert.ToInt32(myDatabaseReader["SqlNativePort"]);
            database.DatabaseSqlNative2008.PRInstancename = Convert.IsDBNull(myDatabaseReader["sqlInstanceName"]) ? string.Empty : Convert.ToString(myDatabaseReader["sqlInstanceName"]);
            //   database.DatabaseSqlNative2008.PRAuthenticationMode = (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(myDatabaseReader["MSsqlnativeauthmode"]), true);


            return database;

        }

        private static DatabaseBase BuildSybaseDBEntity(IDataReader mydbReader, DatabaseBase database)
        {
            switch ((string)mydbReader[2])
            {

                case "PRDatabase":

                    database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
                    database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

                    database.DataBaseSybase.PRDatabaseSID = mydbReader["sybaseDatabaseSID"].ToString();
                    database.DataBaseSybase.PRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseUserName"].ToString());
                    database.DataBaseSybase.PRPassword = StringHelper.Md5Decrypt(mydbReader["sybasePassword"].ToString());
                    database.DataBaseSybase.PRPort = Convert.ToString(mydbReader["sybasePort"]);

                    database.DataBaseSybase.PRTransactionFileLocation = mydbReader["TransactionFileLocation"].ToString();
                    database.DataBaseSybase.EnvironmentPath = mydbReader["EnvironmentPath"].ToString();
                    database.DataBaseSybase.PRSybaseDataServerName = mydbReader["SybaseDataServerName"].ToString();
                    database.DataBaseSybase.PRSybaseBackupServer = mydbReader["SybaseBackupServer"].ToString();
                    database.DataBaseSybase.IsStandByaccess = Convert.ToInt32(mydbReader["IsStandByaccess"]);
                    break;
                case "DRDatabase":
                    database.DRName = mydbReader["Name"].ToString();
                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);

                    database.DataBaseSybase.DRDatabaseSID = mydbReader["sybaseDatabaseSID"].ToString();
                    database.DataBaseSybase.DRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseUserName"].ToString());
                    database.DataBaseSybase.DRPassword = StringHelper.Md5Decrypt(mydbReader["sybasePassword"].ToString());
                    database.DataBaseSybase.DRPort = Convert.ToString(mydbReader["sybasePort"]);

                    database.DataBaseSybase.PRTransactionFileLocation = mydbReader["TransactionFileLocation"].ToString();
                    database.DataBaseSybase.EnvironmentPath = mydbReader["EnvironmentPath"].ToString();
                    database.DataBaseSybase.DRSybaseDataServerName = mydbReader["SybaseDataServerName"].ToString();
                    database.DataBaseSybase.DRSybaseBackupServer = mydbReader["SybaseBackupServer"].ToString();
                    database.DataBaseSybase.IsStandByaccess = Convert.ToInt32(mydbReader["IsStandByaccess"]);
                    break;
            }
            return database;
        }


        private static DatabaseBase BuildSqlDatabaseEntity(IDataRecord mydbReader, DatabaseBase database)
        {
            switch ((string)mydbReader[2])
            {
                case "PRDatabase":

                    //database.PRDBId = Convert.IsDBNull(mydbReader["Id"]) ? 0 : Convert.ToInt32(mydbReader["Id"]);
                    database.PRName = mydbReader["Name"].ToString();
                    database.PRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                    //database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);


                    database.PRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                    database.DatabaseSql.PRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
                    database.DatabaseSql.PRUserName = StringHelper.Md5Decrypt(mydbReader["MSSqlUserName"].ToString());
                    database.DatabaseSql.PRPassword = StringHelper.Md5Decrypt(mydbReader["MSSqlPassword"].ToString());
                    database.DatabaseSql.PRPort = Convert.IsDBNull(mydbReader["MSSqlPort"]) ? 0 : Convert.ToInt32(mydbReader["MSSqlPort"]);
                    database.DatabaseSql.PRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
                    database.DatabaseSql.PRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
                    database.DatabaseSql.PRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
                    database.DatabaseSql.PRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
                    database.DatabaseSql.PRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
                    database.DatabaseSql.PRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);
                    database.DatabaseSql.PRInstancename = Convert.IsDBNull(mydbReader["sqlInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["sqlInstanceName"]);

                    //database.PRName = myDatabaseReader[0].ToString();
                    //database.PRServerId = Convert.ToInt32(myDatabaseReader[3]);
                    //database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);

                    ////database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    //if (!Convert.IsDBNull(myDatabaseReader[4]))
                    //    database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);

                    //database.PRMode = Convert.IsDBNull(myDatabaseReader[5]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader[5]), true);
                    //database.DatabaseSql.PRDatabaseSID = Convert.IsDBNull(myDatabaseReader[10]) ? string.Empty : Convert.ToString(myDatabaseReader[10]);
                    //database.DatabaseSql.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader[11].ToString());
                    //database.DatabaseSql.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader[12].ToString());
                    //database.DatabaseSql.PRPort = Convert.IsDBNull(myDatabaseReader[13]) ? 0 : Convert.ToInt32(myDatabaseReader[13]);
                    //database.DatabaseSql.PRAuthenticationMode = Convert.IsDBNull(myDatabaseReader[14]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(myDatabaseReader[14]), true);
                    //database.DatabaseSql.PRDataFilePath = Convert.IsDBNull(myDatabaseReader[15]) ? string.Empty : Convert.ToString(myDatabaseReader[15]);
                    //database.DatabaseSql.PRTransLogPath = Convert.IsDBNull(myDatabaseReader[16]) ? string.Empty : Convert.ToString(myDatabaseReader[16]);
                    //database.DatabaseSql.PRUndoFilePath = Convert.IsDBNull(myDatabaseReader[17]) ? string.Empty : Convert.ToString(myDatabaseReader[17]);
                    //database.DatabaseSql.PRBackupRestorePath = Convert.IsDBNull(myDatabaseReader[18]) ? string.Empty : Convert.ToString(myDatabaseReader[18]);
                    //database.DatabaseSql.PRNetworkSharedPath = Convert.IsDBNull(myDatabaseReader[19]) ? string.Empty : Convert.ToString(myDatabaseReader[19]);
                    break;
                case "DRDatabase":
                    //database.DRName = myDatabaseReader[0].ToString();
                    //database.DRServerId = Convert.ToInt32(myDatabaseReader[3]);
                    ////database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    //if (!Convert.IsDBNull(myDatabaseReader[4]))
                    //    database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);

                    //database.DRMode = Convert.IsDBNull(myDatabaseReader[5]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader[5]), true);
                    //database.DatabaseSql.DRDatabaseSID = Convert.IsDBNull(myDatabaseReader[10]) ? string.Empty : Convert.ToString(myDatabaseReader[10]);
                    //database.DatabaseSql.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader[11].ToString());
                    //database.DatabaseSql.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader[12].ToString());
                    //database.DatabaseSql.DRPort = Convert.IsDBNull(myDatabaseReader[13]) ? 0 : Convert.ToInt32(myDatabaseReader[13]);
                    //database.DatabaseSql.DRAuthenticationMode = Convert.IsDBNull(myDatabaseReader[14]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(myDatabaseReader[14]), true);
                    //database.DatabaseSql.DRDataFilePath = Convert.IsDBNull(myDatabaseReader[15]) ? string.Empty : Convert.ToString(myDatabaseReader[15]);
                    //database.DatabaseSql.DRTransLogPath = Convert.IsDBNull(myDatabaseReader[16]) ? string.Empty : Convert.ToString(myDatabaseReader[16]);
                    //database.DatabaseSql.DRUndoFilePath = Convert.IsDBNull(myDatabaseReader[17]) ? string.Empty : Convert.ToString(myDatabaseReader[17]);
                    //database.DatabaseSql.DRBackupRestorePath = Convert.IsDBNull(myDatabaseReader[18]) ? string.Empty : Convert.ToString(myDatabaseReader[18]);
                    //database.DatabaseSql.DRNetworkSharedPath = Convert.IsDBNull(myDatabaseReader[19]) ? string.Empty : Convert.ToString(myDatabaseReader[19]);



                    database.DRName = mydbReader["Name"].ToString();
                    database.DRServerId = Convert.ToInt32(mydbReader["ServerId"]);
                    //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                    if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);
                    database.DRMode = Convert.IsDBNull(mydbReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(mydbReader["ModeType"]), true);
                    database.DatabaseSql.DRDatabaseSID = Convert.IsDBNull(mydbReader["DatabaseSID"]) ? string.Empty : Convert.ToString(mydbReader["DatabaseSID"]);
                    database.DatabaseSql.DRUserName = StringHelper.Md5Decrypt(mydbReader["MSSqlUserName"].ToString());
                    database.DatabaseSql.DRPassword = StringHelper.Md5Decrypt(mydbReader["MSSqlPassword"].ToString());
                    database.DatabaseSql.DRPort = Convert.IsDBNull(mydbReader["MSSqlPort"]) ? 0 : Convert.ToInt32(mydbReader["MSSqlPort"]);
                    database.DatabaseSql.DRAuthenticationMode = Convert.IsDBNull(mydbReader["AuthenticationMode"]) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), Convert.ToString(mydbReader["AuthenticationMode"]), true);
                    database.DatabaseSql.DRDataFilePath = Convert.IsDBNull(mydbReader["DataFilePath"]) ? string.Empty : Convert.ToString(mydbReader["DataFilePath"]);
                    database.DatabaseSql.DRTransLogPath = Convert.IsDBNull(mydbReader["TransLogPath"]) ? string.Empty : Convert.ToString(mydbReader["TransLogPath"]);
                    database.DatabaseSql.DRUndoFilePath = Convert.IsDBNull(mydbReader["UndoFilePath"]) ? string.Empty : Convert.ToString(mydbReader["UndoFilePath"]);
                    database.DatabaseSql.DRBackupRestorePath = Convert.IsDBNull(mydbReader["BackupRestorePath"]) ? string.Empty : Convert.ToString(mydbReader["BackupRestorePath"]);
                    database.DatabaseSql.DRNetworkSharedPath = Convert.IsDBNull(mydbReader["NetworkSharedPath"]) ? string.Empty : Convert.ToString(mydbReader["NetworkSharedPath"]);
                    database.DatabaseSql.DRInstancename = Convert.IsDBNull(mydbReader["sqlInstanceName"]) ? string.Empty : Convert.ToString(mydbReader["sqlInstanceName"]);
                    break;
            }
            return database;
        }

        private static DatabaseBase BuildOracleDatabaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {

            //switch ((string)myDatabaseReader[2])
            //{
            //    case "PRDatabase":
            database.PRName = myDatabaseReader["Name"].ToString();
            database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
            database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);

            // database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
            if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
            database.Racdbnode = Convert.IsDBNull(myDatabaseReader["Isracdbnode"]) ? 0 : Convert.ToInt32(myDatabaseReader["Isracdbnode"]);
            database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
            database.PRVersion = Convert.IsDBNull(myDatabaseReader["Version"]) ? string.Empty : Convert.ToString(myDatabaseReader["Version"].ToString());
            database.DatabaseOracle.PROracleSID = myDatabaseReader["OracleSID"].ToString();
            database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader["UserName"].ToString());
            database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader["Password"].ToString());
            database.DatabaseOracle.PRPort = Convert.ToInt32(myDatabaseReader["Port"]);
            //database.DatabaseOracle.PRArchivePath = myDatabaseReader["Archive"].ToString();
            //database.DatabaseOracle.PROracleHome = myDatabaseReader["Home"].ToString();
            //database.DatabaseOracle.PRRedoPath = myDatabaseReader["Redo"].ToString();
            //database.DatabaseOracle.DRASMGridPath = myDatabaseReader["ASMGrid"].ToString();


            database.DatabaseOracle.PRArchivePath = Convert.IsDBNull(myDatabaseReader["Archive"]) ? string.Empty : Convert.ToString(myDatabaseReader["Archive"].ToString());
            database.DatabaseOracle.PROracleHome = Convert.IsDBNull(myDatabaseReader["Home"]) ? string.Empty : Convert.ToString(myDatabaseReader["Home"].ToString());
            database.DatabaseOracle.PRRedoPath = Convert.IsDBNull(myDatabaseReader["Redo"]) ? string.Empty : Convert.ToString(myDatabaseReader["Redo"].ToString());
            database.DatabaseOracle.DRASMGridPath = Convert.IsDBNull(myDatabaseReader["ASMGrid"]) ? string.Empty : Convert.ToString(myDatabaseReader["ASMGrid"].ToString());
            database.DatabaseOracle.PRInstanceName = Convert.IsDBNull(myDatabaseReader["InstanceName"]) ? string.Empty : Convert.ToString(myDatabaseReader["InstanceName"].ToString());
            database.DatabaseOracle.IsAsm = Convert.IsDBNull(myDatabaseReader["IsASM"]) ? 0 : Convert.ToInt32(myDatabaseReader["IsASM"].ToString());
            database.DatabaseOracle.PRAsmInstanceName = Convert.IsDBNull(myDatabaseReader["ASMInstanceName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ASMInstanceName"].ToString());
            // database.DatabaseOracle.PRInstanceName = myDatabaseReader["InstanceName"].ToString();
            //        break;
            //    case "DRDatabase":
            //        database.DRName = myDatabaseReader[0].ToString();
            //        database.DRServerId = Convert.ToInt32(myDatabaseReader[3]);
            //        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
            //        database.Mode = myDatabaseReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
            //        database.DatabaseOracle.DROracleSID = myDatabaseReader[6].ToString();
            //        database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader[7].ToString());
            //        database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader[8].ToString());
            //        database.DatabaseOracle.DRPort = Convert.ToInt32(myDatabaseReader[9]);
            //        break;
            //}

            return database;

        }

        private static DatabaseBase BuildDb2DatabaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {

            switch ((string)myDatabaseReader[2])
            {
                //case "PRDatabase":
                //    database.PRName = myDatabaseReader[0].ToString();
                //    database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                //    database.PRServerId = Convert.ToInt32(myDatabaseReader[3]);
                //    database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
                //    database.PRMode = myDatabaseReader.IsDBNull(5)
                //                          ? DatabaseMode.Undefined
                //                          : (DatabaseMode)
                //                            Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
                //    database.DatabaseDb2.DatabaseSID = myDatabaseReader[22].ToString();
                //    database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(myDatabaseReader[23].ToString());
                //    database.DatabaseDb2.Password = StringHelper.Md5Decrypt(myDatabaseReader[24].ToString());
                //    database.DatabaseDb2.Port = Convert.ToInt32(myDatabaseReader[25]);
                //    break;
                //case "DRDatabase":
                //    database.PRName = myDatabaseReader[0].ToString();
                //    database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                //    database.PRServerId = Convert.ToInt32(myDatabaseReader[3]);
                //    database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
                //    database.PRMode = myDatabaseReader.IsDBNull(5)
                //                          ? DatabaseMode.Undefined
                //                          : (DatabaseMode)
                //                            Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
                //    database.DatabaseDb2.DatabaseSID = myDatabaseReader[22].ToString();
                //    database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(myDatabaseReader[23].ToString());
                //    database.DatabaseDb2.Password = StringHelper.Md5Decrypt(myDatabaseReader[24].ToString());
                //    database.DatabaseDb2.Port = Convert.ToInt32(myDatabaseReader[25]);
                //    break;

                case "PRDatabase":
                    database.PRName = myDatabaseReader[0].ToString();
                    database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DatabaseDb2.DatabaseSID = myDatabaseReader["DB2DatabaseSID"].ToString();
                    database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(myDatabaseReader["DB2UserName"].ToString());
                    database.DatabaseDb2.Password = StringHelper.Md5Decrypt(myDatabaseReader["DB2Password"].ToString());
                    database.DatabaseDb2.Port = Convert.ToInt32(myDatabaseReader["DB2Port"]);
                    break;
                case "DRDatabase":
                    database.DRName = myDatabaseReader[0].ToString();
                    database.DRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
                    database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.DRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
                    database.DatabaseDb2.DatabaseSID = myDatabaseReader["DB2DatabaseSID"].ToString();
                    database.DatabaseDb2.UserName = StringHelper.Md5Decrypt(myDatabaseReader["DB2UserName"].ToString());
                    database.DatabaseDb2.Password = StringHelper.Md5Decrypt(myDatabaseReader["DB2Password"].ToString());
                    database.DatabaseDb2.Port = Convert.ToInt32(myDatabaseReader["DB2Port"]);
                    break;
            }
            return database;

        }

        private static DatabaseBase BuildOracleRacDatabaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {


            database.PRName = myDatabaseReader["Name"].ToString();
            database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
            // database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
            if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

            database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
            database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);

            //switch ((string)myDatabaseReader[2])
            //{
            //    case "PRDatabase":
            //database.PRName = myDatabaseReader[0].ToString();
            //database.PRServerId = Convert.ToInt32(myDatabaseReader[3]);
            //database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
            //database.PRMode = myDatabaseReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
            //database.DatabaseOracle.PROracleSID = myDatabaseReader[6].ToString();
            //database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader[7].ToString());
            //database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader[8].ToString());
            //database.DatabaseOracle.PRPort = Convert.ToInt32(myDatabaseReader[9]);
            //        break;
            //    case "DRDatabase":
            //        database.DRName = myDatabaseReader[0].ToString();
            //        database.DRServerId = Convert.ToInt32(myDatabaseReader[3]);
            //        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
            //        database.Mode = myDatabaseReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
            //        database.DatabaseOracle.DROracleSID = myDatabaseReader[6].ToString();
            //        database.DatabaseOracle.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader[7].ToString());
            //        database.DatabaseOracle.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader[8].ToString());
            //        database.DatabaseOracle.DRPort = Convert.ToInt32(myDatabaseReader[9]);
            //        break;
            //}

            return database;

        }

        private static DatabaseBase BuildPostgres9xDataBaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {
            database.PRDBId = Convert.IsDBNull(myDatabaseReader["Id"]) ? 0 : Convert.ToInt32(myDatabaseReader["Id"]);
            database.PRName = Convert.IsDBNull(myDatabaseReader["Name"]) ? string.Empty : Convert.ToString(myDatabaseReader["Name"]);
            database.PRServerId = Convert.IsDBNull(myDatabaseReader["ServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ServerId"]);
            database.DatabasePostgres9x.PRDatabaseName = Convert.IsDBNull(myDatabaseReader["DataBaseName"]) ? string.Empty : Convert.ToString(myDatabaseReader["DataBaseName"]);
            database.DatabasePostgres9x.PRUserName = Convert.IsDBNull(myDatabaseReader["UserName"]) ? string.Empty : Convert.ToString(myDatabaseReader["UserName"]);
            database.DatabasePostgres9x.PRPassword = Convert.IsDBNull(myDatabaseReader["Password"]) ? string.Empty : Convert.ToString(myDatabaseReader["Password"]);
            database.DatabasePostgres9x.PRPort = Convert.IsDBNull(myDatabaseReader["Port"]) ? 0 : Convert.ToInt32(myDatabaseReader["Port"]);
            database.DatabasePostgres9x.PRDBbinDirectory = Convert.IsDBNull(myDatabaseReader["DBbinDirectory"]) ? string.Empty : Convert.ToString(myDatabaseReader["DBbinDirectory"]);
            database.DatabasePostgres9x.PRDBDataDirectory = Convert.IsDBNull(myDatabaseReader["DBDataDirectory"]) ? string.Empty : Convert.ToString(myDatabaseReader["DBDataDirectory"]);
            database.DatabasePostgres9x.PRSULogin = Convert.IsDBNull(myDatabaseReader["SULogin"]) ? string.Empty : Convert.ToString(myDatabaseReader["SULogin"]);
            database.DatabasePostgres9x.PRServiceName = Convert.IsDBNull(myDatabaseReader["ServiceName"]) ? string.Empty : Convert.ToString(myDatabaseReader["ServiceName"]);
            database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(myDatabaseReader["DatabaseType"]), true);
            return database;
            //  database.PRDatabaseType = dbtype;   
        }

        private static DatabaseBase BuildMysqlDatabaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {


            switch ((string)myDatabaseReader[2])
            {
                case "PRDatabase":
                    database.PRName = myDatabaseReader["Name"].ToString();
                    database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.PRMode = Convert.IsDBNull(myDatabaseReader["MODETYPE"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["MODETYPE"]), true);
                    database.DatabaseMysql.PRDatabaseSID = myDatabaseReader["mysqldatabsesid"].ToString();
                    // database.DatabaseMysql.PRDatabaseType = myDatabaseReader["mysqldatabsestype"].ToString();
                    database.DatabaseMysql.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader["mysqldbuser"].ToString());
                    database.DatabaseMysql.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader["mysqluserpassword"].ToString());
                    database.DatabaseMysql.PRPort = Convert.ToInt32(myDatabaseReader["mysqlport"]);
                    database.Type = myDatabaseReader["Type"].ToString();

                    break;
                case "DRDatabase":
                    database.DRName = myDatabaseReader["Name"].ToString();
                    database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                    if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                        database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);
                    database.Mode = Convert.IsDBNull(myDatabaseReader["MODETYPE"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["MODETYPE"]), true);
                    database.DatabaseMysql.DRDatabaseSID = myDatabaseReader["mysqldatabsesid"].ToString();
                    //database.DatabaseMysql.DRDatabaseType = myDatabaseReader["mysqldatabsestype"].ToString();
                    database.DatabaseMysql.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader["mysqldbuser"].ToString());
                    database.DatabaseMysql.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader["mysqluserpassword"].ToString());
                    database.DatabaseMysql.DRPort = Convert.ToInt32(myDatabaseReader["mysqlport"]);
                    database.Type = myDatabaseReader["Type"].ToString();
                    break;
            }

            return database;

        }

        private static DatabaseBase BuildMAXDatabaseEntity(IDataRecord myDatabaseReader, DatabaseBase database)
        {

            database.PRDBId = Convert.IsDBNull(myDatabaseReader["Id"]) ? 0 : Convert.ToInt32(myDatabaseReader["Id"]);
            database.PRName = Convert.IsDBNull(myDatabaseReader["Name"]) ? string.Empty : Convert.ToString(myDatabaseReader["Name"]);
            database.PRServerId = Convert.IsDBNull(myDatabaseReader["ServerId"]) ? 0 : Convert.ToInt32(myDatabaseReader["ServerId"]);

            database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"]) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), Convert.ToString(myDatabaseReader["ModeType"]), true);
            database.DataBaseMaxDB.PRDatabaseSID = myDatabaseReader["maxDatabaseSID"].ToString();
            database.DataBaseMaxDB.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader["maxdbUserName"].ToString());
            database.DataBaseMaxDB.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader["maxdbPassword"].ToString());
            database.DataBaseMaxDB.PRPort = Convert.ToInt32(myDatabaseReader["maxdbPort"]);

            database.DataBaseMaxDB.PRInstallationPath = myDatabaseReader["maxdbInstallationPath"].ToString();
            database.DataBaseMaxDB.PRMediumName = myDatabaseReader["maxdbMediumName"].ToString();
            database.DataBaseMaxDB.PRLogfileName = myDatabaseReader["maxdbLogfileName"].ToString();
            database.DataBaseMaxDB.PRLogPath = myDatabaseReader["maxdbLogPath"].ToString();
            database.DataBaseMaxDB.PRInstanceName = myDatabaseReader["maxdbInstanceName"].ToString();


            return database;

        }

        private static DatabaseBase BuildSybaseWithSRSDBEntity(IDataReader mydbReader, DatabaseBase database)
        {
            var dbtype = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(mydbReader["DatabaseType"]), true);


            database.PRName = Convert.IsDBNull(mydbReader["Name"]) ? string.Empty : Convert.ToString(mydbReader["Name"]);
            database.PRServerId = Convert.IsDBNull(mydbReader["ServerId"]) ? 0 : Convert.ToInt32(mydbReader["ServerId"]);

            if (!Convert.IsDBNull(mydbReader["IsPartofRac"]))
                database.PRIsPartofRac = Convert.ToBoolean(mydbReader["IsPartofRac"]);


            database.PRDatabaseType = dbtype;
            database.DataBaseSybaseWithSRS.PRDatabaseSID = mydbReader["sybaseSRSDatabaseSID"].ToString();
            database.DataBaseSybaseWithSRS.PRUserName = StringHelper.Md5Decrypt(mydbReader["sybaseSRSUserName"].ToString());
            database.DataBaseSybaseWithSRS.PRPassword = StringHelper.Md5Decrypt(mydbReader["sybaseSRSPassword"].ToString());
            database.DataBaseSybaseWithSRS.PRPort = Convert.ToInt32(mydbReader["sybaseSRSPort"]);

            database.DataBaseSybaseWithSRS.PRSybaseEnv_Path = mydbReader["SybaseSRSEnv_Path"].ToString();
            database.DataBaseSybaseWithSRS.PRSybaseDataServerName = mydbReader["SybaseSRSDataServerName"].ToString();
            database.DataBaseSybaseWithSRS.PRSybaseBackupServer = mydbReader["SybaseSRSBackupServer"].ToString();



            return database;
        }

        public static DatabaseBase GetDatabaseById(int id)
        {
            var database = new DatabaseBase();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBaseService_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            // var dbtype = GetDatabaseType(myDatabaseReader);
                            var dbtype = (DatabaseType)Enum.Parse(typeof(DatabaseType), myDatabaseReader[1].ToString(), true);
                            switch (dbtype)
                            {
                                case DatabaseType.Oracle:
                                    return BuildOracleDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Sql:
                                    return BuildSqlDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Exchange:
                                    return BuildExchangeDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.ExchangeDag:
                                    return BuildExchangeDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.DB2:
                                    return BuildDb2DatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.OracleRac:
                                    return BuildOracleRacDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Postgres9x:
                                    return BuildPostgres9xDataBaseEntity(myDatabaseReader, database);

                                case DatabaseType.MYSQL:
                                    return BuildMysqlDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.SQLNative2008:
                                    return BuildMSSqlNative2008DataBaseEntity(myDatabaseReader, database);

                                case DatabaseType.Sybase:
                                    return BuildSybaseDBEntity(myDatabaseReader, database);

                                case DatabaseType.MaxDB:
                                    return BuildMAXDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.SybaseWithSRS:
                                    return BuildSybaseWithSRSDBEntity(myDatabaseReader, database);
                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By id - " + id, exc);
            }
            return database;
        }

        public static DatabaseBase GetDatabaseByName(string name)
        {
            var database = new DatabaseBase();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBaseService_GetByName"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iName", DbType.AnsiString, name);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            // var dbtype = GetDatabaseType(myDatabaseReader);
                            var dbtype = (DatabaseType)Enum.Parse(typeof(DatabaseType), myDatabaseReader[1].ToString(), true);
                            switch (dbtype)
                            {
                                case DatabaseType.Oracle:
                                    return BuildOracleDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Sql:
                                    return BuildSqlDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Exchange:
                                    return BuildExchangeDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.ExchangeDag:
                                    return BuildExchangeDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.DB2:
                                    return BuildDb2DatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.OracleRac:
                                    return BuildOracleRacDatabaseEntity(myDatabaseReader, database);

                                case DatabaseType.Postgres9x:
                                    return BuildPostgres9xDataBaseEntity(myDatabaseReader, database);

                                case DatabaseType.SQLNative2008:
                                    return BuildMSSqlNative2008DataBaseEntity(myDatabaseReader, database);

                                case DatabaseType.MYSQL:
                                    return BuildMysqlDatabaseEntity(myDatabaseReader, database);
                                case DatabaseType.Sybase:
                                    return BuildSybaseDBEntity(myDatabaseReader, database);

                                case DatabaseType.SybaseWithSRS:
                                    return BuildSybaseWithSRSDBEntity(myDatabaseReader, database);


                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By DBName - " + name, exc);
            }
            return database;
        }

        public static Common.DatabaseBase GetSCRDatabaseById(int id)
        {
            var database = new Common.DatabaseBase();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Database_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
                    using (IDataReader myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            //database.Id = Convert.ToInt32(myDatabaseReader[0]);
                            //database.Name = myDatabaseReader[3].ToString();
                            //database.ServerId = Convert.ToInt32(myDatabaseReader[4]);
                            //database.UserName = myDatabaseReader[5].ToString();
                            //database.Password = myDatabaseReader[6].ToString();
                            //database.Port = Convert.ToInt32(myDatabaseReader[7]);
                            //database.ExchangeStorageGroupName = myDatabaseReader[16].ToString();
                            //database.ExchangeMailBoxDBName = myDatabaseReader[17].ToString();
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By id - " + id, exc);
            }
            return database;
        }

        public static DatabaseBase GetDatabaseByInfraObjectIdSqlNative(int groupId, int prid, int drid)
        {
            var database = new DatabaseBase();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("DB_NativeGetByInfraObjectId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            if (Convert.ToInt32(myDatabaseReader["serverId"]) == prid)
                            {
                                database.PRName = myDatabaseReader["Name"].ToString();
                                database.PRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                                //database.PRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                                    database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

                                database.PRMode = Convert.IsDBNull(myDatabaseReader["ModeType"])
                                                      ? DatabaseMode.Undefined
                                                      : (DatabaseMode)
                                                        Enum.Parse(typeof(DatabaseMode),
                                                                   Convert.ToString(myDatabaseReader["ModeType"]), true);

                                database.PRVersion = Convert.IsDBNull(myDatabaseReader["Version"]) ? string.Empty
                                                                         : Convert.ToString(
                                                                             myDatabaseReader["Version"]);

                                database.DatabaseSql.PRDatabaseSID = Convert.IsDBNull(myDatabaseReader["DatabaseSID"])
                                                                         ? string.Empty
                                                                         : Convert.ToString(
                                                                             myDatabaseReader["DatabaseSID"]);
                                database.DatabaseSql.PRUserName =
                                    StringHelper.Md5Decrypt(myDatabaseReader["UserName"].ToString());
                                database.DatabaseSql.PRPassword =
                                    StringHelper.Md5Decrypt(myDatabaseReader["Password"].ToString());
                                database.DatabaseSql.PRPort = Convert.IsDBNull(myDatabaseReader["Port"])
                                                                  ? 0
                                                                  : Convert.ToInt32(myDatabaseReader["Port"]);
                                database.DatabaseSql.PRAuthenticationMode =
                                    Convert.IsDBNull(myDatabaseReader["AuthenticationMode"])
                                        ? SqlAuthenticateType.Undefined
                                        : (SqlAuthenticateType)
                                          Enum.Parse(typeof(SqlAuthenticateType),
                                                     Convert.ToString(myDatabaseReader["AuthenticationMode"]), true);
                                database.DatabaseSql.PRDataFilePath = Convert.IsDBNull(myDatabaseReader["DataFilePath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["DataFilePath"]);
                                database.DatabaseSql.PRTransLogPath = Convert.IsDBNull(myDatabaseReader["TransLogPath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["TransLogPath"]);
                                database.DatabaseSql.PRUndoFilePath = Convert.IsDBNull(myDatabaseReader["UndoFilePath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["UndoFilePath"]);
                                database.DatabaseSql.PRBackupRestorePath =
                                    Convert.IsDBNull(myDatabaseReader["BackupRestorePath"])
                                        ? string.Empty
                                        : Convert.ToString(myDatabaseReader["BackupRestorePath"]);
                                database.DatabaseSql.PRNetworkSharedPath =
                                    Convert.IsDBNull(myDatabaseReader["NetworkSharedPath"])
                                        ? string.Empty
                                        : Convert.ToString(myDatabaseReader["NetworkSharedPath"]);
                                database.DatabaseSql.PRInstancename = Convert.IsDBNull(myDatabaseReader["sqlInstanceName"]) 
                                    ? string.Empty : Convert.ToString(myDatabaseReader["sqlInstanceName"]);
                            }
                            if (Convert.ToInt32(myDatabaseReader["serverId"]) == drid)
                            {
                                database.DRName = myDatabaseReader["Name"].ToString();
                                database.DRServerId = Convert.ToInt32(myDatabaseReader["ServerId"]);
                                //database.DRIsPartofRac = Convert.ToBoolean(mydbReader[4]);
                                if (!Convert.IsDBNull(myDatabaseReader["IsPartofRac"]))
                                    database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader["IsPartofRac"]);

                                database.DRMode = Convert.IsDBNull(myDatabaseReader["ModeType"])
                                                      ? DatabaseMode.Undefined
                                                      : (DatabaseMode)
                                                        Enum.Parse(typeof(DatabaseMode),
                                                                   Convert.ToString(myDatabaseReader["ModeType"]), true);

                                database.DRVersion = Convert.IsDBNull(myDatabaseReader["Version"]) ? string.Empty
                                                                        : Convert.ToString(
                                                                            myDatabaseReader["Version"]);


                                database.DatabaseSql.DRDatabaseSID = Convert.IsDBNull(myDatabaseReader["DatabaseSID"])
                                                                         ? string.Empty
                                                                         : Convert.ToString(
                                                                             myDatabaseReader["DatabaseSID"]);
                                database.DatabaseSql.DRUserName =
                                    StringHelper.Md5Decrypt(myDatabaseReader["UserName"].ToString());
                                database.DatabaseSql.DRPassword =
                                    StringHelper.Md5Decrypt(myDatabaseReader["Password"].ToString());
                                database.DatabaseSql.DRPort = Convert.IsDBNull(myDatabaseReader["Port"])
                                                                  ? 0
                                                                  : Convert.ToInt32(myDatabaseReader["Port"]);
                                database.DatabaseSql.DRAuthenticationMode =
                                    Convert.IsDBNull(myDatabaseReader["AuthenticationMode"])
                                        ? SqlAuthenticateType.Undefined
                                        : (SqlAuthenticateType)
                                          Enum.Parse(typeof(SqlAuthenticateType),
                                                     Convert.ToString(myDatabaseReader["AuthenticationMode"]), true);
                                database.DatabaseSql.DRDataFilePath = Convert.IsDBNull(myDatabaseReader["DataFilePath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["DataFilePath"]);
                                database.DatabaseSql.DRTransLogPath = Convert.IsDBNull(myDatabaseReader["TransLogPath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["TransLogPath"]);
                                database.DatabaseSql.DRUndoFilePath = Convert.IsDBNull(myDatabaseReader["UndoFilePath"])
                                                                          ? string.Empty
                                                                          : Convert.ToString(
                                                                              myDatabaseReader["UndoFilePath"]);
                                database.DatabaseSql.DRBackupRestorePath =
                                    Convert.IsDBNull(myDatabaseReader["BackupRestorePath"])
                                        ? string.Empty
                                        : Convert.ToString(myDatabaseReader["BackupRestorePath"]);
                                database.DatabaseSql.DRNetworkSharedPath =
                                    Convert.IsDBNull(myDatabaseReader["NetworkSharedPath"])
                                        ? string.Empty
                                        : Convert.ToString(myDatabaseReader["NetworkSharedPath"]);
                                database.DatabaseSql.DRInstancename = Convert.IsDBNull(myDatabaseReader["sqlInstanceName"])
                                    ? string.Empty : Convert.ToString(myDatabaseReader["sqlInstanceName"]);
                            }
                            // if (Convert.ToInt32(myDatabaseReader[3]) == prid)
                            //{
                            //         database.PRName = myDatabaseReader[0].ToString();
                            //         database.PRServerId = Convert.ToInt32(myDatabaseReader[3]);
                            //         database.PRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
                            //         database.Mode = myDatabaseReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
                            //         database.DatabaseSql.PRDatabaseSID = myDatabaseReader[6].ToString();
                            //         database.DatabaseSql.PRUserName = StringHelper.Md5Decrypt(myDatabaseReader[7].ToString());
                            //         database.DatabaseSql.PRPassword = StringHelper.Md5Decrypt(myDatabaseReader[8].ToString());
                            //         database.DatabaseSql.PRPort = Convert.ToInt32(myDatabaseReader[9]);
                            //         database.DatabaseSql.PRAuthenticationMode = myDatabaseReader.IsDBNull(10) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), myDatabaseReader.GetString(10), true);
                            //         database.DatabaseSql.PRDataFilePath = myDatabaseReader[11].ToString();
                            //         database.DatabaseSql.PRTransLogPath = myDatabaseReader[12].ToString();
                            //         database.DatabaseSql.PRUndoFilePath = myDatabaseReader[13].ToString();
                            //         database.DatabaseSql.PRBackupRestorePath = myDatabaseReader[14].ToString();
                            //         database.DatabaseSql.PRNetworkSharedPath = myDatabaseReader[15].ToString();
                            //}
                            //if (Convert.ToInt32(myDatabaseReader[3]) == drid)
                            //{
                            //         database.DRName = myDatabaseReader[0].ToString();
                            //         database.DRServerId = Convert.ToInt32(myDatabaseReader[3]);
                            //         database.DRIsPartofRac = Convert.ToBoolean(myDatabaseReader[4]);
                            //         database.Mode = myDatabaseReader.IsDBNull(5) ? DatabaseMode.Undefined : (DatabaseMode)Enum.Parse(typeof(DatabaseMode), myDatabaseReader.GetString(5), true);
                            //         database.DatabaseSql.DRDatabaseSID = myDatabaseReader[6].ToString();
                            //         database.DatabaseSql.DRUserName = StringHelper.Md5Decrypt(myDatabaseReader[7].ToString());
                            //         database.DatabaseSql.DRPassword = StringHelper.Md5Decrypt(myDatabaseReader[8].ToString());
                            //         database.DatabaseSql.DRPort = Convert.ToInt32(myDatabaseReader[9]);
                            //         database.DatabaseSql.DRAuthenticationMode = myDatabaseReader.IsDBNull(10) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), myDatabaseReader.GetString(10), true);
                            //         database.DatabaseSql.DRDataFilePath = myDatabaseReader[11].ToString();
                            //         database.DatabaseSql.DRTransLogPath = myDatabaseReader[12].ToString();
                            //         database.DatabaseSql.DRUndoFilePath = myDatabaseReader[13].ToString();
                            //         database.DatabaseSql.DRBackupRestorePath = myDatabaseReader[14].ToString();
                            //         database.DatabaseSql.DRNetworkSharedPath = myDatabaseReader[15].ToString();
                            //}
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Database information By id - " + groupId, exc);
            }
            return database;
        }


        public static bool UpdateMAXDBbyInfraid(string LastLogPageApplied, int id)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("maxdb_update_byid"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iLastLogPageApplied", DbType.AnsiString, LastLogPageApplied);

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }

        public static IList<DatabaseBase> GetByDatabaseType(DatabaseType type)
        {
            try
            {
                IList<DatabaseBase> _EBDRpostgressDB = new List<DatabaseBase>();

                const string sp = "DatabaseBase_GetByType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, type.ToString());
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {
                            var database = new DatabaseBase();


                            //database.PRName = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            //database.PRServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            //database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(reader["DatabaseType"]), true);
                            //database.DatabaseOracle.PRUserName = StringHelper.Md5Decrypt(reader["UserName"].ToString());
                            //database.DatabaseOracle.PRPassword = StringHelper.Md5Decrypt(reader["Password"].ToString());
                            //database.DatabaseOracle.PRPort = Convert.ToInt32(reader["Port"]);

                            database.PRDBId = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            database.PRName = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            database.PRServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            database.DatabasePostgres9x.PRDatabaseName = Convert.IsDBNull(reader["DataBaseName"]) ? string.Empty : Convert.ToString(reader["DataBaseName"]);
                            database.DatabasePostgres9x.PRUserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(reader["UserName"]));
                            database.DatabasePostgres9x.PRPassword = Convert.IsDBNull(reader["Password"]) ? string.Empty : StringHelper.Md5Decrypt(Convert.ToString(reader["Password"]));
                            database.DatabasePostgres9x.PRPort = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
                            // database.PRDatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), Convert.ToString(reader["DatabaseType"]), true);

                            _EBDRpostgressDB.Add(database);
                        }


                        return _EBDRpostgressDB;

                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get GetByDatabase information By Type - ", exc);

            }
            return null;
        }
    }
}
