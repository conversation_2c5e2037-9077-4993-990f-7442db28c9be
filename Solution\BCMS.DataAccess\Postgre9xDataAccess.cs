﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class Postgre9xDataAccess : BaseDataAccess
    {
        public static bool AddPostgre9xCompStatus(Postgres9xComponentMonitor _postgres9xComponentMonitor)
        {
            const string sp = "Postgre9xComp_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, _postgres9xComponentMonitor.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring+"iDBVersionPR", DbType.AnsiString, _postgres9xComponentMonitor.DBVersionPR);
                Database.AddInParameter(cmd, Dbstring+"iDBVersionDR", DbType.AnsiString, _postgres9xComponentMonitor.DBVersionDR);
                Database.AddInParameter(cmd, Dbstring+"iDBServiceStatusPR", DbType.AnsiString, _postgres9xComponentMonitor.DBServiceStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iDBServiceStatusDR", DbType.AnsiString, _postgres9xComponentMonitor.DBServiceStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDBRecoveryStatusDR", DbType.AnsiString, _postgres9xComponentMonitor.DBRecoveryStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDBClusterStatePR", DbType.AnsiString, _postgres9xComponentMonitor.DBClusterStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iDBClusterStateDR", DbType.AnsiString, _postgres9xComponentMonitor.DBClusterStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.AnsiString, _postgres9xComponentMonitor.CreatorId);
                int returnCode = Database.ExecuteNonQuery(cmd);
                return returnCode > 0;
            }
        }
        public static bool AddPostgre9xMonitorStatus(Postgres9xMonitorStatus _postgres9xMonitorStatus)
        {
            const string sp = "Postgre9xMonitorStatus_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, _postgres9xMonitorStatus.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring+"iReplicationStatusPR", DbType.AnsiString, _postgres9xMonitorStatus.ReplicationStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iReplicationStatusDR", DbType.AnsiString, _postgres9xMonitorStatus.ReplicationStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDataDirectoryPathPR", DbType.AnsiString, _postgres9xMonitorStatus.DataDirectoryPathPR);
                Database.AddInParameter(cmd, Dbstring+"iDataDirectoryPathDR", DbType.AnsiString, _postgres9xMonitorStatus.DataDirectoryPathDR);
                Database.AddInParameter(cmd, Dbstring+"iCurrent_xlog_location", DbType.AnsiString, _postgres9xMonitorStatus.Current_xlog_location);
                Database.AddInParameter(cmd, Dbstring+"iLast_xlog_receive_location", DbType.AnsiString, _postgres9xMonitorStatus.Last_xlog_receive_location);
                Database.AddInParameter(cmd, Dbstring+"iLast_xlog_replay_location", DbType.AnsiString, _postgres9xMonitorStatus.Last_xlog_replay_location);
                Database.AddInParameter(cmd, Dbstring+"iDataLag_MB", DbType.AnsiString, _postgres9xMonitorStatus.DataLag_MB);
                Database.AddInParameter(cmd, Dbstring+"iDataLag_HHMMSS", DbType.AnsiString, _postgres9xMonitorStatus.DataLag_HHMMSS);
                Database.AddInParameter(cmd, Dbstring+"iCurrentXlogFileName", DbType.AnsiString, _postgres9xMonitorStatus.CurrentXlogFileName);
                Database.AddInParameter(cmd, Dbstring+"iXlogReceiveFileName", DbType.AnsiString, _postgres9xMonitorStatus.XlogReceiveFileName);
                Database.AddInParameter(cmd, Dbstring+"iXlogReplayFileName", DbType.AnsiString, _postgres9xMonitorStatus.XlogReplayFileName);
                Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.AnsiString, _postgres9xMonitorStatus.CreatorId);
                int returnCode = Database.ExecuteNonQuery(cmd);
                return returnCode > 0;
            }
        }

        public static bool AddPostgre9xMonitorLogs(Postgres9xMonitorLogs _postgres9xMonitorLogs)
        {
            const string sp = "Postgre9xMonitorLogs_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, _postgres9xMonitorLogs.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring+"iReplicationStatusPR", DbType.AnsiString, _postgres9xMonitorLogs.ReplicationStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iReplicationStatusDR", DbType.AnsiString, _postgres9xMonitorLogs.ReplicationStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDataDirectoryPathPR", DbType.AnsiString, _postgres9xMonitorLogs.DataDirectoryPathPR);
                Database.AddInParameter(cmd, Dbstring+"iDataDirectoryPathDR", DbType.AnsiString, _postgres9xMonitorLogs.DataDirectoryPathDR);
                Database.AddInParameter(cmd, Dbstring+"iCurrent_xlog_location", DbType.AnsiString, _postgres9xMonitorLogs.Current_xlog_location);
                Database.AddInParameter(cmd, Dbstring+"iLast_xlog_receive_location", DbType.AnsiString, _postgres9xMonitorLogs.Last_xlog_receive_location);
                Database.AddInParameter(cmd, Dbstring+"iLast_xlog_replay_location", DbType.AnsiString, _postgres9xMonitorLogs.Last_xlog_replay_location);
                Database.AddInParameter(cmd, Dbstring+"iDataLag_MB", DbType.AnsiString, _postgres9xMonitorLogs.DataLag_MB);
                Database.AddInParameter(cmd, Dbstring+"iDataLag_HHMMSS", DbType.AnsiString, _postgres9xMonitorLogs.DataLag_HHMMSS);
                Database.AddInParameter(cmd, Dbstring+"iCurrentXlogFileName", DbType.AnsiString, _postgres9xMonitorLogs.CurrentXlogFileName);
                Database.AddInParameter(cmd, Dbstring+"iXlogReceiveFileName", DbType.AnsiString, _postgres9xMonitorLogs.XlogReceiveFileName);
                Database.AddInParameter(cmd, Dbstring+"iXlogReplayFileName", DbType.AnsiString, _postgres9xMonitorLogs.XlogReplayFileName);
                Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.AnsiString, _postgres9xMonitorLogs.CreatorId);
                int returnCode = Database.ExecuteNonQuery(cmd);
                return returnCode > 0;
            }
        }
    }
}
