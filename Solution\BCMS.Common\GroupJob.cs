﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GroupJob", Namespace = "http://www.Bcms.com/types")]
   public class GroupJob : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string StorageImageId { get; set; }

        [DataMember]
        public int JobId { get; set; }

        [DataMember]
        public string TriggerName { get; set; }

        [DataMember]
        public string CronExpression { get; set; }

        [DataMember]
        public string CronTime { get; set; }

        [DataMember]
        public string NextFireTime { get; set; }

        [DataMember]
        public string LastFireTime { get; set; }

        [DataMember]
        public string JobStatus { get; set; }

        [DataMember]
        public int IsEnabled { get; set; }

        [DataMember]
        public int InfraObjType { get; set; }

        [DataMember]
        public string JobName { get; set; }

        [DataMember]
        public string BcmsClassName { get; set; }

        [DataMember]
        public string JobTypeName { get; set; }

        [DataMember]
        public string ParatmeterType { get; set; }

        [DataMember]
        public int CronExpressionChange { get; set; }

        [DataMember]
        public int IsSchedule { get; set; }

        #endregion Properties
    }
}
