﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlServer2000", Namespace = "http://www.Bcms.com/types")]
    
    public class SqlServer2000 : BaseEntity
    {

            #region Properties

            [DataMember]
            public int InfraObjectId
            {
                get;
                set;
            }

            [DataMember]
            public string DBEdition_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBServicePack_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBInstance_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBState_PR
            {
                get;
                set;
            }

            [DataMember]
            public string Database_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBRecoveryMode_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBRestrictAccess_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBUpdateability_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBSize_PR
            {
                get;
                set;
            }

            [DataMember]
            public string DBEdition_DR
            {
                get;
                set;
            }
            [DataMember]
            public string DBServicePack_DR
            {
                get;
                set;
            }
            [DataMember]
            public string DBInstance_DR
            {
                get;
                set;
            }

            [DataMember]
            public string DBState_DR
            {
                get;
                set;
            }

            [DataMember]
            public string Database_DR
            {
                get;
                set;
            }

            [DataMember]
            public string DBRecoveryMode_DR
            {
                get;
                set;
            }

            [DataMember]
            public string DBRestrictAccess_DR
            {
                get;
                set;
            }

            [DataMember]
            public string DBUpdateability_DR
            {
                get;
                set;
            }

            [DataMember]
            public string DBSize_DR
            {
                get;
                set;
            }

            #region Constructor
            public SqlServer2000()
                : base()
            {
            }
            #endregion

            #endregion
        }
    }

