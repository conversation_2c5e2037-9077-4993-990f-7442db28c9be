﻿using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace Bcms.Helper
{
    public static class StringHelper
    {
        public static string Md5Key = "20Pe,rvtu!itLtPa/d10?Mah$petnThaohes%h2ktilso3*4ftMarSe(rTe)cs@ctimizhnviceP";
       
        public static string Md5Decrypt(string cipherString)
        {
            byte[] toEncryptArray = Convert.FromBase64String(cipherString);

            var hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(Md5Key));
            hashmd5.Clear();

            var tdes = new TripleDESCryptoServiceProvider
            {
                Key = keyArray,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            var cTransform = tdes.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tdes.Clear();
            return Encoding.UTF8.GetString(resultArray);

        }

        public static string[] ConvertCronExpressionToArray(string expression)
        {
            string[] parsed = new string[7].Select(el => "").ToArray();

            if (string.IsNullOrEmpty(expression))
            {
                throw new MissingFieldException("ExpressionDescriptor", "expression");
            }
            else
            {
                string[] expressionPartsTemp = expression.Split(' ');

                if (expressionPartsTemp.Length < 5)
                {
                    throw new FormatException(string.Format("Error: Expression only has {0} parts.  At least 5 part are required.", expressionPartsTemp.Length));
                }
                else if (expressionPartsTemp.Length == 5)
                {
                    //5 part cron so shift array past seconds element
                    Array.Copy(expressionPartsTemp, 0, parsed, 1, 5);
                }
                else if (expressionPartsTemp.Length == 6)
                {
                    //If last element ends with 4 digits, a year element has been supplied and no seconds element
                    Regex yearRegex = new Regex("\\d{4}$");
                    if (yearRegex.IsMatch(expressionPartsTemp[5]))
                    {
                        Array.Copy(expressionPartsTemp, 0, parsed, 1, 6);
                    }
                    else
                    {
                        Array.Copy(expressionPartsTemp, 0, parsed, 0, 6);
                    }
                }
                else if (expressionPartsTemp.Length == 7)
                {
                    parsed = expressionPartsTemp;
                }
                else
                {
                    throw new FormatException(string.Format("Error: Expression has too many parts ({0}).  Expression must not have more than 7 parts.", expressionPartsTemp.Length));
                }
            }

            NormalizeExpression(parsed);

            return parsed;
        }

        private static void NormalizeExpression(string[] expressionParts)
        {
            //convert ? to * only for DOM and DOW
            expressionParts[3] = expressionParts[3].Replace("?", "*");
            expressionParts[5] = expressionParts[5].Replace("?", "*");

            //convert 0/, 1/ to */
            expressionParts[0] = expressionParts[0].Replace("0/", "*/"); //seconds
            expressionParts[1] = expressionParts[1].Replace("0/", "*/"); //minutes
            expressionParts[2] = expressionParts[2].Replace("0/", "*/"); //hours
            expressionParts[3] = expressionParts[3].Replace("1/", "*/"); //DOM
            expressionParts[4] = expressionParts[4].Replace("1/", "*/"); //Month
            expressionParts[5] = expressionParts[5].Replace("1/", "*/"); //DOW

            //convert */1 to *
            int len = expressionParts.Length;
            for (int i = 0; i < len; i++)
            {
                if (expressionParts[i] == "*/1")
                {
                    expressionParts[i] = "*";
                }
            }

            //convert SUN-SAT format to 0-6 format
            //for (int i = 0; i <= 6; i++)
            //{
            //    if (!m_options.DayOfWeekStartIndexZero)
            //    {
            //        expressionParts[5] = expressionParts[5].Replace((i + 1).ToString(), i.ToString());
            //    }

            //    DayOfWeek currentDay = (DayOfWeek)i;
            //    string currentDayOfWeekDescription = currentDay.ToString().Substring(0, 3).ToUpper();
            //    expressionParts[5] = expressionParts[5].Replace(currentDayOfWeekDescription, i.ToString());
            //}

            //convert  JAN-DEC format to 1-12 format
            for (int i = 1; i <= 12; i++)
            {
                DateTime currentMonth = new DateTime(DateTime.Now.Year, i, 1);
                string currentMonthDescription = currentMonth.ToString("MMM").ToUpper();
                expressionParts[4] = expressionParts[4].Replace(currentMonthDescription, i.ToString());
            }

            //convert 0 second to (empty)
            if (expressionParts[0] == "0")
            {
                expressionParts[0] = string.Empty;
            }
        }

        public static TimeSpan ConvertCronExpressionToTimeSpan(string expression)
        {
            var span=new TimeSpan();

            string[] parts = expression.Split('/');


            if (parts[1].Contains(" * * * ?"))
            {

                var m = expression.Substring(expression.LastIndexOf('/') + 1,
                                             expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));
                m = m.Trim();

                span = new TimeSpan(0, Convert.ToInt32(m), 0);


            }
            else if (parts[1].Contains("* * ?"))
            {

                var t = expression.Substring(1, 3);
                var w = expression.Substring(expression.LastIndexOf('/') + 1,
                                             expression.IndexOf('*') - (expression.LastIndexOf('/') + 1));
                t = t.Trim();
                w = w.Trim();

                span = new TimeSpan(Convert.ToInt32(w), Convert.ToInt32(t), 0);
            }

            return span;
        }

    }
}
