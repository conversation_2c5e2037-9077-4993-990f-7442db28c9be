﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;



namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MSSqlDoubletek", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MSSqlDoubletek : BaseEntity
    {
        #region Member Variables

      //  private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public int InfraobjectId { get; set; }


        [DataMember]
        public string JobName { get; set; }

        [DataMember]
        public string ReplicationStatus { get; set; }

        [DataMember]
        public string SourceServer { get; set; }

        [DataMember]
        public string TargetServer { get; set; }

        [DataMember]
        public string JobType { get; set; }

        [DataMember]
        public string ReplicationQueue { get; set; }

        [DataMember]
        public string JobActivity { get; set; }

        [DataMember]
        public string MirrorStatus { get; set; }

        [DataMember]
        public string MirrorPercentComplete { get; set; }

      

        [DataMember]
        public string MirrorBytesRemaining { get; set; }



        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get { return _basereplication; }
        //    set { _basereplication = value; }
        //}

        #endregion Properties
    }
}
