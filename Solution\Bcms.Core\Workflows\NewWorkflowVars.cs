﻿using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;

namespace Bcms.Core.Workflows
{
    public class NewWorkflowVars
    {
        #region New Workflow Variables

        //public static bool ActionExecutionStatusNew { get; set; }
        //public static bool ActionConditionStatusNew { get; set; }
        //public static int ActionCompleteCountNew { get; set; }
        //public static bool PreviousConditionCount { get; set; }

        public static ConcurrentDictionary<int, bool> ActionExecutionStatusNew_Old = new ConcurrentDictionary<int, bool>();
        public static ConcurrentDictionary<int, bool> ActionConditionStatusNew_Old = new ConcurrentDictionary<int, bool>();
        public static ConcurrentDictionary<int, int> ActionCompleteCountNew_Old = new ConcurrentDictionary<int, int>();
        public static ConcurrentDictionary<int, bool> PreviousConditionCount_Old = new ConcurrentDictionary<int, bool>();
        public static ConcurrentDictionary<int, bool> FailureTrial_Old = new ConcurrentDictionary<int, bool>();
        public static ConcurrentDictionary<int, int> FailureCount_Old = new ConcurrentDictionary<int, int>();
        public static ConcurrentDictionary<int, int> FailedCondiActionId_Old = new ConcurrentDictionary<int, int>();

        #endregion

    }
}
