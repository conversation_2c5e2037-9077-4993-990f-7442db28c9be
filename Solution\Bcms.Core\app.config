<?xml version="1.0"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Bcms.Core.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <userSettings>
        <Bcms.Core.Properties.Settings>
            <setting name="DataLagInHours" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="AlertCount" serializeAs="String">
                <value>3</value>
            </setting>
            <setting name="AutoReportSentDate" serializeAs="String">
                <value>2012-06-01</value>
            </setting>
            <setting name="TimeToSendReport" serializeAs="String">
                <value>6</value>
            </setting>
            <setting name="StatusReportMailIds" serializeAs="String">
                <value><EMAIL>;<EMAIL>;<EMAIL></value>
            </setting>
        </Bcms.Core.Properties.Settings>
    </userSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/></startup></configuration>
