﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
namespace Bcms.DataAccess
{
   public class BPAutomationdDataAccess:BaseDataAccess
    {
       public static Common.BPAutomation GetByName(string GroupName, string BpComponent)
        {

            var BPAutomationgroup = new Common.BPAutomation();
            try
            {
                
                //var db = DatabaseFactory.CreateDatabase();
                using (DbCommand cmd = Database.GetStoredProcCommand("BusinessProcess_GetByName"))
                {

                    Database.AddInParameter(cmd, Dbstring+"iINFRAOBJECTNAME", DbType.String, GroupName);
                    Database.AddInParameter(cmd, Dbstring+"iBpComponent", DbType.String, BpComponent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(cmd))
                        {
                            while (myReader.Read())
                            {

                                BPAutomationgroup.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                                BPAutomationgroup.GroupName = Convert.IsDBNull(myReader["GroupName"]) ? string.Empty : Convert.ToString(myReader["GroupName"]);
                                BPAutomationgroup.BPComponent = Convert.IsDBNull(myReader["BPComponent"]) ? string.Empty : Convert.ToString(myReader["BPComponent"]);
                                BPAutomationgroup.PRHostName = Convert.IsDBNull(myReader["PRHostId"]) ? 0 : Convert.ToInt32(myReader["PRHostId"]);
                                BPAutomationgroup.Sourcedir = Convert.IsDBNull(myReader["SourceDir"]) ? string.Empty : Convert.ToString(myReader["SourceDir"]);
                                BPAutomationgroup.Destinationdir = Convert.IsDBNull(myReader["DestinationDir"]) ? string.Empty : Convert.ToString(myReader["DestinationDir"]);
                                BPAutomationgroup.DRHostname = Convert.IsDBNull(myReader["DRHostId"]) ? 0 : Convert.ToInt32(myReader["DRHostId"]);
                                BPAutomationgroup.ScriptPath = Convert.IsDBNull(myReader["ScriptPath"]) ? string.Empty : Convert.ToString(myReader["ScriptPath"]);
                                BPAutomationgroup.PRIP = Convert.IsDBNull(myReader["PRIPAddress"]) ? string.Empty : Convert.ToString(myReader["PRIPAddress"]);
                                BPAutomationgroup.DRIP = Convert.IsDBNull(myReader["DRIPAddress"]) ? string.Empty : Convert.ToString(myReader["DRIPAddress"]);

                                //BPAutomationgroup.Id = myReader.IsDBNull(0) ? 0 : Convert.ToInt32(myReader[0]);
                                //BPAutomationgroup.GroupName = myReader.IsDBNull(1) ? string.Empty : myReader[1].ToString();
                                //BPAutomationgroup.BPComponent = myReader.IsDBNull(2) ? string.Empty : myReader[2].ToString();
                                //BPAutomationgroup.PRHostName = myReader.IsDBNull(0) ? 0 : Convert.ToInt32(myReader[3].ToString());
                                //BPAutomationgroup.Sourcedir = myReader.IsDBNull(4) ? string.Empty : myReader[4].ToString();
                                //BPAutomationgroup.Destinationdir = myReader.IsDBNull(5) ? string.Empty : myReader[5].ToString();
                                //BPAutomationgroup.DRHostname = myReader.IsDBNull(0) ? 0 : Convert.ToInt32( myReader[6]);
                                //BPAutomationgroup.ScriptPath = myReader.IsDBNull(7) ? string.Empty : myReader[7].ToString();
                                //BPAutomationgroup.PRIP = myReader.IsDBNull(8) ? string.Empty : myReader[8].ToString();
                                //BPAutomationgroup.DRIP = myReader.IsDBNull(9) ? string.Empty : myReader[9].ToString();
                              
                            }
                        }
                    
                }
                return BPAutomationgroup;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Business Process Automation By GroupName - " + GroupName, exc);
            }
       }
    }
}
