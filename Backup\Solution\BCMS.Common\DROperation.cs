﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DROperation", Namespace = "http://www.BCMS.com/types")]
    public class DROperation : BaseEntity
    {
        #region Properties

        [DataMember]
        public int Type { get; set; }

        [DataMember]
        public DateTime StartTime { get; set; }

        [DataMember]
        public DateTime EndTime { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public int ConditionalOperation { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }

        [DataMember]
        public int GroupId { get; set; }

        [DataMember]
        public int Direction { get; set; }

        [DataMember]
        public ActionMode ActionMode { get; set; }




        #endregion

        #region Constructor

        public DROperation()
            : base()
        {
        }

        #endregion
    }
}