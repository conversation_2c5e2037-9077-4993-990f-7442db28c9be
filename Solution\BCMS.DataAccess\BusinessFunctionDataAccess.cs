﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using System.Data.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using System.Data;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
     public class BusinessFunctionDataAccess:BaseDataAccess
    {
        public static BusinessFunction GetById(int id)
        {

            BusinessFunction businessFunction = new BusinessFunction();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("BusinessFunction_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                             businessFunction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            businessFunction.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty :Convert.ToString(reader["Name"]);
                           businessFunction.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]);

                           businessFunction.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : Convert.ToString(reader["Description"]);
                           businessFunction.CriticalityLevel = Convert.IsDBNull(reader["CriticalityLevel"]) ? string.Empty : Convert.ToString(reader["CriticalityLevel"]);
                           businessFunction.ConfiguredRPO = Convert.IsDBNull(reader["ConfiguredRPO"]) ? string.Empty : Convert.ToString(reader["ConfiguredRPO"]);
                           businessFunction.ConfiguredRTO = Convert.IsDBNull(reader["ConfiguredRTO"]) ? string.Empty : Convert.ToString(reader["ConfiguredRTO"]);
                           businessFunction.ConfiguredMAO = Convert.IsDBNull(reader["ConfiguredMAO"]) ? string.Empty : Convert.ToString(reader["ConfiguredMAO"]);
                           businessFunction.IsStatic = Convert.IsDBNull(reader["IsStatic"]) ? 0 : Convert.ToInt32(reader["IsStatic"]);
                           businessFunction.IsActive = !Convert.IsDBNull(reader["IsActive"]) && Convert.ToBoolean(reader["IsActive"]);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get BusinessFunction Information By Id " + id, ex);
            }
            return businessFunction;
        }
    }
}
