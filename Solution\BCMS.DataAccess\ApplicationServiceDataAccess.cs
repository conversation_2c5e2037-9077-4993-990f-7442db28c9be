﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ApplicationServiceDataAccess : BaseDataAccess
    {
        public static bool UpdateApplicationServiceByStatus(int id, bool status)
        {
            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetStoredProcCommand("ApplicationService_UpdateByStatus"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

                    db.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.Boolean, status);
                   
                    int isuccess = db.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update ApplicationService By Status ", exc);
            }
        }

        public static IList<ApplicationService> GetByApplicationId(int applicationId)
        {
            IList<ApplicationService> services=new List<ApplicationService>();

            var db = DatabaseFactory.CreateDatabase();

            const string sp = "ApplicationService_GetByApplicationId";

            try
            {
                using (DbCommand cmd = db.GetStoredProcCommand(sp))
                {

                    db.AddInParameter(cmd, Dbstring+"iapplicationId", DbType.Int32, applicationId);

                    using (IDataReader reader = db.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {

                            var applicationservice = new ApplicationService();



                            applicationservice.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            applicationservice.ApplicationId = Convert.IsDBNull(reader["ApplicationId"]) ? 0 : Convert.ToInt32(reader["ApplicationId"]);
                            applicationservice.ServiceName = Convert.IsDBNull(reader["ServiceName"]) ? string.Empty : Convert.ToString(reader["ServiceName"]);

                            if (!Convert.IsDBNull(reader["Status"]))
                                applicationservice.Status = Convert.ToBoolean(reader["Status"]);


                            if (!Convert.IsDBNull(reader["IsActive"]))
                                applicationservice.IsActive = Convert.ToBoolean(reader["IsActive"]);

                            //const int fldID = 0;
                            //const int fldApplicationid = 1;
                            //const int fldServicename = 2;
                            //const int fldStatus = 3;
                            //const int fldIsactive = 4;

                            //applicationservice.Id = reader.IsDBNull(fldID) ? 0 : reader.GetInt32(fldID);
                            //applicationservice.ApplicationId = reader.IsDBNull(fldApplicationid)
                            //                                       ? 0
                            //                                       : reader.GetInt32(fldApplicationid);
                            //applicationservice.ServiceName = reader.IsDBNull(fldServicename)
                            //                                     ? string.Empty
                            //                                     : reader.GetString(fldServicename);
                            //applicationservice.Status = !reader.IsDBNull(fldStatus) && reader.GetBoolean(fldStatus);
                            //applicationservice.IsActive = !reader.IsDBNull(fldIsactive) &&
                            //                              reader.GetBoolean(fldIsactive);
                            services.Add(applicationservice);
                        }

                    }
                }
                return services;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching Application Service by ApplicationId "+applicationId, exc);
     
            }
        }
    }
}