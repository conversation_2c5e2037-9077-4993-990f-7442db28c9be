﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PDTake;

using PMSSQLNative;

namespace Bcms.Core.Client
{
   public class doubleTakeClient :  IDisposable
    {
     

       public doubleTakeClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

       public InfraObject CurrentInfraObject { get; set; }


       #region Variable
       public bool _isDisposed;
       private static readonly ILog Logger = LogManager.GetLogger(typeof(doubleTakeClient));

       string DoubletakePath = ConfigurationManager.AppSettings["DoubletakePath"];

       Server _doubleTakeserver;
       private DatabaseBase _prDatabase;
       private DatabaseBase _drDatabase;
       private Server _server;
       private DatabaseBase _database;
       private const int MaxAlertCount = 3;
       # endregion

       #region Properties

       public string DatabaseName { get; set; }
       public string UserName { get; set; }
       public string Password { get; set; }
       public string Port { get; set; }

       # endregion
       public Server CurrentServer
       {
           get
           {
               if (_server == null)
               {
                   if (CurrentInfraObject != null)
                   {
                       _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                       _server.InfraObjectId = CurrentInfraObject.Id;
                       _server.InfraObjectName = CurrentInfraObject.Name;
                       _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                   }
               }
               return _server;
           }

           set
           {
               _server = value;
           }
       }

       public DatabaseBase CurrentDatabase
       {

           get
           {
               if (_database == null)
               {
                   if (CurrentInfraObject != null)
                   {
                      
                       _database = DatabaseBaseDataAccess.GetDatabaseByInfraObjectId(CurrentInfraObject.Id);
                   }
               }
               return _database;
           }

           set
           {
               _database = value;
           }
       }


       public void MonitordoubleTakeReplicationComponent()
       {
           try
           {

               DTServer _serverPR, _serverDR;
                 MSSQLServer _sqlServerPR, _sqlServerDR;
                MSSQLDatabase _sqlDatabasePR, _sqlDatabaseDR;
              if (CurrentServer != null && CurrentDatabase != null)
               {

                   CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                   CurrentServer.InfraObjectId = CurrentInfraObject.Id;
                 //  CurrentServer.JobName = JobName.MSSqlNative2008;

                   var InfraObjicetInfo = InfraObjectDataAccess.GetInfraObjectById(CurrentInfraObject.Id);
                   if (InfraObjicetInfo.PRReplicationId != 0)
                   {
                       var replication = ReplicationBaseDataAccess.GetReplicationById(InfraObjicetInfo.PRReplicationId);
                       if (replication != null)
                       {

                           _serverPR = new DTServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                           _serverDR = new DTServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                        
                           //if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                           //{
                           //    _serverPR = new DTServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                           //    _serverDR = new DTServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                           //}
                           //else
                           //{
                           //    _serverPR = new DTServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                           //    _serverDR = new DTServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                           //}

                           var _healthDT = DTProcess.GetDTJobByName(_serverDR, replication.doubletekRep.JobName, replication.doubletekRep.JobType, DoubletakePath);

                           MSSqlDoubletek _doubletakeMonitor = new MSSqlDoubletek();

                           if (_healthDT != null)
                           {
                               _doubletakeMonitor.InfraobjectId = CurrentInfraObject.Id;
                               _doubletakeMonitor.ReplicationId = replication.doubletekRep.ReplicationId;
                               _doubletakeMonitor.JobName = replication.doubletekRep.JobName;
                               _doubletakeMonitor.ReplicationStatus = _healthDT.ReplicationState;
                               _doubletakeMonitor.SourceServer = CurrentServer.PRName;
                               _doubletakeMonitor.TargetServer = CurrentServer.DRName;
                               _doubletakeMonitor.JobType = _healthDT.JobType;
                               _doubletakeMonitor.ReplicationQueue = _healthDT.ReplicationOpsQueued;
                               _doubletakeMonitor.JobActivity = _healthDT.HighLevelState;
                               _doubletakeMonitor.MirrorStatus = _healthDT.MirrorState;
                               _doubletakeMonitor.MirrorPercentComplete = _healthDT.MirrorPermillage;
                               _doubletakeMonitor.MirrorBytesRemaining = _healthDT.MirrorBytesRemaining;
                               _doubletakeMonitor.CreatorId = 1;
                               _doubletakeMonitor.CreateDate = DateTime.Now;
                               _doubletakeMonitor.UpdatorId = 1;


                               Logger.InfoFormat("Retrived doubleTake Replication Info for Infraobject:" + CurrentInfraObject.Name + " DRIpAddress: " + CurrentServer.DRIPAddress);
                               Logger.InfoFormat("Retrived doubleTake Replication Info:" + " JobName: " + replication.doubletekRep.JobName + " ReplicationStatus: " + _healthDT.ReplicationState + " JobType: " + _healthDT.JobType + " ReplicationQueue: " + _healthDT.ReplicationOpsQueued + " JOBActivity: " + _healthDT.HighLevelState);
                               Logger.InfoFormat("Retrived doubleTake Mirror Replication Info :" + " MirrorState: " + _healthDT.MirrorState + " MirrorPercentComplete: " + _healthDT.MirrorPermillage);
                               var isSuccess = MSSqlDoubleteRepliMonitorkDataAccess.AddMssqldoubletakeMonitorStatus(_doubletakeMonitor);

                               if (isSuccess)
                               {
                                   Logger.InfoFormat("Added doubletake Monitor status Replication details", CurrentInfraObject.Name);
                               }

                               var istrue = MSSqlDoubleteRepliMonitorkDataAccess.AddMssqldoubletakeLogStatusdetails(_doubletakeMonitor);
                               if (istrue)
                               {
                                   Logger.InfoFormat("Added doubletake logs status  details", CurrentInfraObject.Name);
                               }

                 

                           }

                   
                                             
                           if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                            {
                                 _sqlServerDR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                 _sqlServerPR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                                 string PRauthmode = string.Empty;
                                 string DRauthmode = string.Empty;
                                 if (CurrentDatabase.DatabaseSql.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     PRauthmode = "sql";
                                 }
                                 else
                                 {
                                     PRauthmode = "windows";
                                 }
                                 if (CurrentDatabase.DatabaseSql.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     DRauthmode = "sql";
                                 }
                                 else
                                 {
                                     DRauthmode = "windows";
                                 }

                                 _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSql.PRDatabaseSID, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRPort.ToString(),PRauthmode);
                                 _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSql.DRDatabaseSID, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRPort.ToString(),DRauthmode);
                         
                            }
                            else
                            {
                                string PRauthmode = string.Empty;
                                string DRauthmode = string.Empty;
                             

                                
                                 _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                 _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                                 if (CurrentDatabase.DatabaseSql.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     PRauthmode = "sql";
                                 }
                                 else
                                 {
                                     PRauthmode = "windows";
                                 }
                                 if (CurrentDatabase.DatabaseSql.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     DRauthmode = "sql";
                                 }
                                 else
                                 {
                                     DRauthmode = "windows";
                                 }

                                 _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSql.PRDatabaseSID, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRPort.ToString(),PRauthmode);
                                 _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSql.DRDatabaseSID, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRPort.ToString(),DRauthmode);
                            }

                           VerifyServerAndDataBasesqlNative2008(_sqlServerPR, _sqlServerDR);
                         
                           var _sqlhealthPR= SqlNativeProcess.GetSQLHealth(_sqlDatabasePR);
                           var _sqlhealthDR= SqlNativeProcess.GetSQLHealth(_sqlDatabaseDR);
                          // var _sqlhealthDR = SqlNativeProcess
                            Logger.InfoFormat("Retrived sql database Health..");
                            
                            SqlServer2000 health = new SqlServer2000();
                            if (_sqlhealthPR != null)
                            {
                                Logger.InfoFormat("{0} : Executing doubleTake Health Job", CurrentInfraObject.Name);
                                health.InfraObjectId = CurrentInfraObject.Id;
                                health.DBEdition_PR = _sqlhealthPR.MSSQLServerEdition;
                                health.DBServicePack_PR = _sqlhealthPR.MSSQLServerRelease;
                                health.DBInstance_PR = _sqlhealthPR.DatabaseHost;
                                health.DBState_PR = _sqlhealthPR.DatabaseState;
                                health.Database_PR = CurrentDatabase.DatabaseSql.PRDatabaseSID;
                                health.DBRecoveryMode_PR = _sqlhealthPR.DatabaseRecoveryModel;
                                health.DBRestrictAccess_PR = _sqlhealthPR.DatabaseAccessRestrictStatus;

                                string strDBUpdateabilityPR = _sqlhealthPR.DatabaseUpdatability;
                                if (strDBUpdateabilityPR.ToLower().Equals("read_only"))
                                {
                                    strDBUpdateabilityPR = "Standby / Read-Only";
                                }
                                health.DBUpdateability_PR = strDBUpdateabilityPR;
                                
                                health.DBSize_PR = _sqlhealthPR.DatabaseSize;
                                if (_sqlhealthDR.MSSQLServerEdition != null)
                                {
                                    health.DBEdition_DR = _sqlhealthDR.MSSQLServerEdition;
                                }
                                else
                                {
                                    health.DBEdition_DR = "N/A";
                                }
                                if (_sqlhealthDR.MSSQLServerRelease != null)
                                {
                                    health.DBServicePack_DR = _sqlhealthDR.MSSQLServerRelease;
                                }
                                else
                                {
                                    health.DBServicePack_DR = "N/A";
                                }
                                if (_sqlhealthDR.DatabaseHost != null)
                                {
                                    health.DBInstance_DR = _sqlhealthDR.DatabaseHost;
                                }
                                else
                                {
                                    health.DBInstance_DR = "N/A";
                                }
                                if (_sqlhealthDR.DatabaseState != null)
                                {
                                    health.DBState_DR = _sqlhealthDR.DatabaseState;
                                }
                                else
                                {
                                    health.DBState_DR = "N/A";
                                }
                                health.Database_DR = CurrentDatabase.DatabaseSql.DRDatabaseSID;
                                if (_sqlhealthDR.DatabaseRecoveryModel != null)
                                {
                                    health.DBRecoveryMode_DR = _sqlhealthDR.DatabaseRecoveryModel;
                                }
                                else
                                {
                                    health.DBRecoveryMode_DR = "N/A";
                                }
                                if (_sqlhealthDR.DatabaseAccessRestrictStatus != null)
                                {
                                    health.DBRestrictAccess_DR = _sqlhealthDR.DatabaseAccessRestrictStatus;
                                }
                                else
                                {
                                    health.DBRestrictAccess_DR = "N/A";
                                }
                                if (_sqlhealthDR.DatabaseUpdatability != null)
                                {
                                    string strDBUpdateabilityDR = _sqlhealthDR.DatabaseUpdatability;
                                    if (strDBUpdateabilityDR.ToLower().Equals("read_only"))
                                    {
                                        strDBUpdateabilityDR = "Standby / Read-Only";
                                    }
                                    health.DBUpdateability_DR = strDBUpdateabilityDR;
                                }
                                else
                                {
                                    health.DBUpdateability_DR = "N/A";
                                }
                                if (_sqlhealthDR.DatabaseSize != null)
                                {
                                    health.DBSize_DR = _sqlhealthDR.DatabaseSize;
                                }
                                else
                                {
                                    health.DBSize_DR = "N/A";
                                }

                                health.CreatorId = 1;
                                // health.CreateDate = DateTime.Now;
                                var healthreturn = SqlLogDataAccess.CreateHealthMonitor(health);
                                Logger.InfoFormat("{0} : Completed Executing Health Job", CurrentInfraObject.Name);
                                ProcessMSSqldoubletakeAlerts(_sqlDatabasePR, _sqlDatabaseDR, health, _doubletakeMonitor);
                            }
                            else
                            {
                                ProcessMSSqldoubletakeAlerts(_sqlDatabasePR, _sqlDatabaseDR, health, _doubletakeMonitor);
                            }
                           
                       }
                   }
               }


           }

           catch (BcmsException exc)
           {
               Logger.Info("Exception:Monitor doubleTake component:" + CurrentInfraObject.Name + exc.Message + exc.InnerException.Message);
               throw;
           }


       }

        private void VerifyServerAndDataBasesqlNative2008(MSSQLServer prSSHInfo, MSSQLServer drSSHInfo)
        {
            using (BCMS.Core.Utility.SSHHelper SSHHelper = new BCMS.Core.Utility.SSHHelper())
            {
                try
                {
                    //MSSQLServer _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    //MSSQLDatabase _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString());
                    //MSSQLServer _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                    //MSSQLDatabase _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString());
                    MSSQLServer _sqlServerPR, _sqlServerDR;
                    MSSQLDatabase _sqlDatabasePR, _sqlDatabaseDR;
                    _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                    _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSql.PRDatabaseSID, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRPort.ToString());
                    _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSql.DRDatabaseSID, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRPort.ToString());

                    int serverAlertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRServer : (int)AlertNotificationType.PRServer;

                    int serverAlertType1 = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRServer : (int)AlertNotificationType.DRServer;

                    var sentServerAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType, CurrentInfraObject.Id);

                    var sentServerAlertCount1 = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType1, CurrentInfraObject.Id);


                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, "PR"))
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                               "InfraObject Name : " + CurrentInfraObject.Name + " Primary Server (" +
                            //                                              prSSHInfo.Host + ") is connected."), "sqlNative2008ServerStatus", CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            string ResolveMessage = CurrentInfraObject.Name + " : Production Server (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId) + ") is connected";
                            IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, 0, ResolveMessage);

                        }

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, true, CurrentInfraObject.Id);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable,
                            //                                          "InfraObject Name : " + CurrentInfraObject.Name +
                            //                                          " Primary Server (" +
                            //                                          prSSHInfo.Host + ") is not reachable."), "SqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });

                            IncidentManagement inc = new IncidentManagement
                            {
                                IncidentName = "CP-Inc_Server_down",
                                IncidentTime = System.DateTime.Now,
                                InfraID = CurrentInfraObject.Id,
                                InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                InfraComponentType = "Server",
                                IncidentComment = "Server Not Reachable"

                            };
                            IncidentManagementDataAccess.AddIncident(inc);
                        }
                    }
                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, "DR"))
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount1 > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                            "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                                            prSSHInfo.Host + ") is connected."), "MonitorsqlNative2008ServerStatus",
                            //                       CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                            //{
                            //    InfraObjectId = CurrentInfraObject.Id,
                            //    EntityId = CurrentServer.PRId,
                            //    Status = 0,
                            //    ResolveMessage =
                            //    CurrentInfraObject.Name + " : Server (" + CurrentServer.PRIPAddress + ") is connected"
                            //});


                        }

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Down);
                        }
                        if (MaxAlertCount > sentServerAlertCount1)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, true, CurrentInfraObject.Id);


                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                        drSSHInfo.Host + ") is not reachable."), "sqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                            //{
                            //    InfraObjectId = CurrentInfraObject.Id,
                            //    EntityId = CurrentServer.PRId,
                            //    Status = 0,
                            //    ResolveMessage =
                            //    CurrentInfraObject.Name + " : Server (" + CurrentServer.PRIPAddress + ") is connected"
                            //});

                        }
                    }

                }
                catch (BcmsException exc)
                {
                    throw;
                }
                catch (Exception exc)
                {
                    ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 0);
                    throw new BcmsException(BcmsExceptionType.CommonUnhandled, string.Format("{0} : Exception occured while Connecting Server", CurrentInfraObject.Name), exc);
                }
            }
        }

       private void ProcessMSSqldoubletakeAlerts(MSSQLDatabase _mssqlPRDB, MSSQLDatabase _mssqlDRDB, SqlServer2000 _sqlnativehealthCompMonitor, MSSqlDoubletek _doubletakeMonitor)
       {
           #region MSsql2k8 PR DB State
           if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "online")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database " + _mssqlPRDB.DBName + " is in Online State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "offline")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database " + _mssqlPRDB.DBName + " is in offline State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "shutdown")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database " + _mssqlPRDB.DBName + " is in Shutdown State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "restoring")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + " is in Restoring State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "recovering")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + "  is in Recovering State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "recovery pending")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + "  is in Recovery Pending State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "suspect")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + " is in Suspect State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_PR.ToLower() == "emergency")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + " is in Emergency State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlPRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor,PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
           }

           #endregion

           #region MSsql2k8 DR DataBase State
           if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "online")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MS-SQL Database" + _mssqlDRDB.DBName + "  is in Online State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "offline")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in offline State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "shutdown")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in Shutdown State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "restoring")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in Restoring State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "recovering")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in Recovering State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "recovery pending")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in Recovery Pending State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "suspect")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + "  is in Suspect State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBState_DR.ToLower() == "emergency")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " is in Emergency State;";
               int serviceAlertType = (int)AlertNotificationType.MssqlDRDBstate;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
           }

           #endregion

           #region PR MSSQL DatabaseRecovery Model

           if (_sqlnativehealthCompMonitor.DBRecoveryMode_PR.ToLower() == "full")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  MS-SQL Database" + _mssqlPRDB.DBName + " Recovery Model is in Full State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelfull, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRecoveryMode_PR.ToLower() == "bulk-logged")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + "Recovery Model is in Bulk-logged State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelBulklogged, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRecoveryMode_PR.ToLower() == "simple")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MS-SQL Database" + _mssqlPRDB.DBName + " Recovery Model is in Simple State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelSimple, serviceAlertType);
           }

           #endregion

           # region DR MSSQL DatabaseRecovery Model
           if (_sqlnativehealthCompMonitor.DBRecoveryMode_DR.ToLower() == "full")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MS-SQL Database" + _mssqlDRDB.DBName + " Recovery Model is in Full State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelfull, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRecoveryMode_DR.ToLower() == "bulk-logged")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Recovery Model is in Bulk-logged State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelBulklogged, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRecoveryMode_DR.ToLower() == "simple")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Recovery Model is in Simple State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelSimple, serviceAlertType);
           }

           #endregion

           #region PR MSSQL Database RestrictAccess Status

           if (_sqlnativehealthCompMonitor.DBRestrictAccess_PR.ToLower() == "multi_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Multi_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusMultiUser, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRestrictAccess_PR.ToLower() == "single_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Single_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusSingleUser, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRestrictAccess_PR.ToLower() == "restricted_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Restricted_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusRestrictedUser, serviceAlertType);
           }

           #endregion

           #region DR MSSQL Database RestrictAccess Status

           if (_sqlnativehealthCompMonitor.DBRestrictAccess_DR.ToLower() == "multi_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Multi_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusMultiUser, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRestrictAccess_DR.ToLower() == "single_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Single_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusSingleUser, serviceAlertType);
           }
           else if (_sqlnativehealthCompMonitor.DBRestrictAccess_DR.ToLower() == "restricted_user")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ")  MS-SQL Database" + _mssqlDRDB.DBName + " Restrict AccessStatus is in Restricted_User State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRestrictAccessStatus;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor,_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRestrictAccessStatusRestrictedUser, serviceAlertType);
           }

            #endregion

           #region PR DoubleTake Jobtype

           if (_doubletakeMonitor.JobActivity.ToLower() == "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.DTJobactivityisINprotecting, serviceAlertType);
           }
           else if (_doubletakeMonitor.JobActivity.ToLower() != "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is not in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.DTJobactivityisNotinprotecting, serviceAlertType);
           }

           if (_doubletakeMonitor.JobActivity.ToLower() == "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") e DoubleTake JobActivity is in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.DTJobactivityisINprotecting, serviceAlertType);
           }
           else if (_doubletakeMonitor.JobActivity.ToLower() != "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is not in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.DTJobactivityisNotinprotecting, serviceAlertType);
           }

           #endregion

           #region Replication DataLag between PR&DR
           var confDatalag = BusinessFunctionDataAccess.GetById(CurrentInfraObject.BusinessFunctionId);

           int _configdtbytes = Convert.ToInt32(confDatalag.DataLagInByte);
           int _discoverdinBytes = Convert.ToInt32(_doubletakeMonitor.MirrorBytesRemaining);
           Logger.Info("Configured DataLag:" + _configdtbytes.ToString() + " Discovered Datalag :" + _discoverdinBytes.ToString());
           if (_discoverdinBytes > 0)
           {
               Logger.Info(CurrentInfraObject.Name + ":Discovred Datalag is Greater !");
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSqlDoubleTake Datalag  is Deviated with Datalag !" + _doubletakeMonitor.MirrorBytesRemaining;
               int serviceAlertType = (int)AlertNotificationType.MssqlDTRepDataLag;
               ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMssqlDBDatalagDTbetPrDR, serviceAlertType);
           }
           else
               if (_discoverdinBytes <= 0)
               {
                   Logger.Info(CurrentInfraObject.Name + ":Zero(0) Datalag");
                   string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSqlDoubleTake Datalag  is  !" + _doubletakeMonitor.MirrorBytesRemaining;
                   int serviceAlertType = (int)AlertNotificationType.MssqlDTRepDataLag;

                   ProcessNativeAlerts(_sqlnativehealthCompMonitor, _doubletakeMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMssqlDTDatalag, serviceAlertType);
               }
           #endregion
       }

       private void ProcessNativeAlerts(SqlServer2000 _sqlnativehealthCompMonitor, MSSqlDoubletek _doubletakeMonitor, string PRServiceAlertmessage, string type, int status, BcmsExceptionType exceptiontype, int serviceAlertType)
       {
           //int serviceAlertType = string.Equals("PR", type) ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

           var sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Service), serviceAlertType, _sqlnativehealthCompMonitor.InfraObjectId);
           if (status == 1)
           {
               if (sentAlertCount > 0)
               {
                   AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, false,
                       _sqlnativehealthCompMonitor.InfraObjectId);

                   ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqldoubleTakeMonitor", _sqlnativehealthCompMonitor.InfraObjectId, CurrentInfraObject.Name);
               }
           }
           else
           {
               if (MaxAlertCount > sentAlertCount)
               {
                   AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, true,
                       _sqlnativehealthCompMonitor.InfraObjectId);
                   ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqldoubleTakeMonitor", _sqlnativehealthCompMonitor.InfraObjectId, CurrentInfraObject.Name);

               }
           }
       }

       public void MonitordoubleTakeApplicationRepComponent()
       {
           try
           {

               DTServer _serverPR, _serverDR;
               MSSQLServer _sqlServerPR, _sqlServerDR;
               // MSSQLDatabase _sqlDatabasePR, _sqlDatabaseDR;
               if (CurrentServer != null)
               {

                   CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                   CurrentServer.InfraObjectId = CurrentInfraObject.Id;


                   var InfraObjicetInfo = InfraObjectDataAccess.GetInfraObjectById(CurrentInfraObject.Id);
                   if (InfraObjicetInfo.PRReplicationId != 0)
                   {

                       MSSqlDoubletek _doubletakeMonitor = new MSSqlDoubletek();
                       var replication = ReplicationBaseDataAccess.GetReplicationById(InfraObjicetInfo.PRReplicationId);

                       if (replication != null)
                       {

                           _serverPR = new DTServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                           _serverDR = new DTServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                           var _healthDT = DTProcess.GetDTJobByName(_serverDR, replication.doubletekRep.JobName, replication.doubletekRep.JobType, DoubletakePath);



                           if (_healthDT != null)
                           {
                               _doubletakeMonitor.InfraobjectId = CurrentInfraObject.Id;
                               _doubletakeMonitor.ReplicationId = replication.doubletekRep.ReplicationId;
                               _doubletakeMonitor.JobName = replication.doubletekRep.JobName;
                               _doubletakeMonitor.ReplicationStatus = _healthDT.ReplicationState;
                               _doubletakeMonitor.SourceServer = CurrentServer.PRName;
                               _doubletakeMonitor.TargetServer = CurrentServer.DRName;
                               _doubletakeMonitor.JobType = _healthDT.JobType;
                               _doubletakeMonitor.ReplicationQueue = _healthDT.ReplicationOpsQueued;
                               _doubletakeMonitor.JobActivity = _healthDT.HighLevelState;
                               _doubletakeMonitor.MirrorStatus = _healthDT.MirrorState;
                               _doubletakeMonitor.MirrorPercentComplete = _healthDT.MirrorPermillage;
                               _doubletakeMonitor.MirrorBytesRemaining = _healthDT.MirrorBytesRemaining;
                               _doubletakeMonitor.CreatorId = 1;
                               _doubletakeMonitor.CreateDate = DateTime.Now;
                               _doubletakeMonitor.UpdatorId = 1;

                               Logger.InfoFormat("Retrived doubleTake Replication Info for Infraobject:" + CurrentInfraObject.Name + " DRIpAddress: " + CurrentServer.DRIPAddress);
                               Logger.InfoFormat("Retrived doubleTake Replication Info:" + " JobName: " + replication.doubletekRep.JobName + " ReplicationStatus: " + _healthDT.ReplicationState + " JobType: " + _healthDT.JobType + " ReplicationQueue: " + _healthDT.ReplicationOpsQueued + " JOBActivity: " + _healthDT.HighLevelState);
                               Logger.InfoFormat("Retrived doubleTake Mirror Replication Info :" + " MirrorState: " + _healthDT.MirrorState + " MirrorPercentComplete: " + _healthDT.MirrorPermillage);
                               var isSuccess = MSSqlDoubleteRepliMonitorkDataAccess.AddMssqldoubletakeMonitorStatus(_doubletakeMonitor);

                               if (isSuccess)
                               {
                                   Logger.InfoFormat("Added doubletake Monitor status Replication details", CurrentInfraObject.Name);
                               }

                               var istrue = MSSqlDoubleteRepliMonitorkDataAccess.AddMssqldoubletakeLogStatusdetails(_doubletakeMonitor);
                               if (istrue)
                               {
                                   Logger.InfoFormat("Added doubletake logs status  details", CurrentInfraObject.Name);
                               }

                           }

                           if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                           {
                               _sqlServerDR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                               _sqlServerPR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                           }
                           else
                           {

                               _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                               _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                           }

                           VerifydoubleTakeServers(_sqlServerPR, _sqlServerDR);


                           ProcessApplicationdoubletakeAlerts(_doubletakeMonitor);
                           //    ProcessMSSqldoubletakeAlerts(_sqlDatabasePR, _sqlDatabaseDR, health, _doubletakeMonitor);
                       }
                       else
                       {
                           //  ProcessMSSqldoubletakeAlerts(_sqlDatabasePR, _sqlDatabaseDR, health, _doubletakeMonitor);
                           ProcessApplicationdoubletakeAlerts(_doubletakeMonitor);
                       }

                   }
               }
           }

           catch (BcmsException exc)
           {
               Logger.Info("Exception:Monitor doubleTake component:" + CurrentInfraObject.Name + exc.Message + exc.InnerException.Message);
               throw;
           }


       }

       private void ProcessApplicationdoubletakeAlerts(MSSqlDoubletek _doubletakeMonitor)
       {
           #region PR DoubleTake Jobtype

           if (_doubletakeMonitor.JobActivity.ToLower() == "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.DTJobactivityisINprotecting, serviceAlertType);
           }
           else if (_doubletakeMonitor.JobActivity.ToLower() != "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is not in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.DTJobactivityisNotinprotecting, serviceAlertType);
           }

           if (_doubletakeMonitor.JobActivity.ToLower() == "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") e DoubleTake JobActivity is in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.DTJobactivityisINprotecting, serviceAlertType);
           }
           else if (_doubletakeMonitor.JobActivity.ToLower() != "protecting")
           {
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ")  DoubleTake JobActivity is not in protecting State;";
               int serviceAlertType = (int)AlertNotificationType.MSSQLdoubleTakeJobActivity;
               ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.DTJobactivityisNotinprotecting, serviceAlertType);
           }

           #endregion

           #region Replication DataLag between PR&DR
           var confDatalag = BusinessFunctionDataAccess.GetById(CurrentInfraObject.BusinessFunctionId);

           int _configdtbytes = Convert.ToInt32(confDatalag.DataLagInByte);
           int _discoverdinBytes = Convert.ToInt32(_doubletakeMonitor.MirrorBytesRemaining);
           Logger.Info("Configured DataLag:" + _configdtbytes.ToString() + " Discovered Datalag :" + _discoverdinBytes.ToString());
           if (_discoverdinBytes > 0)
           {
               Logger.Info(CurrentInfraObject.Name + ":Discovred Datalag is Greater !");
               string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSqlDoubleTake Datalag  is Deviated with Datalag !" + _doubletakeMonitor.MirrorBytesRemaining;
               int serviceAlertType = (int)AlertNotificationType.MssqlDTRepDataLag;
               ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMssqlDBDatalagDTbetPrDR, serviceAlertType);
           }
           else
               if (_discoverdinBytes <= 0)
               {
                   Logger.Info(CurrentInfraObject.Name + ":Zero(0) Datalag");
                   string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSqlDoubleTake Datalag  is  !" + _doubletakeMonitor.MirrorBytesRemaining;
                   int serviceAlertType = (int)AlertNotificationType.MssqlDTRepDataLag;

                   ProcessappdtAlerts(_doubletakeMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMssqlDTDatalag, serviceAlertType);
               }
           #endregion
       }

       private void ProcessappdtAlerts(MSSqlDoubletek _doubletakeMonitor, string PRServiceAlertmessage, string type, int status, BcmsExceptionType exceptiontype, int serviceAlertType)
       {
           //int serviceAlertType = string.Equals("PR", type) ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

           var sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Service), serviceAlertType, _doubletakeMonitor.InfraobjectId);
           if (status == 1)
           {
               if (sentAlertCount > 0)
               {
                   AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, false,
                       _doubletakeMonitor.InfraobjectId);

                   ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqldoubleTakeMonitor", _doubletakeMonitor.InfraobjectId, CurrentInfraObject.Name);
               }
           }
           else
           {
               if (MaxAlertCount > sentAlertCount)
               {
                   AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, true,
                       _doubletakeMonitor.InfraobjectId);
                   ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MssqldoubleTakeMonitor", _doubletakeMonitor.InfraobjectId, CurrentInfraObject.Name);

               }
           }
       }

        private void VerifydoubleTakeServers(MSSQLServer prSSHInfo, MSSQLServer drSSHInfo)
        {
            using (BCMS.Core.Utility.SSHHelper SSHHelper = new BCMS.Core.Utility.SSHHelper())
            {
                try
                {
                    //MSSQLServer _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    //MSSQLDatabase _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString());
                    //MSSQLServer _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                    //MSSQLDatabase _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString());
                    MSSQLServer _sqlServerPR, _sqlServerDR;
                    //  MSSQLDatabase _sqlDatabasePR, _sqlDatabaseDR;
                    _sqlServerPR = new MSSQLServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    _sqlServerDR = new MSSQLServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                    //  _sqlDatabasePR = new MSSQLDatabase(_sqlServerPR, CurrentDatabase.DatabaseSql.PRDatabaseSID, CurrentDatabase.DatabaseSql.PRUserName, CurrentDatabase.DatabaseSql.PRPassword, CurrentDatabase.DatabaseSql.PRPort.ToString());
                    //   _sqlDatabaseDR = new MSSQLDatabase(_sqlServerDR, CurrentDatabase.DatabaseSql.DRDatabaseSID, CurrentDatabase.DatabaseSql.DRUserName, CurrentDatabase.DatabaseSql.DRPassword, CurrentDatabase.DatabaseSql.DRPort.ToString());

                    int serverAlertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRServer : (int)AlertNotificationType.PRServer;

                    int serverAlertType1 = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRServer : (int)AlertNotificationType.DRServer;

                    var sentServerAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType, CurrentInfraObject.Id);

                    var sentServerAlertCount1 = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType1, CurrentInfraObject.Id);


                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, "PR"))
                    {


                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                               "InfraObject Name : " + CurrentInfraObject.Name + " Primary Server (" +
                            //                                              prSSHInfo.Host + ") is connected."), "sqlNative2008ServerStatus", CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            string ResolveMessage = CurrentInfraObject.Name + " : Production Server (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + ") is connected";
                            IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, 0, ResolveMessage);

                        }

                    }
                    else
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, true, CurrentInfraObject.Id);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable,
                            //                                          "InfraObject Name : " + CurrentInfraObject.Name +
                            //                                          " Primary Server (" +
                            //                                          prSSHInfo.Host + ") is not reachable."), "SqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });

                            IncidentManagement inc = new IncidentManagement
                            {
                                IncidentName = "CP-Inc_Server_down",
                                IncidentTime = System.DateTime.Now,
                                InfraID = CurrentInfraObject.Id,
                                InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                InfraComponentType = "Server",
                                IncidentComment = "Server Not Reachable"

                            };
                            IncidentManagementDataAccess.AddIncident(inc);

                        }
                    }
                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, "DR"))
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount1 > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                            "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                                            prSSHInfo.Host + ") is connected."), "MonitorsqlNative2008ServerStatus",
                            //                       CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                            //{
                            //    InfraObjectId = CurrentInfraObject.Id,
                            //    EntityId = CurrentServer.PRId,
                            //    Status = 0,
                            //    ResolveMessage =
                            //    CurrentInfraObject.Name + " : Server (" + CurrentServer.PRIPAddress + ") is connected"
                            //});


                        }

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Down);
                        }
                        if (MaxAlertCount > sentServerAlertCount1)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, true, CurrentInfraObject.Id);


                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                        drSSHInfo.Host + ") is not reachable."), "sqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });



                        }
                    }

                }
                catch (BcmsException exc)
                {
                    throw;
                }
                catch (Exception exc)
                {
                    ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 0);
                    throw new BcmsException(BcmsExceptionType.CommonUnhandled, string.Format("{0} : Exception occured while Connecting Server", CurrentInfraObject.Name), exc);
                }
            }

        }
      
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~doubleTakeClient()
        {
            Dispose(false);
        }
    }
}
