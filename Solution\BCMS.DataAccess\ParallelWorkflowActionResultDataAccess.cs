﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{

    public class ParallelWorkflowActionResultDataAccess : BaseDataAccess
    {
        //Added by <PERSON><PERSON><PERSON> for ITIT-10566
        public static IList<ParallelWorkflowActionResult> GetByCompletedActionResultStatus(int parallelOperationId, int parallelGroupWorkflowId, int infraobjectId)
        {
            IList<ParallelWorkflowActionResult> parallelresult = new List<ParallelWorkflowActionResult>();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("GetByCompletedActionResultStatus"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParallelOperationId", DbType.Int32, parallelOperationId);
                    Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelGroupWorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraobjectId);
                    // Database.AddInParameter(cmd, Dbstring + "iactionId", DbType.Int32, actionId);


#if ORACLE
                    _logger.Info("Oracle DB_ProgressBar");
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader myReader = Database.ExecuteReader(cmd))
                    {
                        while (myReader.Read())
                        {
                            var parallel = new ParallelWorkflowActionResult();

                            parallel.Id = Convert.ToInt32(myReader["Id"]);
                            parallel.WorkflowActionName = Convert.ToString(myReader["WorkflowActionName"]);
                            parallel.Status = Convert.ToString(myReader["Status"]);
                            parallel.ParallelDROperationId = Convert.ToInt32(myReader["ParallelDROperationId"]);
                            parallel.ParallelGroupWorkflowId = Convert.ToInt32(myReader["ParallelGroupWorkflowId"]);
                            parallel.InfraObjectId = Convert.ToInt32(myReader["InfraobjectId"]);
                            parallel.ActionId = Convert.ToInt32(myReader["ActionId"]);
                            parallelresult.Add(parallel);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All parallel action completed", exc);
            }
            return parallelresult;
        }

        public static ParallelWorkflowActionResult Add(ParallelWorkflowActionResult parallelWorkflow)
        {
            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELWFACTIONRESULT_CREATE"))
            {
                Database.AddInParameter(cmd, Dbstring + "iWorkflowActionName", DbType.AnsiString, parallelWorkflow.WorkflowActionName);
                Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.DateTime, parallelWorkflow.StartTime);
                Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.DateTime, parallelWorkflow.EndTime);

                Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelWorkflow.Status);
                Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelWorkflow.ParallelGroupWorkflowId);

                Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32, parallelWorkflow.ParallelDROperationId);
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, parallelWorkflow.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelWorkflow.Message);
                Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, parallelWorkflow.ActionId);
                Database.AddInParameter(cmd, Dbstring + "iConditionActionId", DbType.Int32, parallelWorkflow.ConditionActionId);
                Database.AddInParameter(cmd, Dbstring + "iSkipStep", DbType.Int32, Convert.ToInt32(parallelWorkflow.SkipStep));
                Database.AddInParameter(cmd, Dbstring + "iDirection", DbType.AnsiString, parallelWorkflow.Direction);
                // Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, parallelWorkflow.CreatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        parallelWorkflow.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        parallelWorkflow = null;
                    }
                }

                return parallelWorkflow;
            }
        }

        public static ParallelWorkflowActionResult Update(ParallelWorkflowActionResult parallelWorkflow)
        {
            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELWFACTIONRESULT_UPDATE"))
            {
                Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelWorkflow.Id);
                Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.DateTime, parallelWorkflow.StartTime);
                Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.DateTime, parallelWorkflow.EndTime);
                Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelWorkflow.Status);
                Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelWorkflow.Message);
                Database.AddInParameter(cmd, Dbstring + "iConditionActionId", DbType.Int32, parallelWorkflow.ConditionActionId);
                Database.AddInParameter(cmd, Dbstring + "iDirection", DbType.AnsiString, parallelWorkflow.Direction);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        parallelWorkflow.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        parallelWorkflow = null;
                    }
                }

                return parallelWorkflow;
            }
        }

        public static ParallelWorkflowActionResult UpdateSkipStep(bool skipstep, int actionId, int infraObjectId)
        {

            var parallelResult = new ParallelWorkflowActionResult();

            using (DbCommand cmd = Database.GetStoredProcCommand("ParallelWFAction_UpdateSkip"))
            {
                Database.AddInParameter(cmd, Dbstring + "iSkipStep", DbType.Boolean, skipstep);
                Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, actionId);
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        parallelResult.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        parallelResult = null;
                    }
                }

                return parallelResult;
            }
        }

        public static ParallelWorkflowActionResult GetLastRecordByInfraObjectIdandWFId(int infraid)
        {
            var parallelResult = new ParallelWorkflowActionResult();

            using (DbCommand cmd = Database.GetStoredProcCommand("ParallelWFAction_GetLastInfra"))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraid);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelWorkflowReader.Read())
                    {
                        parallelResult.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelResult.WorkflowActionName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowActionName"]);
                        parallelResult.StartTime = Convert.IsDBNull(myParallelWorkflowReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["StartTime"]);
                        parallelResult.EndTime = Convert.IsDBNull(myParallelWorkflowReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["EndTime"]);
                        parallelResult.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelResult.ParallelGroupWorkflowId = Convert.IsDBNull(myParallelWorkflowReader["ParallelGroupWorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelGroupWorkflowId"]);
                        parallelResult.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelResult.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                        parallelResult.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelResult.ActionId = Convert.IsDBNull(myParallelWorkflowReader["ActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ActionId"]);
                        parallelResult.ConditionActionId = Convert.IsDBNull(myParallelWorkflowReader["ConditionActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionActionId"]);
                        if (!Convert.IsDBNull(myParallelWorkflowReader["SkipStep"]))
                            parallelResult.SkipStep = Convert.ToBoolean(myParallelWorkflowReader["SkipStep"]);
                        parallelResult.Direction = Convert.IsDBNull(myParallelWorkflowReader["Direction"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Direction"]);



                    }
                    else
                    {
                        parallelResult = null;
                    }
                }

                return parallelResult;
            }

        }


        public static ParallelWorkflowActionResult GetParallelWorkflowActionResultById(int id)
        {
            var parallelResult = new ParallelWorkflowActionResult();

            using (DbCommand cmd = Database.GetStoredProcCommand("ParallelWFAction_GetId"))
            {
                Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelWorkflowReader.Read())
                    {
                        parallelResult.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                        parallelResult.WorkflowActionName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowActionName"]);
                        parallelResult.StartTime = Convert.IsDBNull(myParallelWorkflowReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["StartTime"]);
                        parallelResult.EndTime = Convert.IsDBNull(myParallelWorkflowReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["EndTime"]);
                        parallelResult.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);
                        parallelResult.ParallelGroupWorkflowId = Convert.IsDBNull(myParallelWorkflowReader["ParallelGroupWorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelGroupWorkflowId"]);
                        parallelResult.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                        parallelResult.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraobjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraobjectId"]);
                        parallelResult.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                        parallelResult.ActionId = Convert.IsDBNull(myParallelWorkflowReader["ActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ActionId"]);
                        parallelResult.ConditionActionId = Convert.IsDBNull(myParallelWorkflowReader["ConditionActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionActionId"]);
                        if (!Convert.IsDBNull(myParallelWorkflowReader["SkipStep"]))
                            parallelResult.SkipStep = Convert.ToBoolean(myParallelWorkflowReader["SkipStep"]);
                        parallelResult.Direction = Convert.IsDBNull(myParallelWorkflowReader["Direction"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Direction"]);
                        parallelResult.ISReload = Convert.IsDBNull(myParallelWorkflowReader["ISReload"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ISReload"]);


                    }
                    else
                    {
                        parallelResult = null;
                    }
                }

                return parallelResult;
            }

        }

        public static IList<ParallelWorkflowActionResult> AllParallelActionCompleted(int parallelOperationId, int parallelGroupWorkflowId, int infraobjectId, string actionId)
        {
            IList<ParallelWorkflowActionResult> parallelresult = new List<ParallelWorkflowActionResult>();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELRESULT_ACTIONCOMPLETE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParallelOperationId", DbType.Int32, parallelOperationId);
                    Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelGroupWorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.AnsiString, actionId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(cmd))
                    {
                        while (myReader.Read())
                        {
                            var parallel = new ParallelWorkflowActionResult();

                            parallel.Id = Convert.ToInt32(myReader["Id"]);
                            parallel.WorkflowActionName = Convert.ToString(myReader["WorkflowActionName"]);
                            parallel.Status = Convert.ToString(myReader["Status"]);
                            parallel.ParallelDROperationId = Convert.ToInt32(myReader["ParallelDROperationId"]);
                            parallel.ParallelGroupWorkflowId = Convert.ToInt32(myReader["ParallelGroupWorkflowId"]);
                            parallel.InfraObjectId = Convert.ToInt32(myReader["InfraobjectId"]);
                            parallel.ActionId = Convert.ToInt32(myReader["ActionId"]);
                            parallelresult.Add(parallel);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All parallel action completed", exc);
            }
            return parallelresult;
        }

        public static bool AllParallelActionCompleted(int parallelOperationId, int parallelGroupWorkflowId, int infraobjectId)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("PARALLELRESULT_ACTIONRUNNING"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iParallelOperationId", DbType.Int32, parallelOperationId);
                    Database.AddInParameter(dbCommand, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelGroupWorkflowId);
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraobjectId);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    object myReader = Database.ExecuteScalar(dbCommand);

                    return myReader == null;

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while check All parallel action completed", exc);
            }
        }

        public static ParallelWorkflowActionResult GetByParallelDROPIdWFActionId(int drId, int actionId)
        {
            var parallelWF = new ParallelWorkflowActionResult();
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLWFLOWRESULT_GETBYWFAID"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParallelDrOperationId", DbType.Int32, drId);
                    Database.AddInParameter(cmd, Dbstring + "icurrentactionid", DbType.Int32, actionId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {

                            parallelWF.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            parallelWF.WorkflowActionName = Convert.IsDBNull(reader["WORKFLOWACTIONNAME"]) ? string.Empty : Convert.ToString(reader["WORKFLOWACTIONNAME"]);
                            parallelWF.Status = Convert.IsDBNull(reader["STATUS"]) ? string.Empty : Convert.ToString(reader["STATUS"]);
                            parallelWF.ParallelDROperationId = Convert.IsDBNull(reader["PARALLELDROPERATIONID"]) ? 0 : Convert.ToInt32(reader["PARALLELDROPERATIONID"]);
                            parallelWF.ParallelGroupWorkflowId = Convert.IsDBNull(reader["PARALLELGROUPWORKFLOWID"]) ? 0 : Convert.ToInt32(reader["PARALLELGROUPWORKFLOWID"]);
                            parallelWF.InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                            parallelWF.ActionId = Convert.IsDBNull(reader["ActionId"]) ? 0 : Convert.ToInt32(reader["ActionId"]);

                        }
                    }


                }
            }
            catch (Exception exe)
            {
                throw;
            }
            return parallelWF;
        }

        public static bool GetByParallelDROperIdAndParallelGrIDAndWFId(int parallelDrOpId, int parallelGrId, string workflowActionId)
        {
            try
            {

                const string sp = "PARALLELWFR_GETBYORIDWFAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iparallelDrOpId", DbType.Int32, parallelDrOpId);
                    Database.AddInParameter(cmd, Dbstring + "iparallelGrId", DbType.Int32, parallelGrId);
                    Database.AddInParameter(cmd, Dbstring + "iworkflowActionId", DbType.AnsiString, workflowActionId);
                    int returnCode = Convert.ToInt32(Database.ExecuteScalar(cmd));
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation,
                     "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get Records <see cref="ParallelWorkflowActionResult" />from  parallel_workflowactionresult table.
        /// </summary>
        /// <param name="parallelresult">parallelresult</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Ravindra Mahajan</author>
        /// 
        public static List<ParallelWorkflowActionResult> GetCurrentDRGroupWorkflowActionsResult(ParallelGroupWorkflow CurrentParallelGroupWorkflow)
        {
            try
            {
                var parallelActionList = new List<ParallelWorkflowActionResult>();

                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLWFRESULT_GETBYDROPRID"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iparallelDrOperationId", DbType.Int32, CurrentParallelGroupWorkflow.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iparallelGroupId", DbType.Int32, CurrentParallelGroupWorkflow.Id);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, CurrentParallelGroupWorkflow.InfraObjectId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //if (myDROperationResultReader.Read())
                        //{
                        //    parallelWorkflow.Id = Convert.ToInt32(myDROperationResultReader[0]);
                        //}
                        //else
                        //{
                        //    parallelWorkflow = null;
                        //}


                        while (reader.Read())
                        {
                            var parallelGroup = new ParallelWorkflowActionResult();

                            parallelGroup.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            parallelGroup.WorkflowActionName = Convert.IsDBNull(reader["WorkflowActionName"])
                                ? string.Empty
                                : Convert.ToString(reader["WorkflowActionName"]);
                            parallelGroup.StartTime = Convert.IsDBNull(reader["StartTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["StartTime"]);
                            parallelGroup.EndTime = Convert.IsDBNull(reader["EndTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["EndTime"]);
                            parallelGroup.Message = Convert.IsDBNull(reader["Message"])
                                ? string.Empty
                                : Convert.ToString(reader["Message"]);
                            parallelGroup.ParallelDROperationId = Convert.IsDBNull(reader["ParallelDROperationId"])
                                ? 0
                                : Convert.ToInt32(reader["ParallelDROperationId"]);

                            parallelGroup.Status = Convert.IsDBNull(reader["Status"])
                                ? string.Empty
                                : Convert.ToString(reader["Status"]);

                            parallelGroup.ParallelGroupWorkflowId = Convert.IsDBNull(reader["ParallelGroupWorkflowId"])
                                ? 0
                                : Convert.ToInt32(reader["ParallelGroupWorkflowId"]);

                            parallelGroup.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            parallelGroup.ActionId = Convert.IsDBNull(reader["ActionId"]) ? 0 : Convert.ToInt32(reader["ActionId"]);
                            parallelGroup.ConditionActionId = Convert.IsDBNull(reader["ConditionActionId"]) ? 0 : Convert.ToInt32(reader["ConditionActionId"]);

                            if (!Convert.IsDBNull(reader["SkipStep"]))
                                parallelGroup.SkipStep = Convert.ToBoolean(reader["SkipStep"]);

                            parallelGroup.Direction = Convert.IsDBNull(reader["Direction"]) ? string.Empty : Convert.ToString(reader["Direction"]);
                            parallelGroup.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            parallelGroup.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["CreateDate"]);

                            parallelActionList.Add(parallelGroup);
                        }

                        return parallelActionList;
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Gets Parallel Workflow Action Results Existing Record", exc);
            }

            //return parallelActionList;
        }


        /// <summary>
        ///     Delete Records <see cref="ParallelWorkflowActionResult" />from  parallel_workflowactionresult table.
        /// </summary>
        /// <param name="parallelresult">parallelresult</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Ravindra Mahajan</author>
        ///
        public static bool DeleteExistingRecords(int parallelResultId)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ParallelWFActionResult_Delete"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelResultId);

                    //Database.AddInParameter(cmd, Dbstring + "iparallelDrOperationId", DbType.Int32, CurrentParallelGroupWorkflow.ParallelDROperationId);
                    //Database.AddInParameter(cmd, Dbstring + "iparallelGroupId", DbType.Int32, CurrentParallelGroupWorkflow.Id);
                    //Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, CurrentParallelGroupWorkflow.InfraObjectId);
                    //Database.AddInParameter(cmd, Dbstring + "iActionId ", DbType.Int32, parallelResultId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int isuccess = Database.ExecuteNonQuery(cmd);
                    return isuccess > 0;

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Deleting Parallel Workflow Action Result Record", exc);
            }
        }

       
        public static void UpdateReload(int id, int reload)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("PWFAct_UpdateReload"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsReload", DbType.Int32, reload);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    Database.ExecuteScalar(cmd);
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update reload in action result", exc);
            }
        }


    }
}