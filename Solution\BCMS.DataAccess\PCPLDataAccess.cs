﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Utility;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using System.Collections.Generic;
using log4net;
using Bcms.DataAccess.Base;
using System.Text;
using System.Diagnostics;
using System.Threading;
using CMDExec;


namespace Bcms.DataAccess
{
    public class PCPLDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ServerDataAccess));

        public static bool AddPCPLKeyValues(PCPLKeyValues PCPLKeyValuesDetails)
        {
            try
            {

                //var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("PCPLKeyValues_Create"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iKeyName", DbType.AnsiString, PCPLKeyValuesDetails.KeyName);
                    Database.AddInParameter(dbCommand, Dbstring+"iKeyValue", DbType.AnsiString, PCPLKeyValuesDetails.KeyValue);

                    int value = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value == 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert PCPL Key Values information", exc);
            }
            return false;
        }

        public static PCPLKeyValues GetByKeyName(string iKeyName)
        {

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("PCPLKeyValues_GetByKeyName"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iKeyName", DbType.AnsiString, iKeyName);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {
                        PCPLKeyValues ObjPCPLKeyValues = null;

                        while (myServerReader.Read())
                        {
                            ObjPCPLKeyValues = new PCPLKeyValues();

                            ObjPCPLKeyValues.Id = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                            ObjPCPLKeyValues.KeyName = Convert.IsDBNull(myServerReader["KeyName"]) ? string.Empty : Convert.ToString(myServerReader["KeyName"]);
                            ObjPCPLKeyValues.KeyValue = Convert.IsDBNull(myServerReader["KeyValue"]) ? string.Empty : Convert.ToString(myServerReader["KeyValue"]);
                            ObjPCPLKeyValues.CreatedAt = Convert.IsDBNull(myServerReader["CreatedAt"]) ? DateTime.MinValue : Convert.ToDateTime(myServerReader["CreatedAt"]);
                        }

                        return ObjPCPLKeyValues;
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Getting PCPL Key Values using Key Name", exc);
            }
        }
    }
}
