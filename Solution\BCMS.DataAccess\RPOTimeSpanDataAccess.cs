﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class RPOTimeSpanDataAccess : BaseDataAccess
    {
        public static IList<RPOTimeSpan> GetAllTimeSpanRPO(int id)
        {
            IList<RPOTimeSpan> rpotimespanlst = new List<RPOTimeSpan>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("RPO_TIMESPAN_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            RPOTimeSpan rpotimespan = new RPOTimeSpan();
                            rpotimespan.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            rpotimespan.BusinessFunctionId = Convert.IsDBNull(reader["BusinessFunctionId"]) ? 0 : Convert.ToInt32(reader["BusinessFunctionId"]);
                            rpotimespan.TimeSpanType = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            rpotimespan.StartTime = Convert.IsDBNull(reader["StartTime"]) ? string.Empty : Convert.ToString(reader["StartTime"]);
                            rpotimespan.EndTime = Convert.IsDBNull(reader["EndTime"]) ? string.Empty : Convert.ToString(reader["EndTime"]);
                            rpotimespan.RPO = Convert.IsDBNull(reader["RPO"]) ? string.Empty : Convert.ToString(reader["RPO"]);
                            rpotimespan.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            rpotimespan.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            rpotimespan.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            rpotimespan.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                            rpotimespanlst.Add(rpotimespan);
                        }
                    }
                }
                return rpotimespanlst;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occured while Get All TimeSpanRPO information By Business Function Id - " + id, exc);
            }
        }
    }
}
