﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bcms.Common;
using Bcms.Core.Client;

namespace Bcms.Core.Job
{
    public class Report24HrsStatusJob : IJob
    {
     

        public void Execute(JobExecutionContext context)
        {
            Perform24hoursReportCreation();
            
        }

        public void Perform24hoursReportCreation()
        {
            using (var client = new BcmsClient())
            {
                //client.CreateAndSendReport();
            }
        
        }

       
    }
}
