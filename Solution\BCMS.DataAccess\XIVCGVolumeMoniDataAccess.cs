﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
   public class XIVCGVolumeMoniDataAccess :BaseDataAccess
    {
       public static bool AddXIVCGVolumeMonitoringLogDetails(CGVolMoniLogs CGVolMoniLogs)
       {
           try
           {
               using (DbCommand cmd = Database.GetStoredProcCommand("CGVol_Moni_Logs_CREATE"))
               {
                   Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, CGVolMoniLogs.InfraID);
                   Database.AddInParameter(cmd, Dbstring + "iCGID", DbType.Int32, CGVolMoniLogs.CGID);
                   Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, CGVolMoniLogs.CGName);
                   Database.AddInParameter(cmd, Dbstring + "iPRVolumeName", DbType.AnsiString, CGVolMoniLogs.PRVolumeName);
                   Database.AddInParameter(cmd, Dbstring + "iDRVolumeName", DbType.AnsiString, CGVolMoniLogs.DRVolumeName);
                   Database.AddInParameter(cmd, Dbstring + "iActiveStatus", DbType.AnsiString, CGVolMoniLogs.ActiveStatus);
                   Database.AddInParameter(cmd, Dbstring + "iRPOStatus", DbType.AnsiString, CGVolMoniLogs.RPOStatus);
                   Database.AddInParameter(cmd, Dbstring + "iLinkUP", DbType.AnsiString, CGVolMoniLogs.LinkUP);
          


                   int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                   if (value > 0)
                   {
                       return true;
                   }

               }

           }
           catch (Exception exc)
           {
               throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create CG Volume Detailed Monitor Logs InfraObject Id - " + CGVolMoniLogs.InfraID, exc);

           }

           return false;
       }
    }
}
