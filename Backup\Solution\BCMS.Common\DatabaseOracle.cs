﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseOracle", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseOracle : BaseEntity
    {

        #region Properties

        //DatabaseBase _databaseBase=new DatabaseBase();

        [DataMember]
        public int PRBaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PROracleSID
        {
            get;
            set;
        }

        [DataMember]
        public string PRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string PRPassword
        {
            get;
            set;
        }
        [DataMember]
        public int PRPort
        {
            get;
            set;
        }

        [DataMember]
        public int DRBaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DROracleSID
        {
            get;
            set;
        }

        [DataMember]
        public string DRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string DRPassword
        {
            get;
            set;
        }
        [DataMember]
        public int DRPort
        {
            get;
            set;
        }
        //[DataMember]
        //public DatabaseBase DatabaseBase
        //{
        //    get { return _databaseBase; }
        //    set { _databaseBase = value; }
        //}
        #endregion

        #region Constructor

        public DatabaseOracle()
            : base()
        {
        }

        #endregion
    }
}
