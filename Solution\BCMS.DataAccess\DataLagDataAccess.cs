﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class DataLagDataAccess:BaseDataAccess
    {
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static DataLag GetDataLagByGroupId(int id)
        {
            var datalag = new DataLag();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("OracleLog_GetByInfraObjectId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            datalag.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                            datalag.LogFileName = Convert.IsDBNull(myDataLagReader["LogFileName"]) ? string.Empty : Convert.ToString(myDataLagReader["LogFileName"]);
                            datalag.PRSequenceNo = Convert.IsDBNull(myDataLagReader["PRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["PRSequenceNo"]);
                            datalag.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                            datalag.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                            datalag.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                            datalag.PRLogTime = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                            datalag.DRLogTime = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                            datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                            datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                            datalag.BusinessServiceId = Convert.IsDBNull(myDataLagReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myDataLagReader["BusinessServiceId"]);
                            datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
           


                            //datalag.Id = Convert.ToInt32(myDataLagReader[0]);
                            //datalag.LogFileName = myDataLagReader[1].ToString();
                            //datalag.PRSequenceNo = myDataLagReader[2].ToString();
                            //datalag.DRSequenceNo = myDataLagReader[3].ToString();
                            //datalag.PRTransId = myDataLagReader[4].ToString();
                            //datalag.DRTransId = myDataLagReader[5].ToString();
                            //datalag.PRLogDate = myDataLagReader[6].ToString();
                            //datalag.DRLogDate = myDataLagReader[7].ToString();
                            //datalag.CurrentDataLag = myDataLagReader[8].ToString();
                            //datalag.Health = Convert.ToInt32(myDataLagReader[9]);
                            //datalag.ApplicationGroupId = Convert.ToInt32(myDataLagReader[10]);
                            //datalag.GroupId = Convert.ToInt32(myDataLagReader[11]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Datalag information By InfraObject Id - " + id, exc);               
            }

            return datalag;
        }
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static bool AddOracleLog(DataLag dataLagDetails)
        {
            
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ORACLELOG_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, dataLagDetails.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPRSequenceNo", DbType.AnsiString, dataLagDetails.PRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRSequenceNo", DbType.AnsiString, dataLagDetails.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iPRTransactionId", DbType.AnsiString, dataLagDetails.PRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iDRTransactionId", DbType.AnsiString, dataLagDetails.DRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLogTime", DbType.AnsiString, dataLagDetails.PRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iDRLogTime", DbType.AnsiString, dataLagDetails.DRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iCurrentDataLag", DbType.AnsiString, dataLagDetails.CurrentDataLag);
                    Database.AddInParameter(cmd, Dbstring+"iHealth", DbType.Int32, dataLagDetails.Health);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, dataLagDetails.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iBuinsessFunctionId", DbType.Int32, dataLagDetails.BuinsessFunctionId);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, dataLagDetails.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iNodeId", DbType.Int32, dataLagDetails.GroupDatabaseNodeId);
                    Database.AddInParameter(cmd, Dbstring+"iDrLogFileName", DbType.AnsiString, dataLagDetails.DRLogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPrThreadId", DbType.AnsiString, dataLagDetails.PRThreadId);
                    Database.AddInParameter(cmd, Dbstring+"iDrThreadId", DbType.AnsiString, dataLagDetails.DRThreadId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add DataLag Details", exc);              
                
            }
        }
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static bool AddOracleLogStatus(DataLag dataLagDetails)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ORACLELOGSTATUS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, dataLagDetails.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPRSequenceNo", DbType.AnsiString, dataLagDetails.PRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRSequenceNo", DbType.AnsiString, dataLagDetails.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iPRTransactionId", DbType.AnsiString, dataLagDetails.PRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iDRTransactionId", DbType.AnsiString, dataLagDetails.DRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLogTime", DbType.AnsiString, dataLagDetails.PRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iDRLogTime", DbType.AnsiString, dataLagDetails.DRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iCurrentDataLag", DbType.AnsiString, dataLagDetails.CurrentDataLag);
                    Database.AddInParameter(cmd, Dbstring+"iHealth", DbType.Int32, dataLagDetails.Health);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, dataLagDetails.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iBuinsessFunctionId", DbType.Int32, dataLagDetails.BuinsessFunctionId);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, dataLagDetails.InfraObjectId);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add DataLag Details", exc);

            }
        }
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static bool UpdateOracleLogStatus(DataLag dataLagDetails)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ORACLELOGSTATU_UPDATEBYGROUPID"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, dataLagDetails.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iLogFileName", DbType.AnsiString, dataLagDetails.LogFileName);
                    Database.AddInParameter(cmd, Dbstring+"iPRSequenceNo", DbType.AnsiString, dataLagDetails.PRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRSequenceNo", DbType.AnsiString, dataLagDetails.DRSequenceNo);
                    Database.AddInParameter(cmd, Dbstring+"iPRTransactionId", DbType.AnsiString, dataLagDetails.PRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iDRTransactionId", DbType.AnsiString, dataLagDetails.DRTransId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLogTime", DbType.AnsiString, dataLagDetails.PRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iDRLogTime", DbType.AnsiString, dataLagDetails.DRLogDateTime);
                    Database.AddInParameter(cmd, Dbstring+"iCurrentDataLag", DbType.AnsiString, dataLagDetails.CurrentDataLag);
                    Database.AddInParameter(cmd, Dbstring+"iHealth", DbType.Int32, dataLagDetails.Health);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, dataLagDetails.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iBuinsessFunctionId", DbType.Int32, dataLagDetails.BuinsessFunctionId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("ORACLEFASTCOPYCur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add DataLag Details", exc);

            }
        }
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static IList<DataLag> GetAllCurrentDataLag()
        {
            IList<DataLag> dataLags=new List<DataLag>();


            using (DbCommand cmd = Database.GetStoredProcCommand("ORACLELOG_GETALLCURRENTRECORD"))
            {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDataLagReader = Database.ExecuteReader(cmd))
                {
                    while (myDataLagReader.Read())
                    {
                        var datalag = new DataLag();

                        datalag.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                        datalag.LogFileName = Convert.IsDBNull(myDataLagReader["LogFileName"]) ? string.Empty : Convert.ToString(myDataLagReader["LogFileName"]);
                        datalag.PRSequenceNo = Convert.IsDBNull(myDataLagReader["PRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["PRSequenceNo"]);
                        datalag.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                        datalag.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                        datalag.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                        datalag.PRLogDate = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                        datalag.DRLogDate = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                        datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                        datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                        datalag.BusinessServiceId = Convert.IsDBNull(myDataLagReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myDataLagReader["BusinessServiceId"]);
                        datalag.BuinsessFunctionId = Convert.IsDBNull(myDataLagReader["BuinsessFunctionId"]) ? 0 : Convert.ToInt32(myDataLagReader["BuinsessFunctionId"]);
                        datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
           

                        //datalag.Id = Convert.ToInt32(myDataLagReader[0]);
                        //datalag.LogFileName = myDataLagReader[1].ToString();
                        //datalag.PRSequenceNo = myDataLagReader[2].ToString();
                        //datalag.DRSequenceNo = myDataLagReader[3].ToString();
                        //datalag.PRTransId = myDataLagReader[4].ToString();
                        //datalag.DRTransId = myDataLagReader[5].ToString();
                        //datalag.PRLogDate = myDataLagReader[6].ToString();
                        //datalag.DRLogDate = myDataLagReader[7].ToString();
                        //datalag.CurrentDataLag = myDataLagReader[8].ToString();
                        //datalag.Health = myDataLagReader.IsDBNull(9)? 0: Convert.ToInt32(myDataLagReader[9]);
                        //datalag.ApplicationGroupId = myDataLagReader.IsDBNull(10)? 0 : Convert.ToInt32(myDataLagReader[10]);
                        //datalag.GroupId = Convert.ToInt32(myDataLagReader[11]);

                        dataLags.Add(datalag);
                    }
                }
                
            }

            return dataLags;

        }

        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static IList<DataLag> GetHourlyByGroupId(int id, string type)
        {
            IList<DataLag> hourlydataLags = new List<DataLag>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("OracleLog_GetByHours_InfraId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring+"iType", DbType.AnsiString, type);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            var datalag = new DataLag();

                           datalag.PRSequenceNo = Convert.IsDBNull(myDataLagReader["PRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["PRSequenceNo"]);
                            datalag.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                            datalag.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                            datalag.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                            datalag.PRLogTime = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                            datalag.DRLogTime = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                            datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                            datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                            datalag.BusinessServiceId = Convert.IsDBNull(myDataLagReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myDataLagReader["BusinessServiceId"]);
                            datalag.BuinsessFunctionId = Convert.IsDBNull(myDataLagReader["BuinsessFunctionId"]) ? 0 : Convert.ToInt32(myDataLagReader["BuinsessFunctionId"]);
                            datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
                            datalag.CreateDate = Convert.IsDBNull(myDataLagReader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(myDataLagReader["CreateDate"]);
                           
                            //datalag.PRSequenceNo = myDataLagReader[2].ToString();
                            //datalag.DRSequenceNo = myDataLagReader[3].ToString();
                            
                            //datalag.PRLogTime = myDataLagReader[6].ToString();
                            //datalag.DRLogTime = myDataLagReader[7].ToString();
                            //datalag.CurrentDataLag = myDataLagReader[8].ToString();

                            //datalag.Health = Convert.ToInt32(myDataLagReader[9]);
                            //datalag.ApplicationGroupId = Convert.ToInt32(myDataLagReader[10]);
                            //datalag.GroupId = Convert.ToInt32(myDataLagReader[11]);
                            //datalag.CreateDate = Convert.ToDateTime(myDataLagReader[12].ToString());

                            hourlydataLags.Add(datalag);
                        }

                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Datalag information By InfraObject Id and Type - " + id, exc);
            }

            return hourlydataLags;

        }
        /// <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static DataLag GetOracleLogStatusByGroupId(int id)
        {
            var datalag = new DataLag();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("ORACLELOGSTATUS_GETBYGROUPID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
                    
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("ORACLEFASTCOPYCur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            datalag.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                            datalag.LogFileName = Convert.IsDBNull(myDataLagReader["LogFileName"]) ? string.Empty : Convert.ToString(myDataLagReader["LogFileName"]);
                            datalag.PRSequenceNo = Convert.IsDBNull(myDataLagReader["PRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["PRSequenceNo"]);
                            datalag.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                            datalag.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                            datalag.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                            datalag.PRLogDate = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                            datalag.DRLogDate = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                            datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                            datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                            datalag.BuinsessFunctionId = Convert.IsDBNull(myDataLagReader["BuinsessFunctionId"]) ? 0 : Convert.ToInt32(myDataLagReader["BuinsessFunctionId"]);
                            datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
           

                            //datalag.Id = Convert.ToInt32(myDataLagReader[0]);
                            //datalag.LogFileName = myDataLagReader[1].ToString();
                            //datalag.PRSequenceNo = myDataLagReader[2].ToString();
                            //datalag.DRSequenceNo = myDataLagReader[3].ToString();
                            //datalag.PRTransId = myDataLagReader[4].ToString();
                            //datalag.DRTransId = myDataLagReader[5].ToString();
                            //datalag.PRLogDate = myDataLagReader[6].ToString();
                            //datalag.DRLogDate = myDataLagReader[7].ToString();
                            //datalag.CurrentDataLag = myDataLagReader[8].ToString();
                            //datalag.Health = Convert.ToInt32(myDataLagReader[9]);
                            //datalag.ApplicationGroupId = Convert.ToInt32(myDataLagReader[10]);
                            //datalag.GroupId = Convert.ToInt32(myDataLagReader[11]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Datalag information By InfraObject Id - " + id, exc);
            }

            return datalag;
        }
    }
}
