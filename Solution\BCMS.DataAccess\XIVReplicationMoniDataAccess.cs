﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class XIVReplicationMoniDataAccess : BaseDataAccess
    {
        public static bool AddReplicationMoniLogDetails(XIVReplicationMoniLogs XIVReplicationMoniLogs)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("XIVMONITORLOGS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectID", DbType.Int32, XIVReplicationMoniLogs.InfraobjectID);
                    Database.AddInParameter(cmd, Dbstring + "iPRStorageName", DbType.AnsiString, XIVReplicationMoniLogs.PRStorageName);
                    Database.AddInParameter(cmd, Dbstring + "iDRStorageName", DbType.AnsiString, XIVReplicationMoniLogs.DRStorageName);
                    Database.AddInParameter(cmd, Dbstring + "iPRStorageID", DbType.AnsiString, XIVReplicationMoniLogs.PRStorageID);
                    Database.AddInParameter(cmd, Dbstring + "iDRStorageID", DbType.AnsiString, XIVReplicationMoniLogs.DRStorageID);
                    Database.AddInParameter(cmd, Dbstring + "iPRCGName", DbType.AnsiString, XIVReplicationMoniLogs.PRCGName);
                    Database.AddInParameter(cmd, Dbstring + "iDRCGName", DbType.AnsiString, XIVReplicationMoniLogs.DRCGName);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationMode", DbType.AnsiString, XIVReplicationMoniLogs.ReplicationMode);
                    Database.AddInParameter(cmd, Dbstring + "iRepliJobCreateTime", DbType.AnsiString, XIVReplicationMoniLogs.RepliJobCreateTime);
                    Database.AddInParameter(cmd, Dbstring + "iRepliJobFinishTime", DbType.AnsiString, XIVReplicationMoniLogs.RepliJobFinishTime);
                    Database.AddInParameter(cmd, Dbstring + "iPRStorageStatus", DbType.AnsiString, XIVReplicationMoniLogs.PRStorageStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRStorageStatus", DbType.AnsiString, XIVReplicationMoniLogs.DRStorageStatus);

                    Database.AddInParameter(cmd, Dbstring + "iPRRole", DbType.AnsiString, XIVReplicationMoniLogs.PRROle);
                    Database.AddInParameter(cmd, Dbstring + "iDRRole", DbType.AnsiString, XIVReplicationMoniLogs.DRRole);
                    Database.AddInParameter(cmd, Dbstring + "iCGID", DbType.Int32, XIVReplicationMoniLogs.iCGID);




                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Replication Detailed Monitor Logs InfraObject Id - " + XIVReplicationMoniLogs.InfraobjectID, exc);

            }

            return false;
        }

        public static XIVReplication GetByReplicationId(int id)
        {
            var XIVReplicationDetails = new XIVReplication();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("XIVMIRROR_GETBYREPLID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            //HyperVDetailsRepplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            //HyperVDetailsRepplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            //HyperVDetailsRepplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            XIVReplicationDetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            XIVReplicationDetails.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);

                            XIVReplicationDetails.PRXCLIServerId = Convert.IsDBNull(reader["PRXCLIServerId"]) ? 0 : Convert.ToInt32(reader["PRXCLIServerId"]);
                            XIVReplicationDetails.DRXCLIServerId = Convert.IsDBNull(reader["DRXCLIServerId"]) ? 0 : Convert.ToInt32(reader["DRXCLIServerId"]);
                            XIVReplicationDetails.PRHMCServerId = Convert.IsDBNull(reader["PRHMCServerId"]) ? 0 : Convert.ToInt32(reader["PRHMCServerId"]);
                            XIVReplicationDetails.DRHMCServerId = Convert.IsDBNull(reader["DRHMCServerId"]) ? 0 : Convert.ToInt32(reader["DRHMCServerId"]);
                            XIVReplicationDetails.ReplicationMode = Convert.IsDBNull(reader["ReplicationMode"]) ? string.Empty : Convert.ToString(reader["ReplicationMode"]);
                            XIVReplicationDetails.PRStorageName = Convert.IsDBNull(reader["PRStorageName"]) ? string.Empty : Convert.ToString(reader["PRStorageName"]);
                            XIVReplicationDetails.DRStorageName = Convert.IsDBNull(reader["DRStorageName"]) ? string.Empty : Convert.ToString(reader["DRStorageName"]);
                            XIVReplicationDetails.PRStorageImageID = Convert.IsDBNull(reader["PRStorageImageID"]) ? string.Empty : Convert.ToString(reader["PRStorageImageID"]);
                            XIVReplicationDetails.DRStorageImageID = Convert.IsDBNull(reader["DRStorageImageID"]) ? string.Empty : Convert.ToString(reader["DRStorageImageID"]);
                            XIVReplicationDetails.PRXCLIPath = Convert.IsDBNull(reader["PRXCLIPath"]) ? string.Empty : Convert.ToString(reader["PRXCLIPath"]);
                            XIVReplicationDetails.DRXCLIPath = Convert.IsDBNull(reader["DRXCLIPath"]) ? string.Empty : Convert.ToString(reader["DRXCLIPath"]);

                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get XIV  by  Replication Id - " + id, exc);
            }


            return XIVReplicationDetails;

        }



        public static XIVInfraobjectCG GetInfraobjectCGbyInfraobjectID(int id)
        {
            var XIVInfraobjectCGDetails = new XIVInfraobjectCG();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("xivinfra_cg_GetByInfraobject"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            //HyperVDetailsRepplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            //HyperVDetailsRepplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            //HyperVDetailsRepplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            XIVInfraobjectCGDetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            XIVInfraobjectCGDetails.CGId = Convert.IsDBNull(reader["CGId"]) ? 0
                                : Convert.ToInt32(reader["CGId"]);

                            XIVInfraobjectCGDetails.InfraobjectID = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get XIV  by  Replication Id - " + id, exc);
            }


            return XIVInfraobjectCGDetails;

        }




        public static XIVCG GetCGNamebyID(int id)
        {
            var XIVInfraobjectCGDetails = new XIVCG();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("xiv_cg_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            //HyperVDetailsRepplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            //HyperVDetailsRepplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            //HyperVDetailsRepplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                            XIVInfraobjectCGDetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            XIVInfraobjectCGDetails.PRName = Convert.IsDBNull(reader["PRName"]) ? string.Empty : Convert.ToString(reader["PRName"]);
                            XIVInfraobjectCGDetails.DRName = Convert.IsDBNull(reader["DRName"]) ? string.Empty : Convert.ToString(reader["DRName"]);

                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get XIV  by  Replication Id - " + id, exc);
            }


            return XIVInfraobjectCGDetails;
        }


        public static IList<XIVVolume> GetALLVolumNamebyCGID(int id)
        {

            IList<XIVVolume> XIVInfraobjectCGDetails = new List<XIVVolume>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("xiv_volume_GetByCGID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iCGId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var host = new XIVVolume();

                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.CGID = Convert.ToInt32(myHostReader["CGID"]);
                            host.PRName = Convert.ToString(myHostReader["PRVolumeName"]);
                            host.DRName = Convert.ToString(myHostReader["DRVolumeName"]);                         

                            XIVInfraobjectCGDetails.Add(host);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get Infraobject Volume Name ", exc);
            }

            return XIVInfraobjectCGDetails;


        }


        public static IList<XIVCG> GelAllCGbyInfraobject(int id)
        {
            IList<XIVCG> Infraobject_Scheduler = new List<XIVCG>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("xiv_cg_GetByInfraObject"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var host = new XIVCG();

                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.XIVId = Convert.ToInt32(myHostReader["XIVId"]);
                            host.PRName = Convert.ToString(myHostReader["PRCGName"]);
                            host.DRName = Convert.ToString(myHostReader["DRCGName"]);

                            Infraobject_Scheduler.Add(host);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get Infraobject CG Name ", exc);
            }

            return Infraobject_Scheduler;
        }
    }
}
