﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GlobalMirror", Namespace = "http://www.BCMS.com/types")]
    public class GlobalMirror : BaseEntity
    {
      

        IList<GlobalMirrorLuns> _globalMirrorLuns= new List<GlobalMirrorLuns>();

        #region Properties
        //New Added
        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }
        [DataMember]
        public int DSCLIServerId
        {
            get;
            set;
        }
        [DataMember]
        public int HMCPRServerId
        {
            get;
            set;
        }
        [DataMember]
        public int HMCDRServerId
        {
            get;
            set;
        }
        [DataMember]
        public string PRLSSID
        {
            get;
            set;
        }
        [DataMember]
        public string LSSIDRange
        {
            get;
            set;
        }

        [DataMember]
        public string SessionId
        {
            get;
            set;
        }

        [DataMember]
        public string PRStorageImageId
        {
            get;
            set;
        }
        [DataMember]
        public string DRStorageImageId
        {
            get;
            set;
        }
        [DataMember]
        public string PRWWN
        {
            get;
            set;
        }
        [DataMember]
        public string DRWWN
        {
            get;
            set;
        }
        [DataMember]
        public bool IsManual
        {
            get;
            set;
        }                
        [DataMember]
        public string DRLSSID
        {
            get;
            set;
        }      
        [DataMember]
        public string GroupName
        {
            get;
            set;
        }

       

        [DataMember]
        public IList<GlobalMirrorLuns> GlobalMirrorLuns
        {
            get
            {
                return _globalMirrorLuns;
            }
            set
            {
                _globalMirrorLuns = value;
            }
        }
        #endregion

        #region Constructor

        public GlobalMirror()
            : base()
        {
        }

        #endregion
    }
}
