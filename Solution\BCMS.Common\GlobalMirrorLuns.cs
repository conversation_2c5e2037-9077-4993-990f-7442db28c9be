﻿using System;
using System.Runtime.Serialization;

using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GlobalMirrorLuns", Namespace = "http://www.BCMS.com/types")]
    public class GlobalMirrorLuns : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GlobalMirrorId
        {
            get;
            set;
        }

        [DataMember]
        public string AVolume
        {
            get;
            set;
        }

        [DataMember]
        public string BVolume
        {
            get;
            set;
        }

        [DataMember]
        public string CVolume
        {
            get;
            set;
        }

        [DataMember]
        public string DVolume
        {
            get;
            set;
        }

        [DataMember]
        public string EVolume
        {
            get;
            set;
        }

        [DataMember]
        public string FVolume
        {
            get;
            set;
        }

        #endregion Properties
    }
}