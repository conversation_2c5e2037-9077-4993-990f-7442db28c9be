﻿using System;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common;
namespace BCMS.Common
{
    public class ExchangeDAGReplHealthStatus : BaseEntity
    {
        public ExchangeDAGReplHealthStatus()
            : base()
        {

        }

        #region Properties

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }


        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public int DatabaseId
        {
            get;
            set;
        }


        [DataMember]
        public string ClusterService_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterService_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterService_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterService_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ReplayService_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ReplayService_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ReplayService_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ReplayService_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
         public string ActiveManager_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ActiveManager_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ActiveManager_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ActiveManager_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string TasksRPCListener_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string TasksRPCListener_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string TasksRPCListener_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string TasksRPCListener_Dr_Error
        {
            get;
            set;
        }
        
        [DataMember]
        public string TCPListener_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string TCPListener_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string TCPListener_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string TCPListener_Dr_Error
        {
            get;
            set;
        }


        [DataMember]
        public string ServerLocatorService_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ServerLocatorService_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ServerLocatorService_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ServerLocatorService_Dr_Error
        {
            get;
            set;
        }


        [DataMember]
        public string DAGMembersUp_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DAGMembersUp_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DAGMembersUp_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DAGMembersUp_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterNetwork_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterNetwork_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterNetwork_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterNetwork_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string QuorumGroup_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string QuorumGroup_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string QuorumGroup_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string QuorumGroup_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DbRedundancyCheck_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DbRedundancyCheck_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DbRedundancyCheck_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DbRedundancyCheck_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DbAvailabilityCheck_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DbAvailabilityCheck_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DbAvailabilityCheck_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DbAvailabilityCheck_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopySuspended_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopySuspended_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopySuspended_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopySuspended_Dr_Error
        {
            get;
            set;
        }
     
        [DataMember]
        public string DBCopyFailed_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopyFailed_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopyFailed_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBCopyFailed_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBInitializing_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBInitializing_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBInitializing_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBInitializing_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBDisconnected_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBDisconnected_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBDisconnected_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBDisconnected_Dr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBLogCopyKeepingUp_Pr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBLogCopyKeepingUp_Pr_Error
        {
            get;
            set;
        }

        [DataMember]
        public string DBLogCopyKeepingUp_Dr_Result
        {
            get;
            set;
        }

        [DataMember]
        public string DBLogCopyKeepingUp_Dr_Error
        {
            get;
            set;
        }

        #endregion Properties

    }
}
