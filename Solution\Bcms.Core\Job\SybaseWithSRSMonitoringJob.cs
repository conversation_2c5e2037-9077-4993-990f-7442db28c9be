﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Core;
using Bcms.Core.Client;
using log4net;
using BCMS.Common;


namespace Bcms.Core.Job
{
    public class SybaseWithSRSMonitoringJob : IJob
    {
        public const string InfraObject = "InfraObject";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(SybaseWithSRSMonitoringJob));

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var infraObject = data.Get(InfraObject) as InfraObject;
            if (infraObject != null)
            {
                InfraObject InfraObjectItem = InfraObjectDataAccess.GetInfraObjectById(infraObject.Id);

                if (InfraObjectItem != null)
                {
                    if (InfraObjectItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : Sybase With SRS, InfraObject is not in active mode, skip the monitor components job cycle ", InfraObjectItem.Name);

                        return;
                    }
                    MonitorSybaseWithSRS(InfraObjectItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor Server Job ");
            }
        }

        public void MonitorSybaseWithSRS(InfraObject infraObject)
        {
            try
            {
                using (var client = new SybaseWithSRSClient(infraObject))
                {
                    Logger.Info("SybaseWithSRS InfraObject Name: " + infraObject.Name.ToString() +
                                   " Started Time " + DateTime.Now.ToString());
                    client.SybaseWithSRSMonitoring();

                    Logger.Info("MonitorSybaseWithSRSComponent Completed");
                    Logger.Info("SybaseWithSRSClient InfraObject Name: " + infraObject.Name.ToString() +
                                " Completed Time " + DateTime.Now.ToString());
                }
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception: MonitorSybaseWithSRSMirroringComponent " + infraObject.Name.ToString() + exc.Message);
                ExceptionManager.Manage(exc, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
            catch (Exception exc)
            {
                Logger.Info("Exception: MonitorSybaseWithSRSComponent " + infraObject.Name.ToString() + exc.Message);
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MonitorComponent.ToString(), infraObject.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
        }
    }
}
