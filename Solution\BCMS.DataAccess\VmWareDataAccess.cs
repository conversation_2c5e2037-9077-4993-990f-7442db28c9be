﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class VmWareDataAccess : BaseDataAccess
    {
        public static VmWare GetVmWareByGroupId(int groupid)
        {
            var _objVmware = new VmWare();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("VmWare_GetByGroupId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"igroupId", DbType.Int32, groupid);
                    using (IDataReader myDatabaseReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            _objVmware.ESXIHostName = myDatabaseReader[2].ToString();
                            _objVmware.VirtualMachine = myDatabaseReader[3].ToString();
                            _objVmware.PowerStatePR = Convert.ToInt32(myDatabaseReader[4]);
                            _objVmware.CurrentSnapshotPR = myDatabaseReader[5].ToString();
                            _objVmware.UpdationTimeStampPR = myDatabaseReader[6].ToString();
                            _objVmware.PowerStateDR = Convert.ToInt32(myDatabaseReader[7]);
                            _objVmware.CurrentSnapshotDR = myDatabaseReader[8].ToString();
                            _objVmware.UpdationTimeStampDR = myDatabaseReader[9].ToString();
                            _objVmware.CurrentDataLag = myDatabaseReader[10].ToString();

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get VmWare Database information By id - " + groupid, exc);
            }
            return _objVmware;
        }

        public static bool AddVmWare(VmWare vmWare)
        {
           
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VmWareMonitorLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, vmWare.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStatePR", DbType.Int32, vmWare.PowerStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentSnapshotPR", DbType.AnsiString, vmWare.CurrentSnapshotPR);
                    Database.AddInParameter(cmd, Dbstring + "iUpdationTimeStampPR", DbType.AnsiString, vmWare.UpdationTimeStampPR);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStateDR", DbType.Int32, vmWare.PowerStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentSnapshotDR", DbType.AnsiString, vmWare.CurrentSnapshotDR);
                    Database.AddInParameter(cmd, Dbstring + "iUpdationTimeStampDR", DbType.AnsiString, vmWare.UpdationTimeStampDR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentDataLag", DbType.AnsiString, vmWare.CurrentDataLag);

                    Database.AddInParameter(cmd, Dbstring + "iDataStoreNamePR", DbType.AnsiString, vmWare.DataStoreNamePR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMConfiguredPR", DbType.Int32, vmWare.NoOfVMConfiguredPR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOnStatePR", DbType.Int32, vmWare.NoOfVMPowerOnStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOffStatePR", DbType.Int32, vmWare.NoOfVMPowerOffStatePR);

                    Database.AddInParameter(cmd, Dbstring + "iDataStoreNameDR", DbType.AnsiString, vmWare.DataStoreNameDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMConfiguredDR", DbType.Int32, vmWare.NoOfVMConfiguredDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOnStateDR", DbType.Int32, vmWare.NoOfVMPowerOnStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOffStateDR", DbType.Int32, vmWare.NoOfVMPowerOffStateDR);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add VmWare information", exc);
            }

        }

        public static bool AddVmWareStatus(VmWare vmWare)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VmWareMonitorStatus_Create"))
                {
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, vmWare.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStatePR", DbType.Int32, vmWare.PowerStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentSnapshotPR", DbType.AnsiString, vmWare.CurrentSnapshotPR);
                    Database.AddInParameter(cmd, Dbstring + "iUpdationTimeStampPR", DbType.AnsiString, vmWare.UpdationTimeStampPR);
                    Database.AddInParameter(cmd, Dbstring + "iPowerStateDR", DbType.Int32, vmWare.PowerStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentSnapshotDR", DbType.AnsiString, vmWare.CurrentSnapshotDR);
                    Database.AddInParameter(cmd, Dbstring + "iUpdationTimeStampDR", DbType.AnsiString, vmWare.UpdationTimeStampDR);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentDataLag", DbType.AnsiString, vmWare.CurrentDataLag);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreNamePR", DbType.AnsiString, vmWare.DataStoreNamePR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMConfiguredPR", DbType.Int32, vmWare.NoOfVMConfiguredPR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOnStatePR", DbType.Int32, vmWare.NoOfVMPowerOnStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOffStatePR", DbType.Int32, vmWare.NoOfVMPowerOffStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreNameDR", DbType.AnsiString, vmWare.DataStoreNameDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMConfiguredDR", DbType.Int32, vmWare.NoOfVMConfiguredDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOnStateDR", DbType.Int32, vmWare.NoOfVMPowerOnStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iNoOfVMPowerOffStateDR", DbType.Int32, vmWare.NoOfVMPowerOffStateDR);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add VmWare information", exc);
            }

        }

        public static IList<VMWarePathDetails> GetByReplicationId(int Id)
        {
            try
            {
                if (Id < 1)
                {
                    throw new ArgumentNullException("Id");
                }

                IList<VMWarePathDetails> _objVmwareDetails = new List<VMWarePathDetails>();

                using (DbCommand cmd = Database.GetStoredProcCommand("VMWarePathDetails_GetByRepId"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, Id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        
                        VMWarePathDetails _objVmware = new VMWarePathDetails();

                        if (reader.Read())
                        {
                            _objVmware.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
                            _objVmware.PRVmFilePath = Convert.IsDBNull(reader["PRVmFilePath"]) ? string.Empty : Convert.ToString(reader["PRVmFilePath"]);
                            _objVmware.DRVmFilePath = Convert.IsDBNull(reader["DRVmFilePath"]) ? string.Empty : Convert.ToString(reader["DRVmFilePath"]);
                            _objVmware.DRVmFilePath = Convert.IsDBNull(reader["DRVmFilePath"]) ? string.Empty : Convert.ToString(reader["DRVmFilePath"]);
                            _objVmware.VmwareName = Convert.IsDBNull(reader["VmwareName"]) ? string.Empty : Convert.ToString(reader["VmwareName"]);
                            _objVmware.PRDataStoreName = Convert.IsDBNull(reader["PRDatastoreName"]) ? string.Empty : Convert.ToString(reader["PRDatastoreName"]);
                            _objVmware.DRDataStoreName = Convert.IsDBNull(reader["DRDatastoreName"]) ? string.Empty : Convert.ToString(reader["DRDatastoreName"]);
                            _objVmware.IsActive = Convert.IsDBNull(reader["IsActive"]) ? false : Convert.ToBoolean(reader["IsActive"]);
                            _objVmware.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            _objVmware.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            _objVmware.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            _objVmware.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
                        }
                        _objVmwareDetails.Add(_objVmware);
                    }

                    return _objVmwareDetails;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while GetByReplicationId", exc);
            }
        }

        public static bool AddVmWareImageDetails(VMImageDetails VMImageDetails)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VmWareDiscoveryLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, VMImageDetails.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, VMImageDetails.ServerID);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreName", DbType.AnsiString, VMImageDetails.DataStoreName);
                    Database.AddInParameter(cmd, Dbstring + "iVmPath", DbType.AnsiString, VMImageDetails.VmFilePath);
                    Database.AddInParameter(cmd, Dbstring + "iVmId", DbType.Int32, VMImageDetails.VMId);
                    Database.AddInParameter(cmd, Dbstring + "iVMStatus", DbType.AnsiString, VMImageDetails.VmStatus);
                    Database.AddInParameter(cmd, Dbstring + "iVmOSType", DbType.AnsiString, VMImageDetails.VmOsType);
                    Database.AddInParameter(cmd, Dbstring + "iVMName", DbType.AnsiString, VMImageDetails.VmName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add VmWare Image Logs information", exc);
            }

        }

        public static bool AddVmWareImageStatusDetails(VMImageDetails VMImageDetails)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("VmWareDiscoverStatus_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, VMImageDetails.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, VMImageDetails.ServerID);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreName", DbType.AnsiString, VMImageDetails.DataStoreName);
                    Database.AddInParameter(cmd, Dbstring + "iVmPath", DbType.AnsiString, VMImageDetails.VmFilePath);
                    Database.AddInParameter(cmd, Dbstring + "iVmId", DbType.Int32, VMImageDetails.VMId);
                    Database.AddInParameter(cmd, Dbstring + "iVMStatus", DbType.AnsiString, VMImageDetails.VmStatus);
                    Database.AddInParameter(cmd, Dbstring + "iVmOSType", DbType.AnsiString, VMImageDetails.VmOsType);
                    Database.AddInParameter(cmd, Dbstring + "iVMName", DbType.AnsiString, VMImageDetails.VmName);

//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add VmWare Image Status information", exc);
            }

        }
    }
}
