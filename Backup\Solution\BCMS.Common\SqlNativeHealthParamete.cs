﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlnativeHealth", Namespace = "http://www.BCMS.com/types")]
    public class SqlNativeHealthParamete : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string Database_UpdateabilityPR { get; set; }

        [DataMember]
        public string Database_UpdateabilityDR { get; set; }

        [DataMember]
        public string MSSQLServer_EditionPR { get; set; }

        [DataMember]
        public string MSSQLServer_EditionDR { get; set; }

        [DataMember]
        public string MSSQL_Server_ReleasePR { get; set; }

        [DataMember]
        public string MSSQL_Server_ReleaseDR { get; set; }

        [DataMember]
        public string DatabaseSizePR { get; set; }

        [DataMember]
        public string DatabaseSizeDR { get; set; }

        #endregion
    }
}
