﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "BPAutomation", Namespace = "http://www.BCMS.com/types")]
     public class BPAutomation : BaseEntity
    {
        #region Properties

        [DataMember]
        public string GroupName
        {
            get;
            set;
        }

        [DataMember]
        public string BPComponent
        {
            get;
            set;
        }

        [DataMember]
        public int PRHostName
        {
            get;
            set;
        }

        [DataMember]
        public string Sourcedir
        {
            get;
            set;
        }

        [DataMember]
        public string Destinationdir
        {
            get;
            set;
        }

        [DataMember]
        public int DRHostname
        {
            get;
            set;
        }

        [DataMember]
        public string ScriptPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRIP
        {
            get;
            set;
        }

        [DataMember]
        public string DRIP
        {
            get;
            set;
        }

        #endregion

        #region Constructor
        public BPAutomation()
            : base()
        {
        }

        #endregion
    }
}
