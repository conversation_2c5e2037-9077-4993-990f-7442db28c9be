﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class MSSQLMirroringDataAccess : BaseDataAccess
    {
        public static bool AddMSSQLMonitorStatus(MSqlMirroring _MSqlMirroring,int infraobject)
        {
            const string sp = "DatabaseMirrormontrStat_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraobject);
                Database.AddInParameter(cmd, Dbstring + "iPRDatabaseName", DbType.AnsiString, _MSqlMirroring.PRDatabaseName);
                Database.AddInParameter(cmd, Dbstring + "iDRDatabaseName", DbType.AnsiString, _MSqlMirroring.DRDatabaseName);
                Database.AddInParameter(cmd, Dbstring + "iPRRoleofDB", DbType.AnsiString, _MSqlMirroring.PRDBRole);
                Database.AddInParameter(cmd, Dbstring + "iDRRoleofDB", DbType.AnsiString, _MSqlMirroring.DRDBRole);
                Database.AddInParameter(cmd, Dbstring + "iPRMirroringState", DbType.AnsiString, _MSqlMirroring.PRMirroringState);
                Database.AddInParameter(cmd, Dbstring + "iDRMirroringState", DbType.AnsiString, _MSqlMirroring.DRMirroringState);
                Database.AddInParameter(cmd, Dbstring + "iPRLogGenerateRate", DbType.AnsiString, _MSqlMirroring.PRLogGenerateRate);
                Database.AddInParameter(cmd, Dbstring + "iDRLogGenerateRate", DbType.AnsiString, _MSqlMirroring.DRLogGenerateRate);
                Database.AddInParameter(cmd, Dbstring + "iPRUnsentLog", DbType.AnsiString, _MSqlMirroring.PRUnsentLog);
                Database.AddInParameter(cmd, Dbstring + "iDRUnsentLog", DbType.AnsiString, _MSqlMirroring.DRUnsentLog);
                Database.AddInParameter(cmd, Dbstring + "iPRSentRate", DbType.AnsiString, _MSqlMirroring.PRSentRate);
                Database.AddInParameter(cmd, Dbstring + "iDRSentRate", DbType.AnsiString, _MSqlMirroring.DRSentRate);
                Database.AddInParameter(cmd, Dbstring + "iPRUnrestoredLog", DbType.AnsiString, _MSqlMirroring.PRUnrestoredLog);
                Database.AddInParameter(cmd, Dbstring + "iDRUnrestoredLog", DbType.AnsiString, _MSqlMirroring.DRUnrestoredLog);
                Database.AddInParameter(cmd, Dbstring + "iPRRecoveryRate", DbType.AnsiString, _MSqlMirroring.PRRecoveryRate);
                Database.AddInParameter(cmd, Dbstring + "iDRRecoveryRate", DbType.AnsiString, _MSqlMirroring.DRRecoveryRate);
                Database.AddInParameter(cmd, Dbstring + "iPRTransactionDelay", DbType.AnsiString, _MSqlMirroring.PRTransactionDelay);
                Database.AddInParameter(cmd, Dbstring + "iDRTransactionDelay", DbType.AnsiString, _MSqlMirroring.DRTransactionDelay);
                Database.AddInParameter(cmd, Dbstring + "iPRTransactionPerSecond", DbType.AnsiString, _MSqlMirroring.PRTransactionPerSecond);
                Database.AddInParameter(cmd, Dbstring + "iDRTransactionPerSecond", DbType.AnsiString, _MSqlMirroring.DRTransactionPerSecond);
                Database.AddInParameter(cmd, Dbstring + "iPRAverageDelay", DbType.AnsiString, _MSqlMirroring.PRAverageDelay);
                Database.AddInParameter(cmd, Dbstring + "iDRAverageDelay", DbType.AnsiString, _MSqlMirroring.DRAverageDelay);
                Database.AddInParameter(cmd, Dbstring + "iPRTimeRecorded", DbType.DateTime, _MSqlMirroring.PRTimeRecorded);
                Database.AddInParameter(cmd, Dbstring + "iDRTimeRecorded", DbType.DateTime, _MSqlMirroring.DRTimeRecorded);
                Database.AddInParameter(cmd, Dbstring + "iPRTimeBehind", DbType.DateTime, _MSqlMirroring.PRTimeBehind);
                Database.AddInParameter(cmd, Dbstring + "iDRTimeBehind", DbType.DateTime, _MSqlMirroring.DRTimeBehind);
                Database.AddInParameter(cmd, Dbstring + "iPRLocalTime", DbType.DateTime, _MSqlMirroring.PRLocalTime);
                Database.AddInParameter(cmd, Dbstring + "iDRLocalTime", DbType.DateTime, _MSqlMirroring.DRLocalTime);
                Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, 1);

                int returnCode = Database.ExecuteNonQuery(cmd);
                return returnCode <= 0;
            }
        }
    }
}
