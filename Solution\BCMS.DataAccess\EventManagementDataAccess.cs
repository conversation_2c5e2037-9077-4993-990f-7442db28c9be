﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class EventManagementDataAccess : BaseDataAccess
    {

        public static bool EventManagement_Create(int InfraobjectId,int EventId,string Description,int Trigger,int CreatorId)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Event_Create"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, InfraobjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iEventId", DbType.Int32, EventId);
                    Database.AddInParameter(dbCommand, Dbstring+"iDescription", DbType.AnsiString, Description);
                    Database.AddInParameter(dbCommand, Dbstring+"iTrigger", DbType.Int32, Trigger);
                    Database.AddInParameter(dbCommand, Dbstring+"iCreatorId", DbType.Int32, CreatorId);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while EventManagement Create ", exc);
            }

        }
        public static Event GetEventId(int id)
        {
            var events = new Event();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Event_Management_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            events.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                            events.EventId = Convert.IsDBNull(myDataLagReader["EventId"]) ? 0 : Convert.ToInt32(myDataLagReader["EventId"]);
                            events.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
                            events.Events = Convert.IsDBNull(myDataLagReader["Event"]) ? string.Empty : Convert.ToString(myDataLagReader["Event"]);
                            events.WorkFlowId = Convert.IsDBNull(myDataLagReader["WorkFlowId"]) ? 0 : Convert.ToInt32(myDataLagReader["WorkFlowId"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Datalag information By EventId - " + id, exc);
            }
            return events;
        }
    }
}
