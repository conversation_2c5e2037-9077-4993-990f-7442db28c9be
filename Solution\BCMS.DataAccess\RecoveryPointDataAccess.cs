﻿using System;
using System.Collections.Generic;
using System.Data;
using Bcms.Common.Shared;
using Bcms.Common;
using Bcms.Common.Base;
using System.Data.Common;
using Bcms.ExceptionHandler;
using Bcms.Helper;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
   public class RecoveryPointDataAccess : BaseDataAccess
    {
      


        #region Methods
      

        public RecoveryPoint GetByReplicationId(int id)
        {
            try
            {
                const string sp = "recoveryPoint_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        RecoveryPoint objrecoveryPoint = new RecoveryPoint();
                        if (reader.Read())
                        {
                            objrecoveryPoint.ReplicationId = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objrecoveryPoint.DGroupName = reader["GroupName"] != null ? Convert.ToString(reader["GroupName"]) : string.Empty;
                            objrecoveryPoint.PRClusterNodeName = reader["PRClusterNodeName"] != null ? Convert.ToString(reader["PRClusterNodeName"]) : string.Empty;
                            objrecoveryPoint.DRClusterNodeName = reader["DRClusterNodeName"] != null ? Convert.ToString(reader["DRClusterNodeName"]) : string.Empty;
                        }
                        return objrecoveryPoint;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,                  
                    "Error In DAL While Executing Function Signature iRecoveryPoint.GetByReplicationId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        public IList<RecoveryPoint> GetAll(string replicationtype)
        {
            IList<RecoveryPoint> lstRecoveryPoint = new List<RecoveryPoint>();

            try
            {
                const string sp = "recoveryPoint_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.String, replicationtype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            RecoveryPoint objrecoveryPoint = new RecoveryPoint();

                            objrecoveryPoint.ReplicationId = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objrecoveryPoint.DGroupName = reader["GroupName"] != null ? Convert.ToString(reader["GroupName"]) : string.Empty;
                            objrecoveryPoint.PRClusterNodeName = reader["PRClusterNodeName"] != null ? Convert.ToString(reader["PRClusterNodeName"]) : string.Empty;
                            objrecoveryPoint.DRClusterNodeName = reader["DRClusterNodeName"] != null ? Convert.ToString(reader["DRClusterNodeName"]) : string.Empty;
                             


                            lstRecoveryPoint.Add(objrecoveryPoint);
                        }
                    }
                }
                
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,                  
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return lstRecoveryPoint;
        }

        #endregion
    }
}
