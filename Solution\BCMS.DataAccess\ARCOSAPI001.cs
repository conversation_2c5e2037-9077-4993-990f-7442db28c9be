﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by wsdl, Version=4.0.30319.17929.
// 
namespace Bcms.DataAccess.ARCOSAPIOnline
{
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Configuration;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="ARCOSAPI001Soap", Namespace="http://ARCOS")]
    public partial class ARCOSAPI001 : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback OpenARCOSGatewayOperationCompleted;
        
        private System.Threading.SendOrPostCallback RequestServicePasswordOperationCompleted;
        
        private System.Threading.SendOrPostCallback CloseARCOSGatewayOperationCompleted;
        
        private System.Threading.SendOrPostCallback ActiveSessionCountOperationCompleted;
        
        /// <remarks/>
        public ARCOSAPI001() {            
            this.Url = ConfigurationManager.AppSettings["ARCOSWebURL"].ToString();
        }
        
        /// <remarks/>
        public event OpenARCOSGatewayCompletedEventHandler OpenARCOSGatewayCompleted;
        
        /// <remarks/>
        public event RequestServicePasswordCompletedEventHandler RequestServicePasswordCompleted;
        
        /// <remarks/>
        public event CloseARCOSGatewayCompletedEventHandler CloseARCOSGatewayCompleted;
        
        /// <remarks/>
        public event ActiveSessionCountCompletedEventHandler ActiveSessionCountCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://ARCOS/OpenARCOSGateway", RequestNamespace="http://ARCOS", ResponseNamespace="http://ARCOS", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string OpenARCOSGateway(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, string IsUseCustomPort, string CustomPort) {
            object[] results = this.Invoke("OpenARCOSGateway", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName,
                        IsUseCustomPort,
                        CustomPort});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult BeginOpenARCOSGateway(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, string IsUseCustomPort, string CustomPort, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("OpenARCOSGateway", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName,
                        IsUseCustomPort,
                        CustomPort}, callback, asyncState);
        }
        
        /// <remarks/>
        public string EndOpenARCOSGateway(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void OpenARCOSGatewayAsync(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, string IsUseCustomPort, string CustomPort) {
            this.OpenARCOSGatewayAsync(ARCOSWebAPIURL, ARCOSSharedKey, LOBProfile, ServerIP, ServiceType, UserName, DBInstanceName, IsUseCustomPort, CustomPort, null);
        }
        
        /// <remarks/>
        public void OpenARCOSGatewayAsync(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, string IsUseCustomPort, string CustomPort, object userState) {
            if ((this.OpenARCOSGatewayOperationCompleted == null)) {
                this.OpenARCOSGatewayOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOpenARCOSGatewayOperationCompleted);
            }
            this.InvokeAsync("OpenARCOSGateway", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName,
                        IsUseCustomPort,
                        CustomPort}, this.OpenARCOSGatewayOperationCompleted, userState);
        }
        
        private void OnOpenARCOSGatewayOperationCompleted(object arg) {
            if ((this.OpenARCOSGatewayCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OpenARCOSGatewayCompleted(this, new OpenARCOSGatewayCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://ARCOS/RequestServicePassword", RequestNamespace="http://ARCOS", ResponseNamespace="http://ARCOS", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string RequestServicePassword(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName) {
            object[] results = this.Invoke("RequestServicePassword", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult BeginRequestServicePassword(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("RequestServicePassword", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName}, callback, asyncState);
        }
        
        /// <remarks/>
        public string EndRequestServicePassword(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void RequestServicePasswordAsync(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName) {
            this.RequestServicePasswordAsync(ARCOSWebAPIURL, ARCOSSharedKey, LOBProfile, ServerIP, ServiceType, UserName, DBInstanceName, null);
        }
        
        /// <remarks/>
        public void RequestServicePasswordAsync(string ARCOSWebAPIURL, string ARCOSSharedKey, string LOBProfile, string ServerIP, string ServiceType, string UserName, string DBInstanceName, object userState) {
            if ((this.RequestServicePasswordOperationCompleted == null)) {
                this.RequestServicePasswordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnRequestServicePasswordOperationCompleted);
            }
            this.InvokeAsync("RequestServicePassword", new object[] {
                        ARCOSWebAPIURL,
                        ARCOSSharedKey,
                        LOBProfile,
                        ServerIP,
                        ServiceType,
                        UserName,
                        DBInstanceName}, this.RequestServicePasswordOperationCompleted, userState);
        }
        
        private void OnRequestServicePasswordOperationCompleted(object arg) {
            if ((this.RequestServicePasswordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.RequestServicePasswordCompleted(this, new RequestServicePasswordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://ARCOS/CloseARCOSGateway", RequestNamespace="http://ARCOS", ResponseNamespace="http://ARCOS", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool CloseARCOSGateway(string pSessionID) {
            object[] results = this.Invoke("CloseARCOSGateway", new object[] {
                        pSessionID});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult BeginCloseARCOSGateway(string pSessionID, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("CloseARCOSGateway", new object[] {
                        pSessionID}, callback, asyncState);
        }
        
        /// <remarks/>
        public bool EndCloseARCOSGateway(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void CloseARCOSGatewayAsync(string pSessionID) {
            this.CloseARCOSGatewayAsync(pSessionID, null);
        }
        
        /// <remarks/>
        public void CloseARCOSGatewayAsync(string pSessionID, object userState) {
            if ((this.CloseARCOSGatewayOperationCompleted == null)) {
                this.CloseARCOSGatewayOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCloseARCOSGatewayOperationCompleted);
            }
            this.InvokeAsync("CloseARCOSGateway", new object[] {
                        pSessionID}, this.CloseARCOSGatewayOperationCompleted, userState);
        }
        
        private void OnCloseARCOSGatewayOperationCompleted(object arg) {
            if ((this.CloseARCOSGatewayCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CloseARCOSGatewayCompleted(this, new CloseARCOSGatewayCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://ARCOS/ActiveSessionCount", RequestNamespace="http://ARCOS", ResponseNamespace="http://ARCOS", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string ActiveSessionCount() {
            object[] results = this.Invoke("ActiveSessionCount", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult BeginActiveSessionCount(System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("ActiveSessionCount", new object[0], callback, asyncState);
        }
        
        /// <remarks/>
        public string EndActiveSessionCount(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void ActiveSessionCountAsync() {
            this.ActiveSessionCountAsync(null);
        }
        
        /// <remarks/>
        public void ActiveSessionCountAsync(object userState) {
            if ((this.ActiveSessionCountOperationCompleted == null)) {
                this.ActiveSessionCountOperationCompleted = new System.Threading.SendOrPostCallback(this.OnActiveSessionCountOperationCompleted);
            }
            this.InvokeAsync("ActiveSessionCount", new object[0], this.ActiveSessionCountOperationCompleted, userState);
        }
        
        private void OnActiveSessionCountOperationCompleted(object arg) {
            if ((this.ActiveSessionCountCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ActiveSessionCountCompleted(this, new ActiveSessionCountCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    public delegate void OpenARCOSGatewayCompletedEventHandler(object sender, OpenARCOSGatewayCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OpenARCOSGatewayCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OpenARCOSGatewayCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    public delegate void RequestServicePasswordCompletedEventHandler(object sender, RequestServicePasswordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class RequestServicePasswordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal RequestServicePasswordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    public delegate void CloseARCOSGatewayCompletedEventHandler(object sender, CloseARCOSGatewayCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CloseARCOSGatewayCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CloseARCOSGatewayCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    public delegate void ActiveSessionCountCompletedEventHandler(object sender, ActiveSessionCountCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.17929")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ActiveSessionCountCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ActiveSessionCountCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
}
