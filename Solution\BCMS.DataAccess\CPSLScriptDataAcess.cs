﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;


namespace Bcms.DataAccess
{
    class CPSLScriptDataAcess : BaseDataAccess
    {
        public static readonly ILog Logger = LogManager.GetLogger(typeof(CPSLScriptDataAcess));


        public static IList<cpslScript> GetAllCPSLScheduler(int infraobjectid)
        {
            IList<cpslScript> cpslScript = new List<cpslScript>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("CPSL_GetAll"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraID", DbType.Int32, infraobjectid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var CPSLScript = new cpslScript();

                            CPSLScript.Id = Convert.ToInt32(myHostReader["Id"]);
                            CPSLScript.CPSLName = Convert.ToString(myHostReader["CPSLName"]);
                            CPSLScript.CPSLScript = Convert.ToString(myHostReader["CPSLScript"]);
                            cpslScript.Add(CPSLScript);
                        }
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get CPSL  Script Schedule list", exc);
            }
            return cpslScript;

        }

        public static cpslScript GelCPlSchedulerById(int id)
        {
            cpslScript host = new cpslScript();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("CPSL_scheduler_GetById"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);

                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {

                        while (myHostReader.Read())
                        {


                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.CPSLScript = Convert.ToString(myHostReader["CPSLScript"]);
                            host.CPSLName = Convert.ToString(myHostReader["CPSLName"]);

                        }
                    }
                }

            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Error In DAL While Executing Function Signature CPSLScriptDataAcess.GelCPlSchedulerById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return host;
        }
    }
}
