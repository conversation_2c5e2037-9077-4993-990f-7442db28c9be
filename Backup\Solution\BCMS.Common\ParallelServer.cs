﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ParallelServer", Namespace = "http://www.Bcms.com/types")]
    public class ParallelServer:BaseEntity
    {

         #region Properties

        [DataMember]
        public string IPAddress
        {
            get;
            set;
        }
       
        [DataMember]
        public int Status
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public ParallelServer()
            : base()
        {
        }

        #endregion

         

         
    }
}