﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class DatabasePostgre9X : BaseEntity
    {
         #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string PRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string PRPassword
        {
            get;
            set;
        }

        [DataMember]
        public int PRPort
        {
            get;
            set;
        }

        [DataMember]
        public string PRDBDataDirectory
        { 
            get; 
            set; 
        }

        [DataMember]
        public string PRDBbinDirectory
        { 
            get; 
            set; 
        }

        [DataMember]
        public string PRSULogin
        {
            get;
            set;
        }
        [DataMember]
        public string PRServiceName
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseName
        {
            get;
            set;
        }
        [DataMember]
        public string DRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string DRPassword
        {
            get;
            set;
        }

        [DataMember]
        public int DRPort
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBDataDirectory
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBbinDirectory
        {
            get;
            set;
        }

        [DataMember]
        public string DRSULogin
        {
            get;
            set;
        }

        [DataMember]
        public string DRServiceName
        {
            get;
            set;
        }
        #endregion Properties

         #region Constructor

        public DatabasePostgre9X()
            : base()
        {
        }

        #endregion Constructor

    }
}
