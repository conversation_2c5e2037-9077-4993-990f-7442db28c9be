﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.DataAccess
{
    class CPSL_schedulerDataAcess : BaseDataAccess
    {
        public static readonly ILog Logger = LogManager.GetLogger(typeof(CPSL_schedulerDataAcess));


        public static IList<CPSL_scheduler> GetAllCPSLScheduler()
        {
            IList<CPSL_scheduler>CPSL_scheduler=new List<CPSL_scheduler>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("CPSL_scheduler_All"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var CPSL = new CPSL_scheduler();

                            CPSL.Id = Convert.ToInt32(myHostReader["Id"]);
                            CPSL.InfraobjectId = Convert.ToInt32(myHostReader["InfraobjectId"]);
                            CPSL.CPSLId = Convert.ToInt32(myHostReader["CPSLId"]);
                            CPSL.CPSLType = Convert.ToInt32(myHostReader["CPSLType"]);
                            CPSL.CPSLTime = Convert.ToString(myHostReader["CPSLTime"]);
                            CPSL.IsSchedule = Convert.ToInt32(myHostReader["IsSchedule"]);
                            CPSL_scheduler.Add(CPSL);
                        }
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get CPSL Schedule list", exc);
            }
            return CPSL_scheduler;

        }

         public static CPSL_scheduler GetCPSLSchedulerById(int id)
        {
            CPSL_scheduler CPSL = new CPSL_scheduler();
            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("CPSL_scheduler_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                   
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                           
                            CPSL.Id = Convert.ToInt32(myHostReader["Id"]);
                            CPSL.InfraobjectId = Convert.ToInt32(myHostReader["InfraobjectId"]);
                            CPSL.CPSLId = Convert.ToInt32(myHostReader["CPSLId"]);
                            CPSL.CPSLType = Convert.ToInt32(myHostReader["CPSLType"]);
                            CPSL.CPSLTime = Convert.ToString(myHostReader["CPSLTime"]);
                            CPSL.IsSchedule = Convert.ToInt32(myHostReader["IsSchedule"]);
                          
                        }
                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Error In DAL While Executing Function Signature CPSL_schedulerDataAcess.GetCPSLSchedulerById(" + id + ")" +
                  Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return CPSL;

        }

         public static bool UpdateCPSLScheduler(CPSL_scheduler CPSL)
         {

             try
             {
                 using (DbCommand cmd = Database.GetStoredProcCommand("CPSL_sch_Update_Ser"))
                 {
                     Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, CPSL.Id);                     
                       Database.AddInParameter(cmd, Dbstring + "iIsSchedule", DbType.Int32, CPSL.IsSchedule);

                    
                    
#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                     int returnCode = Database.ExecuteNonQuery(cmd);

                     return returnCode > 0;
                 }
             }
             catch (Exception exc)
             {
                 throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Update CPSL Scheduler Details", exc);

             }
         }
      
    }
}
