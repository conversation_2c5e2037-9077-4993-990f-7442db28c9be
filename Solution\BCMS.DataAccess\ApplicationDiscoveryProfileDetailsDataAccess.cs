﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.Common;
using log4net;

namespace Bcms.DataAccess
{
    public class ApplicationDiscoveryProfileDetailsDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ApplicationDiscoveryDataAccess));

        public static ApplicationDiscoveryProfileDetails AddAppDiscoveryProfileDetails(ApplicationDiscoveryProfileDetails applicationDiscoveryProfileDetails)
        {
            ApplicationDiscoveryProfileDetails returnApplicationDiscoveryProfileDetails = null;

            try
            {
                const string sp = "AppDiscProfileDetails_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDiscoveryProfileName", DbType.AnsiString, applicationDiscoveryProfileDetails.DiscoveryProfileName);
                    Database.AddInParameter(cmd, Dbstring + "iHostFrom", DbType.AnsiString, applicationDiscoveryProfileDetails.HostFrom);
                    Database.AddInParameter(cmd, Dbstring + "iHostTo", DbType.AnsiString, applicationDiscoveryProfileDetails.HostTo);
                    Database.AddInParameter(cmd, Dbstring + "iOsFilter", DbType.AnsiString, applicationDiscoveryProfileDetails.OsFilter);
                    Database.AddInParameter(cmd, Dbstring + "iAppFilter", DbType.AnsiString, applicationDiscoveryProfileDetails.AppFilter);
                    Database.AddInParameter(cmd, Dbstring + "iHostFound", DbType.Int32, applicationDiscoveryProfileDetails.HostFound);
                    Database.AddInParameter(cmd, Dbstring + "iDiscoveryTime", DbType.AnsiString, applicationDiscoveryProfileDetails.DiscoveryTime);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        returnApplicationDiscoveryProfileDetails = new ApplicationDiscoveryProfileDetails();

                        if (reader.Read())
                        {
                            returnApplicationDiscoveryProfileDetails.DiscoveryProfileName = Convert.IsDBNull(reader["DiscoveryProfileName"]) ? string.Empty : Convert.ToString(reader["DiscoveryProfileName"]);

                            returnApplicationDiscoveryProfileDetails.HostFrom = Convert.IsDBNull(reader["HostFrom"]) ? string.Empty : Convert.ToString(reader["HostFrom"]);

                            returnApplicationDiscoveryProfileDetails.HostTo = Convert.IsDBNull(reader["HostTo"]) ? string.Empty : Convert.ToString(reader["HostTo"]);

                            returnApplicationDiscoveryProfileDetails.OsFilter = Convert.IsDBNull(reader["OsFilter"]) ? "All" : Convert.ToString(reader["OsFilter"]);

                            returnApplicationDiscoveryProfileDetails.AppFilter = Convert.IsDBNull(reader["AppFilter"]) ? "All" : Convert.ToString(reader["AppFilter"]);

                            returnApplicationDiscoveryProfileDetails.HostFound = Convert.IsDBNull(reader["HostFound"]) ? 0 : Convert.ToInt32(reader["HostFound"]);

                            returnApplicationDiscoveryProfileDetails.DiscoveryTime = Convert.IsDBNull(reader["DiscoveryTime"]) ? string.Empty : Convert.ToString(reader["DiscoveryTime"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while inserting Application Discovery profile information", exc);
            }
            return returnApplicationDiscoveryProfileDetails;
        }
    }
}
