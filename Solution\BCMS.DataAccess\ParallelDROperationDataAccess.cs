﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ParallelDROperationDataAccess : BaseDataAccess
    {
        // <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static IList<ParallelDROperation> GetByStatus(string status)
        {
            try
            {
                var drOperations = new List<ParallelDROperation>();
                
               

            using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELDROPERAT_GETBYSTATUS"))
            {
                Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, status);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelDROperationReader = Database.ExecuteReader(cmd))
                {
                    while (myParallelDROperationReader.Read())
                    {
                        var drOperation = new ParallelDROperation();

                        drOperation.Id = Convert.IsDBNull(myParallelDROperationReader["Id"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["Id"]);
                        drOperation.StartTime = Convert.IsDBNull(myParallelDROperationReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["StartTime"]);
                        drOperation.EndTime = Convert.IsDBNull(myParallelDROperationReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["EndTime"]);
                        drOperation.Status = Convert.IsDBNull(myParallelDROperationReader["Status"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Status"]);
                        drOperation.Description = Convert.IsDBNull(myParallelDROperationReader["Description"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Description"]);
                        drOperation.ActionMode = Convert.IsDBNull(myParallelDROperationReader["ActionMode"]) ? ActionMode.None : (ActionMode)Enum.Parse(typeof(ActionMode), Convert.ToString(myParallelDROperationReader["ActionMode"]), true);
                        drOperation.CreatorId = Convert.IsDBNull(myParallelDROperationReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["CreatorId"]);


                        drOperations.Add(drOperation);
                    }
                   
                }
            }
            return drOperations;
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert parallel droperation information", exc);
            }

        }

        // <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static bool UpdateByStatus(int id,string status)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("PARALLELDROPERA_UPDATEBYSTATUS"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.AnsiString, status);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel DR Operation Details by state ", exc);
            }
        }

        // <Modified> Jeyapandi - CP 4.0v - 07-06-2014 for OracleDB</Modified>
        public static ParallelDROperation GetByParallelLastId()
        {

            var drOperation = new ParallelDROperation();

            using (DbCommand cmd = Database.GetStoredProcCommand("ParallelDR_GetByLastId"))
            {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelDROperationReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelDROperationReader.Read())
                    {
                        drOperation.Id = Convert.IsDBNull(myParallelDROperationReader["Id"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["Id"]);
                        drOperation.StartTime = Convert.IsDBNull(myParallelDROperationReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["StartTime"]);
                        drOperation.EndTime = Convert.IsDBNull(myParallelDROperationReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["EndTime"]);
                        drOperation.Status = Convert.IsDBNull(myParallelDROperationReader["Status"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Status"]);
                        drOperation.Description = Convert.IsDBNull(myParallelDROperationReader["Description"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Description"]);
                        drOperation.ActionMode = Convert.IsDBNull(myParallelDROperationReader["ActionMode"]) ? ActionMode.None : (ActionMode)Enum.Parse(typeof(ActionMode), Convert.ToString(myParallelDROperationReader["ActionMode"]), true);
                        drOperation.CreatorId = Convert.IsDBNull(myParallelDROperationReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["CreatorId"]);


                        //drOperation.Id = Convert.ToInt32(myParallelDROperationReader[0]);
                        //drOperation.StartTime = Convert.ToDateTime(myParallelDROperationReader[1]);
                        //drOperation.EndTime = Convert.ToDateTime(myParallelDROperationReader[2]);
                        //drOperation.Status = myParallelDROperationReader[3].ToString();
                        //drOperation.UserMessage  = myParallelDROperationReader[4].ToString();
                        //drOperation.ActionMode = (ActionMode)Enum.Parse(typeof(ActionMode), myParallelDROperationReader[5].ToString(), true);// surendra added
                    }
                    else
                    {
                        drOperation = null;
                    }
                }

                return drOperation;
            }

        }
// end method

        public static ParallelDROperation GetByParallelLastId(int Id)
        {

            var drOperation = new ParallelDROperation();

            using (DbCommand cmd = Database.GetStoredProcCommand("ParallelDR_GetByParallelDRId"))
            {

                Database.AddInParameter(cmd, Dbstring + "iId", DbType.AnsiString, Id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelDROperationReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelDROperationReader.Read())
                    {
                        drOperation.Id = Convert.IsDBNull(myParallelDROperationReader["Id"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["Id"]);
                        drOperation.StartTime = Convert.IsDBNull(myParallelDROperationReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["StartTime"]);
                        drOperation.EndTime = Convert.IsDBNull(myParallelDROperationReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelDROperationReader["EndTime"]);
                        drOperation.Status = Convert.IsDBNull(myParallelDROperationReader["Status"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Status"]);
                        drOperation.Description = Convert.IsDBNull(myParallelDROperationReader["Description"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Description"]);
                        drOperation.ActionMode = Convert.IsDBNull(myParallelDROperationReader["ActionMode"]) ? ActionMode.None : (ActionMode)Enum.Parse(typeof(ActionMode), Convert.ToString(myParallelDROperationReader["ActionMode"]), true);
                        drOperation.CreatorId = Convert.IsDBNull(myParallelDROperationReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["CreatorId"]);


                        //drOperation.Id = Convert.ToInt32(myParallelDROperationReader[0]);
                        //drOperation.StartTime = Convert.ToDateTime(myParallelDROperationReader[1]);
                        //drOperation.EndTime = Convert.ToDateTime(myParallelDROperationReader[2]);
                        //drOperation.Status = myParallelDROperationReader[3].ToString();
                        //drOperation.UserMessage  = myParallelDROperationReader[4].ToString();
                        //drOperation.ActionMode = (ActionMode)Enum.Parse(typeof(ActionMode), myParallelDROperationReader[5].ToString(), true);// surendra added
                    }
                    else
                    {
                        drOperation = null;
                    }
                }

                return drOperation;
            }

        }
         
    }
}