﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using CP.Common.DatabaseEntity;
using log4net;

namespace Bcms.DataAccess
{
    public class ScheduleDiscoveryProfDetailsDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ScheduleDiscoveryProfDetailsDataAccess));

        public static IList<ScheduleDiscoveryProfDetails> GetAllScheduledDiscoveryProfileDetailsByIsScheduled()
        {
            IList<ScheduleDiscoveryProfDetails> scheduleDiscoveryProfDetailsList = new List<ScheduleDiscoveryProfDetails>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SCHEDISC_GETPROFDETBYISSCHE"))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var scheduleDiscoveryProfDetails = new ScheduleDiscoveryProfDetails();

                            scheduleDiscoveryProfDetails.ScheDiscProfileName = Convert.IsDBNull(reader["ScheDiscProfileName"]) ? string.Empty : Convert.ToString(reader["ScheDiscProfileName"]);

                            scheduleDiscoveryProfDetails.HostFrom = Convert.IsDBNull(reader["HostFrom"]) ? string.Empty : Convert.ToString(reader["HostFrom"]);

                            scheduleDiscoveryProfDetails.HostTo = Convert.IsDBNull(reader["HostTo"]) ? string.Empty : Convert.ToString(reader["HostTo"]);

                            scheduleDiscoveryProfDetails.OsFilter = Convert.IsDBNull(reader["OsFilter"]) ? "All" : Convert.ToString(reader["OsFilter"]);

                            scheduleDiscoveryProfDetails.AppFilter = Convert.IsDBNull(reader["AppFilter"]) ? "All" : Convert.ToString(reader["AppFilter"]);

                            scheduleDiscoveryProfDetails.IsScheduled = Convert.IsDBNull(reader["IsScheduled"]) ? 0 : Convert.ToInt32(reader["IsScheduled"]);

                            scheduleDiscoveryProfDetailsList.Add(scheduleDiscoveryProfDetails);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Geting all Scheduled Discovery ProfileDetails By IsScheduled", exc);

            }
            return scheduleDiscoveryProfDetailsList;


        }

        public static IList<ScheduleDiscoveryProfDetails> GetAllDiscoveryProfileDetails()
        {
            IList<ScheduleDiscoveryProfDetails> scheduleDiscoveryProfDetailsList = new List<ScheduleDiscoveryProfDetails>();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("ScheDisc_GetAllScheDiscProfDet"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var scheduleDiscoveryProfDetails = new ScheduleDiscoveryProfDetails();

                            scheduleDiscoveryProfDetails.ScheDiscProfileName = Convert.IsDBNull(reader["ScheDiscProfileName"]) ? string.Empty : Convert.ToString(reader["ScheDiscProfileName"]);

                            scheduleDiscoveryProfDetails.HostFrom = Convert.IsDBNull(reader["HostFrom"]) ? string.Empty : Convert.ToString(reader["HostFrom"]);

                            scheduleDiscoveryProfDetails.HostTo = Convert.IsDBNull(reader["HostTo"]) ? string.Empty : Convert.ToString(reader["HostTo"]);

                            scheduleDiscoveryProfDetails.OsFilter = Convert.IsDBNull(reader["OsFilter"]) ? "All" : Convert.ToString(reader["OsFilter"]);

                            scheduleDiscoveryProfDetails.AppFilter = Convert.IsDBNull(reader["AppFilter"]) ? "All" : Convert.ToString(reader["AppFilter"]);

                            scheduleDiscoveryProfDetails.IsScheduled = Convert.IsDBNull(reader["IsScheduled"]) ? 0 : Convert.ToInt32(reader["IsScheduled"]);

                            scheduleDiscoveryProfDetailsList.Add(scheduleDiscoveryProfDetails);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Getting all Discovery ProfileDetails", exc);

            }
            return scheduleDiscoveryProfDetailsList;
        }

        public static ScheduleDiscoveryProfDetails GetScheduledAppDiscProfileByName(string appDiscProfileName)
        {
            ScheduleDiscoveryProfDetails scheduleDiscoveryProfDetails = null;
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SCHEAPPDISC_GETPRODETBYDISNAME"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iDiscoveryProfileName", DbType.AnsiString, appDiscProfileName);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            scheduleDiscoveryProfDetails = new ScheduleDiscoveryProfDetails();

                            scheduleDiscoveryProfDetails.ScheDiscProfileName = Convert.IsDBNull(reader["ScheDiscProfileName"]) ? string.Empty : Convert.ToString(reader["ScheDiscProfileName"]);

                            scheduleDiscoveryProfDetails.HostFrom = Convert.IsDBNull(reader["HostFrom"]) ? string.Empty : Convert.ToString(reader["HostFrom"]);

                            scheduleDiscoveryProfDetails.HostTo = Convert.IsDBNull(reader["HostTo"]) ? string.Empty : Convert.ToString(reader["HostTo"]);

                            scheduleDiscoveryProfDetails.OsFilter = Convert.IsDBNull(reader["OsFilter"]) ? "All" : Convert.ToString(reader["OsFilter"]);

                            scheduleDiscoveryProfDetails.AppFilter = Convert.IsDBNull(reader["AppFilter"]) ? "All" : Convert.ToString(reader["AppFilter"]);

                            scheduleDiscoveryProfDetails.IsScheduled = Convert.IsDBNull(reader["IsScheduled"]) ? 0 : Convert.ToInt32(reader["IsScheduled"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Getting application discovery ProfileDetails by profile Name" + appDiscProfileName, exc);

            }
            return scheduleDiscoveryProfDetails;

        }

        public static bool UpdateIsSchedule(string appDiscProfileName)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SCHEAPPDISC_UPDISSCHEBYDISNAME"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iDiscoveryProfileName", DbType.AnsiString, appDiscProfileName);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while updating isSchedule", exc);
            }

        }
    }
}
