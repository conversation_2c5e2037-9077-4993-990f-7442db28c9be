﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;
namespace Bcms.DataAccess
{
    public class SRMVmwareMonitorDataAccess : BaseDataAccess
    {     

        #region methods

        public static bool AddSRMVmwareMonitorLogs(SRMVmwareMonitor srvmmmonitor)
        {
            try
            {
                string sp = DbRoleName + "SRMVMWAREMONITOR_LOGS_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, srvmmmonitor.InfraObjectId);

                    Database.AddInParameter(cmd, Dbstring + "iVCenterVersionPR", DbType.AnsiString, srvmmmonitor.VCenterVersionPR);
                    Database.AddInParameter(cmd, Dbstring + "iVCenterVersionDR", DbType.AnsiString, srvmmmonitor.VCenterVersionDR);
                   
                    Database.AddInParameter(cmd, Dbstring + "iVCenterBuildPR", DbType.AnsiString, srvmmmonitor.VCenterBuildPR);
                    Database.AddInParameter(cmd, Dbstring + "iVCenterBuildDR", DbType.AnsiString, srvmmmonitor.VCenterBuildDR);
                    
                    Database.AddInParameter(cmd, Dbstring + "iSRMVersionPR", DbType.AnsiString, srvmmmonitor.SRMVersionPR);
                    Database.AddInParameter(cmd, Dbstring + "iSRMVersionDR", DbType.AnsiString, srvmmmonitor.SRMVersionDR);
                    
                    Database.AddInParameter(cmd, Dbstring + "iSRMBuildPR", DbType.AnsiString, srvmmmonitor.SRMBuildPR);
                    Database.AddInParameter(cmd, Dbstring + "iSRMBuildDR", DbType.AnsiString, srvmmmonitor.SRMBuildDR);
                    
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNamePR", DbType.AnsiString, srvmmmonitor.ProtectionGroupsNamePR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNameTypePR", DbType.AnsiString, srvmmmonitor.ProtectionGroupsNameTypePR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNameStatePR", DbType.Int32, srvmmmonitor.ProtectionGroupsNameStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupNameVMCountPR", DbType.AnsiString, srvmmmonitor.ProtectionGroupNameVMCountPR);
                    
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNameDR", DbType.AnsiString, srvmmmonitor.ProtectionGroupsNameDR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNameTypeDR", DbType.AnsiString, srvmmmonitor.ProtectionGroupsNameTypeDR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupsNameStateDR", DbType.AnsiString, srvmmmonitor.ProtectionGroupsNameStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iProtectionGroupNameVMCountDR", DbType.AnsiString, srvmmmonitor.ProtectionGroupNameVMCountDR);
                    
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryPlanNamePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanNamePR);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryPlanStatePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanStatePR);
                   
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryPlanNameDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanNameDR);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryPlanStateDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanStateDR);
                   
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryNamePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryNamePR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryLastRunDatePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryLastRunDatePR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryStatePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryStatePR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryTotalTimePR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryTotalTimePR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryNameDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryNameDR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryLastRunDateDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryLastRunDateDR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryStateDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryStateDR);
                    Database.AddInParameter(cmd, Dbstring + "iRPlanHistoryTotalTimeDR", DbType.AnsiString, srvmmmonitor.RecoveryPlanHistoryTotalTimeDR);
                   

                    int value = Database.ExecuteNonQuery(cmd);

                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exe)
            {
                string error = "Exception occured while adding SRMVmware Monitor Logs and Status :" + exe.Message;
                throw;
            }

            return true;
        }

        #endregion methods
    }
}