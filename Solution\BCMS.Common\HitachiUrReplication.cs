﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "bcms_hitachiUr", Namespace = "http://www.BCMS.com/types")]
    public class HitachiUrReplication : BaseEntity
    {
        // ReplicationBase _replicationBase = new ReplicationBase();

        //    private IList<HitachiURDeviceMonitoring> _monitorings = new List<HitachiURDeviceMonitoring>();

        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int PrServerId
        {
            get;
            set;
        }

        [DataMember]
        public int DrServerId
        {
            get;
            set;
        }

        [DataMember]
        public string PrStorageId
        {
            get;
            set;
        }

        [DataMember]
        public string DrStorageId
        {
            get;
            set;
        }

        [DataMember]
        public string PrHorcomInstance
        {
            get;
            set;
        }

        [DataMember]
        public string DrHorcomInstance
        {
            get;
            set;
        }

        [DataMember]
        public string PrHorcomDeviceGroups
        {
            get;
            set;
        }

        [DataMember]
        public string DrHorcomDeviceGroups
        {
            get;
            set;
        }

        [DataMember]
        public string PrJournalVolume
        {
            get;
            set;
        }

        [DataMember]
        public string DrJournalVolume
        {
            get;
            set;
        }

        [DataMember]
        public string PrCommandDevice
        {
            get;
            set;
        }

        [DataMember]
        public string DrCommandDevice
        {
            get;
            set;
        }

        [DataMember]
        public string PRHORCOMStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRHORCOMStatus
        {
            get;
            set;
        }

        [DataMember]
        public string AverageCopyPendingTime
        {
            get;
            set;
        }

        //[DataMember]
        //public IList<HitachiURDeviceMonitoring> HitachiUrDeviceMonitoringList
        //{
        //    get
        //    {
        //        return _monitorings;
        //    }
        //    set
        //    {
        //        _monitorings = value;
        //    }
        //}

        #endregion Properties
    }
}