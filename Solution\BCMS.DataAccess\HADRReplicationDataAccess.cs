﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class HADRReplicationDataAccess : BaseDataAccess
    {
        public static HADRReplication AddHADRReplicationMonitor(HADRReplication replicationMonitor)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("HadrReplication_Create"))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, replicationMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iHRole", DbType.String, replicationMonitor.HRole);
                    Database.AddInParameter(cmd, Dbstring+"iState", DbType.String, replicationMonitor.State);
                    Database.AddInParameter(cmd, Dbstring+"iSyncMode", DbType.String, replicationMonitor.SyncMode);
                    Database.AddInParameter(cmd, Dbstring+"iConnectionStatus", DbType.String, replicationMonitor.ConnectionStatus);
                    Database.AddInParameter(cmd, Dbstring+"iHeartbeatsMissed", DbType.String, replicationMonitor.HeartbeatsMissed);
                    Database.AddInParameter(cmd, Dbstring+"iLocalHost", DbType.String, replicationMonitor.LocalHost);
                    Database.AddInParameter(cmd, Dbstring+"iLocalService", DbType.String, replicationMonitor.LocalService);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteHost", DbType.String, replicationMonitor.RemoteHost);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteService", DbType.String, replicationMonitor.RemoteService);
                    Database.AddInParameter(cmd, Dbstring+"iTimeout", DbType.String, replicationMonitor.Timeout);
                    Database.AddInParameter(cmd, Dbstring+"iLogGap", DbType.String, replicationMonitor.LogGap);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader Reader = Database.ExecuteReader(cmd))
                    {
                        if (Reader != null)
                        {
                            if (Reader.Read())
                            {
                                replicationMonitor.Id = Convert.ToInt32(Reader[0].ToString());

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Insert Record in HADRReplication", exc);
            }
            return replicationMonitor;
        }

        public static HADRReplication UpdateHADRReplicationMonitor(HADRReplication replicationMonitor)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("HadrReplication_Update"))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, replicationMonitor.Id);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, replicationMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iHRole", DbType.String, replicationMonitor.HRole);
                    Database.AddInParameter(cmd, Dbstring+"iState", DbType.String, replicationMonitor.State);
                    Database.AddInParameter(cmd, Dbstring+"iSyncMode", DbType.String, replicationMonitor.SyncMode);
                    Database.AddInParameter(cmd, Dbstring+"iConnectionStatus", DbType.String, replicationMonitor.ConnectionStatus);
                    Database.AddInParameter(cmd, Dbstring+"iHeartbeatsMissed", DbType.String, replicationMonitor.HeartbeatsMissed);
                    Database.AddInParameter(cmd, Dbstring+"iLocalHost", DbType.String, replicationMonitor.LocalHost);
                    Database.AddInParameter(cmd, Dbstring+"iLocalService", DbType.String, replicationMonitor.LocalService);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteHost", DbType.String, replicationMonitor.RemoteHost);
                    Database.AddInParameter(cmd, Dbstring+"iRemoteService", DbType.String, replicationMonitor.RemoteService);
                    Database.AddInParameter(cmd, Dbstring+"iTimeout", DbType.String, replicationMonitor.Timeout);
                    Database.AddInParameter(cmd, Dbstring+"iLogGap", DbType.String, replicationMonitor.LogGap);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader Reader = Database.ExecuteReader(cmd))
                    {
                        if (Reader != null)
                        {
                            if (Reader.Read())
                            {
                                replicationMonitor.Id = Convert.ToInt32(Reader[0].ToString());

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update Record", exc);
            }
            return replicationMonitor;
        }

        public static HADRReplication HADRReplicationMonitorGetByInfraObjectId(int infraId)
        {
            if (infraId < 1)
            {
                throw new ArgumentNullException("groupId");
            }

            var repMonitor = new HADRReplication();
            const string sp = "HadrReplication_GetByinfraObjectId";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {
                        repMonitor.Id = Convert.ToInt32(myDROperationReader1["Id"]);
                        repMonitor.InfraObjectId = Convert.ToInt32(myDROperationReader1["InfraObjectId"]);
                        repMonitor.HRole = myDROperationReader1["HRole"].ToString();
                        repMonitor.State = myDROperationReader1["State"].ToString();
                        repMonitor.SyncMode = myDROperationReader1["SyncMode"].ToString();
                        repMonitor.ConnectionStatus = myDROperationReader1["ConnectionStatus"].ToString();
                        repMonitor.HeartbeatsMissed = myDROperationReader1["HeartbeatsMissed"].ToString();
                        repMonitor.LocalHost = myDROperationReader1["LocalHost"].ToString();
                        repMonitor.LocalService = myDROperationReader1["LocalService"].ToString();
                        repMonitor.RemoteService = myDROperationReader1["RemoteService"].ToString();
                        repMonitor.Timeout = myDROperationReader1["Timeout"].ToString();
                        repMonitor.LogGap = myDROperationReader1["LogGap"].ToString();


                        //repMonitor.Id = Convert.ToInt32(myDROperationReader1[0]);
                        //repMonitor.GroupId = Convert.ToInt32(myDROperationReader1[1]);
                        //repMonitor.HRole = myDROperationReader1[2].ToString();
                        //repMonitor.State = myDROperationReader1[3].ToString();
                        //repMonitor.SyncMode = myDROperationReader1[4].ToString();
                        //repMonitor.ConnectionStatus = myDROperationReader1[5].ToString();
                        //repMonitor.HeartbeatsMissed = myDROperationReader1[6].ToString();
                        //repMonitor.LocalHost = myDROperationReader1[7].ToString();
                        //repMonitor.LocalService = myDROperationReader1[8].ToString();
                        //repMonitor.RemoteService = myDROperationReader1[9].ToString();
                        //repMonitor.Timeout = myDROperationReader1[10].ToString();
                        //repMonitor.LogGap = myDROperationReader1[11].ToString();
                    }
                    else
                    {
                        repMonitor = null;
                    }
                }

                return repMonitor;
            }
        }
    }
}
