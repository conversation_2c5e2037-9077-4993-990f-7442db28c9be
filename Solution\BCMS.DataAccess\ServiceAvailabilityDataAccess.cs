﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler; 
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class ServiceAvailabilityDataAccess : BaseDataAccess
    {
        public static bool AddServiceAvialbility(ServiceAvailability service)
        {
            
           

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("ServiceAvailability_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, service.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, service.Status);
                    int returnCode = Database.ExecuteNonQuery(cmd);

 #if ORACLE
                    return returnCode < 0;                
#endif
                    return returnCode > 0;
                    
                  
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Service Avilability Details", exc);

            }
        }
    }
}