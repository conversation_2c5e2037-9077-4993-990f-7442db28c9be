﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Server", Namespace = "http://www.Bcms.com/types")]

    public class Server : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PRName
        {
            get;
            set;
        }

        [DataMember]
        public string PRIPAddress
        {
            get;
            set;
        }

         [DataMember]
        public string PRUserName
        {
            get;
            set;
        }
         
        [DataMember]
        public string PRPassword
        {
            get;
            set;
        }

         [DataMember]
        public string PRType
        {
            get;
            set;
        }
         [DataMember]
         public bool PREnableSudoAccess
         {
             get;
             set;
         }

         [DataMember]
         public string PROSType
         {
             get;
             set;
         }


         [DataMember]
         public string DRName
         {
             get;
             set;
         }

         [DataMember]
         public string DRIPAddress
         {
             get;
             set;
         }

         [DataMember]
         public string DRUserName
         {
             get;
             set;
         }

         [DataMember]
         public string DRPassword
         {
             get;
             set;
         }

         [DataMember]
         public string DRType
         {
             get;
             set;
         }

         [DataMember]
         public bool DREnableSudoAccess
         {
             get;
             set;
         }

         [DataMember]
         public string DROSType
         {
             get;
             set;
         }
         [DataMember]
         public string PRSudoUser
         {
             get;
             set;
         }
         [DataMember]
         public string PRSudoPassword
         {
             get;
             set;
         }

         [DataMember]
         public string DRSudoUser
         {
             get;
             set;
         }

         [DataMember]
         public string DRSudoPassword
         {
             get;
             set;
         }

         [DataMember]
         public string PRStatus
         {
             get;
             set;
         }

         [DataMember]
         public string DRStatus
         {
             get;
             set;
         }

         [DataMember]
         public int PRId
         {
             get;
             set;
         }

         [DataMember]
         public int DRId
         {
             get;
             set;
         }

         [DataMember]
         public int GroupId
         {
             get;
             set;
         }
         [DataMember]
         public int InfraObjectId
         {
             get;
             set;
         }

         [DataMember]
         public string GroupName
         {
             get;
             set;
         }
         [DataMember]
         public string InfraObjectName
         {
             get;
             set;
         }
         [DataMember]
         public JobName JobName
         {
             get;
             set;
         }

         [DataMember]
         public string PRVirtualImagePath { get; set; }
         [DataMember]
         public string DRVirtualImagePath { get; set; }

        
         [DataMember]
         public int BusinessServiceId { get; set; }

         [DataMember]
         public bool PRIsUseSShkeyAuth
         {
             get;
             set;
         }

         [DataMember]
         public string PRSSHKeyPath
         {
             get;
             set;
         }

         [DataMember]
         public string PRSSHKeyPassword
         {
             get;
             set;
         }

         [DataMember]
         public bool DRIsUseSShkeyAuth
         {
             get;
             set;
         }

         [DataMember]
         public string DRSSHKeyPath
         {
             get;
             set;
         }

         [DataMember]
         public string DRSSHKeyPassword
         {
             get;
             set;
         }


        #endregion

        #region Constructor

        public Server()
            : base()
        {
        }

        #endregion
    }
}
