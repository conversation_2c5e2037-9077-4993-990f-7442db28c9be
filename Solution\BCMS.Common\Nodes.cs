﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Nodes", Namespace = "http://www.Bcms.com/types")]
    public class Nodes : BaseEntity
    {
        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        public string ArchieveLogPath
        {
            get;
            set;
        }

        [DataMember]
        public int IsPrimary
        {
            get;
            set;
        }

        [DataMember]
        public int IsConfigPath
        {
            get;
            set;
        }

        [DataMember]
        public string TargetArchieveLogPath
        {
            get;
            set;
        }

        [DataMember]
        public string OracleSID
        {
            get;
            set;
        }

        [DataMember]
        public string InstanceName
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }

        [DataMember]
        public NodeStatus Status
        {
            get;
            set;
        }

        [DataMember]
        public string Home
        {
            get;
            set;
        }

        [DataMember]
        public string Redo
        {
            get;
            set;
        }

        [DataMember]
        public string ASMGrid
        {
            get;
            set;
        }
        [DataMember]
        public string PRASMPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRASMUserName
        {
            get;
            set;
        }

        [DataMember]
        public string PRASMPassword
        {
            get;
            set;
        }
        [DataMember]
        public string DRASMPath
        {
            get;
            set;
        }
        [DataMember]
        public string DRASMUserName
        {
            get;
            set;
        }

        [DataMember]
        public string DRASMPassword
        {
            get;
            set;
        }
        [DataMember]
        public string PRAsmInstanceName { get; set; }

        [DataMember]
        public string DRAsmInstanceName { get; set; }

        [DataMember]
        public int IsAsm { get; set; }

        [DataMember]
        public string PreExecutionCommand
        {
            get;
            set;
        }
    }
}