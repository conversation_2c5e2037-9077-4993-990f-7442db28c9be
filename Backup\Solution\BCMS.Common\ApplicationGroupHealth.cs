﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ApplicationGroupHealth", Namespace = "http://www.BCMS.com/types")]
    public class ApplicationGroupHealth : BaseEntity
    {
        #region Constructor

        public ApplicationGroupHealth()
            : base()
        {
        }

        #endregion

        #region Properties
        [DataMember]
        public string ApplicationName
        {
            get;
            set;
        }

        [DataMember]
        public int ApplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int Health
        {
            get;
            set;
        }

        [DataMember]
        public int HealthUp
        {
            get;
            set;
        }
        [DataMember]
        public int HealthDown
        {
            get;
            set;
        }

        [DataMember]
        public string Availability
        {
            get;
            set;
        }
        #endregion


    }
}