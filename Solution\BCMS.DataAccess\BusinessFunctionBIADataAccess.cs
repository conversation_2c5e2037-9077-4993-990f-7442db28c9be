﻿using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
namespace Bcms.DataAccess
{
    public class BusinessFunctionBIADataAccess : BaseDataAccess
    {
        public static BusinessFunctionBIA GetBusinessFunctionBIACostByBIAID(int BIAID)
        {
            var businessFunctionBIA = new BusinessFunctionBIA();

            using (DbCommand cmd = Database.GetStoredProcCommand("BFBIA_GETBIABYBIAID"))
            {
                using (IDataReader myDataLagReader = Database.ExecuteReader(cmd))
                {
                    while (myDataLagReader.Read())
                    {
                        businessFunctionBIA.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                        businessFunctionBIA.Upto2HoursCost = Convert.IsDBNull(myDataLagReader["Upto2HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto2HoursCost"]);
                        businessFunctionBIA.Upto4HoursCost = Convert.IsDBNull(myDataLagReader["Upto4HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto4HoursCost"]);
                        businessFunctionBIA.Upto8HoursCost = Convert.IsDBNull(myDataLagReader["Upto8HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto8HoursCost"]);
                        businessFunctionBIA.Upto12HoursCost = Convert.IsDBNull(myDataLagReader["Upto12HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto12HoursCost"]);
                        businessFunctionBIA.Upto24HoursCost = Convert.IsDBNull(myDataLagReader["Upto24HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto24HoursCost"]);
                        businessFunctionBIA.Upto48HoursCost = Convert.IsDBNull(myDataLagReader["Upto48HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto48HoursCost"]);
                        businessFunctionBIA.Upto72HoursCost = Convert.IsDBNull(myDataLagReader["Upto72HoursCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto72HoursCost"]);
                        businessFunctionBIA.Upto1WeekCost = Convert.IsDBNull(myDataLagReader["Upto1WeekCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto1WeekCost"]);
                        businessFunctionBIA.Upto2WeeksCost = Convert.IsDBNull(myDataLagReader["Upto2WeeksCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto2WeeksCost"]);
                        businessFunctionBIA.Upto1MonthCost = Convert.IsDBNull(myDataLagReader["Upto1MonthCost"]) ? 0 : Convert.ToInt32(myDataLagReader["Upto1MonthCost"]);
                    }
                }
            }

            return businessFunctionBIA;
        }

        public static IList<IncidentManagement> GetIncidentDataByIncidentID(int iInfraObject, int iInfraComponent)
        {
            IList<IncidentManagement> IncidentManagementColl = new List<IncidentManagement>();


            using (DbCommand cmd = Database.GetStoredProcCommand("IncidentMan_GetByInfraComp"))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjID", DbType.Int32, iInfraObject);
                Database.AddInParameter(cmd, Dbstring + "iInfraCompID", DbType.Int32, iInfraComponent);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDataLagReader = Database.ExecuteReader(cmd))
                {
                    while (myDataLagReader.Read())
                    {
                        var incidentManagement = new IncidentManagement();

                        incidentManagement.IncidentID = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                        incidentManagement.IncidentTime = Convert.IsDBNull(myDataLagReader["IncidentTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDataLagReader["IncidentTime"]);
                        incidentManagement.IncidentRecoveryTime = Convert.IsDBNull(myDataLagReader["IncidentRecoveryTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDataLagReader["IncidentRecoveryTime"]);
                        // datalag.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                        //datalag.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                        //datalag.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                        //datalag.PRLogDate = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                        //datalag.DRLogDate = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                        //datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                        //datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                        //datalag.BusinessServiceId = Convert.IsDBNull(myDataLagReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myDataLagReader["BusinessServiceId"]);
                        //datalag.BuinsessFunctionId = Convert.IsDBNull(myDataLagReader["BuinsessFunctionId"]) ? 0 : Convert.ToInt32(myDataLagReader["BuinsessFunctionId"]);
                        //datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);


                        //datalag.Id = Convert.ToInt32(myDataLagReader[0]);
                        //datalag.LogFileName = myDataLagReader[1].ToString();
                        //datalag.PRSequenceNo = myDataLagReader[2].ToString();
                        //datalag.DRSequenceNo = myDataLagReader[3].ToString();
                        //datalag.PRTransId = myDataLagReader[4].ToString();
                        //datalag.DRTransId = myDataLagReader[5].ToString();
                        //datalag.PRLogDate = myDataLagReader[6].ToString();
                        //datalag.DRLogDate = myDataLagReader[7].ToString();
                        //datalag.CurrentDataLag = myDataLagReader[8].ToString();
                        //datalag.Health = myDataLagReader.IsDBNull(9)? 0: Convert.ToInt32(myDataLagReader[9]);
                        //datalag.ApplicationGroupId = myDataLagReader.IsDBNull(10)? 0 : Convert.ToInt32(myDataLagReader[10]);
                        //datalag.GroupId = Convert.ToInt32(myDataLagReader[11]);

                        IncidentManagementColl.Add(incidentManagement);
                    }
                }

            }

            return IncidentManagementColl;

        }
    }
}
