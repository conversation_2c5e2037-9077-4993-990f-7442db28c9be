﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "InfraobjectDiskMonitor", Namespace = "http://www.Bcms.com/types")]
    public class InfraobjectDiskMonitor : BaseEntity
    {        
        [DataMember]
        public int InfraobjectId { get; set; }

        [DataMember]
        public string VolumeNamePR { get; set; }

        [DataMember]
        public string ThresholdPR { get; set; }

        [DataMember]
        public string ScheduleTimePR { get; set; }

        [DataMember]
        public string VolumeNameDR { get; set; }

        [DataMember]
        public string ThresholdDR { get; set; }

        [DataMember]
        public string ScheduleTimeDR { get; set; }

    }
}


