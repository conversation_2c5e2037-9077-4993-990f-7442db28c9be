﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PMSSQLDBMirroring;
using System.Net.NetworkInformation;
using System.Data.SqlClient;


namespace Bcms.Core.Client
{
    public class MSSQLDBMirroringClient : IDisposable
    {

        public MSSQLDBMirroringClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

        public MSSQLDBMirroringClient()
        {
            // TODO: Complete member initialization
        }

        #region Variable
        public bool _isDisposed;
        private Server _server;
        private DatabaseBase _database;
        private const int MaxAlertCount = 3;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(MSSQLDBMirroringClient));
        # endregion

        public InfraObject CurrentInfraObject { get; set; }

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }

        public DatabaseBase CurrentDatabase
        {

            get
            {
                if (_database == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _database = DatabaseBaseDataAccess.GetDatabaseByInfraObjectId(CurrentInfraObject.Id);
                    }
                }
                return _database;
            }

            set
            {
                _database = value;
            }
        }

        public void MonitorMSSQLDBMirroringComponent()
        {
            try
            {
                MSSQLMirrorServer _sqlServerPR, _sqlServerDR;
                MSSQLMirrorDatabase _sqlDatabasePR, _sqlDatabaseDR;
                string PRauthmode = string.Empty;
                string DRauthmode = string.Empty;

                Logger.InfoFormat("MSSQLDB Mirroring Component ..");
                if (CurrentServer != null && CurrentDatabase != null)
                {
                    CurrentServer.InfraObjectName = CurrentInfraObject.Name;
                    CurrentServer.InfraObjectId = CurrentInfraObject.Id;
                    CurrentServer.JobName = JobName.MssqlDBMirroring;
                     var InfraObjicetInfo = InfraObjectDataAccess.GetInfraObjectById(CurrentInfraObject.Id);
                     if (InfraObjicetInfo.PRReplicationId != 0)
                     {
                         var replication = ReplicationBaseDataAccess.GetReplicationById(InfraObjicetInfo.PRReplicationId);
                         if (replication != null)
                         {

                             if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
                             {
                                 _sqlServerDR = new MSSQLMirrorServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                 _sqlServerPR = new MSSQLMirrorServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                                 if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     PRauthmode = "sql";
                                 }
                                 else
                                 {
                                     PRauthmode = "windows";
                                 }
                                 if (CurrentDatabase.DatabaseSqlNative2008.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                 {
                                     DRauthmode = "sql";
                                 }
                                 else
                                 {
                                     DRauthmode = "windows";
                                 }
                                 _sqlDatabaseDR = new MSSQLMirrorDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), PRauthmode);
                                 _sqlDatabasePR = new MSSQLMirrorDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), DRauthmode);
                             }
                             else
                             {
                                 _sqlServerPR = new MSSQLMirrorServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                                 _sqlServerDR = new MSSQLMirrorServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);

                                if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    PRauthmode = "sql";
                                }
                                else
                                {
                                    PRauthmode = "windows";
                                }
                                if (CurrentDatabase.DatabaseSqlNative2008.DRAuthenticationMode == SqlAuthenticateType.SqlServer)
                                {
                                    DRauthmode = "sql";
                                }
                                else
                                {
                                    DRauthmode = "windows";
                                }
                                _sqlDatabasePR = new MSSQLMirrorDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), PRauthmode);
                                _sqlDatabaseDR = new MSSQLMirrorDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), DRauthmode);
                            }

                             var sqlg = new MSSQLDBMirroringClient(CurrentInfraObject);
                            VerifyServer(_sqlServerPR, _sqlServerDR);
                            if (sqlg.VerifyServerAndDatabseforsqlnative2008())
                            {
                                SqlDBMirroring _SqlPRMirroring = new SqlDBMirroring();
                                var _SqlPRMirroringstatus = PMSSQLDBMirroring.MSSQLDBMirroringHelper.MirroringMonitoringStatus(_sqlDatabasePR);
                                SqlDBMirroring _SqlDRMirroring = new SqlDBMirroring();
                                var _SqlDRMirroringstatus = PMSSQLDBMirroring.MSSQLDBMirroringHelper.MirroringMonitoringStatus(_sqlDatabaseDR);
                                if (_SqlPRMirroringstatus != null && _SqlDRMirroringstatus != null)
                                {
                                    _SqlPRMirroring.InfraObjectId = CurrentInfraObject.Id;
                                    _SqlPRMirroring.PRDatabaseName = _SqlPRMirroringstatus.DATABASE_NAME;
                                    _SqlPRMirroring.PRDBRole = _SqlPRMirroringstatus.ROLE_OFDB;
                                    _SqlPRMirroring.PROpreationMode = _SqlPRMirroringstatus.Operationmode;
                                    _SqlPRMirroring.PRMirroringState = _SqlPRMirroringstatus.MIRRORING_STATE;
                                    _SqlPRMirroring.PRLogGenerateRate = _SqlPRMirroringstatus.LOG_GENERAT_RATE;
                                    _SqlPRMirroring.PRUnsentLog = _SqlPRMirroringstatus.UNSENT_LOG;
                                    _SqlPRMirroring.PRSentRate = _SqlPRMirroringstatus.LOGSENT_RATE;
                                    _SqlPRMirroring.PRUnrestoredLog = _SqlPRMirroringstatus.UNRESTORED_LOG;
                                    _SqlPRMirroring.PRRecoveryRate = _SqlPRMirroringstatus.LOGRECOVERY_RATE;
                                    _SqlPRMirroring.PRTransactionDelay = _SqlPRMirroringstatus.TRANSACTION_DELAY;
                                    _SqlPRMirroring.PRTransactionPerSecond = _SqlPRMirroringstatus.TRANSACTION_PER_SEC;
                                    _SqlPRMirroring.PRTimeRecorded = _SqlPRMirroringstatus.TIME_RECORDED;
                                    _SqlPRMirroring.PRTimeBehind = _SqlPRMirroringstatus.TIME_BEHIND;
                                    _SqlPRMirroring.PRLocalTime = _SqlPRMirroringstatus.LOCAL_TIME;

                                    _SqlPRMirroring.DRDatabaseName = _SqlDRMirroringstatus.DATABASE_NAME;
                                    _SqlPRMirroring.DRDBRole = _SqlDRMirroringstatus.ROLE_OFDB;
                                    _SqlPRMirroring.DROpreationMode = _SqlDRMirroringstatus.Operationmode;
                                    _SqlPRMirroring.DRMirroringState = _SqlDRMirroringstatus.MIRRORING_STATE;
                                    _SqlPRMirroring.DRLogGenerateRate = _SqlDRMirroringstatus.LOG_GENERAT_RATE;
                                    _SqlPRMirroring.DRUnsentLog = _SqlDRMirroringstatus.UNSENT_LOG;
                                    _SqlPRMirroring.DRSentRate = _SqlDRMirroringstatus.LOGSENT_RATE;
                                    _SqlPRMirroring.DRUnrestoredLog = _SqlDRMirroringstatus.UNRESTORED_LOG;
                                    _SqlPRMirroring.DRRecoveryRate = _SqlDRMirroringstatus.LOGRECOVERY_RATE;
                                    _SqlPRMirroring.DRTransactionDelay = _SqlDRMirroringstatus.TRANSACTION_DELAY;
                                    _SqlPRMirroring.DRTransactionPerSecond = _SqlDRMirroringstatus.TRANSACTION_PER_SEC;
                                    _SqlPRMirroring.DRTimeRecorded = _SqlDRMirroringstatus.TIME_RECORDED;
                                    _SqlPRMirroring.DRTimeBehind = _SqlDRMirroringstatus.TIME_BEHIND;
                                    _SqlPRMirroring.DRLocalTime = _SqlDRMirroringstatus.LOCAL_TIME;
                                    _SqlPRMirroring.DataLag = Convert.ToString(_SqlPRMirroringstatus.DatalagTime);

                                    Logger.InfoFormat(" Retrived MssqlDBMirroring PR Server and Database info for Infraobject " + CurrentInfraObject.Name + ":" + " PRDatabaseName: " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "," + " PRIpAddress: " + CurrentServer.PRIPAddress);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring PR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + "PRDBRole: " + " " + _SqlPRMirroring.PRDBRole + "," + " PROpreationMode: " + " " + _SqlPRMirroring.PROpreationMode + "," + " PRMirroringState: " + " " + _SqlPRMirroring.PRMirroringState);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring PR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + " PRLogGenerateRate: " + " " + _SqlPRMirroring.PRLogGenerateRate + "," + " PRUnsentLog " + " " + _SqlPRMirroring.PRUnsentLog + "," + " PRSentRate: " + " " + _SqlPRMirroring.PRSentRate + " ," + " PRUnrestoredLog: " + " " + _SqlPRMirroring.PRUnrestoredLog + " ," + " PRRecoveryRate: " + " " + _SqlPRMirroring.PRRecoveryRate);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring PR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + " PRTransactionDelay: " + " " + _SqlPRMirroring.PRTransactionDelay + "," + " PRTransactionPerSecond: " + " " + _SqlPRMirroring.PRTransactionPerSecond + "," + " PRTimeRecorded: " + " " + _SqlPRMirroring.PRTimeRecorded + "," + " PRTimeBehind: " + " " + _SqlPRMirroring.PRTimeBehind + "," + " PRLocalTime: " + " " + _SqlPRMirroring.PRLocalTime);

                                    Logger.InfoFormat(" Retrived MssqlDBMirroring DR Server and Database info for Infraobject " + CurrentInfraObject.Name + ":" + " DRDatabaseName: " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "," + " DRIpAddress: " + CurrentServer.DRIPAddress);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring DR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + "DRDBRole: " + " " + _SqlPRMirroring.DRDBRole + "," + " DROpreationMode: " + " " + _SqlPRMirroring.DROpreationMode + "," + " DRMirroringState: " + " " + _SqlPRMirroring.DRMirroringState);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring DR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + " DRLogGenerateRate: " + " " + _SqlPRMirroring.DRLogGenerateRate + "," + " DRUnsentLog " + " " + _SqlPRMirroring.DRUnsentLog + "," + " DRSentRate: " + " " + _SqlPRMirroring.DRSentRate + " ," + " DRUnrestoredLog: " + " " + _SqlPRMirroring.DRUnrestoredLog + " ," + " DRRecoveryRate: " + " " + _SqlPRMirroring.DRRecoveryRate);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring DR Monitor info for Infraobject " + CurrentInfraObject.Name + ":" + " DRTransactionDelay: " + " " + _SqlPRMirroring.DRTransactionDelay + "," + " DRTransactionPerSecond: " + " " + _SqlPRMirroring.DRTransactionPerSecond + "," + " DRTimeRecorded: " + " " + _SqlPRMirroring.DRTimeRecorded + "," + " DRTimeBehind: " + " " + _SqlPRMirroring.DRTimeBehind + "," + " DRLocalTime: " + " " + _SqlPRMirroring.DRLocalTime);
                                    Logger.InfoFormat(" Retrived MssqlDBMirroring for Infraobject " + CurrentInfraObject.Name + ":" + " DataLag: " + " " + _SqlPRMirroring.DataLag );

                                    var istrue1 = MSSQLDBMirroringDataAccess.AddMSSQLDBMonitorStatus(_SqlPRMirroring, CurrentInfraObject.Id);

                                    if (istrue1)
                                    {
                                        Logger.InfoFormat("Add MssqlDBMonitoring Monitor status created successfully", CurrentInfraObject.Name);
                                    }

                                    var istrue = MSSQLDBMirroringDataAccess.AddMSSQLDBMonitorLogs(_SqlPRMirroring, CurrentInfraObject.Id);
                                    if (istrue)
                                    {
                                        Logger.InfoFormat("Add MssqlDBMonitoring Logs  status created successfully", CurrentInfraObject.Name);
                                    }
                                    BusinessServiceRPOInfo busRpo = new BusinessServiceRPOInfo();
                                    if (_SqlPRMirroring.DataLag != null)
                                    {
                                        Logger.InfoFormat("Binding Buisness Info details..");
                                        busRpo.BusinessFunctionId = CurrentInfraObject.BusinessFunctionId;
                                        busRpo.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                                        busRpo.InfraObjectId = CurrentInfraObject.Id;
                                        busRpo.CurrentRPO = Convert.ToString(_SqlPRMirroring.DataLag);
                                        Logger.InfoFormat("Retrived business Rpo of MssqlDBMirroring Infraobject:" + CurrentInfraObject.Name, "Buisness ServiceId:" + busRpo.BusinessServiceId, "Current RPO " + busRpo.CurrentRPO);
                                        var issuccess = BusinessServiceRPOInfoDataAccess.AddBusinessServiceRPOInfo(busRpo);
                                        if (issuccess)
                                        {
                                            Logger.InfoFormat(CurrentInfraObject.Name + ":" + " Business Service RPO Populated " + " " + busRpo.CurrentRPO);
                                            Logger.InfoFormat(CurrentInfraObject.Name + ":" + " MssqlDBMirror Business Service Data inserted " + " " + busRpo.CurrentRPO);

                                        }
                                    }
                                    var _sqlPRDBhealth = MSSQLDBMirroringHelper.GetSQLHealth(_sqlDatabasePR);
                                     var _sqlDRDBhealth = MSSQLDBMirroringHelper.GetSQLHealth(_sqlDatabaseDR);

                                    if (_sqlPRDBhealth!= null && _sqlDRDBhealth!= null)
                                    {
                                        _SqlPRMirroring.MSSQL_Database_State_PR = _sqlPRDBhealth.DatabaseState;
                                        _SqlPRMirroring.MSSQL_Database_State_DR = _sqlDRDBhealth.DatabaseState;
                                        _SqlPRMirroring.MSSQL_Database_Recovery_Model_PR = _sqlPRDBhealth.DatabaseRecoveryModel;
                                        _SqlPRMirroring.MSSQL_Database_Recovery_Model_DR = _sqlDRDBhealth.DatabaseRecoveryModel;
                                        
                                    }
                                    ProcessMSSql2k8Alerts(_sqlDatabasePR, _sqlDatabaseDR, _SqlPRMirroring);


                                }

                                else
                                {
                                    ProcessMSSql2k8Alerts(_sqlDatabasePR, _sqlDatabaseDR, _SqlPRMirroring);
                                }
                

                             }
   
                         }
                     }
                }
         
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorMSSQLDBMirroringComponent:" + CurrentInfraObject.Name + exc.Message);
                throw;
            }
            catch (Exception exception)
            {
                Logger.Info("Exception:MonitorMSSQLDBMirroringComponent:" + CurrentInfraObject.Name + exception.Message);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                                        "Exception occurred while performing monitor MSSQLDB Mirroring", exception);
            }
        }

        # region VerifyServerAndDatabse

        public bool VerifyPRServer()
        {
            if (SqlNativeIsAvailableHost(CurrentServer.PRIPAddress))
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        public bool VerifyDRServer()
        {
            if (SqlNativeIsAvailableHost(CurrentServer.DRIPAddress))
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        private bool SqlNativeIsAvailableHost(string hostName)
        {
            var ping = new System.Net.NetworkInformation.Ping();
            try
            {
                PingReply reply = ping.Send(hostName);
                return reply != null && reply.Status == IPStatus.Success;
            }
            catch (Exception ex)
            {
                Logger.Error("Exception occurred while ping host (" + hostName + ") .Message " + ex.Message);
                return false;
            }
        }

        private bool VerifyPRDatabase()
        {
            string con = "Server = " + CurrentServer.PRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify Primary SqlServer Database .Message " + e.Message);

                return false;
            }
        }

        private bool VerifyDRDatabase()
        {
            string con = "Server = " + CurrentServer.DRIPAddress + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message);
                return false;
            }
        }

        public bool VerifyServerAndDatabseforsqlnative2008()
        {
            if (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted)
            {
                bool drserververify = VerifyDRServer();
                bool drdatabaseverify = false;
                string drdatabasestate;
                //VerifyDRServer(drserververify);
                if (drserververify)
                {
                    drdatabaseverify = VerifyDRDatabaseForsqlnative2008();
                    drdatabasestate = VerifyDRDatabasechkForsqlnative2008();
                    VerifyDRDatabaseInstanceStatus(drdatabaseverify, drdatabasestate);
                }
                if (drserververify && drdatabaseverify)
                {
                    return true;
                }
            }
            else
            {
                bool prserververify = VerifyPRServer();
                bool prdatabaseverify = false;
                string prdatabasestate;
                //VerifyPRServer(prserververify);
                if (prserververify)
                {
                    prdatabaseverify = VerifyPRDatabaseForsqlnative2008();
                    prdatabasestate = VerifyPRDatabasechkForsqlnative2008();
                    VerifyPRDatabaseInstanceStatus(prdatabaseverify, prdatabasestate);
                }

                if (prserververify && prdatabaseverify)
                {
                    return true;
                }
            }
            return false;
        }

        private bool VerifyPRDatabaseForsqlnative2008()
        {
            string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify Primary SqlServer Database .Message " + e.Message);

                return false;
            }
        }

        private bool VerifyDRDatabaseForsqlnative2008()
        {
            string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return true;
            }
            catch (Exception e)
            {
                Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message);
                return false;
            }
        }

        private string VerifyDRDatabasechkForsqlnative2008()
        {
            string con = "Server = " + CurrentServer.DRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.DRPort + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.DRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.DRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return "Database connected successfully";
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message + " ," + e.InnerException.Message);
                    return e.InnerException.Message;
                }
                else
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message);
                    if (e.Message.Contains("Login failed"))
                    {
                        return "Authentication Failured to Database !!!";
                    }
                    else
                    {
                        return e.Message;
                    }
                }
            }
        }

        private string VerifyPRDatabasechkForsqlnative2008()
        {
            string con = "Server = " + CurrentServer.PRIPAddress + "," + CurrentDatabase.DatabaseSqlNative2008.PRPort + "; Database = " + CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName + "; User Id = " + CurrentDatabase.DatabaseSqlNative2008.PRUserName + "; Password = " + CurrentDatabase.DatabaseSqlNative2008.PRPassword + "";
            SqlConnection cn = new SqlConnection(con);
            try
            {
                cn.Open();
                cn.Close();
                return "Database connected successfully !!!";
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message + " ," + e.InnerException.Message);
                    return e.InnerException.Message;
                }
                else
                {
                    Logger.Error("Exception occurred while verify DR SqlServer Database .Message " + e.Message);
                    if (e.Message.Contains("Login failed"))
                    {
                        return "Authentication Failured to Database !!!";
                    }
                    else
                    {
                        return e.Message;
                    }
                }
            }
        }

        private void VerifyPRDatabaseInstanceStatus(bool verifyprdatabase, string Prdbexception)
        {
            string databasename = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName;
            int alertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRDBcheck : (int)AlertNotificationType.PRDBcheck;

            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (verifyprdatabase)
            {

                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseUP,
                                                              "InfraObject Name : " + CurrentInfraObject.Name +
                                                              " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) +
                                                              " Primary Database Instance: " + databasename + " is Up "), "MonitorPRDatabaseStatus",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);

                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Up.ToString(),
                        IsAffected = false
                    });

                    string ResolveMessage = CurrentInfraObject.Name + " : Production Database (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName) + ") is up";
                    IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId, 0, ResolveMessage);

                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));

                    //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " , " + " Cannot access database " + " " + databasename + " " + "on server" + " " + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRIPAddress : CurrentServer.PRIPAddress) + " " + "due to Reason :" + " " + Prdbexception), "MonitorPRDatabaseStatus",
                    //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Down.ToString(),
                        IsAffected = true
                    });

                    IncidentManagement inc = new IncidentManagement
                    {
                        IncidentName = "CP-Inc_DB_down",
                        IncidentTime = System.DateTime.Now,
                        InfraID = CurrentInfraObject.Id,
                        InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DRDBId : CurrentDatabase.PRDBId,
                        InfraComponentType = "Database",
                        IncidentComment = "Database is Down"

                    };
                    IncidentManagementDataAccess.AddIncident(inc);

                }
            }

        }

        private void VerifyDRDatabaseInstanceStatus(bool verifydrdatabase, string drdbexception)
        {
            string databasename = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName : CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName;
            int alertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

            int sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), alertType, Convert.ToInt32(CurrentInfraObject.Id));

            if (verifydrdatabase)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, false, Convert.ToInt32(CurrentInfraObject.Id));

                    ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseUP,
                                       "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRIPAddress : CurrentServer.DRIPAddress) + " DR Database Instance: " + databasename + " is Up "), "",
                                            CurrentInfraObject.Id, CurrentInfraObject.Name);




                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Up.ToString(),
                        IsAffected = false
                    });

                    //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                    //{
                    //    InfraObjectId = CurrentInfraObject.Id,
                    //    EntityId = CurrentDatabase.DRDBId,
                    //    Status = 0,
                    //    ResolveMessage = CurrentInfraObject.Name + " : DR Database (" + databasename + ") is up"
                    //});

                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), alertType, true, Convert.ToInt32(CurrentInfraObject.Id));



                    //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " HostIPAddress :" + CurrentServer.DRHostName + " Cannot access database :" + databasename + "on server" + CurrentServer.DRIPAddress + " " + "due to Reason :" + drdbexception), "MonitorDRDatabaseStatus",
                    //                   CurrentInfraObject.Id, CurrentInfraObject.Name);

                    //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRDatabaseDown, "InfraObject Name : " + CurrentInfraObject.Name + " , " + " Cannot access database " + " " + databasename + " " + "on server" + " " + CurrentServer.DRIPAddress + " " + "due to Reason :" + " " + drdbexception), "MonitorPRDatabaseStatus",
                    //                        CurrentInfraObject.Id, CurrentInfraObject.Name);




                    HeatmapDataAccess.AddHeatmap(new Heatmap
                    {
                        InfraObjectId = CurrentInfraObject.Id,
                        EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentDatabase.PRDBId : CurrentDatabase.DRDBId,
                        BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                        HeatmapType = HeatmapType.Database.ToString(),
                        Status = DatabaseMode.Down.ToString(),
                        IsAffected = true
                    });

                    //ImpactAnalysisDataAccess.AddImpactAnalysis(new ImpactAnalysis
                    //{
                    //    InfraObjectId = CurrentInfraObject.Id,
                    //    EntityId = CurrentDatabase.DRDBId,
                    //    ImpactType = ImpactType.DatabaseDown.ToString(),
                    //    Status = 1,
                    //    ImpactMessage = "Database is Down "
                    //});
                }
            }
        }


        private void ProcessMSSql2k8Alerts(MSSQLMirrorDatabase _mssqlPRDB, MSSQLMirrorDatabase _mssqlDRDB, SqlDBMirroring _sqlnativehealthCompMonitor)
        {
            #region MSsqlDBMirror PR DB State
            if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "online")
            {
                string PRServiceAlertmessage = "InfraObject Name :" +  CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Online State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "offline")
            {
                string PRServiceAlertmessage = "InfraObject Name :" +  CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in offline State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "shutdown")
            {
                string PRServiceAlertmessage = "InfraObject Name :" +  CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Shutdown State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "restoring")
            {
                string PRServiceAlertmessage = "InfraObject Name :" +  CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Restoring State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "recovering")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Recovering State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "recovery pending")
            {
                string PRServiceAlertmessage = "InfraObject Name :" +  CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Recovery Pending State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "suspect")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Suspect State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_PR.ToLower() == "emergency")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase State is in Emergency State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorPRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
            }

            #endregion

            #region MSsqlDBMirror DR DataBase State
            if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "online")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Online State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseState, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "offline")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in offline State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateOffline, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "shutdown")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Shutdown State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateshutdown, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "restoring")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Restoring State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRestoring, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "recovering")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Recovering State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecovering, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "recovery pending")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Recovery Pending State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateRecoveryPending, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "suspect")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Suspect State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateSuspect, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.MSSQL_Database_State_DR.ToLower() == "emergency")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in Emergency State;";
                int serviceAlertType = (int)AlertNotificationType.MssqlDBMirrorDRDBstate;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDBStateEmergency, serviceAlertType);
            }

            #endregion

            #region MSsqlDBMirror PR Mirror State
            if (_sqlnativehealthCompMonitor.PRMirroringState.ToLower() == "synchronized")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDBMirror State is in synchronized State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLPRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.MSSQLDB_PRMIRROR_State_synchronized, serviceAlertType);
            }

            if (_sqlnativehealthCompMonitor.PRMirroringState.ToLower() == "synchronizing")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDBMirror State is in synchronizing State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLPRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.MSSQLDB_PRMIRROR_State_synchronizing, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.PRMirroringState.ToLower() == "disconnected")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDBMirror State is in disconnected State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLPRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.MSSQLDB_PRMIRROR_State_disconnected, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.PRMirroringState.ToLower() == "suspended")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDBMirror State is in suspended State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLPRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 0, BcmsExceptionType.MSSQLDB_PRMIRROR_State_suspended, serviceAlertType);
            }
          
            #endregion

            #region MSsqlDBMirror DR Mirror State
            if (_sqlnativehealthCompMonitor.DRMirroringState.ToLower() == "synchronized")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in synchronized State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLDRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.MSSQLDB_DRMIRROR_State_synchronized, serviceAlertType);
            }

            if (_sqlnativehealthCompMonitor.DRMirroringState.ToLower() == "synchronizing")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in synchronizing State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLDRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.MSSQLDB_DRMIRROR_State_synchronizing, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.DRMirroringState.ToLower() == "disconnected")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in disconnected State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLDRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.MSSQLDB_DRMIRROR_State_disconnected, serviceAlertType);
            }
            else if (_sqlnativehealthCompMonitor.DRMirroringState.ToLower() == "suspended")
            {
                string PRServiceAlertmessage = "InfraObject Name :" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDBMirror State is in suspended State;";
                int serviceAlertType = (int)AlertNotificationType.MSSQLDRMirroringState;
                ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.MSSQLDB_DRMIRROR_State_suspended, serviceAlertType);
            }

            #endregion

            //#region PR MSSQL DatabaseRecovery Model

            //if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_PR.ToLower() == "full")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase Recovery Model is in Full State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "PR", 1, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelfull, serviceAlertType);
            //}
            //else if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_PR.ToLower() == "bulk-logged")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase Recovery Model is in Bulk-logged State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelBulklogged, serviceAlertType);
            //}
            //else if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_PR.ToLower() == "simple")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.PRIPAddress + ") MSSQLDatabase Recovery Model is in Simple State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLPRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor,  PRServiceAlertmessage, "PR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelSimple, serviceAlertType);
            //}

            //#endregion

            //# region DR MSSQL DatabaseRecovery Model
            //if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_DR.ToLower() == "full")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase Recovery Model is in Full State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 1, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelfull, serviceAlertType);
            //}
            //else if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_DR.ToLower() == "bulk-logged")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase Recovery Model is in Bulk-logged State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor, PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelBulklogged, serviceAlertType);
            //}
            //else if (_sqlnativehealthCompMonitor.MSSQL_Database_Recovery_Model_DR.ToLower() == "simple")
            //{
            //    string PRServiceAlertmessage = "InfraObject Name:" + CurrentInfraObject.Name + " (IP :" + CurrentServer.DRIPAddress + ") MSSQLDatabase Recovery Model is in Simple State;";
            //    int serviceAlertType = (int)AlertNotificationType.MSSQLDRDatabaseRecoveryModel;
            //    ProcessNativeAlerts(_sqlnativehealthCompMonitor,PRServiceAlertmessage, "DR", 0, BcmsExceptionType.NRMSSQLDatabaseRecoveryModelSimple, serviceAlertType);
            //}

            //#endregion


                }

        private void ProcessNativeAlerts(SqlDBMirroring _sqlnativehealthCompMonitor, string PRServiceAlertmessage, string type, int status, BcmsExceptionType exceptiontype, int serviceAlertType)
        {
            //int serviceAlertType = string.Equals("PR", type) ? (int)AlertNotificationType.PRDBcheck : (int)AlertNotificationType.DRDBcheck;

            var sentAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Service), serviceAlertType, _sqlnativehealthCompMonitor.InfraObjectId);
            if (status == 1)
            {
                if (sentAlertCount > 0)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, false,
                        _sqlnativehealthCompMonitor.InfraObjectId);

                    ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MSSQLDB Mirror State", _sqlnativehealthCompMonitor.InfraObjectId, CurrentInfraObject.Name);
                }
            }
            else
            {
                if (MaxAlertCount > sentAlertCount)
                {
                    AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Service), serviceAlertType, true,
                        _sqlnativehealthCompMonitor.InfraObjectId);
                    ExceptionManager.Manage(new BcmsException(exceptiontype, PRServiceAlertmessage), "MSSQLDB Mirror State", _sqlnativehealthCompMonitor.InfraObjectId, CurrentInfraObject.Name);

                }
            }
        }
        #endregion

        private void VerifyServer(PMSSQLDBMirroring.MSSQLMirrorServer prSSHInfo, PMSSQLDBMirroring.MSSQLMirrorServer drSSHInfo)
        {
            using (BCMS.Core.Utility.SSHHelper SSHHelper = new BCMS.Core.Utility.SSHHelper())
            {
                try
                {
                    string authmode = string.Empty;
                    if (CurrentDatabase.DatabaseSqlNative2008.PRAuthenticationMode == SqlAuthenticateType.SqlServer)
                    {
                        authmode = "sql";
                    }
                    else
                    {
                        authmode = "windows";
                    }
                    MSSQLMirrorServer _sqlServerPR = new MSSQLMirrorServer(CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword);
                    MSSQLMirrorDatabase _sqlDatabasePR = new MSSQLMirrorDatabase(_sqlServerPR, CurrentDatabase.DatabaseSqlNative2008.PRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.PRUserName, CurrentDatabase.DatabaseSqlNative2008.PRPassword, CurrentDatabase.DatabaseSqlNative2008.PRPort.ToString(), authmode);
                    MSSQLMirrorServer _sqlServerDR = new MSSQLMirrorServer(CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword);
                    MSSQLMirrorDatabase _sqlDatabaseDR = new MSSQLMirrorDatabase(_sqlServerDR, CurrentDatabase.DatabaseSqlNative2008.DRDatabaseName, CurrentDatabase.DatabaseSqlNative2008.DRUserName, CurrentDatabase.DatabaseSqlNative2008.DRPassword, CurrentDatabase.DatabaseSqlNative2008.DRPort.ToString(), authmode);

                    int serverAlertType = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.DRServer : (int)AlertNotificationType.PRServer;

                    int serverAlertType1 = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? (int)AlertNotificationType.PRServer : (int)AlertNotificationType.DRServer;

                    var sentServerAlertCount = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType, CurrentInfraObject.Id);

                    var sentServerAlertCount1 = AlertDataAccess.GetSentAlertCount(Convert.ToInt32(AlertCategory.Group), serverAlertType1, CurrentInfraObject.Id);


                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.PRIPAddress, CurrentServer.PRUserName, CurrentServer.PRPassword, "PR"))
                    {

                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                               "InfraObject Name : " + CurrentInfraObject.Name + " Primary Server (" +
                            //                                              prSSHInfo.Host + ") is connected."), "sqlNative2008ServerStatus", CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            string ResolveMessage = CurrentInfraObject.Name + " : Production Server (" + (CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId) + ") is connected";
                            IncidentManagementDataAccess.UpdateIncidentStatus(CurrentInfraObject.Id, CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, 0, ResolveMessage);

                        }

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRStatus : CurrentServer.PRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId, ServerStatus.Down);
                        }

                        if (MaxAlertCount > sentServerAlertCount)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType, true, CurrentInfraObject.Id);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable,
                            //                                          "InfraObject Name : " + CurrentInfraObject.Name +
                            //                                          " Primary Server (" +
                            //                                          prSSHInfo.Host + ") is not reachable."), "SqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });

                            IncidentManagement inc = new IncidentManagement
                            {
                                IncidentName = "CP-Inc_Server_down",
                                IncidentTime = System.DateTime.Now,
                                InfraID = CurrentInfraObject.Id,
                                InfraComponentID = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.DRId : CurrentServer.PRId,
                                InfraComponentType = "Server",
                                IncidentComment = "Server Not Reachable"

                            };
                            IncidentManagementDataAccess.AddIncident(inc);


                        }
                    }
                    if (SSHHelper.ConnectWindows(CurrentServer, CurrentServer.DRIPAddress, CurrentServer.DRUserName, CurrentServer.DRPassword, "DR"))
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) != "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Up);
                        }

                        if (sentServerAlertCount1 > 0)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, false, CurrentInfraObject.Id);

                            ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 1);

                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostReachable,
                            //                                            "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                                            prSSHInfo.Host + ") is connected."), "MonitorsqlNative2008ServerStatus",
                            //                       CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Up.ToString(),
                                IsAffected = false
                            });

                            //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                            //{
                            //    InfraObjectId = CurrentInfraObject.Id,
                            //    EntityId = CurrentServer.DRId,
                            //    Status = 0,
                            //    ResolveMessage =
                            //    CurrentInfraObject.Name + " : Server (" + CurrentServer.PRIPAddress + ") is connected"
                            //});


                        }

                    }
                    else
                    {
                        if ((CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRStatus : CurrentServer.DRStatus) == "Up")
                        {
                            ServerDataAccess.UpdateServerByStatus(CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId, ServerStatus.Down);
                        }
                        if (MaxAlertCount > sentServerAlertCount1)
                        {
                            AlertDataAccess.UpdateAlertData(Convert.ToInt32(AlertCategory.Group), serverAlertType1, true, CurrentInfraObject.Id);


                            //ExceptionManager.Manage(new BcmsException(BcmsExceptionType.NRHostNotReachable, "InfraObject Name : " + CurrentInfraObject.Name + " <br/> DR Server (" +
                            //                        drSSHInfo.Host + ") is not reachable."), "sqlNative2008ServerStatus",
                            //                        CurrentInfraObject.Id, CurrentInfraObject.Name);

                            HeatmapDataAccess.AddHeatmap(new Heatmap
                            {
                                InfraObjectId = CurrentInfraObject.Id,
                                EntityId = CurrentInfraObject.IsWorkflow == (int)GroupWorkflowOperation.SwitchOverCompleted ? CurrentServer.PRId : CurrentServer.DRId,
                                BusinessServiceId = CurrentInfraObject.BusinessServiceId,
                                HeatmapType = HeatmapType.Server.ToString(),
                                Status = ServerStatus.Down.ToString(),
                                IsAffected = true
                            });

                            //ImpactAnalysisDataAccess.UpdateImpactAnalysis(new ImpactAnalysis
                            //{
                            //    InfraObjectId = CurrentInfraObject.Id,
                            //    EntityId = CurrentServer.DRId,
                            //    Status = 0,
                            //    ResolveMessage =
                            //    CurrentInfraObject.Name + " : Server (" + CurrentServer.PRIPAddress + ") is not connected"
                            //});

                        }
                    }

                }
                catch (BcmsException exc)
                {
                    throw;
                }
                catch (Exception exc)
                {
                    ServerDataAccess.UpdateServerStatusById(CurrentServer.Id, 0);
                    throw new BcmsException(BcmsExceptionType.CommonUnhandled, string.Format("{0} : Exception occured while Connecting Server", CurrentInfraObject.Name), exc);
                }
            }
        }
       

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~MSSQLDBMirroringClient()
        {
            Dispose(false);
        }
    }
}
