﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Exchange", Namespace = "http://www.BCMS.com/types")]
    public class ExchangeSCRStatus : BaseEntity
    {
        [DataMember]
        public int SCRId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string SCRHealth
        {
            get;
            set;
        }

        [DataMember]
        public string SummaryCopyStatus
        {
            get;
            set;
        }

        [DataMember]
        public string CopyQueueLenght
        {
            get;
            set;
        }

        [DataMember]
        public string ReplyQueueLenght
        {
            get;
            set;
        }

        [DataMember]
        public DateTime LastInspectedLogTime
        {
            get;
            set;
        }
    }
}