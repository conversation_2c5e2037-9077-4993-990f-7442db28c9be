﻿using System;
using System.Net.NetworkInformation;
using Jscape.Ssh;
using log4net;

namespace Bcms.Replication.Shared
{
    public static class SSHHelper
    {

        #region Variable

        private const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";

        private static readonly ILog Logger = LogManager.GetLogger("BcmsMonitor");

        #endregion

        public static bool Connect(string hostname, string username, string password)
        {
            bool isconnect = false;

            var sshParams = new SshParameters(hostname, username, password);

            var ssh = new Ssh(sshParams) { LicenseKey = LicenseKey };
            try
            {

                ssh.Connect();

                if (ssh.Connected)
                {
                    ssh.Disconnect();

                    isconnect = true;
                }
            }
            catch (SshAuthenticationException)
            {
                throw;

            }
            catch (SshTaskTimeoutException)
            {
                throw;
            }
            catch (SshException)
            {
                throw;
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (ssh.Connected)
                {
                    ssh.Disconnect();
                }
            }

            return isconnect;
        }

        public static bool Ping(string server)
        {
            try
            {
                var ping = new Ping();
                const int timeOut = 1;

                PingReply reply = ping.Send(server, timeOut);

                if (reply != null && reply.Status == IPStatus.Success)
                {
                    return true;
                }
                if (reply != null && reply.Status == IPStatus.TimedOut)
                {
                    return false;
                }
            }
            catch (Exception)
            {
                throw;
            }
            return false;
        }
    }
}