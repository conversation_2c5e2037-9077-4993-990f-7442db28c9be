﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Utility;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using System.Collections.Generic;
using BCMS.Common;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class MSsqlnativeDataAccess : BaseDataAccess
    {
        public static MsSqlNative GetServersById(int id)
        {
            var mssqlnative = new MsSqlNative();
            try
            {
                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("MsSqlNative_GetByID"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
                    using (IDataReader myServerReader = db.ExecuteReader(dbCommand))
                    {
                        while (myServerReader.Read())
                        {

                            mssqlnative.Id = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                            mssqlnative.GroupId = Convert.IsDBNull(myServerReader["GroupId"]) ? 0 : Convert.ToInt32(myServerReader["GroupId"]);

                            mssqlnative.BackupFolderName = Convert.IsDBNull(myServerReader["BackupFolderName"]) ? string.Empty : Convert.ToString(myServerReader["BackupFolderName"]);
                            mssqlnative.BackupFolderSharedName = Convert.IsDBNull(myServerReader["BackupFolderSharedName"]) ? string.Empty : Convert.ToString(myServerReader["BackupFolderSharedName"]);
                            mssqlnative.CopyRestoreName = Convert.IsDBNull(myServerReader["CopyRestoreName"]) ? string.Empty : Convert.ToString(myServerReader["CopyRestoreName"]);
                            mssqlnative.CopyRestoreSharedName = Convert.IsDBNull(myServerReader["CopyRestoreSharedName"]) ? string.Empty : Convert.ToString(myServerReader["CopyRestoreSharedName"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get MsSqlNative information by Id - " + id, exc);
            }
            return mssqlnative;
        }
    }
}
