﻿using System.Data;
using Bcms.Common;

namespace Bcms.Replication.Interface
{
    public interface IDBManager
    {
        IDbCommand Command
        {
            get;
        }

       IDbCommand PrepareCommand(DatabaseBase database, string commandText, CommandType commandType, string type);

        void AddCommandParameter(IDbCommand cmd, string paramName, DbType dbType, object paramValue);

        void Dispose();
    }
}
