﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Text;
using System.Threading;
using System.Xml;
using System.ServiceProcess;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.FastCopy;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Replication.Base;
using Bcms.Replication.OracleDataGuard;
using Bcms.Replication.SqlServerNative;
using DNSManagement;
using Jscape.Ssh;
using PDB2HADR;
using PGlobalMirror;
using PSnapMirror;
using PSRM;
using PWebHelper;
using log4net;
using PWinCluster;
using SpreadsheetGear;
using System.Linq;
using Bcms.Replication;
using Bcms.Replication.Shared;
using LogshippingNative;
using BCMS.Common;
using BCMSExchangeClass;
using PEMCSRDF;
using PVMWare;
using MSSQLHelper;
using PHPUNIX;
using System.Configuration;
using MySql.Data.MySqlClient;
using PVxVM;
using PWindows;
using System.Runtime.Serialization.Formatters.Binary;
using Bcms.Helper;
using PDTake;

using PMSSQLNative;

namespace Bcms.Core.Client
{
    public class ApplicationeBDRClient : IDisposable
    {
        public ApplicationeBDRClient(InfraObject infraObject)
        {
            CurrentInfraObject = infraObject;
        }

        public InfraObject CurrentInfraObject { get; set; }


        #region Variable
        public bool _isDisposed;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ApplicationeBDRClient));



        private Server _server;

        private const int MaxAlertCount = 3;
        # endregion


        #region Properties

        public Server CurrentServer
        {
            get
            {
                if (_server == null)
                {
                    if (CurrentInfraObject != null)
                    {
                        _server = ServerDataAccess.GetServerByInfraObjectId(CurrentInfraObject.Id);
                        _server.InfraObjectId = CurrentInfraObject.Id;
                        _server.InfraObjectName = CurrentInfraObject.Name;
                        _server.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                    }
                }
                return _server;
            }

            set
            {
                _server = value;
            }
        }



        public string GetMirationNamefromIPAddress(string SourceIP, string TargetIP)
        {

            var GetAllprofileData = eBDRDataAcess.GetAlleBDRProfileData();

            string AllProfileName = string.Empty;

            foreach (var item in GetAllprofileData)
            {

                if (item.migration_details != null)
                {

                    var itemsplit = (item.migration_details).Split(',');

                    for (int i = 0; i < (itemsplit).Length; i++)
                    {
                        if (itemsplit[i].Contains("src_host"))
                        {
                            var host = itemsplit[i].Split(':');

                            if (host[1].Contains(SourceIP))
                            {

                            }

                        }
                        else if (itemsplit[i].Contains("tgt_host"))
                        {
                            var host = itemsplit[i].Split(':');

                            if (host[1].Contains(TargetIP))
                            {

                            }
                        }


                    }


                }
            }

            return AllProfileName;
        }


        public void MonitoringeBDRProfile()
        {
            try
            {

                Logger.Info("Monitoring for eBDR profile is Started in Client");

                if (CurrentServer != null)
                {
                    Logger.Info("eBDR Profile Source IP : " + CurrentServer.PRIPAddress + " Target IP : " + CurrentServer.DRIPAddress + " for Infraobject Name :" + CurrentInfraObject.Name);

                    var eBDRReplicationDetails = new eBDRProfile();

                    var replication = ReplicationBaseDataAccess.GetReplicationById(CurrentInfraObject.PRReplicationId);

                    //   Logger.Info("Feaching eBDR Profile Info for PR IP : " + CurrentServer.PRIPAddress + " and DR IP :" + CurrentServer.DRIPAddress);

                    //  var GetProfileInfo = eBDRDataAcess.GetAlleBDRProfileInfobyIPAddress(CurrentServer.PRIPAddress, CurrentServer.DRIPAddress);
                    Logger.Info("Feaching eBDR Profile Info for Profile Name : " + replication.doubletekRep.JobName);

                    var GetProfileInfo = eBDRDataAcess.GetAlleBDRProfileInfobyName(replication.doubletekRep.JobName);

                    Logger.Info("Complete Feaching eBDR Profile Info for Profile Name : " + replication.doubletekRep.JobName);


                    //  Logger.Info("Feaching Completed eBDR Profile Info for PR IP : " + CurrentServer.PRIPAddress + " and DR IP :" + CurrentServer.DRIPAddress);


                    if (GetProfileInfo.Count() > 0)
                    {
                        foreach (var Item in GetProfileInfo)
                        {
                            eBDRReplicationDetails.Profile_Name = !string.IsNullOrEmpty(Item.Profile_Name) ? Item.Profile_Name : "NA";
                            eBDRReplicationDetails.MachinName = !string.IsNullOrEmpty(Item.MachinName) ? Item.MachinName : "NA";
                            eBDRReplicationDetails.Source_Host = !string.IsNullOrEmpty(Item.Source_Host) ? Item.Source_Host : "NA";
                            eBDRReplicationDetails.Target_Host = !string.IsNullOrEmpty(Item.Target_Host) ? Item.Target_Host : "NA";
                            eBDRReplicationDetails.OsType = !string.IsNullOrEmpty(Item.OsType) ? Item.OsType : "NA";
                            eBDRReplicationDetails.Health = !string.IsNullOrEmpty(Item.Health) ? Item.Health : "0";
                            eBDRReplicationDetails.Status = !string.IsNullOrEmpty(Item.Status) ? Item.Status : "NA";
                            eBDRReplicationDetails.RPO = !string.IsNullOrEmpty(Item.RPO) ? Item.RPO : "NA";
                            eBDRReplicationDetails.CalculatedRPO = !string.IsNullOrEmpty(Item.CalculatedRPO) ? Item.CalculatedRPO : "NA";
                            eBDRReplicationDetails.Datalag = !string.IsNullOrEmpty(Item.Datalag) ? Item.Datalag : "00:00:00";
                            eBDRReplicationDetails.InfraobjectId = CurrentInfraObject.Id;

                            try
                            {
                                bool AddeBDRStatus = eBDRStatusLogs.AddeBDRProfileStatus(eBDRReplicationDetails);

                                if (AddeBDRStatus)
                                    Logger.Info("eBDRReplicationDetails is inserted in eBDRStaus table Successfully..!");
                                else
                                    Logger.Info("Failed to Insert  eBDRReplicationDetails in eBDRStaus ..!");

                            }
                            catch (Exception ex)
                            {
                                if (ex.InnerException != null)
                                    Logger.Error("Exception in inserting eBDRReplicationDetails :" + ex.InnerException);
                                else
                                    Logger.Error("Exception in  inserting eBDRReplicationDetails :" + ex.Message);

                            }



                        }
                    }
                    else
                    {
                        Logger.Info("GetProfileInfo is null for eBDR Profile PR IP :" + _server.PRIPAddress + " DR IP :" + _server.DRIPAddress);
                    }

                }
                else
                {
                    Logger.Info("Server Inforamation is NUll for Application eBDR Profile");
                }

                Logger.Info("Monitoring for eBDR profile is Completed in Client");
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Error("Exception in MonitoringeBDRProfile :" + ex.InnerException);
                else
                    Logger.Error("Exception in MonitoringeBDRProfile :" + ex.Message);
            }
        }



        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~ApplicationeBDRClient()
        {
            Dispose(false);
        }

        #endregion

    }
}
