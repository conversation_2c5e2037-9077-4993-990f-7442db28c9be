﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;


namespace Bcms.DataAccess
{
    public class HADRDataAccess : BaseDataAccess
    {
        public static HADR AddHADRMonitor(HADR hadrMonitor)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("Hadr_Create"))
                {
                    //db.AddInParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, hadrMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iPrIp", DbType.String, hadrMonitor.PRIp);
                    Database.AddInParameter(cmd, Dbstring+"iDrIp", DbType.String, hadrMonitor.DRIp);
                    Database.AddInParameter(cmd, Dbstring+"iPrDatabaseInstance", DbType.String, hadrMonitor.PRDatabaseInstance);
                    Database.AddInParameter(cmd, Dbstring+"iDrDatabaseInstance", DbType.String, hadrMonitor.DRDatabaseInstance);
                    Database.AddInParameter(cmd, Dbstring+"iPrDatabaseStatus", DbType.String, hadrMonitor.PRDatabaseStatus);
                    Database.AddInParameter(cmd, Dbstring+"iDrDatabaseStatus", DbType.String, hadrMonitor.DRDatabaseStatus);
                    Database.AddInParameter(cmd, Dbstring+"iPrLogFile", DbType.String, hadrMonitor.PRLogFile);
                    Database.AddInParameter(cmd, Dbstring+"iDrLogFile", DbType.String, hadrMonitor.DRLogFile);
                    Database.AddInParameter(cmd, Dbstring+"iPrCurrentLsn", DbType.String, hadrMonitor.PRCurrentLSN);
                    Database.AddInParameter(cmd, Dbstring+"iDrCurrentLsn", DbType.String, hadrMonitor.DRCurrentLSN);
                    Database.AddInParameter(cmd, Dbstring+"iPrLsn", DbType.String, hadrMonitor.PRLSN);
                    Database.AddInParameter(cmd, Dbstring+"iDrLsn", DbType.String, hadrMonitor.DRLSN);
                    Database.AddInParameter(cmd, Dbstring+"iPrTimestamp", DbType.String, Convert.ToString(hadrMonitor.PRTimestamp));
                    Database.AddInParameter(cmd, Dbstring+"iDrTimestamp", DbType.String, Convert.ToString(hadrMonitor.DRTimestamp));
                    Database.AddInParameter(cmd, Dbstring+"iDatalag", DbType.String, hadrMonitor.Datalag);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader Reader = Database.ExecuteReader(cmd))
                    {
                        if (Reader != null)
                        {
                            if (Reader.Read())
                            {
                                hadrMonitor.Id = Convert.ToInt32(Reader[0].ToString());

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Insert Record", exc);
            }
            return hadrMonitor;
        }

        public static HADR UpdateHADRMonitor(HADR hadrMonitor)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("Hadr_Update"))
                {
                    //db.AddInParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, hadrMonitor.Id);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, hadrMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iPrIp", DbType.String, hadrMonitor.PRIp);
                    Database.AddInParameter(cmd, Dbstring+"iDrIp", DbType.String, hadrMonitor.DRIp);
                    Database.AddInParameter(cmd, Dbstring+"iPrDatabaseInstance", DbType.String, hadrMonitor.PRDatabaseInstance);
                    Database.AddInParameter(cmd, Dbstring+"iDrDatabaseInstance", DbType.String, hadrMonitor.DRDatabaseInstance);
                    Database.AddInParameter(cmd, Dbstring+"iPrDatabaseStatus", DbType.String, hadrMonitor.PRDatabaseStatus);
                    Database.AddInParameter(cmd, Dbstring+"iDrDatabaseStatus", DbType.String, hadrMonitor.DRDatabaseStatus);
                    Database.AddInParameter(cmd, Dbstring+"iPrLogFile", DbType.String, hadrMonitor.PRLogFile);
                    Database.AddInParameter(cmd, Dbstring+"iDrLogFile", DbType.String, hadrMonitor.DRLogFile);
                    Database.AddInParameter(cmd, Dbstring+"iPrCurrentLsn", DbType.String, hadrMonitor.PRCurrentLSN);
                    Database.AddInParameter(cmd, Dbstring+"iDrCurrentLsn", DbType.String, hadrMonitor.DRCurrentLSN);
                    Database.AddInParameter(cmd, Dbstring+"iPrLsn", DbType.String, hadrMonitor.PRLSN);
                    Database.AddInParameter(cmd, Dbstring+"iDrLsn", DbType.String, hadrMonitor.DRLSN);
                    Database.AddInParameter(cmd, Dbstring+"iPrTimestamp", DbType.String, Convert.ToString(hadrMonitor.PRTimestamp));
                    Database.AddInParameter(cmd, Dbstring+"iDrTimestamp", DbType.String, Convert.ToString(hadrMonitor.DRTimestamp));
                    Database.AddInParameter(cmd, Dbstring+"iDatalag", DbType.String, hadrMonitor.Datalag);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader Reader = Database.ExecuteReader(cmd))
                    {
                        if (Reader != null)
                        {
                            if (Reader.Read())
                            {
                                hadrMonitor.Id = Convert.ToInt32(Reader[0].ToString());

                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update Record", exc);
            }
            return hadrMonitor;
        }

        public static HADR GetHADRByiInfraObjectId(int infraId)
        {
            if (infraId < 1)
            {
                throw new ArgumentNullException("groupId");
            }

            var databaseMonitor = new HADR();
            const string sp = "Hadr_GetByInfraObjectId";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {
                        databaseMonitor.Id = Convert.ToInt32(myDROperationReader1["Id"]);
                        databaseMonitor.InfraObjectId = Convert.ToInt32(myDROperationReader1["InfraObjectId"]);

                        databaseMonitor.PRIp = myDROperationReader1["PR_IP"].ToString();
                        databaseMonitor.DRIp = myDROperationReader1["DR_IP"].ToString();
                        databaseMonitor.PRDatabaseInstance = myDROperationReader1["PR_DatabaseInstance"].ToString();
                        databaseMonitor.DRDatabaseInstance = myDROperationReader1["DR_DatabaseInstance"].ToString();
                        databaseMonitor.PRDatabaseStatus = myDROperationReader1["PR_DatabaseStatus"].ToString();
                        databaseMonitor.DRDatabaseStatus = myDROperationReader1["DR_DatabaseStatus"].ToString();
                        databaseMonitor.PRLogFile = myDROperationReader1["PR_LogFile"].ToString();
                        databaseMonitor.DRLogFile = myDROperationReader1["DR_LogFile"].ToString();

                        databaseMonitor.PRCurrentLSN = myDROperationReader1["PR_CurrentLSN"].ToString();
                        databaseMonitor.DRCurrentLSN = myDROperationReader1["DR_CurrentLSN"].ToString();
                        databaseMonitor.PRLSN = myDROperationReader1["PR_LSN"].ToString();
                        databaseMonitor.DRLSN = myDROperationReader1["DR_LSN"].ToString();
                        databaseMonitor.PRTimestamp = myDROperationReader1["PR_Timestamp"].ToString();
                        databaseMonitor.DRTimestamp = myDROperationReader1["DR_Timestamp"].ToString();
                        databaseMonitor.Datalag = myDROperationReader1["Datalag"].ToString();



                        //databaseMonitor.Id = Convert.ToInt32(myDROperationReader1[0]);
                        //databaseMonitor.GroupId = Convert.ToInt32(myDROperationReader1[1]);

                        //databaseMonitor.PRIp = myDROperationReader1[2].ToString();
                        //databaseMonitor.DRIp = myDROperationReader1[3].ToString();
                        //databaseMonitor.PRDatabaseInstance = myDROperationReader1[4].ToString();
                        //databaseMonitor.DRDatabaseInstance = myDROperationReader1[5].ToString();
                        //databaseMonitor.PRDatabaseStatus = myDROperationReader1[6].ToString();
                        //databaseMonitor.DRDatabaseStatus = myDROperationReader1[7].ToString();
                        //databaseMonitor.PRLogFile = myDROperationReader1[8].ToString();
                        //databaseMonitor.DRLogFile = myDROperationReader1[9].ToString();

                        //databaseMonitor.PRCurrentLSN = myDROperationReader1[10].ToString();
                        //databaseMonitor.DRCurrentLSN = myDROperationReader1[11].ToString();
                        //databaseMonitor.PRLSN = myDROperationReader1[12].ToString();
                        //databaseMonitor.DRLSN = myDROperationReader1[13].ToString();
                        //databaseMonitor.PRTimestamp = myDROperationReader1[14].ToString();
                        //databaseMonitor.DRTimestamp = myDROperationReader1[15].ToString();
                        //databaseMonitor.Datalag = myDROperationReader1[16].ToString();

                    }
                    else
                    {
                        databaseMonitor = null;
                    }
                }

                return databaseMonitor;
            }
        }

        public static HADR GetHADRById(int Id)
        {
            if (Id < 1)
            {
                throw new ArgumentNullException("Id");
            }

            var databaseMonitor = new HADR();
            const string sp = "Hadr_GetById";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, Id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {


                        databaseMonitor.Id = Convert.ToInt32(myDROperationReader1["Id"]);
                        databaseMonitor.InfraObjectId = Convert.ToInt32(myDROperationReader1["InfraObjectId"]);

                        databaseMonitor.PRIp = myDROperationReader1["PR_IP"].ToString();
                        databaseMonitor.DRIp = myDROperationReader1["DR_IP"].ToString();
                        databaseMonitor.PRDatabaseInstance = myDROperationReader1["PR_DatabaseInstance"].ToString();
                        databaseMonitor.DRDatabaseInstance = myDROperationReader1["DR_DatabaseInstance"].ToString();
                        databaseMonitor.PRDatabaseStatus = myDROperationReader1["PR_DatabaseStatus"].ToString();
                        databaseMonitor.DRDatabaseStatus = myDROperationReader1["DR_DatabaseStatus"].ToString();
                        databaseMonitor.PRLogFile = myDROperationReader1["PR_LogFile"].ToString();
                        databaseMonitor.DRLogFile = myDROperationReader1["DR_LogFile"].ToString();

                        databaseMonitor.PRCurrentLSN = myDROperationReader1["PR_CurrentLSN"].ToString();
                        databaseMonitor.DRCurrentLSN = myDROperationReader1["DR_CurrentLSN"].ToString();
                        databaseMonitor.PRLSN = myDROperationReader1["PR_LSN"].ToString();
                        databaseMonitor.DRLSN = myDROperationReader1["DR_LSN"].ToString();
                        databaseMonitor.PRTimestamp = myDROperationReader1["PR_Timestamp"].ToString();
                        databaseMonitor.DRTimestamp = myDROperationReader1["DR_Timestamp"].ToString();
                        databaseMonitor.Datalag = myDROperationReader1["Datalag"].ToString();


                        //hadrMonitor.Id = Convert.ToInt32(myDROperationReader1[0]);
                        //hadrMonitor.GroupId = Convert.ToInt32(myDROperationReader1[1]);
                        //hadrMonitor.PRIp = myDROperationReader1[2].ToString();
                        //hadrMonitor.DRIp = myDROperationReader1[3].ToString();
                        //hadrMonitor.PRDatabaseInstance = myDROperationReader1[4].ToString();
                        //hadrMonitor.DRDatabaseInstance = myDROperationReader1[5].ToString();
                        //hadrMonitor.PRDatabaseStatus = myDROperationReader1[6].ToString();
                        //hadrMonitor.DRDatabaseStatus = myDROperationReader1[7].ToString();
                        //hadrMonitor.PRLogFile = myDROperationReader1[8].ToString();
                        //hadrMonitor.DRLogFile = myDROperationReader1[9].ToString();
                        //hadrMonitor.PRCurrentLSN = myDROperationReader1[10].ToString();
                        //hadrMonitor.DRCurrentLSN = myDROperationReader1[11].ToString();
                        //hadrMonitor.PRLSN = myDROperationReader1[12].ToString();
                        //hadrMonitor.DRLSN = myDROperationReader1[13].ToString();
                        //hadrMonitor.PRTimestamp = myDROperationReader1[14].ToString();
                        //hadrMonitor.DRTimestamp = myDROperationReader1[15].ToString();
                        //hadrMonitor.Datalag = myDROperationReader1[16].ToString();
                    }
                    else
                    {
                        databaseMonitor = null;
                    }
                }

                return databaseMonitor;
            }
        }
    }
}
