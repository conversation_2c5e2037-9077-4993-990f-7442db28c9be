﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;
namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DataGuard", Namespace = "http://www.BCMS.com/types")]
    public class DataGuard : BaseEntity
    {

        //#region Member Variables

        //ReplicationBase _basereplication = new ReplicationBase();

        //#endregion


        #region Properties
        
        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int SiteId
        {
            get;
            set;
        }

        [DataMember]

        public DataGuardMode Mode
        {
            get;
            set;
        }

        [DataMember]
        public string Service
        {
            get;
            set;
        }

        [DataMember]
        public string ProtectionMode
        {
            get;
            set;
        }
        
        [DataMember]
        public string PRStatus
        {
            get;
            set;
        }
        [DataMember]
        public string DRStatus
        {
            get;
            set;
        }
        
        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get
        //    {
        //        return _basereplication;
        //    }
        //    set
        //    {
        //        _basereplication = value;
        //    }
        //}
        #endregion

        #region Constructor

         public DataGuard()
            : base()
        {
        }

        #endregion

    }
}
