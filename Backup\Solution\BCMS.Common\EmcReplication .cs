﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class EmcReplication : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ReplicationName
        {
            get;
            set;
        }

        [DataMember]
        public string ReplicationType
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DGroupName
        {
            get;
            set;
        }

        [DataMember]
        public string DGType
        {
            get;
            set;
        }

        [DataMember]
        public string DGSummetrixID
        {
            get;
            set;
        }

        [DataMember]
        public string RemoteSymmetrixID
        {
            get;
            set;
        }

        [DataMember]
        public string RdfRaGroupNumber
        {
            get;
            set;
        }


        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public string DataLag
        {
            get;
            set;
        }


        [DataMember]
        public string PendingTracks
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public EmcReplication()
            : base()
        { }

        #endregion
    }
}