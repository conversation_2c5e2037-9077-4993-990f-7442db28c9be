﻿using System;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class SqlLog : BaseEntity
    {
        #region Properties


        public string LogFileName
        {
            get;
            set;
        }

        public string PRSequenceNo
        {
            get;
            set;
        }


        public string DRSequenceNo
        {
            get;
            set;
        }
      
        public string PRTransId
        {
            get;
            set;
        }
      
        public string DRTransId
        {
            get;
            set;
        }

        public DateTime LogGenerationTime
        {
            get;
            set;
        }

        public DateTime LogAppliedTime
        {
            get;
            set;
        }

        public int InfraObjectId
        {
            get;
            set;
        }

        public string InfraObjectName
        {
            get;
            set;
        }

        public string DataLag
        {
            get;
            set;
        }

        public int IsApplied
        {
            get; 
            set;
        }

        public string CopiedFile
        {
            get;
            set;
        }

        public DateTime LogCopiedTime
        {
            get;
            set;
        }

        #endregion

    }
}