﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MySqlMonitor", Namespace = "http://www.BCMS.com/types")]
    public class MySqlMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PRSERVERSTATUS
        {
            get;
            set;
        }

        [DataMember]
        public string DRSERVERSTATUS
        {
            get;
            set;
        }

        [DataMember]
        public string PRSERVERVERSION
        {
            get;
            set;
        }

        [DataMember]
        public string DRSERVERVERSION
        {
            get;
            set;
        }
        [DataMember]
        public string PRDAEMONSTATUS
        {
            get;
            set;
        }
        [DataMember]
        public string DRDAEMONSTATUS
        {
            get;
            set;
        }

        [DataMember]
        public string MASTERBINLOG
        {
            get;
            set;
        }

        [DataMember]
        public string SLAVEBINLOG
        {
            get;
            set;
        }

        [DataMember]
        public int IsMonitor
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }




        [DataMember]
        public string MASTERPOSITION
        {
            get;
            set;
        }
        [DataMember]
        public string SLAVEPOSITION
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public MySqlMonitor()
            : base()
        {

        }
        #endregion
    }
}
