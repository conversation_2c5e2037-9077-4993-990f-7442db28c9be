﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseOracleRac", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseOracleRac : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }

        [DataMember]
        public string ArchivePath
        {
            get;
            set;
        }

        [DataMember]
        public string InitSPFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string ControlFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string RedoPath
        {
            get;
            set;
        }
        #endregion

        #region Constructor

        public DatabaseOracleRac()
            : base()
        {
        }

        #endregion
    }
}
