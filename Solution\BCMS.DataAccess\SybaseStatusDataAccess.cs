﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class SybaseStatusDataAccess : BaseDataAccess
    {
        public static SyBaseMonitor GetSybaseMonitorStatusByInfraobjectId(int InfraobjectId)
        {
            var _syBaseMonitor = new SyBaseMonitor();
            try
            {
                using (DbCommand cmmond = Database.GetStoredProcCommand("SybaseMonit_GetByInfraId"))
                {
                    Database.AddInParameter(cmmond, Dbstring + "iInfraobjectId", DbType.AnsiString, InfraobjectId);
#if ORACLE
                    cmmond.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader mySybaseReader = Database.ExecuteReader(cmmond))
                    {
                        while (mySybaseReader.Read())
                        {
                            _syBaseMonitor.Id = Convert.IsDBNull(mySybaseReader["Id"]) ? 0 : Convert.ToInt32(mySybaseReader["Id"]);
                            _syBaseMonitor.LastDumpedLog = Convert.IsDBNull(mySybaseReader["LastDumpedLog"]) ? string.Empty : Convert.ToString(mySybaseReader["LastDumpedLog"]);
                            _syBaseMonitor.LastDumpedSequence = Convert.IsDBNull(mySybaseReader["LastDumpedSequence"]) ? string.Empty : Convert.ToString(mySybaseReader["LastDumpedSequence"]);
                            _syBaseMonitor.LastDumpedDateTime = Convert.IsDBNull(mySybaseReader["LastDumpedDateTime"]) ? DateTime.MinValue : Convert.ToDateTime(mySybaseReader["LastDumpedDateTime"]);

                            _syBaseMonitor.LastLoadedLog = Convert.IsDBNull(mySybaseReader["LastLoadedLog"]) ? string.Empty : Convert.ToString(mySybaseReader["LastLoadedLog"]);
                            _syBaseMonitor.LastLoadedSequence = Convert.IsDBNull(mySybaseReader["LastLoadedSequence"]) ? string.Empty : Convert.ToString(mySybaseReader["LastLoadedSequence"]);
                            _syBaseMonitor.LastLoadedDateTime = Convert.IsDBNull(mySybaseReader["LastLoadedDateTime"]) ? DateTime.MinValue : Convert.ToDateTime(mySybaseReader["LastLoadedDateTime"]);
                            _syBaseMonitor.IsApplied = Convert.IsDBNull(mySybaseReader["IsApplied"]) ? 0 : Convert.ToInt32(mySybaseReader["IsApplied"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Server information by Server Name - " + InfraobjectId, exc);
            }
            return _syBaseMonitor;
        }

        public static bool AddSybaseMonitorStatus(SyBaseMonitor _SyBaseMonitor)
        {
            try
            {

                const string sp = "Sybasemonitrstatus_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, _SyBaseMonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseState", DbType.AnsiString, _SyBaseMonitor.PRDatabaseState);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseState", DbType.AnsiString, _SyBaseMonitor.DRDatabaseState);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseVersion", DbType.AnsiString, _SyBaseMonitor.PRDatabaseVersion);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseVersion", DbType.AnsiString, _SyBaseMonitor.DRDatabaseVersion);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseSize", DbType.AnsiString, _SyBaseMonitor.PRDatabaseSize);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseSize", DbType.AnsiString, _SyBaseMonitor.DRDatabaseSize);
                    Database.AddInParameter(cmd, Dbstring + "iLastDumpedLog", DbType.AnsiString, _SyBaseMonitor.LastDumpedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLastDumpedSequence", DbType.AnsiString, _SyBaseMonitor.LastDumpedSequence);
                    Database.AddInParameter(cmd, Dbstring + "iLastDumpedDateTime", DbType.DateTime, _SyBaseMonitor.LastDumpedDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iLastLoadedLog", DbType.AnsiString, _SyBaseMonitor.LastLoadedLog);
                    Database.AddInParameter(cmd, Dbstring + "iLastLoadedSequence", DbType.AnsiString, _SyBaseMonitor.LastLoadedSequence);
                    Database.AddInParameter(cmd, Dbstring + "iLastLoadedDateTime", DbType.DateTime, _SyBaseMonitor.LastLoadedDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, _SyBaseMonitor.DataLag);
                    Database.AddInParameter(cmd, Dbstring + "iIsApplied", DbType.Int32, _SyBaseMonitor.IsApplied);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.AnsiString, 1);

                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseLoggingStatus", DbType.AnsiString, _SyBaseMonitor.PRDatabaseLoggingStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseLoggingStatus", DbType.AnsiString, _SyBaseMonitor.DRDatabaseLoggingStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatadeviceDetails", DbType.AnsiString, _SyBaseMonitor.PRDatadeviceDetails);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatadeviceDetails", DbType.AnsiString, _SyBaseMonitor.DRDatadeviceDetails);

                    Database.AddInParameter(cmd, Dbstring + "iPRDataSpaceUsed", DbType.AnsiString, _SyBaseMonitor.PRDataSpaceUsed);
                    Database.AddInParameter(cmd, Dbstring + "iDRDataSpaceUsed", DbType.AnsiString, _SyBaseMonitor.DRDataSpaceUsed);

                    Database.AddInParameter(cmd, Dbstring + "iPRLogDeviceDetails", DbType.AnsiString, _SyBaseMonitor.PRLogDeviceDetails);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogDeviceDetails", DbType.AnsiString, _SyBaseMonitor.DRLogDeviceDetails);

                    Database.AddInParameter(cmd, Dbstring + "iPRLogSpaceused", DbType.AnsiString, _SyBaseMonitor.PRLogSpaceused);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSpaceused", DbType.AnsiString, _SyBaseMonitor.DRLogSpaceused);

                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                    if (returnCode >= 0)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Insert AddSybaseMonitorStatus() method " + exc);
            }
        }

        public static bool UpdateSybaseMonitorStatus(SyBaseMonitor _SyBaseMonitor)
        {
            const string sp = "Sybasemonitrstatus_Update";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, _SyBaseMonitor.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring + "iLastLoadedLog", DbType.AnsiString, _SyBaseMonitor.LastLoadedLog);
                Database.AddInParameter(cmd, Dbstring + "iLastLoadedSequence", DbType.AnsiString, _SyBaseMonitor.LastLoadedSequence);
                Database.AddInParameter(cmd, Dbstring + "iLastLoadedDateTime", DbType.DateTime, _SyBaseMonitor.LastLoadedDateTime);
                Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, _SyBaseMonitor.DataLag);
                Database.AddInParameter(cmd, Dbstring + "iIsApplied", DbType.AnsiString, _SyBaseMonitor.IsApplied);
                Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.AnsiString, 1);
                int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                if (returnCode >= 0)
                {
                    return true;
                }
                return false;
            }
        }

        public static bool UpdateGenerateSybaselastTransactionLog(int infraid, string Last_Dumplog, string Last_DumpedDate, string Last_DumpedSequence)
        {
            //const string sp = "Sybasemonitrstatus_UpdatebyWorkflow";
            const string sp = "Sybasemonitrstatus_UpbyWF";
            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraid);
                Database.AddInParameter(cmd, Dbstring + "iLast_Dumplog", DbType.AnsiString, Last_Dumplog);
                Database.AddInParameter(cmd, Dbstring + "iLast_DumpedSequence", DbType.AnsiString, Last_DumpedSequence);
                Database.AddInParameter(cmd, Dbstring + "iLast_DumpedDate", DbType.DateTime, Convert.ToDateTime(Last_DumpedDate));
                int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                if (returnCode >= 0)
                {
                    return true;
                }
                return false;
            }
        }

        public static bool UpdatebyworkflowLoadTransactionlogFile(int infraid, string last_Loadded_Log, string last_Loadded_DateTime, string last_Loadded_Sequence)
        {
            //const string sp = "Sybasemonitrstatus_loaddedUpdatebyWorkflow";
            const string sp = "Sybasemonitrstatus_loadUpbyWF";
            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, infraid);
                Database.AddInParameter(cmd, Dbstring + "ilast_Loadded_Log", DbType.AnsiString, last_Loadded_Log);
                Database.AddInParameter(cmd, Dbstring + "ilast_Loadded_Sequence", DbType.AnsiString, last_Loadded_Sequence);
                Database.AddInParameter(cmd, Dbstring + "ilast_Loadded_DateTime", DbType.DateTime, Convert.ToDateTime(last_Loadded_DateTime));
                int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                    {
                        return true;
                    }
#endif
                if (returnCode >= 0)
                {
                    return true;
                }
                return false;
            }
        }

    }
}
