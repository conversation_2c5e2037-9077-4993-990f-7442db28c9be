﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "bcms_hitachiUrDeviceMonitoring", Namespace = "http://www.BCMS.com/types")]

    public class HitachiURDeviceMonitoring : BaseEntity
    {
        #region Properties
        [DataMember]
        public int ID
        {
            get;
            set;
        }
        [DataMember]
        public int GroupId
        {
            get;
            set;
        }
        [DataMember]
        public string DeviceGroupName
        {
            get;
            set;
        }
        [DataMember]
        public string Type
        {
            get;
            set;
        }
        [DataMember]
        public string LunsId
        {
            get;
            set;
        }
        [DataMember]
        public string VolumeStatus
        {
            get;
            set;
        }
        #endregion

         #region Constructor

        public HitachiURDeviceMonitoring()
            : base()
        {
        }

        #endregion
    }
}
