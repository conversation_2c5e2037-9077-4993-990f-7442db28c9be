﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class DROperationResultDataAccess:BaseDataAccess
    {

        public static DROperationResult AddDROperationResult(DROperationResult drOperationResult)
        {
            const string sp = "DROperationResult_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iActionName", DbType.AnsiString, drOperationResult.ActionName);
                Database.AddInParameter(cmd, Dbstring+"iStartTime", DbType.DateTime, drOperationResult.StartTime);
                Database.AddInParameter(cmd, Dbstring+"iEndTime", DbType.DateTime, drOperationResult.EndTime);
                Database.AddInParameter(cmd, Dbstring+"iElapsedTime", DbType.AnsiString, drOperationResult.ElapsedTime);
                Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, drOperationResult.Status);
                Database.AddInParameter(cmd, Dbstring+"iDROperationId", DbType.Int32, drOperationResult.DROperationId);
                Database.AddInParameter(cmd, Dbstring+"iActionId", DbType.Int32, drOperationResult.ActionId);
                Database.AddInParameter(cmd, Dbstring+"iMessage", DbType.AnsiString, drOperationResult.Message);

                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        drOperationResult.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        drOperationResult = null;
                    }
                }

                return drOperationResult;
            }
        }

        public static DROperationResult UpdateDROperationResult(DROperationResult drOperationResult)
        {
            const string sp = "DROperationResult_Update";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, drOperationResult.Id);
                Database.AddInParameter(cmd, Dbstring+"iActionName", DbType.AnsiString, drOperationResult.ActionName);
                Database.AddInParameter(cmd, Dbstring+"iStartTime", DbType.DateTime, drOperationResult.StartTime);
                Database.AddInParameter(cmd, Dbstring+"iEndTime", DbType.DateTime, drOperationResult.EndTime);
                Database.AddInParameter(cmd, Dbstring+"iElapsedTime", DbType.AnsiString, drOperationResult.ElapsedTime);
                Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, drOperationResult.Status);
                Database.AddInParameter(cmd, Dbstring+"iDROperationId", DbType.Int32, drOperationResult.DROperationId);
                Database.AddInParameter(cmd, Dbstring+"iMessage", DbType.AnsiString, drOperationResult.Message);


                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        drOperationResult.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        drOperationResult = null;
                    }
                }

                return drOperationResult;
            }
        }

        public static DROperationResult GetDROperationResultByDROperationIdAndStatus(int droperation)
        {
            var droperationresult = new DROperationResult();

            const string sp = "DROperationResult_GetByDROperationAndStatus";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {

                Database.AddInParameter(cmd, Dbstring+"iDROperationId", DbType.Int32, droperation);

                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        droperationresult.Id = Convert.IsDBNull(myDROperationResultReader["Id"]) ? 0 : Convert.ToInt32(myDROperationResultReader["Id"]);
                        droperationresult.ActionName = Convert.IsDBNull(myDROperationResultReader["ActionName"]) ? string.Empty : Convert.ToString(myDROperationResultReader["ActionName"]);
                        droperationresult.StartTime = Convert.IsDBNull(myDROperationResultReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["StartTime"]);
                        droperationresult.EndTime = Convert.IsDBNull(myDROperationResultReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["EndTime"]);
                        droperationresult.ElapsedTime = Convert.IsDBNull(myDROperationResultReader["ElapsedTime"]) ? string.Empty : Convert.ToString(myDROperationResultReader["ElapsedTime"]);
                        droperationresult.Status = Convert.IsDBNull(myDROperationResultReader["Status"]) ? string.Empty : Convert.ToString(myDROperationResultReader["Status"]);
                        droperationresult.DROperationId = Convert.IsDBNull(myDROperationResultReader["DROperationId"]) ? 0 : Convert.ToInt32(myDROperationResultReader["DROperationId"]);
                        droperationresult.ActionId = Convert.IsDBNull(myDROperationResultReader["ActionId"]) ? 0 : Convert.ToInt32(myDROperationResultReader["ActionId"]);
                        droperationresult.Message = Convert.IsDBNull(myDROperationResultReader["Message"]) ? string.Empty : Convert.ToString(myDROperationResultReader["Message"]);
                        droperationresult.CreateDate = Convert.IsDBNull(myDROperationResultReader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["CreateDate"]);
            
                        return droperationresult;
                         
                    }
                     
                }
                return null;
            }

        }

        public static DROperationResult DROperationResult_GetByActionAndStatus(string Action,int droptid)
        {
            var droperationresult = new DROperationResult();

            const string sp = "DROperationResult_GetByActionAndStatus";
    

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {

                Database.AddInParameter(cmd, Dbstring+"iActionName", DbType.String, Action);
                Database.AddInParameter(cmd, Dbstring+"iDROperationId", DbType.Int32, droptid);
                using (IDataReader myDROperationResultReader = Database.ExecuteReader(cmd))
                {
                    if (myDROperationResultReader.Read())
                    {
                        droperationresult.Id = Convert.IsDBNull(myDROperationResultReader["Id"]) ? 0 : Convert.ToInt32(myDROperationResultReader["Id"]);
                        droperationresult.ActionName = Convert.IsDBNull(myDROperationResultReader["ActionName"]) ? string.Empty : Convert.ToString(myDROperationResultReader["ActionName"]);
                        droperationresult.StartTime = Convert.IsDBNull(myDROperationResultReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["StartTime"]);
                        droperationresult.EndTime = Convert.IsDBNull(myDROperationResultReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["EndTime"]);
                        droperationresult.ElapsedTime = Convert.IsDBNull(myDROperationResultReader["ElapsedTime"]) ? string.Empty : Convert.ToString(myDROperationResultReader["ElapsedTime"]);
                        droperationresult.Status = Convert.IsDBNull(myDROperationResultReader["Status"]) ? string.Empty : Convert.ToString(myDROperationResultReader["Status"]);
                        droperationresult.DROperationId = Convert.IsDBNull(myDROperationResultReader["DROperationId"]) ? 0 : Convert.ToInt32(myDROperationResultReader["DROperationId"]);
                        droperationresult.ActionId = Convert.IsDBNull(myDROperationResultReader["ActionId"]) ? 0 : Convert.ToInt32(myDROperationResultReader["ActionId"]);
                        droperationresult.Message = Convert.IsDBNull(myDROperationResultReader["Message"]) ? string.Empty : Convert.ToString(myDROperationResultReader["Message"]);
                        droperationresult.CreateDate = Convert.IsDBNull(myDROperationResultReader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationResultReader["CreateDate"]);
            
                        return droperationresult;

                    }

                }
                return null;
            }

        }
    }
}