﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class DROperationDataAccess : BaseDataAccess
    {
        public static DROperation AddDROperation(DROperation drOperation)
        {
            const string SP = "DROperation_Create";

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                db.AddInParameter(cmd, Dbstring+"iType", DbType.Int32, drOperation.Type);
                db.AddInParameter(cmd, Dbstring+"iStartTime", DbType.DateTime, drOperation.StartTime);
                // db.AddInParameter(cmd, Dbstring+"iEndTime", DbType.DateTime, drOperation.EndTime); // commented by surendra
                db.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, drOperation.Status);
                db.AddInParameter(cmd, Dbstring+"iWorkflowId", DbType.Int32, drOperation.WorkflowId);
                db.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, drOperation.GroupId);
                db.AddInParameter(cmd, Dbstring+"iDirection", DbType.Int32, drOperation.Direction);
                // db.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, drOperation.CreatorId);

                using (IDataReader myDROperationReader = db.ExecuteReader(cmd))
                {
                    if (myDROperationReader.Read())
                    {
                        drOperation.Id = Convert.ToInt32(myDROperationReader[0]);
                    }
                    else
                    {
                        drOperation = null;
                    }
                }

            }

            return drOperation;
        }

        public static bool UpdateDROperation(DROperation drOperation)
        {
            const string sp = "DROperation_UpdtByStatusnEndTm";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, drOperation.Id);

                Database.AddInParameter(cmd, Dbstring+"iEndTime", DbType.DateTime, drOperation.EndTime);

                Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, drOperation.Status);

                var returnCode = Database.ExecuteNonQuery(cmd);

                return returnCode > 0;

            }
        }

        public static bool UpdateDROperationByConditional(int drId, int conditonal)
        {
            const string sp = "DROperation_UpdtByConditional";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, drId);

                Database.AddInParameter(cmd, Dbstring+"iConditonal", DbType.Int32, conditonal);

                var returnCode = Database.ExecuteNonQuery(cmd);

                return returnCode > 0;

            }
        }



        public static DROperation GetLastDROperation(int groupId)
        {

            DROperation drOperation = new DROperation();


            using (DbCommand cmd = Database.GetStoredProcCommand("DROperation_GetLastByInfraID"))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectID", DbType.Int32, groupId);

                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {
                        drOperation.Id = Convert.IsDBNull(myDROperationReader1["Id"]) ? 0 : Convert.ToInt32(myDROperationReader1["Id"]);
                        drOperation.Type = Convert.IsDBNull(myDROperationReader1["Type"]) ? 0 : Convert.ToInt32(myDROperationReader1["Type"]);
                        drOperation.StartTime = Convert.IsDBNull(myDROperationReader1["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["StartTime"]);
                        drOperation.EndTime = Convert.IsDBNull(myDROperationReader1["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["EndTime"]);
                        drOperation.Status = Convert.IsDBNull(myDROperationReader1["Status"]) ? string.Empty : Convert.ToString(myDROperationReader1["Status"]);
                        drOperation.ConditionalOperation = Convert.IsDBNull(myDROperationReader1["ConditionalOperation"]) ? 0 : Convert.ToInt32(myDROperationReader1["ConditionalOperation"]);
                        drOperation.WorkflowId = Convert.IsDBNull(myDROperationReader1["WorkflowId"]) ? 0 : Convert.ToInt32(myDROperationReader1["WorkflowId"]);
                        drOperation.GroupId = Convert.IsDBNull(myDROperationReader1["InfraObjectId"]) ? 0 : Convert.ToInt32(myDROperationReader1["InfraObjectId"]);
                        drOperation.Direction = Convert.IsDBNull(myDROperationReader1["Direction"]) ? 0 : Convert.ToInt32(myDROperationReader1["Direction"]);
                        drOperation.ActionMode = (ActionMode)Convert.ToInt32(myDROperationReader1["ActionMode"]);

                    }
                    else
                    {
                        drOperation = null;
                    }
                }

                return drOperation;
            }

        }

        public static DROperation GetDROperationById(int id)
        {

            var drOperation = new DROperation();

            using (DbCommand cmd = Database.GetStoredProcCommand("DROperation_GetById"))
            {
                Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {
                        drOperation.Id = Convert.IsDBNull(myDROperationReader1["Id"]) ? 0 : Convert.ToInt32(myDROperationReader1["Id"]);
                        drOperation.Type = Convert.IsDBNull(myDROperationReader1["Type"]) ? 0 : Convert.ToInt32(myDROperationReader1["Type"]);
                        drOperation.StartTime = Convert.IsDBNull(myDROperationReader1["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["StartTime"]);
                        drOperation.EndTime = Convert.IsDBNull(myDROperationReader1["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["EndTime"]);
                        drOperation.Status = Convert.IsDBNull(myDROperationReader1["Status"]) ? string.Empty : Convert.ToString(myDROperationReader1["Status"]);
                        drOperation.ConditionalOperation = Convert.IsDBNull(myDROperationReader1["ConditionalOperation"]) ? 0 : Convert.ToInt32(myDROperationReader1["ConditionalOperation"]);
                        drOperation.WorkflowId = Convert.IsDBNull(myDROperationReader1["WorkflowId"]) ? 0 : Convert.ToInt32(myDROperationReader1["WorkflowId"]);
                        drOperation.GroupId = Convert.IsDBNull(myDROperationReader1["InfraObjectId"]) ? 0 : Convert.ToInt32(myDROperationReader1["InfraObjectId"]);
                        drOperation.Direction = Convert.IsDBNull(myDROperationReader1["Direction"]) ? 0 : Convert.ToInt32(myDROperationReader1["Direction"]);
                        drOperation.ActionMode = (ActionMode)Convert.ToInt32(myDROperationReader1["ActionMode"]);

                    }
                    else
                    {
                        drOperation = null;
                    }
                }

                return drOperation;
            }

        }

    }
}