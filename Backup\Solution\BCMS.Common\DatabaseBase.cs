﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseBase", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseBase : BaseEntity
    {

        #region Member Variables

        DatabaseExchange _databaseExchange = new DatabaseExchange();

        DatabaseOracle _databaseOracle = new DatabaseOracle();

        DatabaseSql _databaseSql = new DatabaseSql();
        DatabaseDB2 _databaseDb2 = new DatabaseDB2();
        #endregion


        #region Properties
        [DataMember]
        public int PRDBId
        {
            get;
            set;
        }

        [DataMember]
        public int DRDBId
        {
            get;
            set;
        }
        [DataMember]
        public string PRName
        {
            get;
            set;
        }

        [DataMember]
        public string DRName
        {
            get;
            set;
        }

        [DataMember]
        public DatabaseType DatabaseType
        {
            get;
            set;
        }


        [DataMember]
        public DatabaseType PRDatabaseType
        {
            get;
            set;
        }

        [DataMember]
        public DatabaseType DRDatabaseType
        {
            get;
            set;
        }

        [DataMember]
        public string PRVersion
        {
            get;
            set;
        }

        [DataMember]
        public string DRVersion
        {
            get;
            set;
        }


        [DataMember]
        public string Type
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }
        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }


        [DataMember]
        public bool PRIsPartofRac
        {
            get;
            set;
        }
        [DataMember]
        public bool DRIsPartofRac
        {
            get;
            set;
        }

        [DataMember]
        public DatabaseMode Mode
        {
            get;
            set;

        }

        [DataMember]
        public DatabaseMode PRMode
        {
            get;
            set;

        }

        [DataMember]
        public DatabaseMode DRMode
        {
            get;
            set;
        }


        [DataMember]
        public DatabaseExchange DatabaseExchange
        {
            get
            {
               
                return _databaseExchange;
            }
            set
            {
                _databaseExchange = value;
            }
        }



        [DataMember]
        public DatabaseOracle DatabaseOracle
        {
            get
            {
                return _databaseOracle;
            }
            set
            {
                _databaseOracle = value;
            }
        }


        [DataMember]
        public DatabaseSql DatabaseSql
        {
            get
            {
                return _databaseSql;
            }
            set
            {
                _databaseSql = value;
            }
        }

        [DataMember]
        public DatabaseDB2 DatabaseDb2
        {
            get
            {
                return _databaseDb2;
            }
            set
            {
                _databaseDb2 = value;
            }
        }

        #endregion

        #region Constructor

        public DatabaseBase()
            : base()
        {
        }

        #endregion
    }
}
