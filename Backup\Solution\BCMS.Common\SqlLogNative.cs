﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;


namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "sqllognative", Namespace = "http://www.BCMS.com/types")]
    public class SqlLogNative : BaseEntity
    {
        #region Properties

        public int GroupId
        {
            get;
            set;
        }

        public string PRServer
        {
            get;
            set;
        }

        public string DRServer
        {
            get;
            set;
        }

        public string DatabaseName
        {
            get;
            set;
        }

        public string LastGenLog
        {
            get;
            set;
        }

        public string LastApplyLog
        {
            get;
            set;
        }

        public string LastCopiedLog
        {
            get;
            set;
        }

        public string LSNlastbackupLog
        {
            get;
            set;
        }

        public string LSNlastrestoredLog
        {
            get;
            set;
        }

        public string LSNLastCopiedLog
        {
            get;
            set;
        }

        public string LastLogGenTime
        {
            get;
            set;
        }

        public string LastLogApplTime
        {
            get;
            set;
        }

        public string LastLogCopyTime
        {
            get;
            set;
        }

        public string DataLag
        {
            get;
            set;
        }
        #endregion

        #region Constructor
        public SqlLogNative() : base()
            {

            }
        #endregion

    }
}
