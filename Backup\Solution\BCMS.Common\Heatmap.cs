﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
     [Serializable]
    [DataContract(Name = "Alert", Namespace = "http://www.ContinuityVault.com/types")]
 
    public class Heatmap:BaseEntity
    {
         #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int EntityId
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessServiceId
        {
            get;
            set;
        }

        [DataMember]
        public string HeatmapType
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public bool IsAffected
        {
            get;
            set;
        }


        #endregion
    }
}
