﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bcms.Replication.Shared;
using Jscape.Ssh;
using PGlobalMirror;
using log4net;

namespace Bcms.Replication
{
    public class OracleRacClient
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(OracleRacClient));
        public static bool AlterSystemSetLogArchiveDestDefer(SSHInfo serverPRInfo, string PRsudouser, string PRDBName, SSHInfo serverDRInfo, string DRsudouser, string DRDBName, int PRorDR)
        {
            SshSession racdrsession, racprsession;
            try
            {

                // for IDEA


                string sshprompt = "\\$|#|>";
                string dbuniquename = string.Empty;
                string proutput2 = string.Empty;

                racdrsession = new SshSession(serverDRInfo.SSHHost, serverDRInfo.SSHUser, serverDRInfo.SSHPass) { LicenseKey = Bcms.Replication.Shared.Constants.QueryConstants.LicenseKey };
                racdrsession.SetShellPrompt("\\$|#|>", true);
                racdrsession.Connect(3000);

                Logger.Info("Command : sudo su - " + DRsudouser);
                string output1 = racdrsession.Send("sudo su - " + DRsudouser);
                Logger.Info("Output :" + output1);

                Logger.Info("Command : " + Constants.QueryConstants.ExportOracle + DRDBName);
                string cmd = Constants.QueryConstants.ExportOracle + DRDBName;
                output1 = racdrsession.Send(cmd);
                Logger.Info("\r\nOutput :" + output1);

                Logger.Info("Command : " + Constants.QueryConstants.ConnectDbaSingle);
                output1 = racdrsession.SendWait(Constants.QueryConstants.ConnectDbaSingle, Constants.QueryConstants.GreaterThan);
                Logger.Info("\r\nOutput :" + output1);

                if (output1.Contains("Connected"))
                {
                    Logger.Info("Command : " + Constants.QueryConstants.discoveryqueryondr);
                    string output = racdrsession.SendWait(Constants.QueryConstants.discoveryqueryondr,
                                                          Constants.QueryConstants.GreaterThan, 600000);
                    Logger.Info("output : " + output);
                    dbuniquename = output;
                    if (!string.IsNullOrEmpty(output) && output.Contains("DB_UNIQUE_NAME"))
                    {

                        dbuniquename = dbuniquename.Replace("DB_UNIQUE_NAME", "");
                        dbuniquename = dbuniquename.Replace("-", "");
                        dbuniquename = dbuniquename.Replace("\r", "");
                        dbuniquename = dbuniquename.Replace("\n", "");
                        dbuniquename = dbuniquename.Replace(" ", "");
                        dbuniquename = dbuniquename.Replace("SQL>", "");



                        //racdrsession.Disconnect();

                        // on pr server

                        racprsession = new SshSession(serverPRInfo.SSHHost, serverPRInfo.SSHUser, serverPRInfo.SSHPass) { LicenseKey = Bcms.Replication.Shared.Constants.QueryConstants.LicenseKey };
                        racprsession.SetShellPrompt("\\$|#|>", true);
                        racprsession.Connect(3000);

                        Logger.Info("Command : sudo su - " + PRsudouser);
                        output1 = racprsession.Send("sudo su - " + PRsudouser, 600000);
                        Logger.Info("\r\nOutput :" + output1);

                        Logger.Info("Command : " + Constants.QueryConstants.ExportOracle + PRDBName);
                        output1 = racprsession.Send(Constants.QueryConstants.ExportOracle + PRDBName, 600000);
                        Logger.Info("\r\nOutput :" + output1);

                        Logger.Info("Command : " + Constants.QueryConstants.ConnectDbaSingle);
                        output1 = racprsession.SendWait(Constants.QueryConstants.ConnectDbaSingle, Constants.QueryConstants.GreaterThan);
                        Logger.Info("\r\nOutput :" + output1);

                        if (output1.Contains("Connected"))
                        {
                            string command = Constants.QueryConstants.discoveryqueryonprdeffer + "'" + dbuniquename +
                                             "' and target='STANDBY';";
                            Logger.Info("Command : " + command);

                            string proutput = racprsession.SendWait(command, Constants.QueryConstants.GreaterThan, 600000);
                            Logger.Info("\r\nOutput : " + proutput);
                            string prquery = proutput;


                            if (!string.IsNullOrEmpty(proutput) &&
                                proutput.Contains("ALTERSYSTEMSETLOG_ARCHIVE_DEST_STATE_'||DEST_ID||'=DEFER"))
                            {

                                prquery = prquery.Replace("ALTERSYSTEMSETLOG_ARCHIVE_DEST_STATE_'||DEST_ID||'=DEFER", "");
                                prquery = prquery.Replace("'", "");
                                prquery = prquery.Replace("-", "");
                                prquery = prquery.Replace("\r", "");
                                prquery = prquery.Replace("\n", "");

                                prquery = prquery.Replace("SQL>", "");
                                prquery = prquery.Replace(";", "");


                                if (PRorDR == 1)
                                {
                                    racdrsession.Disconnect();

                                    Logger.Info("Command : " + prquery + ";");

                                    proutput2 = racprsession.SendWait(prquery + ";", Constants.QueryConstants.GreaterThan,
                                                                      600000);

                                    Logger.Info("\r\nOutput : " + proutput2);
                                }
                                else
                                {
                                    if (PRorDR == 2)
                                    {
                                        racprsession.Disconnect();

                                        Logger.Info("Command : " + prquery + ";");

                                        proutput2 = racdrsession.SendWait(prquery + ";", Constants.QueryConstants.GreaterThan,
                                                                          600000);

                                        Logger.Info("\r\nOutput : " + proutput2);
                                    }

                                }
                            }
                        }
                    }

                    if (proutput2.Contains("System altered"))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }

            }
            catch (Exception ex)
            {
                //  racdrsession.Disconnect();
                // racprsession.Disconnect();

                Logger.Info("Exception in AlterSystemSetLogArchiveDestDefer Action :" + ex.Message);
                Logger.Info("Exception in AlterSystemSetLogArchiveDestDefer Action :" + ex.InnerException.ToString());
                return false;
            }
        }

        public static bool AlterSystemSetLogArchiveDestEnable(SSHInfo serverPRInfo, string PRsudouser, string PRDBName, SSHInfo serverDRInfo, string DRsudouser, string DRDBName, int PRorDR)
        {
            SshSession racdrsession, racprsession;
            try
            {

                // for IDEA


                string sshprompt = "\\$|#|>";
                string dbuniquename = string.Empty;
                string proutput2 = string.Empty;

                racdrsession = new SshSession(serverDRInfo.SSHHost, serverDRInfo.SSHUser, serverDRInfo.SSHPass) { LicenseKey = Bcms.Replication.Shared.Constants.QueryConstants.LicenseKey };
                racdrsession.SetShellPrompt("\\$|#|>", true);
                racdrsession.Connect(3000);

                Logger.Info("Command : sudo su - " + DRsudouser);
                string output1 = racdrsession.Send("sudo su - " + DRsudouser, 600000);
                Logger.Info("Output :" + output1);

                Logger.Info("Command : " + Constants.QueryConstants.ExportOracle + DRDBName);
                output1 = racdrsession.Send(Constants.QueryConstants.ExportOracle + DRDBName, 600000);
                Logger.Info("\r\nOutput :" + output1);

                Logger.Info("Command : " + Constants.QueryConstants.ConnectDbaSingle);
                output1 = racdrsession.SendWait(Constants.QueryConstants.ConnectDbaSingle, Constants.QueryConstants.GreaterThan);
                Logger.Info("\r\nOutput :" + output1);

                if (output1.Contains("Connected"))
                {
                    Logger.Info("Command : " + Constants.QueryConstants.discoveryqueryondr);
                    string output = racdrsession.SendWait(Constants.QueryConstants.discoveryqueryondr,
                                                          Constants.QueryConstants.GreaterThan, 600000);
                    Logger.Info("output : " + output);
                    dbuniquename = output;
                    if (!string.IsNullOrEmpty(output) && output.Contains("DB_UNIQUE_NAME"))
                    {

                        dbuniquename = dbuniquename.Replace("DB_UNIQUE_NAME", "");
                        dbuniquename = dbuniquename.Replace("-", "");
                        dbuniquename = dbuniquename.Replace("\r", "");
                        dbuniquename = dbuniquename.Replace("\n", "");
                        dbuniquename = dbuniquename.Replace(" ", "");
                        dbuniquename = dbuniquename.Replace("SQL>", "");


                        //racdrsession.Disconnect();

                        // on pr server

                        racprsession = new SshSession(serverPRInfo.SSHHost, serverPRInfo.SSHUser, serverPRInfo.SSHPass) { LicenseKey = Bcms.Replication.Shared.Constants.QueryConstants.LicenseKey };
                        racprsession.SetShellPrompt("\\$|#|>", true);
                        racprsession.Connect(3000);

                        Logger.Info("Command : sudo su - " + PRsudouser);
                        output1 = racprsession.Send("sudo su - " + PRsudouser, 600000);
                        Logger.Info("\r\nOutput :" + output1);

                        Logger.Info("Command : " + Constants.QueryConstants.ExportOracle + PRDBName);
                        output1 = racprsession.Send(Constants.QueryConstants.ExportOracle + PRDBName, 600000);
                        Logger.Info("\r\nOutput :" + output1);

                        Logger.Info("Command : " + Constants.QueryConstants.ConnectDbaSingle);
                        output1 = racprsession.SendWait(Constants.QueryConstants.ConnectDbaSingle,
                                                        Constants.QueryConstants.GreaterThan);
                        Logger.Info("\r\nOutput :" + output1);

                        if (output1.Contains("Connected"))
                        {
                            string command = Constants.QueryConstants.discoveryqueryonprenable + "'" + dbuniquename +
                                             "' and target='STANDBY';";
                            Logger.Info("Command : " + command);

                            string proutput = racprsession.SendWait(command, Constants.QueryConstants.GreaterThan,
                                                                    600000);
                            Logger.Info("\r\nOutput : " + proutput);
                            string prquery = proutput;


                            if (!string.IsNullOrEmpty(proutput) &&
                                proutput.Contains("ALTERSYSTEMSETLOG_ARCHIVE_DEST_STATE_'||DEST_ID||'=ENABLE"))
                            {

                                prquery = prquery.Replace("ALTERSYSTEMSETLOG_ARCHIVE_DEST_STATE_'||DEST_ID||'=ENABLE",
                                                          "");
                                prquery = prquery.Replace("'", "");
                                prquery = prquery.Replace("-", "");
                                prquery = prquery.Replace("\r", "");
                                prquery = prquery.Replace("\n", "");

                                prquery = prquery.Replace("SQL>", "");
                                prquery = prquery.Replace(";", "");

                                if (PRorDR == 1)
                                {
                                    racdrsession.Disconnect();

                                    Logger.Info("Command : " + prquery + ";");
                                    Logger.Info("Command Run on :" + serverPRInfo.SSHHost);
                                    proutput2 = racprsession.SendWait(prquery + ";",
                                                                      Constants.QueryConstants.GreaterThan,
                                                                      600000);

                                    Logger.Info("\r\nOutput : " + proutput2);
                                }
                                else
                                {
                                    if (PRorDR == 2)
                                    {
                                        racprsession.Disconnect();

                                        Logger.Info("Command : " + prquery + ";");
                                        Logger.Info("Command Run on :" + serverDRInfo.SSHHost);
                                        proutput2 = racdrsession.SendWait(prquery + ";",
                                                                          Constants.QueryConstants.GreaterThan,
                                                                          600000);

                                        Logger.Info("\r\nOutput : " + proutput2);
                                    }

                                }
                            }

                        }
                    }
                    if (proutput2.Contains("System altered"))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }

            }
            catch (Exception ex)
            {
                //  racdrsession.Disconnect();
                // racprsession.Disconnect();

                Logger.Info("Exception in AlterSystemSetLogArchiveDestEnable Action :" + ex.Message);
                Logger.Info("Exception in AlterSystemSetLogArchiveDestEnable Action :" + ex.InnerException.ToString());
                return false;
            }
        }
    }
}
