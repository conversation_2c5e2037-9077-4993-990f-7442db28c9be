﻿ namespace Bcms.Core
{
    using System;
    using log4net;

    /// <summary> 
    /// The bcms server factory.
    /// </summary>
    public class BcmsServerFactory
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private static readonly ILog Logger = LogManager.GetLogger(typeof(BcmsServerFactory));
     
        /// <summary>
        /// The create server.
        /// </summary>
        /// <returns>
        /// The <see cref="IBcmsCore"/>.
        /// </returns>
        public static IBcmsCore CreateServer()
        {
            string typeName = Configuration.Configuration.ServerImplementationType;
            var t = Type.GetType(typeName, true);
            Logger.Info("Creating new instance of server type '" + typeName + "'");
            if (t != null)
            {
                var retValue = (IBcmsCore)Activator.CreateInstance(t);
                Logger.Info("Bcms ServerInstance successfully created");
                return retValue;
            }

            return null;
        } 
    }
}