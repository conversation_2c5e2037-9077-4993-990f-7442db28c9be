﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.DataAccess.Utility;
using BCMS.Common;


namespace Bcms.DataAccess
{
    public class ExchangeDAGRepilcationMonitorDataAccess : BaseDataAccess
    {
        #region IExchangeDataAccess Members

        public static bool AddDAGRepilcationMonitor(ExchangeDAGReplicationMonitor ReplicationMonitor)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand(DbRoleName + "EXCDAG_REPMONITOR_CREATE"))
                {

                    Database.AddInParameter(dbCommand, Dbstring+"iREPLICATIONID", DbType.Int32, ReplicationMonitor.ReplicationId);
                    Database.AddInParameter(dbCommand, Dbstring+"iINFRAOBJECTID", DbType.Int32, ReplicationMonitor.InfraobjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iPRMAILBOXDATABASENAME", DbType.AnsiString, ReplicationMonitor.PRMailboxDatabaseName);
                    Database.AddInParameter(dbCommand, Dbstring+"iDRMAILBOXDATABASENAME", DbType.AnsiString, ReplicationMonitor.DRMailboxDatabaseName);
                    Database.AddInParameter(dbCommand, Dbstring+"iCOPYQUEUELENGTH", DbType.AnsiString, ReplicationMonitor.CopyQueueLength);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLAYQUEUELENGTH", DbType.AnsiString, ReplicationMonitor.ReplayQueueLength);
                    Database.AddInParameter(dbCommand, Dbstring+"iREPLICATIONSTATUS", DbType.AnsiString, ReplicationMonitor.ReplicationStatus);
                    Database.AddInParameter(dbCommand, Dbstring+"iDAGNAME", DbType.AnsiString, ReplicationMonitor.DAGName);
                    Database.AddInParameter(dbCommand, Dbstring+"iIPADDRESS", DbType.AnsiString, ReplicationMonitor.IPAddress);
                    Database.AddInParameter(dbCommand, Dbstring+"iWITNESSSERVER", DbType.AnsiString, ReplicationMonitor.WitnessServer);
                   // Database.AddInParameter(dbCommand, Dbstring+"iWITNESSDIRECTORY", DbType.AnsiString, ReplicationMonitor.Witnessd);
                    Database.AddInParameter(dbCommand, Dbstring+"iServerId", DbType.AnsiString, ReplicationMonitor.ServerId);
                    Database.AddInParameter(dbCommand, Dbstring+"iDatabaseId", DbType.AnsiString, ReplicationMonitor.DatabaseId);
                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occured while insert AddDAGRepilcationMonitor information", exc);
            }
            return false;
        }

        ///// <summary>
        ///// Get <see cref="ExchangeService"/> From bcms_exchange_healthstatus table by Id.
        ///// </summary>
        ///// <param name="groupid">Id of the ExchangeHealth</param>
        ///// <returns>ExchangeHealth</returns>
        ///// <author>Kiran Ghadge</author>
        /////
        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupId(int groupid)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeHealth_GetByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}

        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupIdandPrDatabaseId(int groupid, int prdatabaseid)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeDAGComponantMonitor_GetByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            Database.AddInParameter(cmd, Dbstring+"iDataBaseId", DbType.Int32, prdatabaseid);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}

        //ExchangeDAGComponantMonitor IExchangeDAGCompMonitorDataAccess.GetByGroupIdandMailBoxName(int groupid,string mailboxname)
        //{
        //    try
        //    {
        //        if (groupid < 1)
        //        {
        //            throw new ArgumentNullException("groupid");
        //        }

        //        const string sp = "ExchangeDAGComponantMonitor_GetByGroupIdAndMailBoxName";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            Database.AddInParameter(cmd, Dbstring+"iMailBoxName", DbType.String, mailboxname);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                if (reader.Read())
        //                {
        //                    return (CreateEntityBuilder<ExchangeDAGComponantMonitor>()).BuildEntity(reader, new ExchangeDAGComponantMonitor());
        //                }
        //                return null;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetByGroupId(" + groupid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}
        ///// <summary>
        ///// Get <see cref="ExchangeService"/> From bcms_exchange_healthstatus table.
        ///// </summary>
       
        ///// <returns>ExchangeHealth List</returns>
        ///// <author>Kiran Ghadge</author>
        /////
        ///// 
        ///// 
        
        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByGroupIdGetAll(int groupid)
        //{
        //    try
        //    {
        //        const string sp = "ExchangeDAGComponatMonitor_GetAllByGroupId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        { 
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupid);
        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
                    
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetAll" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }

        //}

        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetAll()
        //{
        //    try
        //    {
        //        const string sp = "ExchangeHealthStatus_GetAll";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.GetAll" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }

        //}

        //IList<ExchangeDAGComponantMonitor> IExchangeDAGCompMonitorDataAccess.GetByDate(int groupId, string startDate, string endDate)
        //{

        //    try
        //    {
        //        const string sp = "ExchangeSCRSLog_GetByDate";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupId);
        //            Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.AnsiString, startDate);
        //            Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.AnsiString, endDate);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<ExchangeDAGComponantMonitor>().BuildEntities(reader);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IOraleLogDataAccess.GetByDate(" + groupId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}
       
        #endregion        
    }
}
