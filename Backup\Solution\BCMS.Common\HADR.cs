﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ApplicationDetail", Namespace = "http://www.BCMS.com/types")]
    public class HADR : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRIp
        {
            get;
            set;
        }

        [DataMember]
        public string DRIp
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseInstance
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseInstance
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogFile
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogFile
        {
            get;
            set;
        }

        [DataMember]
        public string PRCurrentLSN
        {
            get;
            set;
        }
        [DataMember]
        public string DRCurrentLSN
        {
            get;
            set;
        }

        [DataMember]
        public string PRLSN
        {
            get;
            set;
        }
        [DataMember]
        public string DRLSN
        {
            get;
            set;
        }

        [DataMember]
        public string PRTimestamp
        {
            get;
            set;
        }
        [DataMember]
        public string DRTimestamp
        {
            get;
            set;
        }
        [DataMember]
        public string Datalag
        {
            get;
            set;
        }
       
        #endregion
    }
}
