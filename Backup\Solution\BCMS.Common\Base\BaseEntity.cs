﻿using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Xml.Serialization;

namespace Bcms.Common.Base
{
    [Serializable]
    public abstract class BaseEntity : ICloneable
    {
        [DataMember]
        public virtual int Id { get; set; }

        [DataMember]
        public bool IsRemoved { get; set; }

        [DataMember]
        public DateTime CreateDate { get; set; }

        [DataMember]
        public DateTime UpdateDate { get; set; }

        [DataMember]
        public bool IsActive { get; set; }

        [DataMember]
        public int CreatorId { get; set; }

        [DataMember]
        public int UpdatorId { get; set; }

        [XmlIgnore()]
        public virtual bool IsNew
        {
            [DebuggerStepThrough]
            get { return (Id <= 0); }
        }
        protected BaseEntity()
        {
            CreateDate = UpdateDate = DateTime.Now;
        }

        object ICloneable.Clone()
        {
            return Clone();
        }

        public virtual BaseEntity Clone()
        {
            using (var ms = new MemoryStream())
            {
                (new BinaryFormatter()).Serialize(ms, this);

                ms.Seek(0, SeekOrigin.Begin);

                return (new BinaryFormatter()).Deserialize(ms) as BaseEntity;
            }
        }
    }
}
