﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAWorkflowAndActionIdDataAcces : BaseDataAccess
    {
        // insert records into  BIAWorkflowAndActionId BIA table
        public static void AddBIAWorkflowAndActionId(IList<BIAWorkflowAndActionID> AllBIAWorkflowAndActionId)
        {
            try
            {
                foreach (var _BIAWorkflowAndActionId in AllBIAWorkflowAndActionId)
                {
                    using (DbCommand cmd = Database.GetStoredProcCommand("BIAWorkflowAndActionId_Create"))
                    {
                        Database.AddInParameter(cmd, Dbstring + "iActionID", DbType.Int32, _BIAWorkflowAndActionId.ActionID);
                        Database.AddInParameter(cmd, Dbstring + "iWorkflowID", DbType.Int32, _BIAWorkflowAndActionId.WorkflowID);
                        Database.AddInParameter(cmd, Dbstring + "iWorkflowCreateDate", DbType.DateTime, _BIAWorkflowAndActionId.WorkflowCreateDate);
                        Database.AddInParameter(cmd, Dbstring + "iWorkflowUpdateDate", DbType.DateTime, _BIAWorkflowAndActionId.WorkflowUpdateDate);
                        int value = Database.ExecuteNonQuery(cmd);

                    }

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlLogHealth Create entry ", exc);
            }


        }
    }
}
