﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ActionExecution", Namespace = "http://www.BCMS.com/types")]
    public class Infraobject_Scheduler : BaseEntity
    {
        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public int WorkFlowId
        {
            get;
            set;
        }

        [DataMember]
        public string WorkFlowName
        {
            get;
            set;
        }

        [DataMember]
        public int ScheduleType
        {
            get;
            set;
        }

        [DataMember]
        public string ScheduleTime
        {
            get;
            set;
        }

        [DataMember]
        public int IsEnable
        {
            get;
            set;
        }

        [DataMember]
        public int IsSchedule
        {
            get;
            set;
        }

    }
}
