﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseVersion", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseVersion : BaseEntity
    {
        #region Properties

       [DataMember]
        public int DataBaseTypeId
        {
            get;
            set;
        }

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public DatabaseVersion()
            : base()
        {
        }

        #endregion
    }
}
