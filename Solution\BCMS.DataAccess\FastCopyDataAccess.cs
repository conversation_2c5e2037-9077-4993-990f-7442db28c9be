﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using BCMS.Common;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class FastCopyDataAccess : BaseDataAccess
    {
        public static FastCopy GetByReplicationId(int id)
        {
            try
            {
                var fast = new FastCopy();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPY_GETBYREPLICATIONID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            fast.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            fast.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                                ? 0
                                : Convert.ToInt32(reader["ReplicationId"]);
                            if (!Convert.IsDBNull(reader["IsCompression"]))
                                fast.IsCompression = Convert.ToBoolean(reader["IsCompression"]);

                            if (!Convert.IsDBNull(reader["IsFilter"]))
                                fast.IsFilter = Convert.ToBoolean(reader["IsFilter"]);

                            fast.Wildcard = Convert.IsDBNull(reader["WildCard"]) ? string.Empty : Convert.ToString(reader["WildCard"]);
                            fast.DataSyncPath = Convert.IsDBNull(reader["DataSyncPath"]) ? string.Empty : Convert.ToString(reader["DataSyncPath"]);
                            fast.ProcessCode = Convert.IsDBNull(reader["ProcessCode"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessCode"]);
                            fast.LocalDirectory = Convert.IsDBNull(reader["LocalDirectory"])
                                ? string.Empty
                                : Convert.ToString(reader["LocalDirectory"]);
                            fast.RemoteDirectory = Convert.IsDBNull(reader["RemoteDirectory"])
                                ? string.Empty
                                : Convert.ToString(reader["RemoteDirectory"]);
                            fast.OSPlatform = Convert.IsDBNull(reader["OSPlatform"])
                                ? string.Empty
                                : Convert.ToString(reader["OSPlatform"]);
                            fast.JrePath = Convert.IsDBNull(reader["DataSyncJREPath"])
                              ? string.Empty
                              : Convert.ToString(reader["DataSyncJREPath"]);
                            fast.ScheduleTime = Convert.IsDBNull(reader["ScheduleTime"])
                                ? string.Empty
                                : Convert.ToString(reader["ScheduleTime"]);
                            fast.LastReplicationCount = Convert.IsDBNull(reader["LastReplicationCount"])
                                ? 0
                                : Convert.ToInt32(reader["LastReplicationCount"]);
                            fast.ModeType = Convert.IsDBNull(reader["ModeType"])
                                ? FastCopyMode.Undefined
                                : (FastCopyMode)Enum.Parse(typeof(FastCopyMode), Convert.ToString(reader["ModeType"]), true);
                            fast.AccesstoSudoUser = Convert.IsDBNull(reader["ACCESSTOSUDOUSER"]) ? 0 : Convert.ToInt32(reader["ACCESSTOSUDOUSER"]);
                            if (!Convert.IsDBNull(reader["IsExcMultiInstance"]))
                                fast.IsExcMultiInstance = Convert.ToBoolean(reader["IsExcMultiInstance"]);
                            fast.ISRECOVER = Convert.IsDBNull(reader["ISRECOVER"])
                          ? 0
                          : Convert.ToInt32(reader["ISRECOVER"]);
                        }
                    }
                }

                return fast;
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,

                    "Error In DAL While Executing Function Signature IFastCopyDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        public static bool UpdateFastCopyIsRecoveryById(int mode, int fastCopyId)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("FASTCOPYJOB_UPDATEISRECOVERY"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iISRECOVER", DbType.Int32, mode);
                    Database.AddInParameter(dbCommand, Dbstring + "iFastCopyId", DbType.Int32, fastCopyId);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update FastCopy IsRecovery By Id", exc);
            }
        }
    }
}
