﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.DataAccess.Utility;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class SqlNative2008HealthDataAccess : BaseDataAccess
    {

        public static bool AddSqlNative2008HealthLogs(SqlNative2008Health sqlnativeHealth)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTH_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SQLNATIVEHEALTH_CREATE SqlLogHealth Create entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNative2008HealthStatus(SqlNative2008Health sqlnativeHealth)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTHMONSTAT_CRTUPD"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);

                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SQLNATIVEHEALTHMONSTAT_CRTUPD SqlLogHealth Create entry ", exc);
            }
            return false;
        }

        public static bool UpdateSqlNative2008Health(SqlNative2008Health sqlnativeHealth)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("SQLNATIVEHEALTHMONSTAT_CRTUPD"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqlnativeHealth.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Database_State_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_State_DR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_PR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_PR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_DB_REC_MODEL_DR", DbType.AnsiString, sqlnativeHealth.MSSQL_Database_Recovery_Model_DR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_PR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iTRAN_LOG_SHIPPING_STAT_DR", DbType.AnsiString, sqlnativeHealth.Transaction_Log_Shipping_status_DR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_PR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_PR);
                    Database.AddInParameter(cmd, Dbstring + "iDB_RESTRICT_ACCESS_STAT_DR", DbType.AnsiString, sqlnativeHealth.Database_Restrict_Access_Status_DR);

                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Status);
                    Database.AddInParameter(cmd, Dbstring + "iBackup_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Backup_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iCopy_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Copy_Job_Execution_Status);
                    Database.AddInParameter(cmd, Dbstring + "iRestore_Job_Execution_Status", DbType.AnsiString, sqlnativeHealth.Restore_Job_Execution_Status);

                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqlnativeHealth.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqlnativeHealth.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while update SQLNATIVEHEALTHMONSTAT_CRTUPD SqlLogNative_Create entry ", exc);
            }
            return false;
        }

        public static bool AddSqlNative2008HealthParameter(SqlNative2008HealthParamete SqlNativeHealthParamete)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("SqlHealthParameter_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, SqlNativeHealthParamete.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabase_UpdateabilityPR", DbType.AnsiString, SqlNativeHealthParamete.Database_UpdateabilityPR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabase_UpdateabilityDR", DbType.AnsiString, SqlNativeHealthParamete.Database_UpdateabilityDR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQLServer_EditionPR", DbType.AnsiString, SqlNativeHealthParamete.MSSQLServer_EditionPR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQLServer_EditionDR", DbType.AnsiString, SqlNativeHealthParamete.MSSQLServer_EditionDR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Server_ReleasePR", DbType.AnsiString, SqlNativeHealthParamete.MSSQL_Server_ReleasePR);
                    Database.AddInParameter(cmd, Dbstring + "iMSSQL_Server_ReleaseDR", DbType.AnsiString, SqlNativeHealthParamete.MSSQL_Server_ReleaseDR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSizePR", DbType.AnsiString, SqlNativeHealthParamete.DatabaseSizePR);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSizeDR", DbType.AnsiString, SqlNativeHealthParamete.DatabaseSizeDR);
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, SqlNativeHealthParamete.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, SqlNativeHealthParamete.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting SqlHealthParameter_Create SqlLogHealth Create entry ", exc);
            }
            return false;
        }
    }
}
