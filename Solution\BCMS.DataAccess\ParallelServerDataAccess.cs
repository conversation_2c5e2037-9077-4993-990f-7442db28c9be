﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using System.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class ParallelServerDataAccess : BaseDataAccess
    {

        public static IList<ParallelServer> GetAllParallelServer()
        {

            IList<ParallelServer> servers = new List<ParallelServer>();

            try
            {
                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("ParallelServer_GetAll"))
                {
                    using (IDataReader myReader = db.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            var server = new ParallelServer
                                             {
                                                 Id = Convert.ToInt32(myReader["Id"]),
                                                 IPAddress = StringHelper.Md5Decrypt(myReader["IPAddress"].ToString()),
                                                 Status = Convert.IsDBNull(myReader["Status"]) ? 0 : Convert.ToInt32(myReader["Status"])
                                             };

                            servers.Add(server);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", exc);
            }
            return servers;


        }


        public static bool UpdateParallelServerByStatus(int id,int status)
        {
            try
            {
                var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = db.GetStoredProcCommand("ParallelServer_UpdateByStatus"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
                    db.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.Int32, status);
                    int isuccess = db.ExecuteNonQuery(dbCommand);                    
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Parallel server Details by status ", exc);
            }

        }

        public static bool UpdateNetworkIpByStatus(int status, string ip)
        {
            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetStoredProcCommand("NetworkIP_UpdateByStatus"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"istatus", DbType.Int32, status);
                    db.AddInParameter(dbCommand, Dbstring+"iIP", DbType.AnsiString, ip);
                    int isuccess = db.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Error occurred while updating Network ip status -" + ip, exc);
            }
        }


    }
}
