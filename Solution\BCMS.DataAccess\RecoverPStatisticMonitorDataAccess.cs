﻿using System;
using System.Collections.Generic;
using System.Data;
using Bcms.Common;
using Bcms.Common.Shared;
using System.Data.Common;
using Bcms.ExceptionHandler;
using Bcms.Helper;
using Bcms.DataAccess.Base;


namespace Bcms.DataAccess
{
    public class RecoverPStatisticMonitorDataAccess : BaseDataAccess
    {

        public static bool AddRecoverPointStatistic(RecoverPStatisticMonitor recoverPStatisticMonitor)
        {
            try
            {
                const string sp = "RecoverPointStatistic_Create";
                // var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iGGroupName", DbType.AnsiString, recoverPStatisticMonitor.CGGroupName);
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, recoverPStatisticMonitor.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring + "iUsage", DbType.AnsiString, recoverPStatisticMonitor.Usage);
                    Database.AddInParameter(dbCommand, Dbstring + "iTotal", DbType.AnsiString, recoverPStatisticMonitor.Total);
                    Database.AddInParameter(dbCommand, Dbstring + "iLast_Image", DbType.DateTime, recoverPStatisticMonitor.LastImage);
                    Database.AddInParameter(dbCommand, Dbstring + "iCurrent_Image", DbType.DateTime, recoverPStatisticMonitor.CurrentImage);
                    Database.AddInParameter(dbCommand, Dbstring + "iProtection_Window", DbType.AnsiString, recoverPStatisticMonitor.ProtectionWindow);
                    Database.AddInParameter(dbCommand, Dbstring + "iJournal_Lag", DbType.AnsiString, recoverPStatisticMonitor.Journal_Lag);
                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, recoverPStatisticMonitor.Status);
                    Database.AddInParameter(dbCommand, Dbstring + "iReplicationLagTime", DbType.AnsiString, recoverPStatisticMonitor.ReplicationLagTime);
                    Database.AddInParameter(dbCommand, Dbstring + "iReplicationLagData", DbType.AnsiString, recoverPStatisticMonitor.ReplicationLagData);
                    Database.AddInParameter(dbCommand, Dbstring + "iReplicationLagWrites", DbType.AnsiString, recoverPStatisticMonitor.ReplicationLagWriter);
                    Database.AddInParameter(dbCommand, Dbstring + "iDataLag", DbType.AnsiString, recoverPStatisticMonitor.DataLag);
                    Database.AddInParameter(dbCommand, Dbstring + "iSiteName", DbType.AnsiString, recoverPStatisticMonitor.SiteName);
                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert RecoverPoint Statistic Monitor information", exc);
            }
            return false;
        }

        public static bool AddRecoverPointMonitorLog(RecoverPStatisticMonitor recoverPStatisticMonitor)
        {
            try
            {
                const string sp = "RecoverPointmonitor_RPT_Log_Create";
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, recoverPStatisticMonitor.RecoverPState.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring + "iGGroupName", DbType.AnsiString, recoverPStatisticMonitor.RecoverPState.GGroupName);
                    Database.AddInParameter(dbCommand, Dbstring + "iTransfer_source", DbType.AnsiString, recoverPStatisticMonitor.RecoverPState.TransferSource);
                    Database.AddInParameter(dbCommand, Dbstring + "iCopy", DbType.AnsiString, recoverPStatisticMonitor.RecoverPState.Copy);

                    Database.AddInParameter(dbCommand, Dbstring + "iReplicationLagTime", DbType.AnsiString, recoverPStatisticMonitor.ReplicationLagTime);
                    Database.AddInParameter(dbCommand, Dbstring + "iReplicationLagData", DbType.AnsiString, recoverPStatisticMonitor.ReplicationLagData);

                    Database.AddInParameter(dbCommand, Dbstring + "iCreatorId", DbType.Int32, recoverPStatisticMonitor.RecoverPState.CreatorId);
                    Database.AddInParameter(dbCommand, Dbstring + "iCreateDate", DbType.DateTime, recoverPStatisticMonitor.RecoverPState.CreateDate);

                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert RecoverPoint Monitor Report Log information", exc);
            }
            return false;
        }
       
    }
}
