﻿using System;
using System.Data;
using System.Data.Common;

using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class EmcReplicationDataAccess : BaseDataAccess
    {
        public static EmcReplication GetEmcReplicationById(int id)
        {
 

            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("EMCSRDF_GETBYREPLICATIONID"))
                {
                    var emcreplication = new EmcReplication();

                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            emcreplication.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            emcreplication.ReplicationType = Convert.IsDBNull(reader["ReplicationId"]) ? string.Empty : Convert.ToString(reader["ReplicationId"]);
                            emcreplication.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            emcreplication.DGroupName = Convert.IsDBNull(reader["DGroupName"]) ? string.Empty : Convert.ToString(reader["DGroupName"]);
                            emcreplication.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
                            emcreplication.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"]) ? string.Empty : Convert.ToString(reader["DGSummetrixID"]);
                            emcreplication.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"]) ? string.Empty : Convert.ToString(reader["RemoteSymmetrixID"]);
                            emcreplication.RdfRaGroupNumber = Convert.IsDBNull(reader["RDF_RA_GroupNumber"]) ? string.Empty : Convert.ToString(reader["RDF_RA_GroupNumber"]);
                            emcreplication.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
                            emcreplication.PendingTracks = Convert.IsDBNull(reader["PendingTracks"]) ? string.Empty : Convert.ToString(reader["PendingTracks"]);
                            emcreplication.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);


                            //const int FLD_ID = 0;
                            //const int FLD_ReplicationName = 1;
                            //const int FLD_ReplicationType = 2;
                            //const int FLD_ServerId = 3;
                            //const int FLD_DGroupName = 4;
                            //const int FLD_DGType = 5;
                            //const int FLD_DGSummetrixID = 6;
                            //const int FLD_RemoteSymmetrixID = 7;
                            //const int FLD_RDF_RA_GroupNumber = 8;
                            //const int FLD_State = 9;
                            //const int FLD_PendingTraks = 10;
                            //const int FLD_DataLag = 11;
                            //const int FLD_CreatorId = 13;
                            //const int FLD_CreateDate = 14;
                            //const int FLD_UpdatorId = 15;
                            //const int FLD_UpdateDate = 16;

                            //emcreplication.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
                            //emcreplication.ReplicationName = reader.IsDBNull(FLD_ReplicationName) ? string.Empty : reader.GetString(FLD_ReplicationName);
                            //emcreplication.ReplicationType = reader.IsDBNull(FLD_ReplicationType) ? string.Empty : reader.GetString(FLD_ReplicationType);
                            //emcreplication.ServerId = reader.IsDBNull(FLD_ServerId) ? 0 : reader.GetInt32(FLD_ServerId);
                            //emcreplication.DGroupName = reader.IsDBNull(FLD_DGroupName) ? string.Empty : reader.GetString(FLD_DGroupName);
                            //emcreplication.DGType = reader.IsDBNull(FLD_DGType) ? string.Empty : reader.GetString(FLD_DGType);
                            //emcreplication.DGSummetrixID = reader.IsDBNull(FLD_DGSummetrixID) ? string.Empty : reader.GetString(FLD_DGSummetrixID);
                            //emcreplication.RemoteSymmetrixID = reader.IsDBNull(FLD_RemoteSymmetrixID) ? string.Empty : reader.GetString(FLD_RemoteSymmetrixID);
                            //emcreplication.RdfRaGroupNumber = reader.IsDBNull(FLD_RDF_RA_GroupNumber) ? string.Empty : reader.GetString(FLD_RDF_RA_GroupNumber);
                            //emcreplication.State = reader.IsDBNull(FLD_State) ? string.Empty : reader.GetString(FLD_State);
                            //emcreplication.PendingTracks = reader.IsDBNull(FLD_PendingTraks) ? string.Empty : reader.GetString(FLD_PendingTraks);
                            //emcreplication.DataLag = reader.IsDBNull(FLD_DataLag) ? string.Empty : reader.GetString(FLD_DataLag);
                            //emcreplication.CreatorId = reader.IsDBNull(FLD_CreatorId) ? 0 : reader.GetInt32(FLD_CreatorId);
                            //emcreplication.CreateDate = reader.IsDBNull(FLD_CreateDate) ? DateTime.MinValue : reader.GetDateTime(FLD_CreateDate);
                            //emcreplication.UpdatorId = reader.IsDBNull(FLD_UpdatorId) ? 0 : reader.GetInt32(FLD_UpdatorId);
                            //emcreplication.UpdateDate = reader.IsDBNull(FLD_UpdateDate) ? DateTime.MinValue : reader.GetDateTime(FLD_UpdateDate);

                        }

                        return emcreplication;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", exc);
            }
        }


        public static bool UpdateEmcReplicationByState(int emcId, int infraobjectid, int replicationid, string state, string pendingtraks, string dgtype, string dgsymm, string remoteSymm, string rdfaNo, string lag, int serverid, string dgname, string imagecapturetime)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("EmcReplication_UpdateByState"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, emcId);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, infraobjectid);
                    Database.AddInParameter(dbCommand, Dbstring+"iReplicationId", DbType.Int32, replicationid);
                    Database.AddInParameter(dbCommand, Dbstring+"iState", DbType.AnsiString, state);
                    Database.AddInParameter(dbCommand, Dbstring+"iPending", DbType.AnsiString, pendingtraks);
                    Database.AddInParameter(dbCommand, Dbstring+"idgtype", DbType.AnsiString, dgtype);
                    Database.AddInParameter(dbCommand, Dbstring+"idgsymm", DbType.AnsiString, dgsymm);
                    Database.AddInParameter(dbCommand, Dbstring+"iremoteSymm", DbType.AnsiString, remoteSymm);
                    Database.AddInParameter(dbCommand, Dbstring+"irdfaNo", DbType.AnsiString, rdfaNo);
                    Database.AddInParameter(dbCommand, Dbstring+"ilag", DbType.AnsiString, lag);
                    Database.AddInParameter(dbCommand, Dbstring+"iServerId", DbType.Int32, serverid);
                    Database.AddInParameter(dbCommand, Dbstring+"iDGGroupName", DbType.AnsiString, dgname);
                    Database.AddInParameter(dbCommand, Dbstring+"iImageCaptureTime", DbType.AnsiString, imagecapturetime);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by state ", exc);
            }
        }

        public static bool UpdateEmcReplicationByState(int emcId,string state,string pendingtraks,string dgtype,string dgsymm,string remoteSymm,string rdfaNo)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("EmcReplication_UpdateByState"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, emcId);

                    Database.AddInParameter(dbCommand, Dbstring+"iState", DbType.AnsiString, state);
                    Database.AddInParameter(dbCommand, Dbstring+"iPending", DbType.AnsiString, pendingtraks);
                    Database.AddInParameter(dbCommand, Dbstring+"idgtype", DbType.AnsiString, dgtype);
                    Database.AddInParameter(dbCommand, Dbstring+"idgsymm", DbType.AnsiString, dgsymm);
                    Database.AddInParameter(dbCommand, Dbstring+"iremoteSymm", DbType.AnsiString, remoteSymm);
                    Database.AddInParameter(dbCommand, Dbstring+"irdfaNo", DbType.AnsiString, rdfaNo);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by state ", exc);
            }

        }

        public static bool UpdateEmcReplicationByDataLag(int emcId,string lag)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("EmcReplication_UpdateByDataLag"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, emcId);
                    Database.AddInParameter(dbCommand, Dbstring+"iLag", DbType.AnsiString, lag);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by datalag ", exc);
            }

        }

    }
}