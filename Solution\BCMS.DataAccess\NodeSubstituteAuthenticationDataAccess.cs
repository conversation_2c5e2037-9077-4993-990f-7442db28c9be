﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;

namespace Bcms.DataAccess
{
    public class NodeSubstituteAuthenticationDataAccess : BaseDataAccess
    {
        public static NodeSubstituteAuthentication GetNodeSubstituteById(int Id)
        {
            if (Id < 1)
            {
                throw new ArgumentNullException("Id");
            }

            var databaseMonitor = new NodeSubstituteAuthentication();
            //const string sp = "DatabaseAuth_GetBydbId_Node";
            const string sp = "SUBSTITUTE_DBAUTH_FOR_NODE";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, Id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
                {
                    if (myDROperationReader1.Read())
                    {
                        databaseMonitor.Id = Convert.ToInt32(myDROperationReader1["Id"]);
                        //databaseMonitor.BaseDatabaseId = Convert.ToInt32(myDROperationReader1["BaseDatabaseId"]);

                        databaseMonitor.BaseDatabaseId = Convert.ToInt32(myDROperationReader1["NodeId"]);

                        databaseMonitor.DBConnectionCmd = myDROperationReader1["DBConnectionCmd"].ToString();
                        //databaseMonitor.Sqlplus = myDROperationReader1["Sqlplus"].ToString();
                        databaseMonitor.Credential = Convert.ToInt32(myDROperationReader1["Credential"]);
                        databaseMonitor.Role = myDROperationReader1["Role"].ToString();
                        databaseMonitor.IsSubstituteAuth = Convert.ToInt32(myDROperationReader1["IsSubstituteAuth"]);



                    }
                    else
                    {
                        databaseMonitor = null;
                    }
                }

                return databaseMonitor;
            }
        }
    }
}
