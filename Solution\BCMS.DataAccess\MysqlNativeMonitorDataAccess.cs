﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.DataAccess
{
    public class MysqlNativeMonitorDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(MysqlNativeMonitorDataAccess));


        public static bool AddMysqlNativeMonitorLogs(MySqlNativeMonitor mysqlmonitor)
        {
            try
            {
                string sp = DbRoleName + "MysqlNativeMonitorLog_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, mysqlmonitor.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseVersion", DbType.AnsiString, mysqlmonitor.PRDatabaseVersion);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseVersion", DbType.AnsiString, mysqlmonitor.DRDatabaseVersion);
                    Database.AddInParameter(cmd, Dbstring + "iMasterServiceStatus", DbType.AnsiString, mysqlmonitor.MasterServiceStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSlaveServiceStatus", DbType.AnsiString, mysqlmonitor.SlaveServiceStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseState", DbType.AnsiString, mysqlmonitor.PRDatabaseState);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseState", DbType.AnsiString, mysqlmonitor.DRDatabaseState);
                    Database.AddInParameter(cmd, Dbstring + "iPRSlaveRunningState", DbType.AnsiString, mysqlmonitor.PRSlaveRunningState);
                    Database.AddInParameter(cmd, Dbstring + "iDRSlaveRunningState", DbType.AnsiString, mysqlmonitor.DRSlaveRunningState);
                    Database.AddInParameter(cmd, Dbstring + "iPRReplicationConnectState", DbType.AnsiString, mysqlmonitor.PRReplicationConnectState);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicationConnectState", DbType.AnsiString, mysqlmonitor.DRReplicationConnectState);
                    Database.AddInParameter(cmd, Dbstring + "iSlaveIORunningStatus", DbType.AnsiString, mysqlmonitor.SlaveIORunningStatus);
                    Database.AddInParameter(cmd, Dbstring + "iSlaveSQLRunningStatus", DbType.AnsiString, mysqlmonitor.SlaveSQLRunningStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRMasterLogFile", DbType.AnsiString, mysqlmonitor.PRMasterLogFile);
                    Database.AddInParameter(cmd, Dbstring + "iDRMasterLogFile", DbType.AnsiString, mysqlmonitor.DRMasterLogFile);
                    Database.AddInParameter(cmd, Dbstring + "iRelayMasterLogFile", DbType.AnsiString, mysqlmonitor.RelayMasterLogFile);
                    Database.AddInParameter(cmd, Dbstring + "iMasterLogPosition", DbType.AnsiString, mysqlmonitor.MasterLogPosition);
                    Database.AddInParameter(cmd, Dbstring + "iExecMasterLogPosition", DbType.AnsiString, mysqlmonitor.ExecMasterLogPosition);
                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, mysqlmonitor.DataLag);

//#if ORACLE
//                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif

                    int value = Database.ExecuteNonQuery(cmd);

                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exe)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create Mysql Native Monitor Logs", exe);
            }

            return true;
        }
    }
}
