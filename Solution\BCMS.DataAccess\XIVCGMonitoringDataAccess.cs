﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;


namespace Bcms.DataAccess
{
    public class XIVCGMonitoringDataAccess : BaseDataAccess
    {
        public static bool AddCGMonitoringLogDetails(CGMoniLogs CGMoniLogs)
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("CG_Moni_Logs_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.Int32, CGMoniLogs.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iPRCGName", DbType.AnsiString, CGMoniLogs.PRCGName);
                    Database.AddInParameter(cmd, Dbstring + "iDRCGName", DbType.AnsiString, CGMoniLogs.DRCGName);
                    Database.AddInParameter(cmd, Dbstring + "iActiveStatus", DbType.AnsiString, CGMoniLogs.ActiveStatus);
                    Database.AddInParameter(cmd, Dbstring + "iRPOStatus", DbType.AnsiString, CGMoniLogs.RPOStatus);
                    Database.AddInParameter(cmd, Dbstring + "iLinkUp", DbType.AnsiString, CGMoniLogs.LinkUp);
                    Database.AddInParameter(cmd, Dbstring + "iCGID", DbType.AnsiString, CGMoniLogs.CGId); 
                 
                 

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Create CG Monitoring Detailed Monitor Logs InfraObject Id - " + CGMoniLogs.InfraID, exc);

            }

            return false;
        }
    }
}
