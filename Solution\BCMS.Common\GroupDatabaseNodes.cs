﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "GroupDatabaseNodes", Namespace = "http://www.BCMS.com/types")]
    public class GroupDatabaseNodes : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId { get; set; }

        [DataMember]
        public int PrServerId { get; set; }

        [DataMember]
        public int DrServerId { get; set; }

        [DataMember]
        public int PrDbseId { get; set; }

        [DataMember]
        public int DrDbseId { get; set; }

        [DataMember]
        public int PrNodeId { get; set; }

        [DataMember]
        public int DrNodeId { get; set; }

        #endregion Properties
    }
}