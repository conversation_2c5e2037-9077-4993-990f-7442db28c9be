﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class EventManagement : BaseDataAccess
    {

        public static bool EventManagement_Create(int InfraobjectId,int EventId,int Description,int Time,int Trigger,int CreatorId)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("EmcReplication_UpdateByState"))
                {
                    Database.AddInParameter(dbCommand, "?iInfraobjectId", DbType.Int32, InfraobjectId);
                    Database.AddInParameter(dbCommand, "?iEventId", DbType.Int32, EventId);
                    Database.AddInParameter(dbCommand, "?iDescription", DbType.AnsiString, Description);
                    Database.AddInParameter(dbCommand, "?iTime", DbType.AnsiString, Time);
                    Database.AddInParameter(dbCommand, "?iTrigger", DbType.Int32, Trigger);
                    Database.AddInParameter(dbCommand, "?iCreatorId", DbType.Int32, CreatorId);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while EventManagement Create ", exc);
            }

        }
        public static Event GetEventId(int id)
        {
            var events = new Event();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Event_Management_GetById"))
                {
                    Database.AddInParameter(dbCommand, "?iIdd", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataLagReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataLagReader.Read())
                        {
                            events.Id = Convert.IsDBNull(myDataLagReader["Id"]) ? 0 : Convert.ToInt32(myDataLagReader["Id"]);
                            events.EventId = Convert.IsDBNull(myDataLagReader["LogFileName"]) ? string.Empty : Convert.ToString(myDataLagReader["LogFileName"]);
                            events.PRSequenceNo = Convert.IsDBNull(myDataLagReader["PRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["PRSequenceNo"]);
                            events.DRSequenceNo = Convert.IsDBNull(myDataLagReader["DRSequenceNo"]) ? string.Empty : Convert.ToString(myDataLagReader["DRSequenceNo"]);
                            events.PRTransId = Convert.IsDBNull(myDataLagReader["PRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["PRTransactionId"]);
                            events.DRTransId = Convert.IsDBNull(myDataLagReader["DRTransactionId"]) ? string.Empty : Convert.ToString(myDataLagReader["DRTransactionId"]);
                            events.PRLogTime = Convert.IsDBNull(myDataLagReader["PRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["PRLogTime"]);
                            datalag.DRLogTime = Convert.IsDBNull(myDataLagReader["DRLogTime"]) ? string.Empty : Convert.ToString(myDataLagReader["DRLogTime"]);
                            datalag.CurrentDataLag = Convert.IsDBNull(myDataLagReader["CurrentDataLag"]) ? string.Empty : Convert.ToString(myDataLagReader["CurrentDataLag"]);
                            datalag.Health = Convert.IsDBNull(myDataLagReader["Health"]) ? 0 : Convert.ToInt32(myDataLagReader["Health"]);
                            datalag.BusinessServiceId = Convert.IsDBNull(myDataLagReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myDataLagReader["BusinessServiceId"]);
                            datalag.InfraObjectId = Convert.IsDBNull(myDataLagReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataLagReader["InfraObjectId"]);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Datalag information By InfraObject Id - " + id, exc);
            }

            return datalag;
        }
    }
}
