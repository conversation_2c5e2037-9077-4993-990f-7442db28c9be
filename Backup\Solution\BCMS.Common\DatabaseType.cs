﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseType", Namespace = "http://www.BCMS.com/types")]
   public class DatabaseType : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public DatabaseType()
            : base()
        {
        }

        #endregion

    }
}
