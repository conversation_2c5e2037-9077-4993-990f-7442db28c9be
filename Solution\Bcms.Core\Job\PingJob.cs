﻿using System;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.ExceptionHandler;

namespace Bcms.Core.Job
{
    public class PingJob : IJob
    {
        public void Execute(JobExecutionContext context)
        {
            DoPing();
        }

        
       
        private void DoPing()
        {
            try
            {
                var client = new DiscoverHostClient();

                client.PerformHostDiscover();


            }
            catch (BcmsException exc)
            {
                ExceptionManager.Manage(exc, JobName.Ping.ToString(), 0, string.Empty);
            }
            catch (Exception exc)
            {
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occurred while pinging host", exc);
                ExceptionManager.Manage(bcmsException, JobName.Ping.ToString(), 0, string.Empty);
            }

        }
    }
}