﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
   public class HitachiURDeviceMonitoringStatusDataAccess :BaseDataAccess
    {
       public static bool  AddHitachiURDeviceMonitoring(HitachiURDeviceMonitoring hitachiUrDeviceMonitoring)
       {
           try
           {
               //var db = DatabaseFactory.CreateDatabase();
               const string sp = "HitachiURDMonitoring_Create";
               using (DbCommand dbCommand =Database.GetStoredProcCommand(sp))
               {
                   //AddOutputParameter(cmd);
                   Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, hitachiUrDeviceMonitoring.GroupId);
                   Database.AddInParameter(dbCommand, Dbstring + "iDeviceGroupName", DbType.String, hitachiUrDeviceMonitoring.DeviceGroupName);
                   Database.AddInParameter(dbCommand, Dbstring + "iType", DbType.String, hitachiUrDeviceMonitoring.Type);
                   Database.AddInParameter(dbCommand, Dbstring + "iLunsId", DbType.String, hitachiUrDeviceMonitoring.LunsId);
                   Database.AddInParameter(dbCommand, Dbstring + "iVolumeStatus", DbType.String, hitachiUrDeviceMonitoring.VolumeStatus);
                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }
               
           }
           catch (Exception exc)
           {
              
               throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Insert Record in HitachiURDeviceMonitoring", exc);
           }
           return false;
       }
        
    }
}