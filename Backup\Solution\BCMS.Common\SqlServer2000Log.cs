﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlServer2000Log", Namespace = "http://www.Bcms.com/types")]
    public class SqlServer2000Log : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string LastBackUpFile
        {
            get;
            set;
        }

        [DataMember]
        public string LastBackUpTime
        {
            get;
            set;
        }

        [DataMember]
        public string LastBackUpLSN
        {
            get;
            set;
        }

        public string LastCopyFile
        {
            get;
            set;
        }

        [DataMember]
        public string LastCopyTime
        {
            get;
            set;
        }

        [DataMember]
        public string LastCopyLSN
        {
            get;
            set;
        }

        public string LastRestoreFile
        {
            get;
            set;
        }

        [DataMember]
        public string LastRestoreTime
        {
            get;
            set;
        }

        [DataMember]
        public string LastRestoreLSN
        {
            get;
            set;
        }

         [DataMember]
        public string DataLag
        {
            get;
            set;
        }

        
        #endregion

        #region Constructor
        public SqlServer2000Log() : base()
        {
        }
        #endregion

    }
}
