﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "OracleDataGuardReplication", Namespace = "http://www.BCMS.com/types")]
    public class DataGuardMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public string PRStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PRErrorReason
        {
            get;
            set;
        }

        [DataMember]
        public string DRErrorReason
        {
            get;
            set;
        }

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public DataGuardMonitor()
            : base()
        {
        }

        #endregion
    }
}