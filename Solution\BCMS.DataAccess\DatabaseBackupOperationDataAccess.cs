﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class DatabaseBackupOperationDataAccess:BaseDataAccess
    {

        public static DatabaseBackupOperation AddDatabaseBackupOperation(DatabaseBackupOperation databaseBackup)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBackupOperation_Create"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iDatabaseName", DbType.AnsiString, databaseBackup.DatabaseName);
                    Database.AddInParameter(dbCommand, Dbstring+"iBackupFileName", DbType.AnsiString, databaseBackup.BackupFileName);
                    Database.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.AnsiString, databaseBackup.Status);
                    Database.AddInParameter(dbCommand, Dbstring+"iIsBackupNow", DbType.Boolean, databaseBackup.IsBackupNow);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader myDataBackupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataBackupReader.Read())
                        {
                            databaseBackup.Id = Convert.ToInt32(myDataBackupReader[0]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }
            return databaseBackup;
        }

        public static DatabaseBackupOperation GetLastDatabaseBackupOperation()
        {
            var databaseBackup = new DatabaseBackupOperation();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBkupOper_GetLast"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataBackupReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myDataBackupReader.Read())
                        {
                            databaseBackup.Id = Convert.IsDBNull(myDataBackupReader["Id"]) ? 0 : Convert.ToInt32(myDataBackupReader["Id"]);
                            databaseBackup.DatabaseName = Convert.IsDBNull(myDataBackupReader["DatabaseName"]) ? string.Empty : Convert.ToString(myDataBackupReader["DatabaseName"]);
                            databaseBackup.BackupFileName = Convert.IsDBNull(myDataBackupReader["BackupFileName"]) ? string.Empty : Convert.ToString(myDataBackupReader["BackupFileName"]);
                            databaseBackup.Status = Convert.IsDBNull(myDataBackupReader["Status"]) ? string.Empty : Convert.ToString(myDataBackupReader["Status"]);
                            databaseBackup.ErrorDescription = Convert.IsDBNull(myDataBackupReader["ErrorDescription"]) ? string.Empty : Convert.ToString(myDataBackupReader["ErrorDescription"]);
                            //databaseBackup.IsBackupNow = Convert.ToBoolean(myDataBackupReader[5].ToString());
                            if (!Convert.IsDBNull(myDataBackupReader["IsBackupNow"]))
                                databaseBackup.IsBackupNow = Convert.ToBoolean(myDataBackupReader["IsBackupNow"]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }
            return databaseBackup;
        }

        public static bool UpdateDatabaseBackupOperationByStatus(DatabaseBackupOperation databaseBackup)
        {

            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabseBkupOper_UpdateByStatus"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, databaseBackup.Id);
                    Database.AddInParameter(dbCommand, Dbstring+"iBackupFileName", DbType.AnsiString, databaseBackup.BackupFileName);
                    Database.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.AnsiString, databaseBackup.Status);
                    Database.AddInParameter(dbCommand, Dbstring+"iErrorDescription", DbType.AnsiString, databaseBackup.ErrorDescription);


#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int value = Database.ExecuteNonQuery(dbCommand);

                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert alert information", exc);
            }
            return false;
        }
         
    }
}