﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.DataAccess
{
    public class InfrastructureDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(InfrastructureDataAccess));

        public static IList<Infrastructure> GetAll()
        {
            IList<Infrastructure> infrastructureList = new List<Infrastructure>();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Infrastructure_GetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var infrastructure = new Infrastructure();

                            infrastructure.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            infrastructure.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            infrastructure.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            infrastructure.CPUThreshold = Convert.IsDBNull(reader["CPUThreshold"]) ? 0 : Convert.ToInt32(reader["CPUThreshold"]);
                            infrastructure.MemoryThreshold = Convert.IsDBNull(reader["MemoryThreshold"]) ? 0 : Convert.ToInt32(reader["MemoryThreshold"]);
                            infrastructure.MountPointThreshold = Convert.IsDBNull(reader["MountPointThreshold"]) ? string.Empty : reader["MountPointThreshold"].ToString();
                            infrastructure.UserNames = Convert.IsDBNull(reader["UserNames"]) ? string.Empty : reader["UserNames"].ToString();
                            infrastructure.FileName = Convert.IsDBNull(reader["FileNames"]) ? string.Empty : reader["FileNames"].ToString();
                            infrastructureList.Add(infrastructure);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get All Infrastructure Details", exc);

            }
            return infrastructureList;
        }

    }
}
