﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseNodes", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseNodes : BaseEntity
    {
        #region Properties

        [DataMember]
        public int DatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int NodeId
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public DatabaseNodes()
            : base()
        {
        }

        #endregion
    }
}
