﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class HeatmapDataAccess : BaseDataAccess
    {
        public static bool AddHeatmap(Heatmap heatmap)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Heatmap_Create"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, heatmap.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iBusinessServiceId", DbType.Int32, heatmap.BusinessServiceId);
                    Database.AddInParameter(dbCommand, Dbstring+"iEntityId", DbType.Int32, heatmap.EntityId);
                    Database.AddInParameter(dbCommand, Dbstring+"iHeatmapType", DbType.String, heatmap.HeatmapType);
                    Database.AddInParameter(dbCommand, Dbstring+"iHeatmapStatus", DbType.String, heatmap.Status);
                    Database.AddInParameter(dbCommand, Dbstring+"iIsAffected", DbType.Int32, heatmap.IsAffected);

                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    return value < 0;
#endif
                    return value > 0;
                  
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation,
                    "Exception occurred while insert heatmap information", exc);
            }
            return false;
        }

        public static IList<Heatmap> GetByBSIdAndInfraId(int businessServiceId, int infraObjectId)
        {
            IList<Heatmap> Heatmaplist = new List<Heatmap>();
            try
            {
                if (businessServiceId < 1 || infraObjectId < 1)
                {
                    throw new ArgumentNullException("businessServiceId/infraObjectId");
                }

                const string sp = "HEATMAP_GETBYBSINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, businessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iinfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {

                            var Heatmap = new Heatmap();
                            Heatmap.Id = Convert.ToInt32(reader["Id"]);
                            Heatmap.InfraObjectId = Convert.ToInt32(reader["InfraobjectId"]);
                            Heatmap.EntityId = Convert.ToInt32(reader["EntityId"]);
                            Heatmap.HeatmapType = reader["HeatmapType"].ToString();
                            Heatmap.Status = reader["HeatmapStatus"].ToString();
                            Heatmap.BusinessServiceId = Convert.ToInt32(reader["BusinessServiceId"]);
                            Heatmaplist.Add(Heatmap);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,

                    "Error In DAL While Executing Function Signature IHeatmapDataAccess.GetByBSIdAndInfraId(" + businessServiceId + "," + infraObjectId + ")", exc);
            }
            return Heatmaplist;
        }

        public static bool UpdateHeatMap(Heatmap heatmap)
        {

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("HeatmapStatus_Create"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, heatmap.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring + "iBusinessServiceId", DbType.Int32, heatmap.BusinessServiceId);
                    Database.AddInParameter(dbCommand, Dbstring + "iEntityId", DbType.Int32, heatmap.EntityId);
                    Database.AddInParameter(dbCommand, Dbstring + "iHeatmapType", DbType.String, heatmap.HeatmapType);
                    Database.AddInParameter(dbCommand, Dbstring + "iHeatmapStatus", DbType.String, heatmap.Status);
                    Database.AddInParameter(dbCommand, Dbstring + "iIsAffected", DbType.Int32, heatmap.IsAffected);

                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    return value < 0;
#endif
                    return value > 0;

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation,
                    "Exception occurred while insert heatmap information", exc);
            }
            return false;

        }

    }
}
