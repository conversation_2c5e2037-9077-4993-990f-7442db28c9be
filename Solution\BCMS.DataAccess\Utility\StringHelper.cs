﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Bcms.DataAccess.Utility
{
    public static class StringHelper
    {
        public static string Md5Key = "20Pe,rvtu!itLtPa/d10?Mah$petnThaohes%h2ktilso3*4ftMarSe(rTe)cs@ctimizhnviceP";

        public static string Md5Decrypt(string cipherString)
        {
            byte[] toEncryptArray = Convert.FromBase64String(cipherString);

            var hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(Md5Key));
            hashmd5.Clear();

            var tdes = new TripleDESCryptoServiceProvider
            {
                Key = keyArray,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            var cTransform = tdes.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tdes.Clear();
            return Encoding.UTF8.GetString(resultArray);

        }

    }
}