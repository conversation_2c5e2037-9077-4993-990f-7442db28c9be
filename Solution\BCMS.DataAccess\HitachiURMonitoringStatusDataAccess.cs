﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class HitachiURMonitoringStatusDataAccess:BaseDataAccess
    {
        public static bool AddHitachiURMonitoring(HitachiURMonitoring hitachiUrMonitoring)
        {
            try
            {
               // var db = DatabaseFactory.CreateDatabase();
                const string sp = "HitachiURMonitoring_Create";
                using (DbCommand dbCommand =Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, hitachiUrMonitoring.GroupId);
                    Database.AddInParameter(dbCommand, Dbstring+"iPRHORCOMStatus", DbType.String, hitachiUrMonitoring.PRHORCOMStatus);
                    Database.AddInParameter(dbCommand, Dbstring+"iDRHORCOMStatus", DbType.String, hitachiUrMonitoring.DRHORCOMStatus);
                    Database.AddInParameter(dbCommand, Dbstring+"iAverageCopyPendingTime", DbType.String, hitachiUrMonitoring.AverageCopyPendingTime);
                    Database.AddInParameter(dbCommand, Dbstring + "iLastPairTime", DbType.DateTime, hitachiUrMonitoring.LastPairTime);
                    int value = Database.ExecuteNonQuery(dbCommand);

#if ORACLE
                    if (value ==-1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation,
                                        "Exception occurred while Insert Record in HitachiURDeviceMonitoring", exc);
            }
            return false;
        }

        public static HitachiURMonitoring Get_HitachiURMonitoing_byInfraId(int infraObjectId)
        {
            var hitachiURMonitoring = new HitachiURMonitoring();
            try
            {
                // var db = DatabaseFactory.CreateDatabase();
                const string sp = "HitachiMonitor_GetbyInfraId";

                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);

                    using (IDataReader myServerReader = Database.ExecuteReader(dbCommand))
                    {

                        while (myServerReader.Read())
                        {
                            hitachiURMonitoring.Id = Convert.IsDBNull(myServerReader["Id"]) ? 0 : Convert.ToInt32(myServerReader["Id"]);
                            hitachiURMonitoring.GroupId = Convert.IsDBNull(myServerReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myServerReader["InfraObjectId"]);
                            hitachiURMonitoring.PRHORCOMStatus = Convert.IsDBNull(myServerReader["PRHORCOMStatus"]) ? string.Empty : Convert.ToString(myServerReader["PRHORCOMStatus"]);
                            hitachiURMonitoring.DRHORCOMStatus = Convert.IsDBNull(myServerReader["DRHORCOMStatus"]) ? string.Empty : Convert.ToString(myServerReader["DRHORCOMStatus"]);
                            hitachiURMonitoring.AverageCopyPendingTime = Convert.IsDBNull(myServerReader["AverageCopyPendingTime"]) ? string.Empty : Convert.ToString(myServerReader["AverageCopyPendingTime"]);
                            hitachiURMonitoring.LastPairTime = Convert.IsDBNull(myServerReader["LastPairTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(myServerReader["LastPairTime"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation,
                                        "Exception occurred while Insert Record in HitachiURDeviceMonitoring", exc);
            }
            return hitachiURMonitoring;
        }
    }
}
