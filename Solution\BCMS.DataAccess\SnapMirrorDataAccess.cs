﻿using System;
using System.Data;
using System.Data.Common;
using BCMS.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class SnapMirrorDataAccess : BaseDataAccess
    {
        public static SnapMirror GetSnapMirrorById(int id)
        {
            var snapmirror = new SnapMirror();
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SnapMirror_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader mySnapReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySnapReader.Read())
                        {
                            snapmirror.Id = Convert.IsDBNull(mySnapReader["Id"]) ? 0 : Convert.ToInt32(mySnapReader["Id"]);
                            snapmirror.ReplicationId = Convert.IsDBNull(mySnapReader["ReplicationId"]) ? 0 : Convert.ToInt32(mySnapReader["ReplicationId"]);
                            snapmirror.MCPRServerId = Convert.IsDBNull(mySnapReader["MCPRServerId"]) ? 0 : Convert.ToInt32(mySnapReader["MCPRServerId"]);
                            snapmirror.MCDRServerId = Convert.IsDBNull(mySnapReader["MCDRServerId"]) ? 0 : Convert.ToInt32(mySnapReader["MCDRServerId"]);
                            snapmirror.PRStorageId = Convert.IsDBNull(mySnapReader["PRStorageId"]) ? string.Empty : Convert.ToString(mySnapReader["PRStorageId"]);
                            snapmirror.DRStorageId = Convert.IsDBNull(mySnapReader["DRStorageId"]) ? string.Empty : Convert.ToString(mySnapReader["DRStorageId"]);
                            snapmirror.Mode = Convert.IsDBNull(mySnapReader["ModeType"]) ? 0 : Convert.ToInt32(mySnapReader["ModeType"]);
                            snapmirror.PRVolume = Convert.IsDBNull(mySnapReader["PRVolume"]) ? string.Empty : Convert.ToString(mySnapReader["PRVolume"]);
                            snapmirror.DRVolume = Convert.IsDBNull(mySnapReader["DRVolume"]) ? string.Empty : Convert.ToString(mySnapReader["DRVolume"]);
                            snapmirror.PRLunPath = Convert.IsDBNull(mySnapReader["PRLunPath"]) ? string.Empty : Convert.ToString(mySnapReader["PRLunPath"]);
                            snapmirror.DRLunPath = Convert.IsDBNull(mySnapReader["DRLunPath"]) ? string.Empty : Convert.ToString(mySnapReader["DRLunPath"]);
                            snapmirror.PRLunmapId = Convert.IsDBNull(mySnapReader["PRLunmapId"]) ? string.Empty : Convert.ToString(mySnapReader["PRLunmapId"]);
                            snapmirror.DRLunmapId = Convert.IsDBNull(mySnapReader["DRLunmapId"]) ? string.Empty : Convert.ToString(mySnapReader["DRLunmapId"]);
                            snapmirror.PRLunSerialNo = Convert.IsDBNull(mySnapReader["PRLunSerialNo"])? string.Empty: Convert.ToString(mySnapReader["PRLunSerialNo"]);
                            snapmirror.DRLunSerialNo = Convert.IsDBNull(mySnapReader["DRLunSerialNo"])? string.Empty: Convert.ToString(mySnapReader["DRLunSerialNo"]);
                            //snapMirror.Id = Convert.ToInt32(mySnapReader[0]);
                            //snapMirror.ReplicationId = Convert.ToInt32(mySnapReader[1].ToString());
                            //snapMirror.MCPRServerId = Convert.ToInt32(mySnapReader[3]);
                            //snapMirror.MCDRServerId = Convert.ToInt32(mySnapReader[4]);
                            //snapMirror.PRStorageId = mySnapReader[5].ToString();
                            //snapMirror.DRStorageId =  mySnapReader[6].ToString();
                            //snapMirror.Mode = Convert.ToInt32(mySnapReader[7]);
                            //snapMirror.PRVolume = mySnapReader[8].ToString();
                            //snapMirror.DRVolume = mySnapReader[9].ToString();
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get SnapMirror information by Id - " + id, exc);

            }
            return snapmirror;
        }

        public static SnapMirror GetSnapMirrorByReplicationId(int id)
        {
            var snapmirror = new SnapMirror();
            try
            {
                //var db = DatabaseFactory.CreateDatabase();

                using (DbCommand dbCommand = Database.GetStoredProcCommand("SnapMirror_GetByReplicationId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
        

                    using (IDataReader mySnapReader = Database.ExecuteReader(dbCommand))
                    {
                        while (mySnapReader.Read())
                        {
                            snapmirror.Id = Convert.IsDBNull(mySnapReader["Id"]) ? 0 : Convert.ToInt32(mySnapReader["Id"]);
                            snapmirror.ReplicationId = Convert.IsDBNull(mySnapReader["ReplicationId"]) ? 0 : Convert.ToInt32(mySnapReader["ReplicationId"]);
                            snapmirror.MCPRServerId = Convert.IsDBNull(mySnapReader["MCPRServerId"]) ? 0 : Convert.ToInt32(mySnapReader["MCPRServerId"]);
                            snapmirror.MCDRServerId = Convert.IsDBNull(mySnapReader["MCDRServerId"]) ? 0 : Convert.ToInt32(mySnapReader["MCDRServerId"]);
                            snapmirror.PRStorageId = Convert.IsDBNull(mySnapReader["PRStorageId"]) ? string.Empty : Convert.ToString(mySnapReader["PRStorageId"]);
                            snapmirror.DRStorageId = Convert.IsDBNull(mySnapReader["DRStorageId"]) ? string.Empty : Convert.ToString(mySnapReader["DRStorageId"]);
                            snapmirror.Mode = Convert.IsDBNull(mySnapReader["ModeType"]) ? 0 : Convert.ToInt32(mySnapReader["ModeType"]);
                            snapmirror.PRVolume = Convert.IsDBNull(mySnapReader["PRVolume"]) ? string.Empty : Convert.ToString(mySnapReader["PRVolume"]);
                            snapmirror.DRVolume = Convert.IsDBNull(mySnapReader["DRVolume"]) ? string.Empty : Convert.ToString(mySnapReader["DRVolume"]);
                            snapmirror.PRLunPath = Convert.IsDBNull(mySnapReader["PRLunPath"]) ? string.Empty : Convert.ToString(mySnapReader["PRLunPath"]);
                            snapmirror.DRLunPath = Convert.IsDBNull(mySnapReader["DRLunPath"]) ? string.Empty : Convert.ToString(mySnapReader["DRLunPath"]);
                            snapmirror.PRLunmapId = Convert.IsDBNull(mySnapReader["PRLunmapId"]) ? string.Empty : Convert.ToString(mySnapReader["PRLunmapId"]);
                            snapmirror.DRLunmapId = Convert.IsDBNull(mySnapReader["DRLunmapId"]) ? string.Empty : Convert.ToString(mySnapReader["DRLunmapId"]);
                            snapmirror.PRLunSerialNo = Convert.IsDBNull(mySnapReader["PRLunSerialNo"]) ? string.Empty : Convert.ToString(mySnapReader["PRLunSerialNo"]);
                            snapmirror.DRLunSerialNo = Convert.IsDBNull(mySnapReader["DRLunSerialNo"]) ? string.Empty : Convert.ToString(mySnapReader["DRLunSerialNo"]);

                            //snapMirror.Id = Convert.ToInt32(mySnapReader[0]);
                            //snapMirror.ReplicationId = Convert.ToInt32(mySnapReader[1].ToString());
                            //snapMirror.MCPRServerId = Convert.ToInt32(mySnapReader[2]);
                            //snapMirror.MCDRServerId = Convert.ToInt32(mySnapReader[3]);
                            //snapMirror.PRStorageId = mySnapReader[4].ToString();
                            //snapMirror.DRStorageId = mySnapReader[5].ToString();
                            //snapMirror.Mode = Convert.ToInt32(mySnapReader[6]);
                            //snapMirror.PRVolume = mySnapReader[7].ToString();
                            //snapMirror.DRVolume = mySnapReader[8].ToString();
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get SnapMirror information by Replication Id - " + id, exc);

            }
            return snapmirror;
        }
         
    }
}