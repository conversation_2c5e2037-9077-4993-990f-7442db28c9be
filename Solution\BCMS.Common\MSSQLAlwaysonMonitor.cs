﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class MSSQLAlwaysonMonitor : BaseEntity
    {
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRInstanceName { get; set; }

        [DataMember]
        public string DRInstanceName { get; set; }

        [DataMember]
        public string PRAGroupname { get; set; }

        [DataMember]
        public string DRAGroupname { get; set; }

        [DataMember]
        public string PRAGrouprole { get; set; }

        [DataMember]
        public string DRAGrouprole { get; set; }

        [DataMember]
        public string PRReplicaMode { get; set; }

        [DataMember]
        public string DRReplicaMode { get; set; }

        [DataMember]
        public string PRFailovermode { get; set; }

        [DataMember]
        public string DRFailovermode { get; set; }


        [DataMember]
        public string PRPSroleallowconn { get; set; }

        [DataMember]
        public string DRPSroleallowconn { get; set; }

        [DataMember]
        public string PRAGOperationalstate { get; set; }

        [DataMember]
        public string DRAGOperationalstate { get; set; }

        [DataMember]
        public string PRAGconnectedstate { get; set; }

        [DataMember]
        public string DRAGconnectedstate { get; set; }

        [DataMember]
        public string PRDBSynchronizationState { get; set; }

        [DataMember]
        public string DRDBSynchronizationState { get; set; }

        [DataMember]
        public string PRDBSynchronizationhealth { get; set; }

        [DataMember]
        public string DRDBSynchronizationhealth { get; set; }

        [DataMember]
        public string PRDBstate { get; set; }

        [DataMember]
        public string DRDBstate { get; set; }

        [DataMember]
        public string PRDBsynchron_state_onavaildatabase { get; set; }

        [DataMember]
        public string DRDBsynchron_state_onavaildatabase { get; set; }

        [DataMember]
        public string PREndpointPortNumber { get; set; }

        [DataMember]
        public string DREndpointPortNumber { get; set; }

        [DataMember]
        public string PRLastSentLSN { get; set; }

        [DataMember]
        public string DRLastSentLSN { get; set; }

        [DataMember]
        public string PRLastReceivedLSN { get; set; }

        [DataMember]
        public string DRLastReceivedLSN { get; set; }

        [DataMember]
        public string PRLastRedoneLSN { get; set; }

        [DataMember]
        public string DRLastRedoneLSN { get; set; }

        [DataMember]
        public string PRLastCommitLSN { get; set; }

        [DataMember]
        public string DRLastCommitLSN { get; set; }

        [DataMember]
        public string PRLastSentTime { get; set; }

        [DataMember]
        public string DRLastSentTime { get; set; }

        [DataMember]
        public string PRLastReceivedTime { get; set; }

        [DataMember]
        public string DRLastReceivedTime { get; set; }

        [DataMember]
        public string PRLastRedoneTime { get; set; }

        [DataMember]
        public string DRLastRedoneTime { get; set; }

        [DataMember]
        public string PRLastCommitTime { get; set; }

        [DataMember]
        public string DRLastCommitTime { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        [DataMember]
        public string PRLogSendQueueSizeinKB { get; set; }

        [DataMember]
        public string DRLogSendQueueSizeinKB { get; set; }

        [DataMember]
        public string PRRedoQueueSizeinKB { get; set; }

        [DataMember]
        public string DRRedoQueueSizeinKB { get; set; }

        [DataMember]
        public string PRLastHardenedLSN { get; set; }

        [DataMember]
        public string PRLastHardenedTime { get; set; }

        [DataMember]
        public string DRLastHardenedLSN { get; set; }

        [DataMember]
        public string DRLastHardenedTime { get; set; }

        [DataMember]
        public string PRDatabaseSize { get; set; }

        [DataMember]
        public string DRDatabaseSize { get; set; }

        [DataMember]
        public string PRMSSQLEdition { get; set; }

        [DataMember]
        public string DRMSSQLEdition { get; set; }


        [DataMember]
        public string DataLag_HHMMSS { get; set; }
    }
}
