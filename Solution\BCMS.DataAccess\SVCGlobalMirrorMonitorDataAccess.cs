﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;


namespace Bcms.DataAccess
{
    public class SVCGlobalMirrorMonitorDataAccess : BaseDataAccess
    {

        public static bool AddSVCGlobalMirrorMonitorLog(SVCGlobalMirrorMonitor svcglobalMirror)
        {

            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("SVCGLBLMIRMONITORLOGS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "IINFRAOBJECTID", DbType.Int32, svcglobalMirror.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPID", DbType.AnsiString, svcglobalMirror.PRGroupID);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPNAME", DbType.AnsiString, svcglobalMirror.PRGroupName);
                    Database.AddInParameter(cmd, Dbstring + "IPRMASTERCLUSTERNAME", DbType.AnsiString, svcglobalMirror.PRMasterClusterName);
                    Database.AddInParameter(cmd, Dbstring + "IPRAUXILIARYCLUSTERNAME", DbType.AnsiString, svcglobalMirror.PRAuxiliaryClusterName);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPPRIMARYVALUE", DbType.AnsiString, svcglobalMirror.PRGroupPrimaryValue);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPSTATE", DbType.AnsiString, svcglobalMirror.PRGroupState);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPFREEZETIME", DbType.AnsiString, svcglobalMirror.PRGroupfreezetime);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPSTATUS", DbType.AnsiString, svcglobalMirror.PRGroupStatus);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPSYNCSTATUS", DbType.AnsiString, svcglobalMirror.PRGroupSyncStatus);
                    Database.AddInParameter(cmd, Dbstring + "IPRMIRRORRELNAME", DbType.AnsiString, svcglobalMirror.PRMirrorRelName);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPRELCOUNT", DbType.AnsiString, svcglobalMirror.PRGroupRelCount);
                    Database.AddInParameter(cmd, Dbstring + "IPRGROUPRELNAME", DbType.AnsiString, svcglobalMirror.PRGroupRelName);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPID", DbType.AnsiString, svcglobalMirror.DRGroupID);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPNAME", DbType.AnsiString, svcglobalMirror.DRGroupName);
                    Database.AddInParameter(cmd, Dbstring + "IDRMASTERCLUSTERNAME", DbType.AnsiString, svcglobalMirror.DRMasterClusterName);
                    Database.AddInParameter(cmd, Dbstring + "IDRAUXILIARYCLUSTERNAME", DbType.AnsiString, svcglobalMirror.DRAuxiliaryClusterName);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPPRIMARYVALUE", DbType.AnsiString, svcglobalMirror.DRGroupPrimaryValue);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPSTATE", DbType.AnsiString, svcglobalMirror.DRGroupState);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPFREEZETIME", DbType.AnsiString, svcglobalMirror.DRGroupfreezetime);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPSTATUS", DbType.AnsiString, svcglobalMirror.DRGroupStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPSYNCSTATUS", DbType.AnsiString, svcglobalMirror.DRGroupSyncStatus);
                    Database.AddInParameter(cmd, Dbstring + "IDRMIRRORRELNAME", DbType.AnsiString, svcglobalMirror.DRMirrorRelName);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPRELCOUNT", DbType.AnsiString, svcglobalMirror.DRGroupRelCount);
                    Database.AddInParameter(cmd, Dbstring + "IDRGROUPRELNAME", DbType.AnsiString, svcglobalMirror.DRGroupRelName);
                    Database.AddInParameter(cmd, Dbstring + "IDATALAG", DbType.AnsiString, svcglobalMirror.Datalag);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add SVCGlobalMirrorMonitor Details", exc);
            }
        }
    }
}
