﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using log4net;
using Bcms.Common.Shared;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class PostgreDataAccess : BaseDataAccess
    {
        public static bool AddPostgreDBStatus(PostgredbMonitoring postgredbmonitoring)
        {
            const string sp = "PostgreDBStatus_Create";

            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, postgredbmonitoring.InfraObjectId);
                Database.AddInParameter(cmd, Dbstring+"iDBVersionPR", DbType.AnsiString, postgredbmonitoring.DBVersionPR);
                Database.AddInParameter(cmd, Dbstring+"iDBVersionDR", DbType.AnsiString, postgredbmonitoring.DBVersionDR);
                Database.AddInParameter(cmd, Dbstring+"iDBServiceStatusPR", DbType.AnsiString, postgredbmonitoring.DBServiceStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iDBServiceStatusDR", DbType.AnsiString, postgredbmonitoring.DBServiceStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDBClusterStatePR", DbType.AnsiString, postgredbmonitoring.DBClusterStatePR);
                Database.AddInParameter(cmd, Dbstring+"iDBClusterStateDR", DbType.AnsiString, postgredbmonitoring.DBClusterStateDR);
                Database.AddInParameter(cmd, Dbstring+"iDBRecoveryStatusPR", DbType.AnsiString, postgredbmonitoring.DBRecoveryStatusPR);
                Database.AddInParameter(cmd, Dbstring+"iDBRecoveryStatusDR", DbType.AnsiString, postgredbmonitoring.DBRecoveryStatusDR);
                Database.AddInParameter(cmd, Dbstring+"iDBDataDirectorypathPR", DbType.AnsiString, postgredbmonitoring.DBDataDirectorypathPR);
                Database.AddInParameter(cmd, Dbstring+"iDBDataDirectorypathDR", DbType.AnsiString, postgredbmonitoring.DBDataDirectorypathDR);
                Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.AnsiString, postgredbmonitoring.CreatorId);
                int returnCode = Database.ExecuteNonQuery(cmd);
                return returnCode > 0;
            }
        }

        public static bool AddPostgreDBReplication(PostgreReplication postgrereplication)
        {
            const string sp = "PostgreDBRep_Create";

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, postgrereplication.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationStatusPR", DbType.AnsiString, postgrereplication.ReplicationStatusPR);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationStatusDR", DbType.AnsiString, postgrereplication.ReplicationStatusDR);
                    Database.AddInParameter(cmd, Dbstring+"iCurrent_xlog_location", DbType.AnsiString, postgrereplication.Current_xlog_location);
                    Database.AddInParameter(cmd, Dbstring+"iLast_xlog_receive_location", DbType.AnsiString, postgrereplication.Last_xlog_receive_location);
                    Database.AddInParameter(cmd, Dbstring+"iLast_xlog_replay_location", DbType.AnsiString, postgrereplication.Last_xlog_replay_location);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag_MB", DbType.AnsiString, postgrereplication.DataLag_MB);
                    Database.AddInParameter(cmd, Dbstring+"iDataLag_HHMMSS", DbType.AnsiString, postgrereplication.DataLag_HHMMSS);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.AnsiString, postgrereplication.CreatorId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                string str = ex.Message;
                return false;
            }
        }
    }
}
