﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Bcms.Common.Base;
using System.Runtime.Serialization;

namespace Bcms.Common
{
    public class SMSConfiguration: BaseEntity
    {
         #region Properties

        [DataMember]
        public string URL
        {
            get;
            set;
        }

        [DataMember]
        public string SenderId
        {
            get;
            set;
        }

         [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public SMSConfiguration()
            : base()
        {
        }

        #endregion
    }
}
