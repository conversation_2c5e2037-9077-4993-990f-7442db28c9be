﻿using System;
using System.Runtime.Serialization;
using Bcms.Common;
using Bcms.Common.Base;
namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "bcms_hitachiur_luns", Namespace = "http://www.BCMS.com/types")]

    public class HitachiUrLuns : BaseEntity
    {
        ReplicationBase _replicationBase = new ReplicationBase();

        #region Properties

        [DataMember]
        public string ArchVGName
        {
            get;
            set;
        }

        [DataMember]
        public string ArchMountPoint
        {
            get;
            set;
        }

        [DataMember]
        public string ArchDevicetype
        {
            get;
            set;
        }

        [DataMember]
        public string ArchHUR_TrueCopySource
        {
            get;
            set;
        }

        [DataMember]
        public string ArchShadowimagePR
        {
            get;
            set;
        }

        [DataMember]
        public string ArchTarget
        {
            get;
            set;
        }

        [DataMember]
        public string RedoVGName
        {
            get;
            set;
        }

        [DataMember]
        public string RedoMountPoint
        {
            get;
            set;
        }

        [DataMember]
        public string RedoDeviceType
        {
            get;
            set;
        }

        [DataMember]
        public string RedoHUR_TrueCopySource
        {
            get;
            set;
        }

        [DataMember]
        public string RedoShadowimagePR
        {
            get;
            set;
        }

        [DataMember]
        public string RedoTarget
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get
            {
                return _replicationBase;
            }
            set
            {
                _replicationBase = value;
            }
        }

        #endregion

        #region Constructor

        public HitachiUrLuns()
            : base()
        {
        }

        #endregion
    }
}
