﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Core;
using Bcms.Core.Client;
using log4net;
using BCMS.Common;

namespace Bcms.Core.Job
{
    public class SybaseWithEmcSRDFMonitoringJob : IJob
    {
        public const string InfraObject = "InfraObject";

        private static readonly ILog Logger = LogManager.GetLogger(typeof(SybaseWithEmcSRDFMonitoringJob));

        #region IJob Members

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var infraObject = data.Get(InfraObject) as InfraObject;
            if (infraObject != null)
            {
                InfraObject InfraObjectItem = InfraObjectDataAccess.GetInfraObjectById(infraObject.Id);

                if (InfraObjectItem != null)
                {
                    if (InfraObjectItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : InfraObject is not in active mode, skip the monitor components job cycle ", InfraObjectItem.Name);

                        return;
                    }
                    MonitorSybaseWithEmcSrdf(InfraObjectItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor Server Job ");
            }
        }

        public void MonitorSybaseWithEmcSrdf(InfraObject infraObject)
        {
            try
            {
                using (var client = new SybaseWithEmcSrdfClient(infraObject))
                {
                    Logger.Info("Sybase With EMCSRDF InfraObject Name: " + infraObject.Name.ToString() +
                                   " Started Time " + DateTime.Now.ToString());
                    SyBaseMonitor _syBaseGetMonitorDetailsForCheckGen = SybaseStatusDataAccess.GetSybaseMonitorStatusByInfraobjectId(infraObject.Id);
                    //if (_syBaseGetMonitorDetailsForCheckGen.IsApplied == 3 || _syBaseGetMonitorDetailsForCheckGen.IsApplied == 0)
                    //{
                    client.SybaseComponentMonitoring();
                    //}                   
                    Logger.Info("SybaseWithEMCSRDFMonitoring Component Completed");
                    Logger.Info("Sybase With EmcSrdf InfraObject Name: " + infraObject.Name.ToString() +
                                " Completed Time " + DateTime.Now.ToString());
                }
            }
            catch (BcmsException exc)
            {
                Logger.Info("Exception:MonitorSybaseWithEMCSRDFComponent " + infraObject.Name.ToString() + exc.Message);
                ExceptionManager.Manage(exc, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
            catch (Exception exc)
            {
                Logger.Info("Exception:MonitorSybaseWithEMCSRDFComponent " + infraObject.Name.ToString() + exc.Message);
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.MonitorComponent.ToString(), infraObject.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.MonitorComponent.ToString(), infraObject.Id, infraObject.Name);
            }
        }

        #endregion
    }
}
