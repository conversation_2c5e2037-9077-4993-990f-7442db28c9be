﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using Bcms.Core;
using log4net;

namespace Bcms.ParallelWorkflowService
{
    public partial class BcmsParallelWorkflowService : ServiceBase
    {
        private readonly IBcmsCore _server;

        private readonly ILog _logger = LogManager.GetLogger(typeof(BcmsParallelWorkflowService));

        public BcmsParallelWorkflowService()
        {
            log4net.Config.XmlConfigurator.Configure();

            CanShutdown = true;
            CanStop = true;
            CanPauseAndContinue = true;
            ServiceName = "CPParallelService";

            _logger.Info("Obtaining instance of an ICPServer");

            _server = BcmsServerFactory.CreateServer();

            _logger.Info("Parallel Server initialized");
         
        }

        protected override void OnStart(string[] args)
        {
            _logger.Info("CP Parallel Service Started");
        }

        protected override void OnStop()
        {
            _logger.Info("CP Parallel Service stopped");
        }

        protected override void OnCustomCommand(int command)
        {
            // Must be int between 128 and 255

            switch (command)
            {
                case 130:
                    _logger.InfoFormat("SwitchOver Start");

                    _server.SwitchOver();

                    break;
                case 131:
                    _logger.InfoFormat("SwitchBack Start");

                    _server.SwitchBack();

                    break;
                case 132:
                    _logger.InfoFormat("Power On Start");

                    //  _server.PowerOn();

                    break;
                case 133:
                    _logger.InfoFormat("Power Off Start");

                    // _server.PowerOff();

                    break;

                case 134:

                    _logger.InfoFormat("Custom Action workflow Start");

                    _server.CustomAction();

                    break;

                case 135:

                    _logger.InfoFormat(" Database backup Start");

                    _server.ExecuteBackup();

                    break;

                case 136:

                    _logger.InfoFormat("Custom workflow Start");

                    _server.Custom();

                    break;


                case 137:

                    _logger.InfoFormat("Start Parallel workflow");

                    _server.InitializeParallel();

                    break;
                case 138:

                    _logger.InfoFormat("Start Updating SqlNative Job Status");

                    _server.SqlNativeJobStatus();

                    break;
            }
        }
    }
}
