﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;


namespace Bcms.DataAccess
{
    public class GlobalMirrorLunsDataAccess : BaseDataAccess
    {
        public static IList<GlobalMirrorLuns> GetGlobalMirrorLunsByGlobalMirrorId(int id)
        {
            IList<GlobalMirrorLuns> lunsList = new List<GlobalMirrorLuns>();

            try
            {
                var db = DatabaseFactory.CreateDatabase();
                using (DbCommand dbCommand = db.GetStoredProcCommand("GlobalMirrorLuns_GetByGlobalMirrorId"))
                {
                    db.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);
                    using (IDataReader myGlobalLunsReader = db.ExecuteReader(dbCommand))
                    {
                        while (myGlobalLunsReader.Read())
                        {
                            var globalMirrorLuns = new GlobalMirrorLuns
                                                       {
                                                           Id = Convert.IsDBNull(myGlobalLunsReader["Id"]) ? 0 : Convert.ToInt32(myGlobalLunsReader["Id"]),
                                                           GlobalMirrorId = Convert.IsDBNull(myGlobalLunsReader["GlobalMirrorId"]) ? 0 : Convert.ToInt32(myGlobalLunsReader["GlobalMirrorId"]),
                                                           AVolume = Convert.IsDBNull(myGlobalLunsReader["AVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["AVolume"]),
                                                           BVolume = Convert.IsDBNull(myGlobalLunsReader["BVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["BVolume"]),
                                                           CVolume = Convert.IsDBNull(myGlobalLunsReader["CVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["CVolume"]),
                                                           DVolume = Convert.IsDBNull(myGlobalLunsReader["DVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["DVolume"]),
                                                           EVolume = Convert.IsDBNull(myGlobalLunsReader["EVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["EVolume"]),
                                                           FVolume = Convert.IsDBNull(myGlobalLunsReader["FVolume"]) ? string.Empty : Convert.ToString(myGlobalLunsReader["FVolume"])


                                                           //Id = Convert.ToInt32(myGlobalLunsReader[0]),
                                                           //GlobalMirrorId = Convert.ToInt32(myGlobalLunsReader[1]),
                                                           //AVolume = myGlobalLunsReader[2].ToString(),
                                                           //BVolume = myGlobalLunsReader[3].ToString(),
                                                           //CVolume = myGlobalLunsReader[4].ToString(),
                                                           //DVolume = myGlobalLunsReader[5].ToString(),
                                                           //EVolume = myGlobalLunsReader[6].ToString(),
                                                           //FVolume = myGlobalLunsReader[7].ToString()
                                                       };
                            lunsList.Add(globalMirrorLuns);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get GlobalMirrorLuns information by Id - " + id, exc);
            }

            return lunsList;
        }
    }
}
