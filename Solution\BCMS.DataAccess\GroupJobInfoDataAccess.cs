﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.DataAccess.Utility;


namespace Bcms.DataAccess
{
    public  class GroupJobInfoDataAccess : BaseDataAccess
    {
        public static IList<GroupJob> GetAllInfraJobInfo()
        {

            IList<GroupJob> infrajob = new List<GroupJob>();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("JobManagamentGetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_dbbackupInfo"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            var infrjobtype = new GroupJob()
                            {
                                Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]),
                                InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]),
                                BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]),
                                StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]),
                                JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]),
                                TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]),
                                CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]),
                                CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]),
                                NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]),
                                LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]),
                                JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]),
                                IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]),
                                IsActive =  Convert.ToBoolean(myReader["IsActive"]),
                                InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]),
                                JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]),
                                BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]),
                                JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]),
                                ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]),
                                CronExpressionChange = Convert.IsDBNull(myReader["CronExpressionChange"]) ? 0 : Convert.ToInt32(myReader["CronExpressionChange"]),
                                IsSchedule = Convert.IsDBNull(myReader["IsSchedule"]) ? 0 : Convert.ToInt32(myReader["IsSchedule"]),
                            };


                            infrajob.Add(infrjobtype);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", exc);
            }

            return infrajob;

        }

        public  GroupJob GetInfraObjectJobById(int id)
        {
            var infraObjectjob = new GroupJob();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("INFRAOBJECTJOB_GETBYINFRAID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, id);
                  
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                                infraObjectjob.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                                infraObjectjob.InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]);
                                infraObjectjob.BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]);
                                infraObjectjob.StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]);
                                infraObjectjob.JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]);
                                infraObjectjob.TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]);
                                infraObjectjob.CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]);
                                infraObjectjob.CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]);
                                infraObjectjob.NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]);
                                infraObjectjob.LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]);
                                infraObjectjob.JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]);
                                infraObjectjob.IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]);
                                infraObjectjob.IsActive = Convert.ToBoolean(myReader["IsActive"]);
                                infraObjectjob.InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]);
                                infraObjectjob.JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]);
                                infraObjectjob.BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]);
                                infraObjectjob.JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]);
                                infraObjectjob.ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObjectjob;
        }

        public static GroupJob GetInfraObjectJobByRepId(int id, int repid)
        {
            var infraObjectjob = new GroupJob();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObjectJob_GetByInfraRepId"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring + "iRepId", DbType.Int32, repid);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            infraObjectjob.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                            infraObjectjob.InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]);
                            infraObjectjob.BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]);
                            infraObjectjob.StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]);
                            infraObjectjob.JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]);
                            infraObjectjob.TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]);
                            infraObjectjob.CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]);
                            infraObjectjob.CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]);
                            infraObjectjob.NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]);
                            infraObjectjob.LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]);
                            infraObjectjob.JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]);
                            infraObjectjob.IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]);
                            infraObjectjob.IsActive = Convert.ToBoolean(myReader["IsActive"]);
                            infraObjectjob.InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]);
                            infraObjectjob.JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]);
                            infraObjectjob.BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]);
                            infraObjectjob.JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]);
                            infraObjectjob.ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObjectjob;
        }

        public static GroupJob TestGetInfraObjectJobById(int id, string classname)
        {
            var infraObjectjob = new GroupJob();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("INFRAOBJECTJOB_GETBYINFRAIDJOB"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring + "iClassName", DbType.String, classname);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            infraObjectjob.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                            infraObjectjob.InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]);
                            infraObjectjob.BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]);
                            infraObjectjob.StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]);
                            infraObjectjob.JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]);
                            infraObjectjob.TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]);
                            infraObjectjob.CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]);
                            infraObjectjob.CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]);
                            infraObjectjob.NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]);
                            infraObjectjob.LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]);
                            infraObjectjob.JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]);
                            infraObjectjob.IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]);
                            infraObjectjob.IsActive = Convert.ToBoolean(myReader["IsActive"]);
                            infraObjectjob.InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]);
                            infraObjectjob.JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]);
                            infraObjectjob.BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]);
                            infraObjectjob.JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]);
                            infraObjectjob.ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObjectjob;
        }

        public static bool UpdateCronValue(int iInfraObjectId)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("INFRAOBJ_CRONEXPRCHANGE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iInfraObjectId", DbType.Int32, iInfraObjectId);                  
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update InfraObject Details by Notification ", exc);
            }

        }
        public static GroupJob GetInfraObjectJobByBusinessId(int id)
        {
            var infraObjectjob = new GroupJob();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraObjectJob_GetByBusinessID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iBusinessId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            infraObjectjob.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                            infraObjectjob.InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]);
                            infraObjectjob.BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]);
                            infraObjectjob.StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]);
                            infraObjectjob.JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]);
                            infraObjectjob.TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]);
                            infraObjectjob.CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]);
                            infraObjectjob.CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]);
                            infraObjectjob.NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]);
                            infraObjectjob.LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]);
                            infraObjectjob.JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]);
                            infraObjectjob.IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]);
                            infraObjectjob.IsActive = Convert.ToBoolean(myReader["IsActive"]);
                            infraObjectjob.InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]);
                            infraObjectjob.JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]);
                            infraObjectjob.BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]);
                            infraObjectjob.JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]);
                            infraObjectjob.ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObjectjob;
        }

        public static GroupJob GetInfraObjectJobByJobId(int id)
        {
            var infraObjectjob = new GroupJob();

            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("INFRAOBJECTJOB_GETBYMONITORSERVICEJOBID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iJobId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            infraObjectjob.Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]);
                            infraObjectjob.InfraObjectId = Convert.IsDBNull(myReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myReader["InfraObjectId"]);
                            infraObjectjob.BusinessServiceId = Convert.IsDBNull(myReader["BusinessServiceId"]) ? 0 : Convert.ToInt32(myReader["BusinessServiceId"]);
                            infraObjectjob.StorageImageId = Convert.IsDBNull(myReader["StorageImageId"]) ? string.Empty : Convert.ToString(myReader["StorageImageId"]);
                            infraObjectjob.JobId = Convert.IsDBNull(myReader["JobId"]) ? 0 : Convert.ToInt32(myReader["JobId"]);
                            infraObjectjob.TriggerName = Convert.IsDBNull(myReader["TriggerName"]) ? string.Empty : Convert.ToString(myReader["TriggerName"]);
                            infraObjectjob.CronExpression = Convert.IsDBNull(myReader["CronExpression"]) ? string.Empty : Convert.ToString(myReader["CronExpression"]);
                            infraObjectjob.CronTime = Convert.IsDBNull(myReader["CronTime"]) ? string.Empty : Convert.ToString(myReader["CronTime"]);
                            infraObjectjob.NextFireTime = Convert.IsDBNull(myReader["NextFireTime"]) ? string.Empty : Convert.ToString(myReader["NextFireTime"]);
                            infraObjectjob.LastFireTime = Convert.IsDBNull(myReader["LastFireTime"]) ? string.Empty : Convert.ToString(myReader["LastFireTime"]);
                            infraObjectjob.JobStatus = Convert.IsDBNull(myReader["JobStatus"]) ? string.Empty : Convert.ToString(myReader["JobStatus"]);
                            infraObjectjob.IsEnabled = Convert.IsDBNull(myReader["IsEnabled"]) ? 0 : Convert.ToInt32(myReader["IsEnabled"]);
                            infraObjectjob.IsActive = Convert.ToBoolean(myReader["IsActive"]);
                            infraObjectjob.InfraObjType = Convert.IsDBNull(myReader["InfraObjType"]) ? 0 : Convert.ToInt32(myReader["InfraObjType"]);
                            infraObjectjob.JobName = Convert.IsDBNull(myReader["JobName"]) ? string.Empty : Convert.ToString(myReader["JobName"]);
                            infraObjectjob.BcmsClassName = Convert.IsDBNull(myReader["BcmsClassName"]) ? string.Empty : Convert.ToString(myReader["BcmsClassName"]);
                            infraObjectjob.JobTypeName = Convert.IsDBNull(myReader["JobTypeName"]) ? string.Empty : Convert.ToString(myReader["JobTypeName"]);
                            infraObjectjob.ParatmeterType = Convert.IsDBNull(myReader["ParameterType"]) ? string.Empty : Convert.ToString(myReader["ParameterType"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get InfraObject Details by id - " + id, exc);
            }

            return infraObjectjob;
        }

    }
}
