﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "VmWare", Namespace = "http://www.Bcms.com/types")]
    public class VmWare : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        [DataMember]
        public string ESXIhostName
        {
            get;
            set;
        }


        [DataMember]
        public string VirtualMachine
        {
            get;
            set;
        }

         [DataMember]
        public int PowerStatePR
        {
            get;
            set;
        }
         
        [DataMember]
        public string CurrentSnapshotPR
        {
            get;
            set;
        }

         [DataMember]
        public string UpdationTimeStampPR
        {
            get;
            set;
        }

         [DataMember]
         public int PowerStateDR
         {
             get;
             set;
         }

         [DataMember]
         public string CurrentSnapshotDR
         {
             get;
             set;
         }

         [DataMember]
         public string UpdationTimeStampDR
         {
             get;
             set;
         }

         [DataMember]
         public string CurrentDataLag
         {
             get;
             set;
         }
       

        #endregion

       #region Constructor

        public VmWare()
            : base()
        {
        }

        #endregion
    }
}
