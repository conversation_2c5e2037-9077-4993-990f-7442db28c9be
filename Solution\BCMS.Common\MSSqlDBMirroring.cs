﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
   public class MSSqlDBMirroring :  BaseEntity
    {

       #region Properties

        [DataMember]
        public string PRDatabaseName { get; set; }

        [DataMember]
        public string DRDatabaseName { get; set; }

        [DataMember]
        public string PRDBRole { get; set; }

        [DataMember]
        public string DRDBRole { get; set; }

        [DataMember]
        public string PRMirroring_State { get; set; }

        [DataMember]
        public string DRMirroring_State { get; set; }

        [DataMember]
        public string PRLog_GenerateRate { get; set; }

        [DataMember]
        public string DRLog_GenerateRate { get; set; }

        [DataMember]
        public string PRUnsent_Log { get; set; }

        [DataMember]
        public string DRUnsent_Log { get; set; }

        [DataMember]
        public string PRSent_Rate { get; set; }

        [DataMember]
        public string DRSent_Rate { get; set; }

        [DataMember]
        public string PRUnrestored_Log { get; set; }

        [DataMember]
        public string DRUnrestored_Log { get; set; }

        [DataMember]
        public string PRRecovery_Rate { get; set; }

        [DataMember]
        public string DRRecovery_Rate { get; set; }

        [DataMember]
        public string PRTransaction_Delay { get; set; }

        [DataMember]
        public string DRTransaction_Delay { get; set; }

        [DataMember]
        public string PRTransaction_PerSecond { get; set; }

        [DataMember]
        public string DRTransaction_PerSecond { get; set; }

        [DataMember]
        public string PRAverage_Delay { get; set; }

        [DataMember]
        public string DRAverage_Delay { get; set; }

        [DataMember]
        public DateTime PRTime_Recorded { get; set; }

        [DataMember]
        public DateTime DRTime_Recorded { get; set; }

        [DataMember]
        public DateTime PRTime_Behind { get; set; }

        [DataMember]
        public DateTime DRTime_Behind { get; set; }

        [DataMember]
        public DateTime PRLocal_Time { get; set; }

        [DataMember]
        public DateTime DRLocal_Time { get; set; }

       


        [DataMember]
        public int InfraObjectId { get; set; }

        #endregion Properties

        #region Constructor

        public MSSqlDBMirroring()
            : base()
        {
        }
        #endregion Constructor
    }
}
