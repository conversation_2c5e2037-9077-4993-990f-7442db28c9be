﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using System.Data.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;

namespace Bcms.DataAccess
{
    public class ApplicationDataAccess : BaseDataAccess
    {
        public static IList<Application> GetAllApplication()
        {

            IList<Application> applicatuions = new List<Application>();

            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Application_GetAll"))
                {

                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            var application = new Application();

                            application.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            application.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Name"]));
                            application.Details = Convert.IsDBNull(reader["Details"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Details"]));
                            application.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
                            application.ApplicationGroupId = Convert.IsDBNull(reader["ApplicationGroupId"]) ? 0 : Convert.ToInt32(reader["ApplicationGroupId"]);
                            application.PRApplicationServerId = Convert.IsDBNull(reader["PRAppServerId"]) ? 0 : Convert.ToInt32(reader["PRAppServerId"]);
                            application.DRApplicationServerId = Convert.IsDBNull(reader["DRAppServerId"]) ? 0 : Convert.ToInt32(reader["DRAppServerId"]);
                            application.Severity = Convert.IsDBNull(reader["Severity"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Severity"]));
                            application.Process = Convert.IsDBNull(reader["Process"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Process"]));
                            application.Output = Convert.IsDBNull(reader["Output"]) ? string.Empty : Convert.ToString(reader["Output"]);
                            application.IsMonitor = Convert.IsDBNull(reader["IsMonitor"]) ? 0 : Convert.ToInt32(reader["IsMonitor"]);
                            application.IsReplication = Convert.IsDBNull(reader["IsReplication"]) ? 0 : Convert.ToInt32(reader["IsReplication"]);
                            application.Method = Convert.IsDBNull(reader["Method"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Method"]));
                            application.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            application.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
                            application.RecoveryTime = Convert.IsDBNull(reader["RecoveryTime"]) ? 0 : Convert.ToInt32(reader["RecoveryTime"]);
                            application.ConfigureDataLag = Convert.IsDBNull(reader["ConfigureDataLag"]) ? 0 : Convert.ToInt32(reader["ConfigureDataLag"]);
                            application.MTPOD = Convert.IsDBNull(reader["MTPOD"]) ? 0 : Convert.ToInt32(reader["MTPOD"]);
                            application.DROperationStatus = Convert.IsDBNull(reader["DROperationStatus"]) ? 0 : Convert.ToInt32(reader["DROperationStatus"]);
                            application.DROperationId = Convert.IsDBNull(reader["DROperationId"]) ? 0 : Convert.ToInt32(reader["DROperationId"]);
                            applicatuions.Add(application);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Application Information", exc);
            }
            return applicatuions;


        }


        public static Application GetApplicationById(int id)
        {

            Application application = new Application();

            try
            {


                using (DbCommand dbCommand = Database.GetStoredProcCommand("Application_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            application.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            application.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Name"]));
                            application.Details = Convert.IsDBNull(reader["Details"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Details"]));
                            application.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
                            application.ApplicationGroupId = Convert.IsDBNull(reader["ApplicationGroupId"]) ? 0 : Convert.ToInt32(reader["ApplicationGroupId"]);
                            application.PRApplicationServerId = Convert.IsDBNull(reader["PRAppServerId"]) ? 0 : Convert.ToInt32(reader["PRAppServerId"]);
                            application.DRApplicationServerId = Convert.IsDBNull(reader["DRAppServerId"]) ? 0 : Convert.ToInt32(reader["DRAppServerId"]);
                            application.Severity = Convert.IsDBNull(reader["Severity"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Severity"]));
                            application.Process = Convert.IsDBNull(reader["Process"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Process"]));
                            application.Output = Convert.IsDBNull(reader["Output"]) ? string.Empty : Convert.ToString(reader["Output"]);
                            application.IsMonitor = Convert.IsDBNull(reader["IsMonitor"]) ? 0 : Convert.ToInt32(reader["IsMonitor"]);
                            application.IsReplication = Convert.IsDBNull(reader["IsReplication"]) ? 0 : Convert.ToInt32(reader["IsReplication"]);
                            application.Method = Convert.IsDBNull(reader["Method"]) ? string.Empty : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Method"]));
                            application.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            application.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
                            application.RecoveryTime = Convert.IsDBNull(reader["RecoveryTime"]) ? 0 : Convert.ToInt32(reader["RecoveryTime"]);
                            application.ConfigureDataLag = Convert.IsDBNull(reader["ConfigureDataLag"]) ? 0 : Convert.ToInt32(reader["ConfigureDataLag"]);
                            application.MTPOD = Convert.IsDBNull(reader["MTPOD"]) ? 0 : Convert.ToInt32(reader["MTPOD"]);
                            application.DROperationStatus = Convert.IsDBNull(reader["DROperationStatus"]) ? 0 : Convert.ToInt32(reader["DROperationStatus"]);
                            application.DROperationId = Convert.IsDBNull(reader["DROperationId"]) ? 0 : Convert.ToInt32(reader["DROperationId"]);
                    

                        }
                    }
                }              
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Application Information By Id " +id, exc);
            }

            return application;

        }


        public static bool UpdateApplicationByStatus(int applicationId, InfraObjectState state)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("Application_UpdateByStatus"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, applicationId);
                    Database.AddInParameter(dbCommand, Dbstring+"iStatus", DbType.AnsiString, state.ToString());
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Application Details by state ", exc);
            }

        }

    }
}
