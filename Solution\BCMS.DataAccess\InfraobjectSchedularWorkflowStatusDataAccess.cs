﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using System.Collections.Generic;

namespace Bcms.DataAccess
{
    public class InfraobjectSchedularWorkflowStatusDataAccess : BaseDataAccess
    {
        public static bool AddInfraobjectSchedularworkflowStatus(InfraobjectSchedularWorkflowStatus InfraobjectSchedularWorkflowStatus)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("InfraschuWfdeatils_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectName", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.InfraObjectName);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowName", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.WorkflowName);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowID", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionName", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.CurrentActionName);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionID", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.CurrentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iScheduleId", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.ScheduleId);
                    Database.AddInParameter(cmd, Dbstring + "iScheduleType", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.ScheduleType);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.Status);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, "2");
                    //Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.StartTime);
                    //Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.EndTime);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add Infraobject Schedular Workflow Status Details", exc);

            }
        }

        public static bool UpdateScheduleworkflowByStatus(InfraobjectSchedularWorkflowStatus InfraobjectSchedularWorkflowStatus)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("InfraschuWfdeatils_Update"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, InfraobjectSchedularWorkflowStatus.Id);
                    Database.AddInParameter(dbCommand, Dbstring + "iCurrentActionName", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.CurrentActionName);
                    Database.AddInParameter(dbCommand, Dbstring + "iCurrentActionId", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.CurrentActionId);
                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.Status);
                    Database.AddInParameter(dbCommand, Dbstring + "iType", DbType.AnsiString,"1");
                 //   Database.AddInParameter(dbCommand, Dbstring + "iEndTime", DbType.AnsiString, InfraobjectSchedularWorkflowStatus.EndTime);
                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Infraobject Schedular Workflow Status Details by Id ", exc);
            }
        }

        public static InfraobjectSchedularWorkflowStatus GetScheduleworkflowById(int ScheduleId, string ScheduleType, string Type, string status)
        {

            var SchedularWorkflow = new InfraobjectSchedularWorkflowStatus();

            using (DbCommand cmd = Database.GetStoredProcCommand("InfraWfGetbyschId"))
            {
                Database.AddInParameter(cmd, Dbstring + "iScheduleId", DbType.AnsiString, ScheduleId);
                Database.AddInParameter(cmd, Dbstring + "iScheduleType", DbType.AnsiString, ScheduleType);
                Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, Type);
                Database.AddInParameter(cmd, Dbstring + "istatus", DbType.AnsiString, status);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                using (IDataReader myParallelDROperationReader = Database.ExecuteReader(cmd))
                {
                    if (myParallelDROperationReader.Read())
                    {
                        SchedularWorkflow.Id = Convert.IsDBNull(myParallelDROperationReader["Id"]) ? 0 : Convert.ToInt32(myParallelDROperationReader["Id"]);
                        SchedularWorkflow.Status = Convert.IsDBNull(myParallelDROperationReader["Status"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Status"]);
                        SchedularWorkflow.Type = Convert.IsDBNull(myParallelDROperationReader["Type"]) ? string.Empty : Convert.ToString(myParallelDROperationReader["Type"]);

                    }
                    else
                    {
                        SchedularWorkflow = null;
                    }
                }

                return SchedularWorkflow;
            }

        }
    }
}
