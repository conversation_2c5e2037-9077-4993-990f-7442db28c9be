﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "FastCopy", Namespace = "http://www.BCMS.com/types")]
    public class FastCopy : BaseEntity
    {
 

        [DataMember]
        public string DataSyncPath { get; set; }

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }
      
        [DataMember]
        public bool IsCompression
        {
            get;
            set;
        }
        [DataMember]
        public bool IsFilter
        {
            get;
            set;
        }
        [DataMember]
        public string Wildcard 
        {
            get;
            set;
        }
        [DataMember]
        public string ProcessCode
        {
            get;
            set;
        }
        [DataMember]
        public string LocalDirectory
        {
            get;
            set;
        }
        [DataMember]
        public string RemoteDirectory
        {
            get;
            set;
        }
        [DataMember]
        public string OSPlatform
        {
            get;
            set;
        }
        [DataMember]
        public FastCopyMode ModeType
        {
            get;
            set;
        }
        [DataMember]
        public string ScheduleTime
        {
            get;
            set;
        }
        [DataMember]
        public int LastReplicationCount
        {
            get;
            set;
        }

        [DataMember]
        public string Datalag
        {
            get;
            set;
        }

        [DataMember]
        public int AccesstoSudoUser
        {
            get;
            set;
        }

        public FastCopy()
            : base()
        {
        }
         
    }
}