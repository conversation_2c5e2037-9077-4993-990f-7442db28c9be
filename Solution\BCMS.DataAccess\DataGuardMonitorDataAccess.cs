﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class DataGuardMonitorDataAccess : BaseDataAccess
    {
        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static bool AddDataGuardMonitorLogs(DataGuardMonitor oracleData)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("DataGuardMonitorLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iPRStatus", DbType.AnsiString, oracleData.PRStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRStatus", DbType.AnsiString, oracleData.DRStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRErrorReason", DbType.AnsiString, oracleData.PRErrorReason);
                    Database.AddInParameter(cmd, Dbstring + "iDRErrorReason", DbType.AnsiString, oracleData.DRErrorReason);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, oracleData.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseID", DbType.Int32, oracleData.PRDatabaseID);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseID", DbType.Int32, oracleData.DRDatabaseID);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }

                }

            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }
        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static bool AddDataGuardMonitorStatus(DataGuardMonitor oracleData)
        {
            try
            {

                using (DbCommand cmd = Database.GetStoredProcCommand("DataGuardMonitorStatus_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iPRStatus", DbType.AnsiString, oracleData.PRStatus);
                    Database.AddInParameter(cmd, Dbstring + "iDRStatus", DbType.AnsiString, oracleData.DRStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPRErrorReason", DbType.AnsiString, oracleData.PRErrorReason);
                    Database.AddInParameter(cmd, Dbstring + "iDRErrorReason", DbType.AnsiString, oracleData.DRErrorReason);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, oracleData.InfraObjectId);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }

            return false;
        }
        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static bool UpdateDataGuardMonitorStatus(DataGuardMonitor oracleData)
        {
            using (DbCommand cmd = Database.GetStoredProcCommand("DataGuardMonitorStatus_Create"))
            {
                Database.AddInParameter(cmd, Dbstring + "iPRStatus", DbType.AnsiString, oracleData.PRStatus);
                Database.AddInParameter(cmd, Dbstring + "iDRStatus", DbType.AnsiString, oracleData.DRStatus);
                Database.AddInParameter(cmd, Dbstring + "iPRErrorReason", DbType.AnsiString, oracleData.PRErrorReason);
                Database.AddInParameter(cmd, Dbstring + "iDRErrorReason", DbType.AnsiString, oracleData.DRErrorReason);
                Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, oracleData.InfraObjectId);

                var value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                if (value == -1)
                {
                    return true;
                }
#endif
                if (value > 0)
                {
                    return true;
                }

            }

            return false;
        }

        /// <Modified> Jeyapandi - CP 4.0v - 06-06-2014 for OracleDB</Modified>
        public static DataGuardMonitor GetDataGuardMonitorStatusGetByGroupId(int groupId)
        {
            var dgStatus = new DataGuardMonitor();

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("DataGuardMonitor_GetByInfrId"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, groupId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myDataguardReader = Database.ExecuteReader(cmd))
                    {
                        while (myDataguardReader.Read())
                        {

                            dgStatus.PRStatus = Convert.IsDBNull(myDataguardReader["PRStatus"]) ? string.Empty : Convert.ToString(myDataguardReader["PRStatus"]);
                            dgStatus.DRStatus = Convert.IsDBNull(myDataguardReader["DRStatus"]) ? string.Empty : Convert.ToString(myDataguardReader["DRStatus"]);
                            dgStatus.PRErrorReason = Convert.IsDBNull(myDataguardReader["PRErrorReason"]) ? string.Empty : Convert.ToString(myDataguardReader["PRErrorReason"]);
                            dgStatus.DRErrorReason = Convert.IsDBNull(myDataguardReader["DRErrorReason"]) ? string.Empty : Convert.ToString(myDataguardReader["DRErrorReason"]);
                            dgStatus.InfraObjectId = Convert.IsDBNull(myDataguardReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myDataguardReader["InfraObjectId"]);



                            //dgStatus.PRStatus = myDataguardReader.IsDBNull(1) ? string.Empty : myDataguardReader.GetString(1);
                            //dgStatus.DRStatus = myDataguardReader.IsDBNull(2) ? string.Empty : myDataguardReader.GetString(2);
                            //dgStatus.PRErrorReason = myDataguardReader.IsDBNull(3) ? string.Empty : myDataguardReader.GetString(3);
                            //dgStatus.DRErrorReason = myDataguardReader.IsDBNull(4) ? string.Empty : myDataguardReader.GetString(4);
                            //dgStatus.GroupId = myDataguardReader.IsDBNull(5)
                            //                       ? 0
                            //                       : myDataguardReader.GetInt32(5);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get DataGuard Monitor Status Details by GroupId - " + groupId, exc);
            }

            return dgStatus;
        }

    }
}