﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.Common;
using log4net;

namespace Bcms.DataAccess
{
    public class ApplicationDiscoveryDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(ApplicationDiscoveryDataAccess));

        public static ApplicationDiscovery AddApplicationDiscoveryData(ApplicationDiscovery applicationDiscovery)
        {
            ApplicationDiscovery returnApplicationDiscovery = null;

            try
            {
                const string sp = "ApplicationDiscovery_Create";

                using (DbCommand dbCommand = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iHost", DbType.AnsiString, applicationDiscovery.Host);
                    Database.AddInParameter(dbCommand, Dbstring + "iState", DbType.AnsiString, applicationDiscovery.State);
                    Database.AddInParameter(dbCommand, Dbstring + "iLastBoot", DbType.AnsiString, applicationDiscovery.LastBoot);
                    Database.AddInParameter(dbCommand, Dbstring + "iOperatingSystem", DbType.AnsiString, applicationDiscovery.OperatingSystem);
                    Database.AddInParameter(dbCommand, Dbstring + "iAllApps", DbType.AnsiString, applicationDiscovery.AllApps);
                    Database.AddInParameter(dbCommand, Dbstring + "iApplicationCount", DbType.Int32, applicationDiscovery.ApplicationCount);
                    Database.AddInParameter(dbCommand, Dbstring + "iDiscoveryProfileName", DbType.AnsiString, applicationDiscovery.DiscoveryProfileName);

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        returnApplicationDiscovery = new ApplicationDiscovery();

                        if (reader.Read())
                        {
                            returnApplicationDiscovery.Host = Convert.IsDBNull(reader["Host"]) ? string.Empty : Convert.ToString(reader["Host"]);

                            returnApplicationDiscovery.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);

                            returnApplicationDiscovery.LastBoot = Convert.IsDBNull(reader["LastBoot"]) ? string.Empty : Convert.ToString(reader["LastBoot"]);

                            returnApplicationDiscovery.OperatingSystem = Convert.IsDBNull(reader["OperatingSystem"]) ? string.Empty : Convert.ToString(reader["OperatingSystem"]);

                            returnApplicationDiscovery.AllApps = Convert.IsDBNull(reader["AllApps"]) ? string.Empty : Convert.ToString(reader["AllApps"]);

                            returnApplicationDiscovery.ApplicationCount = Convert.IsDBNull(reader["ApplicationCount"]) ? 0 : Convert.ToInt32(reader["ApplicationCount"]);

                            returnApplicationDiscovery.DiscoveryProfileName = Convert.IsDBNull(reader["DiscoveryProfileName"]) ? string.Empty : Convert.ToString(reader["DiscoveryProfileName"]);

                        }

                    }
                    return returnApplicationDiscovery;

                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert ApplicationDiscovery information", exc);
            }

        }
    }
}
