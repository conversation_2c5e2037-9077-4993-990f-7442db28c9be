﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "bcms_hitachiURMonitoring", Namespace = "http://www.BCMS.com/types")]
    public class HitachiURMonitoring : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }

        [DataMember]
        public string PRHORCOMStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DRHORCOMStatus
        {
            get;
            set;
        }

        [DataMember]
        public string AverageCopyPendingTime
        {
            get;
            set;
        }
         [DataMember]
        public DateTime LastPairTime
        {
            get;
            set;
        }
        
        #endregion Properties
    }
}