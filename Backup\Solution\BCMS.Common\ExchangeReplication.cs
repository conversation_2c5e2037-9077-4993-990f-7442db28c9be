﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ExchangeReplication", Namespace = "http://www.BCMS.com/types")]
    public class ExchangeReplication : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ReplicationName
        {
            get;
            set;
        }
        [DataMember]
        public int PRServerID
        {
            get;
            set;
        }
        [DataMember]
        public int DRServerID
        {
            get;
            set;
        }

        [DataMember]
        public string PRExchangeInstallationFolderPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRExchangeInstallationFolderPath
        {
            get;
            set;
        }
        [DataMember]
        public string PRExchangeNewMailboxPath
        {
            get;
            set;
        }
        [DataMember]
        public string DRExchangeNewMailboxPath
        {
            get;
            set;
        }
        [DataMember]
        public string PRExchangeBCMSExchangeComponentPath
        {
            get;
            set;
        }
        [DataMember]
        public string DRExchangeBCMSExchangeComponentPath
        {
            get;
            set;
        }
        [DataMember]
        public string ExchangeBCMSExchangeEXEName
        {
            get;
            set;
        }
        [DataMember]
        public string ReplaylagTime
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public ExchangeReplication()
            : base()
        {
        }

        #endregion
    }
}
