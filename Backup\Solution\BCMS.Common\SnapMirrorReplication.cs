﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SnapMirrorReplication", Namespace = "http://www.BCMS.com/types")]
    public class SnapMirrorReplication : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Source
        {
            get;
            set;
        }
        [DataMember]
        public string Destination
        {
            get;
            set;
        }
        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public string Lag
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }
        #endregion

        #region Constructor

        public SnapMirrorReplication()
            : base()
        {
        }

        #endregion
    }
}