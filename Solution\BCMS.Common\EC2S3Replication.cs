﻿using Bcms.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "EC2S3Replication", Namespace = "http://www.BCMS.com/types")]
    public class EC2S3Replication : BaseEntity
    {
        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public string AWSKey
        {
            get;
            set;
        }

        [DataMember]
        public string AWSSecretKey
        {
            get;
            set;
        }

        [DataMember]
        public string EC2InstanceId
        {
            get;
            set;
        }

        [DataMember]
        public string RegionEndPoint
        {
            get;
            set;
        }
        
        [DataMember]
        public string S3BucketName
        {
            get;
            set;
        }

        [DataMember]
        public string SourceDataPath
        {
            get;
            set;
        }
        
        [DataMember]
        public bool IsPullPushMechanism
        {
            get;
            set;
        }
        

        #endregion Properties

    }
}
