﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MAXDB", Namespace = "http://www.BCMS.com/types")]
    public class MAXDBMonitoring : BaseEntity
    {
         #region Constructor

        public MAXDBMonitoring()
            : base()
        {
        }
        #endregion Constructor
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRDBIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBIPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string PRDBName
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBName
        {
            get;
            set;
        }

        [DataMember]
        public string PRDBState
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBState
        {
            get;
            set;
        }

        [DataMember]
        public string PRDBVersion
        {
            get;
            set;
        }

        [DataMember]
        public string DRDBVersion
        {
            get;
            set;
        }


        #endregion Properties
    }
}
