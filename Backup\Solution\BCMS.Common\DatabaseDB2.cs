﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseSql", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseDB2 : BaseEntity
    {
        #region Properties


        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseSID
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }
        [DataMember]
        public int Port
        {
            get;
            set;
        }


        #endregion

        #region Constructor

        public DatabaseDB2()
            : base()
        {
        }

        #endregion
    }
}
