﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Xml;
using System.Linq;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess;
using Bcms.Replication.Shared;
using log4net;
using Quartz;
using Bcms.Impl;
using System.Threading.Tasks;
using Bcms.ExceptionHandler;

namespace Bcms.Core.Workflows
{
    public class WorkflowClient : IDisposable
    {

        #region Global Variables

        private bool _isDisposed;

        private static readonly ILog Logger = LogManager.GetLogger(typeof(WorkflowClient));
        private Serilog.ILogger Logger1;

        private ISchedulerFactory _parallelSchedulerFactory;

        private IScheduler _parallelScheduler;

        private IScheduler _parallelScheduler1;

        //ITIT-10566
        private ParallelOptions option = new ParallelOptions();

        #endregion

        #region Properties

        public ParallelGroupWorkflow CurrentParallelGroupWorkflow { get; set; }

        public InfraObject CurrentInfraObject { get; set; }

        public Workflow CurrentWorkflow { get; set; }

        public int TotalActionCount { get; set; }

        public bool IsAbort { get; set; }

        public ParallelOptions Option = new ParallelOptions();

        public CancellationTokenSource CancellationTokenSource { get; set; }

        #endregion

        #region Workflow Methods

        /// <summary>
        /// Run Workflow in Parallel mode
        /// </summary>
        public void PerformParallelWorkflow(Serilog.ILogger logger)
        {
            Logger1 = logger;
            try
            {
                Logger.InfoFormat("{0} : Perform Parallel workflow", CurrentParallelGroupWorkflow.WorkflowName);

                if (CurrentInfraObject != null)
                {
                    //Get workflow according to ParallelGroupWorkflowId 

                    var workflow = WorkflowDataAccess.GetWorkflowById(CurrentParallelGroupWorkflow.WorkflowId);

                    if (workflow != null)
                    {
                        PerformParallelWorkflowActions(workflow, logger);

                        var groupWorkflow = GroupWorkflowDataAccess.GetByGroupIdAndWorkflowId(CurrentParallelGroupWorkflow.InfraObjectId,
                                                                              CurrentParallelGroupWorkflow.WorkflowId);

                        if (groupWorkflow != null)
                        {
                            int status = 0;

                            switch (groupWorkflow.ActionType)
                            {
                                case (int)DROperationType.SwitchOver:

                                    status = (int)GroupWorkflowOperation.SwitchOverCompleted;

                                    try
                                    {
                                        string totalRto = ParallelGroupWorkflowDataAccess.GetRTOByParallelGroupWorkflowId(CurrentParallelGroupWorkflow.Id, CurrentInfraObject.Id);

                                        Logger.Info("Workflow (" + CurrentParallelGroupWorkflow.WorkflowName +
                                                    ") Total RTO (Seconds)" + totalRto);

                                        if (!string.IsNullOrEmpty(totalRto))
                                        {

                                            string totalRtoTimespan = new TimeSpan(0, 0, 0, Convert.ToInt32(totalRto)).ToString();


                                            Logger.Info("Workflow (" + CurrentParallelGroupWorkflow.WorkflowName +
                                                        ") Total RTO (TimeSpan)" + totalRtoTimespan);


                                            var rto = new BusinessServiceRTOInfo();

                                            rto.BusinessServiceId = CurrentInfraObject.BusinessServiceId;
                                            rto.BusinessFunctionId = CurrentInfraObject.BusinessFunctionId;
                                            rto.InfraObjectId = CurrentInfraObject.Id;
                                            rto.CurrentRTO = totalRtoTimespan;
                                            BusinessServiceRTOInfoDataAccess.AddBusinessServiceRTOInfo(rto);


                                        }
                                    }
                                    catch (Exception exc)
                                    {
                                        Logger.Error("Exception occurred while insert business Service RTO info" + exc.Message);
                                    }
                                    break;
                                case (int)DROperationType.SwitchBack:

                                    status = (int)GroupWorkflowOperation.SwitchBackCompleted;

                                    break;
                                case (int)DROperationType.FailBack:
                                    status = (int)GroupWorkflowOperation.FailBackCompleted;

                                    break;
                                case (int)DROperationType.FailOver:

                                    status = (int)GroupWorkflowOperation.FailOverCompleted;

                                    break;
                                case (int)DROperationType.Customized:

                                    status = (int)GroupWorkflowOperation.CustomCompleted;

                                    break;
                            }

                            InfraObjectDataAccess.UpdateWorkflowOperation(CurrentParallelGroupWorkflow.InfraObjectId, status, 0);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception : Perform Parallel Workflow", exc);

                throw;
            }
        }

        public string[] RemoveArrayFirstElement(IEnumerable<string> workflowActions)
        {
            List<string> workflowActionList = workflowActions.ToList();

            workflowActionList.RemoveAt(0);

            return workflowActionList.ToArray();

        }


        protected virtual ISchedulerFactory CreateSchedulerFactory()
        {
            return new StdSchedulerFactory();
        }

        protected virtual IScheduler GetParallelScheduler()
        {
            return _parallelSchedulerFactory.GetScheduler();
        }


        //private static string[] GetWorkFlowActionFromXml(string rawXml)
        //{
        //    string returnValue = string.Empty;

        //    XmlReader reader = XmlReader.Create(new StringReader(rawXml));
        //    while (reader.Read())
        //    {
        //        switch (reader.NodeType)
        //        {
        //            case XmlNodeType.Element:
        //                if (reader.HasAttributes)
        //                {
        //                    if (!reader.GetAttribute(0).Contains("divCondi"))
        //                    {
        //                        if (reader.GetAttribute(0).Contains("True") || reader.GetAttribute(0).Contains("False"))
        //                        {
        //                            returnValue = returnValue + reader.GetAttribute(0) + ",";
        //                        }

        //                        else
        //                        {
        //                            if (reader.AttributeCount < 2)
        //                            {
        //                                returnValue = returnValue + reader.GetAttribute(0) + ",";
        //                            }
        //                            else
        //                            {
        //                                //returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(1) + ",";
        //                                returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(4) + ",";
        //                            }
        //                            //if (string.IsNullOrEmpty(reader.GetAttribute(3)) || reader.GetAttribute(3).Equals("undefined"))
        //                            //{
        //                            //    if (reader.AttributeCount < 5)
        //                            //    {
        //                            //        returnValue = returnValue + reader.GetAttribute(0) + ",";
        //                            //    }
        //                            //    else
        //                            //    {
        //                            //        returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(4) + ",";
        //                            //    }
        //                            //}
        //                            //else
        //                            //{
        //                            //    returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(3) + "@" + reader.GetAttribute(2) + "@" + reader.GetAttribute(4) + ",";
        //                            //}
        //                        }

        //                    }

        //                }
        //                break;
        //        }
        //    }
        //    returnValue = returnValue.TrimEnd(':').TrimEnd(',');

        //    return returnValue.Split(',');
        //}

        

        public string[] GetWorkFlowActionFromXml(string rawXml)
        {

            string returnValue = string.Empty;

            try
            {
                XmlReader reader = XmlReader.Create(new StringReader(rawXml));
                while (reader.Read())
                {
                    switch (reader.NodeType)
                    {
                        case XmlNodeType.Element:
                            if (reader.HasAttributes)
                            {
                                if (!reader.GetAttribute(0).Contains("divCondi"))
                                {
                                    if (reader.GetAttribute(0).Contains("True") || reader.GetAttribute(0).Contains("False"))
                                    {
                                        returnValue = returnValue + reader.GetAttribute(0) + ",";
                                    }

                                    else
                                    {
                                        if (reader.AttributeCount < 2)
                                        {
                                            returnValue = returnValue + reader.GetAttribute(0) + ",";
                                        }
                                        else
                                        {
                                            //returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(1) + ",";
                                            returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(4) + ",";
                                        }
                                        //if (string.IsNullOrEmpty(reader.GetAttribute(3)) || reader.GetAttribute(3).Equals("undefined"))
                                        //{
                                        //    if (reader.AttributeCount < 5)
                                        //    {
                                        //        returnValue = returnValue + reader.GetAttribute(0) + ",";
                                        //    }
                                        //    else
                                        //    {
                                        //        returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(4) + ",";
                                        //    }
                                        //}
                                        //else
                                        //{
                                        //    returnValue = returnValue + reader.GetAttribute(0) + "@" + reader.GetAttribute(3) + "@" + reader.GetAttribute(2) + "@" + reader.GetAttribute(4) + ",";
                                        //}
                                    }

                                }

                            }
                            break;
                    }
                }
                returnValue = returnValue.TrimEnd(':').TrimEnd(',');
            }
            catch (Exception ex)
            {
                Logger.Error("Exception while read workflow xml : " + ex.Message + " XML :" + rawXml);
                if (ex.InnerException != null)
                    Logger.Error("Inner Exception  " + ex.InnerException.ToString());

            }
            return returnValue.Split(',');
        }
        //Added by Karthick B for ITIT-10566
        public void PerformParallelWorkflowActions(Workflow workflow, Serilog.ILogger logger)
        {
            try
            {
                Logger1 = logger;
                //Clear the Action Completed count which is executed earlier
                Logger1.Information("Started PerformParallelWorkflowActions : " + workflow.Name);
                var manger = new WorkflowManager();

                manger.ParallelOption = Option;
                manger.ClearDictionary(CurrentParallelGroupWorkflow.Id);

                CheckThreadState();

                Logger1.Information("{0} : Perform Parallel workflow Action", CurrentParallelGroupWorkflow.WorkflowName);

                CurrentWorkflow = workflow;

                TotalActionCount = 0;

                string[] workflowActions = GetWorkFlowActionFromXml(workflow.Xml);

                //Verify workflow is Parallel(True)  or Sequential (False)

                if (workflowActions[0].Contains("True"))
                {
                    //Remove first element in array (it is contain true or false)
                    Logger.Info("Started Parallel PerformParallelWorkflowActions : " + workflow.Name);
                    Logger1.Information("Started Parallel PerformParallelWorkflowActions : " + workflow.Name);
                    workflowActions = RemoveArrayFirstElement(workflowActions);

                    TotalActionCount = workflowActions.Length;

                    IList<WorkflowBundle> bundle = CreateWorkflowBundle(workflowActions);
                    if (bundle != null)
                    {
                        for (var input = 0; input < bundle.Count; input++)
                        {

                            bool isNotLast = input != (bundle.Count - 1);


                            if (bundle[input].IsActionSet)
                            {

                                var resultCollection = new List<bool>();


                                Option.CancellationToken = CancellationTokenSource.Token;

                                Logger1.Information(" **** Parallel PerformParallelWorkflow Actions Set Index : " + input);
                                Logger1.Information(" **** Parallel PerformParallelWorkflow Actions Set Count : " + bundle[input].ActionSet.Count);

                                ThreadPool.SetMinThreads(100, 100);
                                Parallel.ForEach(bundle[input].ActionSet, Option, x => resultCollection.Add(RunParallelActionWorkflow(x.ActionId, isNotLast)));

                                bool containTrue = resultCollection.Any(item => item);

                                if (containTrue)
                                {
                                    break;
                                }

                            }
                            else
                            {
                                if (RunParallelActionWorkflow(bundle[input].ActionId, isNotLast))
                                {
                                    break;
                                }
                            }
                        }
                    }
                }
                else if (workflowActions[0].Contains("False"))
                {
                    Logger.Info("Started Sequential PerformParallelWorkflowActions : " + workflow.Name);
                    Logger1.Information("Started Sequential PerformParallelWorkflowActions : " + workflow.Name);
                    workflowActions = RemoveArrayFirstElement(workflowActions);

                    TotalActionCount = workflowActions.Length;

                    if (NewWorkflowVars.FailureCount_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                        NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] = 0;
                    if (NewWorkflowVars.FailureTrial_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                        NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = true;
                    if (NewWorkflowVars.FailedCondiActionId_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                        NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = 0;


                    for (var input = 0; input < TotalActionCount; input++)
                    {


                        if (NewWorkflowVars.FailureTrial_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                            if (input < TotalActionCount && NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id])
                                if (workflowActions[input].Contains("^") && workflowActions[input].Split('^').Length >= 3)
                                {
                                    NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = true;
                                    NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = Convert.ToInt32(workflowActions[input].Split('@')[0]);
                                }
                                else
                                    NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = false;
                            else
                                NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = false;

                        var failedCount = 0;

                        if (workflowActions[input].Split('^').Length >= 4)
                            failedCount = Convert.ToInt32(workflowActions[input].Split('^')[4]);

                        if (NewWorkflowVars.FailureCount_Old.ContainsKey(CurrentParallelGroupWorkflow.Id) && (NewWorkflowVars.FailedCondiActionId_Old.ContainsKey(CurrentParallelGroupWorkflow.Id)))
                            if (NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] >= failedCount && NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] == Convert.ToInt32(workflowActions[input].Split('@')[0]))
                            {
                                NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] = 0;
                                NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = true;
                                NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = 0;
                            }

                        if (workflowActions[input].Contains("@"))
                        {
                            string[] actionarr = workflowActions[input].Split('@');

                            Option.CancellationToken = CancellationTokenSource.Token;

                            if (RunSequentialWorkflow(workflowActions[input], input != (TotalActionCount - 1)))
                            {
                                break;
                            }
                            else if (NewWorkflowVars.ActionExecutionStatusNew_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                                if (!NewWorkflowVars.ActionExecutionStatusNew_Old[CurrentParallelGroupWorkflow.Id])
                                {
                                    //var failureIndex = Array.FindIndex(nodes, x => x.Equals(nodes[input + 1].Split('^')[1]));

                                    if (NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id])
                                    {
                                        Thread.Sleep(10000);

                                        var failureIndex = Array.FindIndex(workflowActions, x => x.Split('^')[1].Contains((workflowActions[input].Split('^')[3])));

                                        if (failureIndex < input)
                                        {
                                            NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id]++;

                                            if (NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] >= Convert.ToInt32(workflowActions[input].Split('^')[4]))
                                            {
                                                NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = false;
                                            }

                                            var actionResultList = ParallelWorkflowActionResultDataAccess.GetCurrentDRGroupWorkflowActionsResult(CurrentParallelGroupWorkflow);

                                            for (int i = failureIndex; i <= input; i++)      //deleting all actions results which are between condition and failure action
                                            {
                                                var deleteResult = ParallelWorkflowActionResultDataAccess.DeleteExistingRecords(actionResultList[i].Id);

                                                // --NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];
                                            }

                                            NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id] = failureIndex;
                                            //++NewWorkflow.ActionCompleteCountNew;

                                            //Update Progress bar status
                                            string progress = string.Format("{0}/{1}", failureIndex, TotalActionCount);
                                            Logger1.Information("2.progress Bar count :" + progress);
                                            ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);

                                            input = failureIndex - 1;
                                        }

                                        else if (failureIndex > input)
                                        {
                                            // failureIndex++;
                                            int result = input - failureIndex;
                                            if (result < 0)
                                            {
                                                result *= -1;
                                            }
                                            if (1 == result)
                                            {
                                                string progress = string.Format("{0}/{1}", input + 1, TotalActionCount);
                                                Logger1.Information("3.progress Bar count:" + progress);
                                                ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);
                                                if (NewWorkflowVars.ActionCompleteCountNew_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                                                    NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id] = input + 1;
                                            }
                                            else
                                            {

                                                for (int i = input + 1; i < failureIndex; i++)
                                                {
                                                    CheckThreadState();


                                                    ++NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];

                                                    string progress = string.Format("{0}/{1}", failureIndex + 1, TotalActionCount);
                                                    Logger1.Information("4.progress Bar count:" + progress);
                                                    ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);

                                                    var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32(workflowActions[i].Split('@')[0]));

                                                    var parallelWFActionResult = new ParallelWorkflowActionResult();

                                                    parallelWFActionResult.WorkflowActionName = workflowAction.Name;
                                                    parallelWFActionResult.ActionId = workflowAction.Id;
                                                    parallelWFActionResult.StartTime = DateTime.Now;
                                                    parallelWFActionResult.EndTime = DateTime.Now;
                                                    parallelWFActionResult.Status = "Skip";
                                                    parallelWFActionResult.ParallelGroupWorkflowId = CurrentParallelGroupWorkflow.Id;
                                                    parallelWFActionResult.ParallelDROperationId = CurrentParallelGroupWorkflow.ParallelDROperationId;
                                                    parallelWFActionResult.InfraObjectId = CurrentInfraObject.Id;
                                                    parallelWFActionResult.Message = string.Empty;
                                                    parallelWFActionResult.CreatorId = CurrentParallelGroupWorkflow.CreatorId;
                                                    parallelWFActionResult = ParallelWorkflowActionResultDataAccess.Add(parallelWFActionResult);

                                                }
                                                ++NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];
                                            }
                                            //++NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];

                                            input = failureIndex - 1;
                                        }
                                    }
                                }
                        }

                    }

                }
                else
                {
                    TotalActionCount = workflowActions.Length;

                    for (var input = 0; input < TotalActionCount; input++)
                    {
                        Option.CancellationToken = CancellationTokenSource.Token;

                        if (RunSequentialWorkflow(workflowActions[input],
                            input != (TotalActionCount - 1)))
                        {
                            break;
                        }
                    }
                }

                Logger1.Information("Parallel workflow Action Name Performing Ended and update to completed");
            }
            catch (Exception exc)
            {
                Logger1.Information("Parallel workflow Executing Action Exception :" + exc.Message);

                Logger.Error("ERROR Executing workflow :" + exc.Message + " inner exception" + exc.InnerException.Message);

                throw;
            }
        }

        private void CheckThreadState()
        {
            Logger.Info("CheckThreadState method in WorkflowClient");
            Logger.Info(" Current Cancellation Token is Cancelled Requred: " + Option.CancellationToken.IsCancellationRequested);

            if (Thread.CurrentThread.ThreadState.ToString() == "AbortRequested" || Option.CancellationToken.IsCancellationRequested)
            {
                try
                {
                    Logger.Info("Current Thread State " + Thread.CurrentThread.ThreadState);
                    Logger.Info("Current Thread State return id:" + Thread.CurrentThread.ManagedThreadId);
                    Thread.CurrentThread.Abort();
                    Logger.Info("Current Thread State after abort " + Thread.CurrentThread.ThreadState);
                    Logger.Info("Current Thread State return id after abort:" + Thread.CurrentThread.ManagedThreadId);

                    throw new BcmsException(BcmsExceptionType.ThreadAborted, "Current Thread is aborted" + Thread.CurrentThread.ManagedThreadId);

                }
                catch (ThreadAbortException exc)
                {


                    Logger.Info("Current Thread Abort Exception : " + exc.Message);
                    Logger.Info("Current Thread Abort Exception State " + Thread.CurrentThread.ThreadState);
                    Logger.Info("Current Thread State Abort Exception Thread ID:" + Thread.CurrentThread.ManagedThreadId);
                    throw new BcmsException(BcmsExceptionType.ThreadAborted, "Current Thread is aborted" + Thread.CurrentThread.ManagedThreadId);
                }

            }

        }
        //Added by Karthick B for ITIT-10566
        public IList<WorkflowBundle> CreateWorkflowBundle(string[] workflowActions)
        {
            IList<WorkflowBundle> bundle = new List<WorkflowBundle>();

            IList<WorkflowBundle> actionSet = new List<WorkflowBundle>();

            for (int i = 0; i < workflowActions.Length; i++)
            {
                string[] actionArray1 = workflowActions[i].Split('^');
                string[] actionArray = actionArray1[0].Split('@');

                if (actionArray.Length == 2)
                {
                    if (actionArray[1].Equals("undefined") || actionArray[1].Equals("sequential"))
                    {
                        if (actionSet.Count > 0)
                        {
                            bundle.Add(new WorkflowBundle
                            {
                                ActionSet = actionSet,
                                IsActionSet = true
                            });
                            actionSet = new List<WorkflowBundle>();
                        }

                        bundle.Add(new WorkflowBundle
                        {
                            ActionId = (actionArray[0]),
                            IsActionSet = false
                        });
                    }
                    else if (actionArray[1].Equals("parallelRun"))
                    {
                        if (i == (workflowActions.Length - 1))
                        {
                            actionSet.Add(new WorkflowBundle
                            {
                                ActionId = (actionArray[0]),
                            });


                            bundle.Add(new WorkflowBundle
                            {
                                ActionSet = actionSet,
                                IsActionSet = true
                            });

                            actionSet = new List<WorkflowBundle>();
                        }
                        else
                        {
                            actionSet.Add(new WorkflowBundle
                            {
                                ActionId = (actionArray[0]),
                            });
                        }
                    }
                }

            }

            return bundle;
        }
        private static string[] ReturnActionsArrayLogical(string[] workflowActns)
        {
            var workflowActions = workflowActns;

            //var workflowActions = RemoveArrayFirstElement(workflowActns);

            //var test = (IEnumerable<string>)(workflowActns);


            ////////List<string> workflowActionList = workflowActns.ToList();

            ////////workflowActionList.RemoveAt(0);

            ////////var workflowActions = workflowActionList.ToArray();

            for (int i = 0; i < workflowActions.Length - 1; i++)
            {
                if (workflowActions[i].Split('^').Length > 2)
                {
                    //int divCount = 0;


                    int failedIndex = Convert.ToInt32(workflowActions[i].Split('^')[3]);

                    if (failedIndex > 0)
                    {
                        //for (int j = failedIndex; j < i; j++)
                        //{
                        //    if (workflowActions[j].Split('^').Length > 2)
                        //        failedIndex--;
                        //}

                        //for (int j = 0; j < failedIndex; j++)
                        //{
                        //    if (workflowActions[j].Split('^').Length > 2)
                        //    {
                        //        failedIndex--;
                        //        j++;
                        //    }
                        //}

                        var tempCount = 0;// = failedIndex;

                        for (int j = i - 1; j > 0; j--)
                        {
                            if (workflowActions[j].Split('^').Length > 2)
                            {
                                failedIndex--;
                            }

                            tempCount++;

                            if (tempCount == failedIndex)
                                break;
                        }

                        failedIndex = i - failedIndex;
                    }

                    if (failedIndex < 0)
                    {
                        for (int j = i; j < ((failedIndex * (-1)) - 1); j++)
                        {
                            if (workflowActions[j].Split('^').Length > 2)
                                failedIndex++;
                        }

                        failedIndex = failedIndex * (-1);
                        failedIndex = failedIndex + i;
                        failedIndex--;
                    }

                    failedIndex++;

                    var nodeName = workflowActions[failedIndex].Split('^')[1];

                    var actString = workflowActions[i].Split('^');

                    actString[3] = nodeName;


                    workflowActions[i] = string.Join("^", actString); ;
                }
            }

            return workflowActions;
        }


        private void PerformParallelWorkflowActions(Workflow workflow)
        {
            try
            {
                //Clear the Action Completed count which is executed earlier

                var manger = new WorkflowManager();

                manger.ClearDictionary(CurrentParallelGroupWorkflow.Id);



                Logger.InfoFormat("{0} : Perform Parallel workflow Action", CurrentParallelGroupWorkflow.WorkflowName);

                CurrentWorkflow = workflow;

                TotalActionCount = 0;

                string[] workflowActions = GetWorkFlowActionFromXml(workflow.Xml);

                //Verify workflow is Parallel(True)  or Sequential (False)

                if (workflowActions[0].Contains("True"))
                {
                    //Remove first element in array (it is contain true or false)

                    workflowActions = RemoveArrayFirstElement(workflowActions);

                    TotalActionCount = workflowActions.Length;

                    IList<WorkflowBundle> bundle = CreateWorkflowBundle(workflowActions);
                    int bundleCount = bundle.Count;
                    if (bundle != null)
                    {
                        for (var input = 0; input < bundle.Count; input++)
                        {
                            //ITIT-10566
                            var workflow1 = WorkflowDataAccess.GetWorkflowById(CurrentParallelGroupWorkflow.WorkflowId);
                            workflowActions = GetWorkFlowActionFromXml(workflow1.Xml);
                            workflowActions = RemoveArrayFirstElement(workflowActions);
                            TotalActionCount = workflowActions.Length;
                            bundle = CreateWorkflowBundle(workflowActions);


                            bundleCount = bundle.Count;
                            //ReloadCountMismatch 

                            Logger.Info("Parallel Actions bundleCount : " + bundleCount);


                            bool isNotLast = input != (bundleCount - 1);
                           

                            //if (!IsAbort)
                            //{
                            if (bundle[input].IsActionSet)
                            {
                                // CompletedActionCount = CompletedActionCount + bundle[input].ActionSet.Count;

                                var resultCollection = new List<bool>();

                                //System.Threading.Tasks.Parallel.ForEach(bundle[input].ActionSet, x => resultCollection.Add(RunParallelActionWorkflow(x.ActionId, isNotLast)));

                                //System.Threading.Tasks.Parallel.ForEach(bundle[input].ActionSet, new System.Threading.Tasks.ParallelOptions { MaxDegreeOfParallelism = 30 }, x => resultCollection.Add(RunParallelActionWorkflow(x.ActionId, isNotLast)));
                                option.CancellationToken = CancellationTokenSource.Token;

                                Logger.Info(" **** Parallel PerformParallelWorkflow Actions Set Index : " + input);
                                Logger.Info(" **** Parallel PerformParallelWorkflow Actions Set Count : " + bundle[input].ActionSet.Count);

                                ThreadPool.SetMinThreads(100, 100);
                                Parallel.ForEach(bundle[input].ActionSet, option, x => resultCollection.Add(RunParallelActionWorkflow(x.ActionId, isNotLast, option)));

                                bool containTrue = resultCollection.Any(item => item);

                                if (containTrue)
                                {
                                    break;
                                }

                                //_parallelSchedulerFactory = CreateSchedulerFactory();

                                //_parallelScheduler = GetParallelScheduler();

                                //foreach (var item in bundle[input].ActionSet)
                                //{
                                //    InitializeParallelAction(item.ActionId, isNotLast);
                                //}
                                //Thread.Sleep(60000);
                            }
                            else
                            {
                                // CompletedActionCount = CompletedActionCount + 1;

                                //if (RunParallelActionWorkflow(bundle[input].ActionId, isNotLast))
                                if (RunParallelActionWorkflow(bundle[input].ActionId, isNotLast, option))
                                {
                                    break;
                                }
                            }
                            //}
                            //else
                            //{
                            //    break;
                            //}
                        }
                    }
                    else
                    {
                        Logger.Info("bundle is null");
                    }
                }
                else if (workflowActions[0].Contains("False"))
                {
                    Logger.Info("Started Sequential PerformParallelWorkflowActions : " + workflow.Name);
                    workflowActions = RemoveArrayFirstElement(workflowActions);

                    Logger.Info("TotalActionCount" + TotalActionCount);
                    TotalActionCount = workflowActions.Length;

                    NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] = 0;
                    NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = true;
                    NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = 0;


                    for (var input = 0; input < TotalActionCount; input++)
                    {
                        //CompletedActionCount = input + 1;

                        if (NewWorkflowVars.FailureTrial_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                            if (input < TotalActionCount && NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id])
                                if (workflowActions[input].Contains("^") && workflowActions[input].Split('^').Length >= 3)
                                {
                                    NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = true;
                                    NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = Convert.ToInt32(workflowActions[input].Split('@')[0]);
                                }
                                else
                                    NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = false;
                            else
                                NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id] = false;

                        var failedCount = 0;

                        if (workflowActions[input].Split('^').Length >= 4)
                            failedCount = Convert.ToInt32(workflowActions[input].Split('^')[4]);

                        if (NewWorkflowVars.FailureCount_Old.ContainsKey(CurrentParallelGroupWorkflow.Id) && (NewWorkflowVars.FailedCondiActionId_Old.ContainsKey(CurrentParallelGroupWorkflow.Id)))
                            if (NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] >= failedCount && NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] == Convert.ToInt32(workflowActions[input].Split('@')[0]))
                            {
                                NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] = 0;
                                NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = true;
                                NewWorkflowVars.FailedCondiActionId_Old[CurrentParallelGroupWorkflow.Id] = 0;
                            }

                        if (workflowActions[input].Contains("@"))
                        {
                            string[] actionarr = workflowActions[input].Split('@');
                            //ITIT-10566
                            option.CancellationToken = CancellationTokenSource.Token;
                            //ReloadCountMismatch  added by Naren and Miru
                            var workflow1 = WorkflowDataAccess.GetWorkflowById(CurrentParallelGroupWorkflow.WorkflowId);
                            workflowActions = GetWorkFlowActionFromXml(workflow1.Xml);
                            workflowActions = RemoveArrayFirstElement(workflowActions);
                            TotalActionCount = workflowActions.Length;
                            //ReloadCountMismatch 
                            Logger.Info("Enter sequential workflowActions[input] : " + workflowActions[input]);
                            Logger.Info("Enter sequential workflow After updating worfklow Actions TotalActionCount : " + TotalActionCount);

                            if (RunSequentialWorkflow(workflowActions[input], input != (TotalActionCount - 1)))
                            {
                                break;
                            }
                            else if (NewWorkflowVars.ActionExecutionStatusNew_Old.ContainsKey(CurrentParallelGroupWorkflow.Id))
                                if (!NewWorkflowVars.ActionExecutionStatusNew_Old[CurrentParallelGroupWorkflow.Id])
                                {
                                    //var failureIndex = Array.FindIndex(nodes, x => x.Equals(nodes[input + 1].Split('^')[1]));

                                    if (NewWorkflowVars.ActionConditionStatusNew_Old[CurrentParallelGroupWorkflow.Id])
                                    {
                                        Thread.Sleep(10000);

                                        var failureIndex = Array.FindIndex(workflowActions, x => x.Split('^')[1].Contains((workflowActions[input].Split('^')[3])));

                                        if (failureIndex < input)
                                        {
                                            NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id]++;

                                            if (NewWorkflowVars.FailureCount_Old[CurrentParallelGroupWorkflow.Id] >= Convert.ToInt32(workflowActions[input].Split('^')[4]))
                                            {
                                                NewWorkflowVars.FailureTrial_Old[CurrentParallelGroupWorkflow.Id] = false;
                                            }

                                            var actionResultList = ParallelWorkflowActionResultDataAccess.GetCurrentDRGroupWorkflowActionsResult(CurrentParallelGroupWorkflow);

                                            for (int i = failureIndex; i <= input; i++)      //deleting all actions results which are between condition and failure action
                                            {
                                                var deleteResult = ParallelWorkflowActionResultDataAccess.DeleteExistingRecords(actionResultList[i].Id);

                                                --NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];
                                            }

                                            //++NewWorkflow.ActionCompleteCountNew;

                                            //Update Progress bar status
                                            string progress = string.Format("{0}/{1}", failureIndex, TotalActionCount);
                                            ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);

                                            input = failureIndex - 1;
                                        }

                                        if (failureIndex > input)
                                        {
                                            failureIndex++;

                                            for (int i = input + 1; i < failureIndex; i++)
                                            {
                                                ++NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];

                                                string progress = string.Format("{0}/{1}", failureIndex + 1, TotalActionCount);
                                                ParallelGroupWorkflowDataAccess.UpdateByProgress(CurrentParallelGroupWorkflow.Id, progress);

                                                var workflowAction = WorkflowActionDataAccess.GetWorkflowActionById(Convert.ToInt32(workflowActions[input].Split('@')[0]));

                                                var parallelWFActionResult = new ParallelWorkflowActionResult();

                                                parallelWFActionResult.WorkflowActionName = workflowAction.Name;
                                                parallelWFActionResult.ActionId = workflowAction.Id;
                                                parallelWFActionResult.StartTime = DateTime.Now;
                                                parallelWFActionResult.EndTime = DateTime.Now;
                                                parallelWFActionResult.Status = "Skip";
                                                parallelWFActionResult.ParallelGroupWorkflowId = CurrentParallelGroupWorkflow.Id;
                                                parallelWFActionResult.ParallelDROperationId = CurrentParallelGroupWorkflow.ParallelDROperationId;
                                                parallelWFActionResult.InfraObjectId = CurrentInfraObject.Id;
                                                parallelWFActionResult.Message = string.Empty;
                                                parallelWFActionResult.CreatorId = CurrentParallelGroupWorkflow.CreatorId;
                                                parallelWFActionResult = ParallelWorkflowActionResultDataAccess.Add(parallelWFActionResult);

                                            }

                                            ++NewWorkflowVars.ActionCompleteCountNew_Old[CurrentParallelGroupWorkflow.Id];

                                            input = failureIndex - 1;
                                        }
                                    }
                                }
                        }

                    }

                }
                else
                {
                    TotalActionCount = workflowActions.Length;

                    for (var input = 0; input < TotalActionCount; input++)
                    {
                        //CompletedActionCount=input+1;

                        if (RunSequentialWorkflow(workflowActions[input],
                            input != (TotalActionCount - 1)))
                        {
                            break;
                        }
                    }
                }

                Logger.InfoFormat("Parallel workflow Action Name Performing Ended and update to completed");
            }
            catch (Exception exc)
            {
                Logger.InfoFormat("Parallel workflow Executing Action Exception :" + exc.Message);

                Logger.ErrorFormat("ERROR Executing workflow :" + exc.Message + " inner exception" + exc.InnerException.Message);

                throw;
            }
        }

        //ITIT-10566
        private bool RunParallelActionWorkflow(string actionid, bool isNotLastAction, ParallelOptions option)
        {
            try
            {
                using (var manager = new WorkflowManager(CurrentParallelGroupWorkflow, CurrentWorkflow))
                {
                    manager.TotalActionCount = TotalActionCount;
                    manager.ParallelOptions = option;
                    //manager.CompletedActionCount = CompletedActionCount;
                    return manager.RunWorkflow(actionid, true, isNotLastAction);
                }

            }
            catch (OperationCanceledException exc)
            {
                Logger.Error("Exception occurred while RunParallelActionWorkflow" + Environment.NewLine + "ERROR" + exc.Message);
                throw new BcmsException(BcmsExceptionType.ThreadAborted, "Current Thread is aborted" + Thread.CurrentThread.ManagedThreadId);
            }
        }

        private void InitializeParallelAction(int actionid, bool isNotLastAction)
        {
            try
            {
                //var parallelJobDetail = JobBuilder.Create<ParallelActionJob>()
                //   .WithIdentity(actionid + "ParallelActionJob", actionid + "ParallelActionJobGroup")
                //   .UsingJobData(ParallelActionJob.ActionId, actionid)
                //   .UsingJobData(ParallelActionJob.IsNotLastAction, isNotLastAction)
                //   .UsingJobData(ParallelActionJob.ParallelGroupWorkflowId, CurrentParallelGroupWorkflow.Id)
                //   .UsingJobData(ParallelActionJob.WorkflowId, CurrentWorkflow.Id)
                //   .UsingJobData(ParallelActionJob.TotalActionCount, TotalActionCount)
                //   .Build();

                var parallelJobDetail = new JobDetail(actionid + "ParallelActionJob", actionid + "ParallelActionJobGroup", typeof(ParallelActionJob));

                parallelJobDetail.JobDataMap.Put(ParallelActionJob.ActionId, actionid);
                parallelJobDetail.JobDataMap.Put(ParallelActionJob.IsNotLastAction, isNotLastAction);
                parallelJobDetail.JobDataMap.Put(ParallelActionJob.ParallelGroupWorkflowId, CurrentParallelGroupWorkflow.Id);
                parallelJobDetail.JobDataMap.Put(ParallelActionJob.WorkflowId, CurrentWorkflow.Id);
                parallelJobDetail.JobDataMap.Put(ParallelActionJob.TotalActionCount, TotalActionCount);

                //var simpleTrigger = (ISimpleTrigger)TriggerBuilder.Create()
                //                                     .WithIdentity(actionid + "ParallelJobTrigger", "ParallelJobTrigger")
                //                                     .StartNow().WithSimpleSchedule(x => x.WithRepeatCount(0).WithInterval(TimeSpan.Zero))
                //                                     .Build();
                var simpleTrigger = new SimpleTrigger(actionid + "ParallelJobTrigger", DateTime.UtcNow);

                simpleTrigger.RepeatCount = 0;
                simpleTrigger.RepeatInterval = TimeSpan.Zero;

                //_parallelScheduler.ScheduleJob(parallelJobDetail, simpleTrigger);

                // _parallelScheduler.ScheduleJob(parallelJobDetail, simpleTrigger);

                _parallelScheduler1.ScheduleJob(parallelJobDetail, simpleTrigger);

                Logger.Info(parallelJobDetail.Key + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                Logger.Error("Exception occurred while initializing Parallel Action Job " + Environment.NewLine + "ERROR" + exc.Message);
            }
        }


        private bool RunParallelActionWorkflow(string actionid, bool isNotLastAction)
        {
            using (var manager = new WorkflowManager(CurrentParallelGroupWorkflow, CurrentWorkflow))
            {
                Logger.Info("RunParallelActionWorkflow has Started");
                manager.TotalActionCount = TotalActionCount;
                //manager.CompletedActionCount = CompletedActionCount;
                return manager.RunWorkflow(actionid.ToString(), true, isNotLastAction);
            }

        }

        private bool RunSequentialWorkflow(string actionid, bool isNotLastAction)
        {
            using (var manager = new WorkflowManager(CurrentParallelGroupWorkflow, CurrentWorkflow))
            {
                Logger.Info("RunSequentialWorkflow has Started");
                manager.TotalActionCount = TotalActionCount;
                //manager.CompletedActionCount = CompletedActionCount;
                return manager.RunWorkflow(actionid.ToString(), false, isNotLastAction);
            }
        }
        #endregion

        #region Constructor
        //Added by Karthick B for ITIT-10566
        public WorkflowClient(ParallelGroupWorkflow parallel, CancellationTokenSource cancellationToken, Serilog.ILogger logger)
        {
            CurrentParallelGroupWorkflow = parallel;

            CancellationTokenSource = cancellationToken;

            CurrentInfraObject = InfraObjectDataAccess.GetInfraObjectById(parallel.InfraObjectId);

            Logger1 = logger;
        }
        //Added by Karthick B for ITIT-10566
        public WorkflowClient(ParallelGroupWorkflow parallel)
        {
            CurrentParallelGroupWorkflow = parallel;

            CurrentInfraObject = InfraObjectDataAccess.GetInfraObjectById(parallel.InfraObjectId);
        }

        public WorkflowClient(InfraObject group)
        {
            CurrentInfraObject = group;
        }

        public WorkflowClient()
        {
        }

        #endregion

        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {

                }
            }

            _isDisposed = true;
        }

        ~WorkflowClient()
        {
            Dispose(false);
        }

        #endregion
    }

    [PersistJobDataAfterExecution]
    [DisallowConcurrentExecution]
    public class ParallelActionJob : IJob
    {
        public const string ActionId = "actionId";

        public const string IsNotLastAction = "isNotLastAction";

        public const string ParallelGroupWorkflowId = "ParallelGroupWorkflowId";

        public const string WorkflowId = "workflowId";

        public const string TotalActionCount = "totalActionId";


        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;

            var actionId = data.GetInt(ActionId);

            var isNotLastAction = data.GetBoolean(IsNotLastAction);

            var parallelGroupId = data.GetInt(ParallelGroupWorkflowId);

            var workflowId = data.GetInt(WorkflowId);

            var totalActionCount = data.GetInt(TotalActionCount);

            if (actionId > 0 && parallelGroupId > 0 && workflowId > 0 && totalActionCount > 0)
            {

                var parallelGroup = ParallelGroupWorkflowDataAccess.GetById(parallelGroupId);

                var workflow = WorkflowDataAccess.GetWorkflowById(workflowId);

                using (var manager = new WorkflowManager(parallelGroup, workflow))
                {
                    manager.TotalActionCount = totalActionCount;

                    manager.RunWorkflow(ActionId, true, isNotLastAction);
                }

            }
            else
            {
                throw new ArgumentException(context.JobDetail.Key + " getting null value in Parallel Action ");
            }
        }
    }
}