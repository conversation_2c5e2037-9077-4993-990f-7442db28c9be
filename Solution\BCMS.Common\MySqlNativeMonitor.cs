﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "MySqlMonitor", Namespace = "http://www.BCMS.com/types")]
    public class MySqlNativeMonitor
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseVersion
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseVersion
        {
            get;
            set;
        }

        [DataMember]
        public string MasterServiceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string SlaveServiceStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseState
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseState
        {
            get;
            set;
        }

        [DataMember]
        public string PRSlaveRunningState
        {
            get;
            set;
        }

        [DataMember]
        public string DRSlaveRunningState
        {
            get;
            set;
        }

        [DataMember]
        public string PRReplicationConnectState
        {
            get;
            set;
        }

        [DataMember]
        public string DRReplicationConnectState
        {
            get;
            set;
        }

        [DataMember]
        public string SlaveIORunningStatus
        {
            get;
            set;
        }

        [DataMember]
        public string SlaveSQLRunningStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PRMasterLogFile
        {
            get;
            set;
        }

        [DataMember]
        public string DRMasterLogFile
        {
            get;
            set;
        }

        [DataMember]
        public string RelayMasterLogFile
        {
            get;
            set;
        }

        [DataMember]
        public string MasterLogPosition
        {
            get;
            set;
        }

        [DataMember]
        public string ExecMasterLogPosition
        {
            get;
            set;
        }

        [DataMember]
        public string DataLag
        {
            get;
            set;
        }

        #endregion Properties


        #region Constructor

        public MySqlNativeMonitor()
            : base()
        {

        }

        #endregion


    }
}
