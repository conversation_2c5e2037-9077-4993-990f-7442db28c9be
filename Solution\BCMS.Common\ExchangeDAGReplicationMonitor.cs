﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common;
namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "ExchangeDAGReplicationMonitor", Namespace = "http://www.BCMS.com/types")]

    public class ExchangeDAGReplicationMonitor : BaseEntity
    {
        //ReplicationBase _replicationBase = new ReplicationBase();

        private IList<ExchangeDAGReplicationMonitor> _monitorings = new List<ExchangeDAGReplicationMonitor>();

        #region Properties

       
        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }


        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public int DatabaseId
        {
            get;
            set;
        }



        [DataMember]
        public string DAGName
        {
            get;
            set;
        }

        [DataMember]
        public string IPAddress
        {
            get;
            set;
        }

        [DataMember]
        public string PRMailboxDatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string DRMailboxDatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string WitnessServer
        {
            get;
            set;
        }

        [DataMember]
        public string CopyQueueLength
        {
            get;
            set;
        }

        [DataMember]
        public string ReplayQueueLength
        {
            get;
            set;
        }

        [DataMember]
        public string ReplicationStatus
        {
            get;
            set;
        }
             

        //[DataMember]
        //public ReplicationBase ReplicationBase
        //{
        //    get
        //    {
        //        return _replicationBase;
        //    }
        //    set
        //    {
        //        _replicationBase = value;
        //    }
        //}

        //[DataMember]
        //public IList<ExchangeDAGMonitoring> ExchangeDAGMonitoringList
        //{
        //    get
        //    {
        //        return _monitorings;
        //    }
        //    set
        //    {
        //        _monitorings = value;
        //    }
        //}


        #endregion

        #region Constructor

        public ExchangeDAGReplicationMonitor()
            : base()
        {
        }

        #endregion
    }
}
