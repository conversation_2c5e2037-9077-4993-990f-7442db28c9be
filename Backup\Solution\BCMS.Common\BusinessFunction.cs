﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "BusinessFunction", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessFunction : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public string CriticalityLevel { get; set; }

        [DataMember]
        public string ConfiguredRPO { get; set; }

        [DataMember]
        public string ConfiguredRTO { get; set; }

        [DataMember]
        public string ConfiguredMAO { get; set; }

        [DataMember]
        public int IsStatic { get; set; }

        #endregion
    }
}
