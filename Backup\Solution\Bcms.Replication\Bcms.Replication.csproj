﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="3.5" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5C5CEED3-44CC-419F-8EFD-C853E06636ED}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Bcms.Replication</RootNamespace>
    <AssemblyName>Bcms.Replication</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Jscape.Ssh, Version=*******, Culture=neutral, PublicKeyToken=504a4aa00214df6f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\JSCAPE\Jscape.Ssh.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=*******, Culture=neutral, PublicKeyToken=b32731d11ce58905">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Log4Net\log4net.dll</HintPath>
    </Reference>
    <Reference Include="LogShippingNative, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Logshipping\LogShippingNative.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ObjectBuilder, Version=1.0.51206.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Microsoft\Microsoft.Practices.ObjectBuilder.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=2.112.3.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86" />
    <Reference Include="OracleDbSwitchoverwithDG, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\OracleSwitchoverwithDG\OracleDbSwitchoverwithDG.dll</HintPath>
    </Reference>
    <Reference Include="PGlobalMirror, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\Global\PGlobalMirror.dll</HintPath>
    </Reference>
    <Reference Include="POracleDatabase, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Thirdparty\WindowsOraclewithoutDG\POracleDatabase.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdoUtility\DBManager.cs" />
    <Compile Include="AdoUtility\SqlLogDelegate.cs" />
    <Compile Include="Entity\SecureConnection.cs" />
    <Compile Include="FastCopyMSSql\FastCopyMSSqlClient.cs" />
    <Compile Include="FastCopyOracle\FastCopyOracleClient.cs" />
    <Compile Include="FastCopy\FastCopyClient.cs" />
    <Compile Include="Interface\IDBManager.cs" />
    <Compile Include="OracleDataGuard\DataGuardClient.cs" />
    <Compile Include="Base\DRHost.cs" />
    <Compile Include="Base\Host.cs" />
    <Compile Include="Base\PrimaryHost.cs" />
    <Compile Include="OracleRac\OracleRacClient.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Shared\QueryConstants.cs" />
    <Compile Include="Shared\LogHelper.cs" />
    <Compile Include="Shared\SSHHelper.cs" />
    <Compile Include="SqlServerNative\SqlServerNative.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="oci.dll" />
    <Content Include="ociw32.dll" />
    <Content Include="orannzsbb11.dll" />
    <Content Include="oraocci11.dll" />
    <Content Include="oraociicus11.dll" />
    <Content Include="OraOps11w.dll" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BCMS.Common\Bcms.Common.csproj">
      <Project>{4F12B107-29B7-4E7A-98A2-E4219142261F}</Project>
      <Name>Bcms.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.DataAccess\Bcms.DataAccess.csproj">
      <Project>{E8CB1405-A5F6-4A2C-AF32-46301ED4DF0C}</Project>
      <Name>Bcms.DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\BCMS.ExceptionHandler\Bcms.ExceptionHandler.csproj">
      <Project>{0DFB8DA8-A766-4C68-9D49-0511764793AA}</Project>
      <Name>Bcms.ExceptionHandler</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>