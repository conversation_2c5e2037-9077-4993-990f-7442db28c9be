﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;
using System.Collections.Generic;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class GlobalMirrorMonitorDataAccess : BaseDataAccess
    {
        public static bool AddGlobalMirrorMonitorLogs(GlobalMirrorMonitor gmreplication)
        {
          

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("GlobalMirrorMonitorLogs_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iGMRResultId", DbType.AnsiString, gmreplication.GMRResultId);
                    Database.AddInParameter(cmd, Dbstring + "iMasterCount", DbType.AnsiString, gmreplication.MasterCount);
                    Database.AddInParameter(cmd, Dbstring + "iMasterSessionId", DbType.AnsiString, gmreplication.MasterSessionId);
                    Database.AddInParameter(cmd, Dbstring + "iCopyState", DbType.AnsiString, gmreplication.CopyState);
                    Database.AddInParameter(cmd, Dbstring + "iFatalReason", DbType.AnsiString, gmreplication.FatalReason);

                    Database.AddInParameter(cmd, Dbstring + "iCGIntervalTime", DbType.AnsiString, gmreplication.CGIntervalTime);
                    Database.AddInParameter(cmd, Dbstring + "iCoordTime", DbType.AnsiString, gmreplication.CoordTime);
                    Database.AddInParameter(cmd, Dbstring + "iMaxCGDrainTime", DbType.AnsiString, gmreplication.MaxCGDrainTime);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentTime", DbType.AnsiString, gmreplication.CurrentTime);
                    Database.AddInParameter(cmd, Dbstring + "iCGTime", DbType.AnsiString, gmreplication.CGTime);

                    Database.AddInParameter(cmd, Dbstring + "iSuccessfulCGPercentage", DbType.AnsiString,
                                      gmreplication.SuccessfulCGPercentage);
                    Database.AddInParameter(cmd, Dbstring + "iFlashCopySequenceNumber", DbType.AnsiString,
                                      gmreplication.FlashCopySequenceNumber);
                    Database.AddInParameter(cmd, Dbstring + "iMasterId", DbType.AnsiString, gmreplication.MasterId);
                    Database.AddInParameter(cmd, Dbstring + "iSubordinateCount", DbType.AnsiString, gmreplication.SubordinateCount);
                    Database.AddInParameter(cmd, Dbstring + "iMasterSubordinateAssoc", DbType.AnsiString,
                                      gmreplication.MasterSubordinateAssoc);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add GlobalMirrorReplication information",exc);
            }

        }

        public static bool AddGlobalMirrorMonitorStatus(GlobalMirrorMonitor gmreplication)
        {
            

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("GLOBALMIRRORMONITSTATUS_CREATE"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iGMRResultId", DbType.AnsiString, gmreplication.GMRResultId);
                    Database.AddInParameter(cmd, Dbstring + "iMasterCount", DbType.AnsiString, gmreplication.MasterCount);
                    Database.AddInParameter(cmd, Dbstring + "iMasterSessionId", DbType.AnsiString, gmreplication.MasterSessionId);
                    Database.AddInParameter(cmd, Dbstring + "iCopyState", DbType.AnsiString, gmreplication.CopyState);
                    Database.AddInParameter(cmd, Dbstring + "iFatalReason", DbType.AnsiString, gmreplication.FatalReason);

                    Database.AddInParameter(cmd, Dbstring + "iCGIntervalTime", DbType.AnsiString, gmreplication.CGIntervalTime);
                    Database.AddInParameter(cmd, Dbstring + "iCoordTime", DbType.AnsiString, gmreplication.CoordTime);
                    Database.AddInParameter(cmd, Dbstring + "iMaxCGDrainTime", DbType.AnsiString, gmreplication.MaxCGDrainTime);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentTime", DbType.AnsiString, gmreplication.CurrentTime);
                    Database.AddInParameter(cmd, Dbstring + "iCGTime", DbType.AnsiString, gmreplication.CGTime);

                    Database.AddInParameter(cmd, Dbstring + "iSuccessfulCGPercentage", DbType.AnsiString,
                                      gmreplication.SuccessfulCGPercentage);
                    Database.AddInParameter(cmd, Dbstring + "iFlashCopySequenceNumber", DbType.AnsiString,
                                      gmreplication.FlashCopySequenceNumber);
                    Database.AddInParameter(cmd, Dbstring + "iMasterId", DbType.AnsiString, gmreplication.MasterId);
                    Database.AddInParameter(cmd, Dbstring + "iSubordinateCount", DbType.AnsiString, gmreplication.SubordinateCount);
                    Database.AddInParameter(cmd, Dbstring + "iMasterSubordinateAssoc", DbType.AnsiString,
                                      gmreplication.MasterSubordinateAssoc);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Add GlobalMirrorReplication information", exc);
            }

        }

        public static GlobalMirrorMonitor GetCurrentStatus(string strogeImageId)
        {
            var globalMirrorReplication = new GlobalMirrorMonitor();

          

             try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("GLOBALMIRRORMON_GETCURRTSTATUS"))
                {
                    Database.AddInParameter(cmd, Dbstring + "istrogeImageId", DbType.AnsiString, strogeImageId);

                    using (IDataReader myRepliocationReader = Database.ExecuteReader(cmd))
                    {
                        while (myRepliocationReader.Read())
                        {
                            globalMirrorReplication.Id = Convert.IsDBNull(myRepliocationReader["Id"]) ? 0 : Convert.ToInt32(myRepliocationReader["Id"]);
                            globalMirrorReplication.GMRResultId = Convert.IsDBNull(myRepliocationReader["GMRResultId"]) ? string.Empty : Convert.ToString(myRepliocationReader["GMRResultId"]);
                            globalMirrorReplication.MasterCount = Convert.IsDBNull(myRepliocationReader["MasterCount"]) ? string.Empty : Convert.ToString(myRepliocationReader["MasterCount"]);
                            globalMirrorReplication.MasterSessionId = Convert.IsDBNull(myRepliocationReader["MasterSessionId"]) ? string.Empty : Convert.ToString(myRepliocationReader["MasterSessionId"]);

                            globalMirrorReplication.CopyState = Convert.IsDBNull(myRepliocationReader["CopyState"]) ? string.Empty : Convert.ToString(myRepliocationReader["CopyState"]);
                            globalMirrorReplication.FatalReason = Convert.IsDBNull(myRepliocationReader["FatalReason"]) ? string.Empty : Convert.ToString(myRepliocationReader["FatalReason"]);
                            globalMirrorReplication.CGIntervalTime = Convert.IsDBNull(myRepliocationReader["CGIntervalTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CGIntervalTime"]);

                            globalMirrorReplication.CoordTime = Convert.IsDBNull(myRepliocationReader["CoordTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CoordTime"]);
                            globalMirrorReplication.MaxCGDrainTime = Convert.IsDBNull(myRepliocationReader["MaxCGDrainTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["MaxCGDrainTime"]);
                            globalMirrorReplication.CurrentTime = Convert.IsDBNull(myRepliocationReader["CurrentTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CurrentTime"]);

                            globalMirrorReplication.CGTime = Convert.IsDBNull(myRepliocationReader["CGTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CGTime"]);
                            globalMirrorReplication.MasterId = Convert.IsDBNull(myRepliocationReader["MasterId"]) ? string.Empty : Convert.ToString(myRepliocationReader["MasterId"]);


                            //globalMirrorReplication.Id = Convert.ToInt32(myRepliocationReader[0]);
                            //globalMirrorReplication.GMRResultId = myRepliocationReader[1].ToString();
                            //globalMirrorReplication.MasterCount = myRepliocationReader[2].ToString();
                            //globalMirrorReplication.MasterSessionId = myRepliocationReader[3].ToString();
                            //globalMirrorReplication.CopyState = myRepliocationReader[4].ToString();
                            //globalMirrorReplication.FatalReason = myRepliocationReader[5].ToString();
                            //globalMirrorReplication.CGIntervalTime = myRepliocationReader[6].ToString();
                            //globalMirrorReplication.CoordTime = myRepliocationReader[7].ToString();
                            //globalMirrorReplication.MaxCGDrainTime = myRepliocationReader[8].ToString();
                            //globalMirrorReplication.CurrentTime = myRepliocationReader[9].ToString();
                            //globalMirrorReplication.CGTime = myRepliocationReader[10].ToString();
                            //globalMirrorReplication.MasterId = myRepliocationReader[13].ToString();//line of code present in existing solution not in newversion  and copied from sailens copy
                        }
                    }
                }
            }
            catch (Exception exc)
            {
              throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,"Exception occurred while Get GlobalMirrorReplication current status",exc);
              
            }

            return globalMirrorReplication;
        }

        //Added by Neeraj for 24Hours Status Report.
        public static IList<GlobalMirrorMonitor> GetByStorageImageId(string strogeImageId)
        {
            IList<GlobalMirrorMonitor> globalMirrorReplication = new List<GlobalMirrorMonitor>();

           

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("GLOBALMIRRORMON_GETBYSTRGIMGID"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iStorageImageId", DbType.AnsiString, strogeImageId);

                    using (IDataReader myRepliocationReader = Database.ExecuteReader(cmd))
                    {
                        while (myRepliocationReader.Read())
                        {
                            var gmReplication = new GlobalMirrorMonitor();


                            gmReplication.Id = Convert.IsDBNull(myRepliocationReader["Id"]) ? 0 : Convert.ToInt32(myRepliocationReader["Id"]);
                            gmReplication.GMRResultId = Convert.IsDBNull(myRepliocationReader["GMRResultId"]) ? string.Empty : Convert.ToString(myRepliocationReader["GMRResultId"]);
                            gmReplication.MasterCount = Convert.IsDBNull(myRepliocationReader["MasterCount"]) ? string.Empty : Convert.ToString(myRepliocationReader["MasterCount"]);
                            gmReplication.MasterSessionId = Convert.IsDBNull(myRepliocationReader["MasterSessionId"]) ? string.Empty : Convert.ToString(myRepliocationReader["MasterSessionId"]);

                            gmReplication.CopyState = Convert.IsDBNull(myRepliocationReader["CopyState"]) ? string.Empty : Convert.ToString(myRepliocationReader["CopyState"]);
                            gmReplication.FatalReason = Convert.IsDBNull(myRepliocationReader["FatalReason"]) ? string.Empty : Convert.ToString(myRepliocationReader["FatalReason"]);
                            gmReplication.CGIntervalTime = Convert.IsDBNull(myRepliocationReader["CGIntervalTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CGIntervalTime"]);

                            gmReplication.CoordTime = Convert.IsDBNull(myRepliocationReader["CoordTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CoordTime"]);
                            gmReplication.MaxCGDrainTime = Convert.IsDBNull(myRepliocationReader["MaxCGDrainTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["MaxCGDrainTime"]);
                            gmReplication.CurrentTime = Convert.IsDBNull(myRepliocationReader["CurrentTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CurrentTime"]);

                            gmReplication.CGTime = Convert.IsDBNull(myRepliocationReader["CGTime"]) ? string.Empty : Convert.ToString(myRepliocationReader["CGTime"]);
                            

                            //gmReplication.Id = Convert.ToInt32(myRepliocationReader[0]);
                            //gmReplication.GMRResultId = myRepliocationReader[1].ToString();
                            //gmReplication.MasterCount = myRepliocationReader[2].ToString();
                            //gmReplication.MasterSessionId = myRepliocationReader[3].ToString();
                            //gmReplication.CopyState = myRepliocationReader[4].ToString();
                            //gmReplication.FatalReason = myRepliocationReader[5].ToString();
                            //gmReplication.CGIntervalTime = myRepliocationReader[6].ToString();
                            //gmReplication.CoordTime = myRepliocationReader[7].ToString();
                            //gmReplication.MaxCGDrainTime = myRepliocationReader[8].ToString();
                            //gmReplication.CurrentTime = myRepliocationReader[9].ToString();
                            //gmReplication.CGTime = myRepliocationReader[10].ToString();
                            globalMirrorReplication.Add(gmReplication);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get GlobalMirrorReplication storageImageId", exc);

            }

            return globalMirrorReplication;





        }

    }
}
