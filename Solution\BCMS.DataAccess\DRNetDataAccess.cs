﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using BCMS.Common;

namespace Bcms.DataAccess
{
    public class DRNetDataAccess : BaseDataAccess
    {
        public static bool AddDRNetReplicationDetails(DRNetReplicationMonitor drNetReplication)
        {
            const string SP = "DRNetReplicationMonitor_Create"; 

            var db = DatabaseFactory.CreateDatabase();

            using (DbCommand cmd = db.GetStoredProcCommand(SP))
            {
                //db.AddInParameter(cmd, Dbstring+"iType", DbType.Int32, drOperation.Type);
                db.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, drNetReplication.InfraobjectId);

                db.AddInParameter(cmd, Dbstring + "iPRPRocessName", DbType.AnsiString, drNetReplication.PRPRocessName);
                db.AddInParameter(cmd, Dbstring + "iDRPRocessName", DbType.AnsiString, drNetReplication.DRPRocessName);

                db.AddInParameter(cmd, Dbstring + "iPRMode", DbType.AnsiString, drNetReplication.PRMode);
                db.AddInParameter(cmd, Dbstring + "iDRMode", DbType.AnsiString, drNetReplication.DRMode);

                db.AddInParameter(cmd, Dbstring + "iPRStatus", DbType.AnsiString, drNetReplication.PRStatus);
                db.AddInParameter(cmd, Dbstring + "iDRStatus", DbType.AnsiString, drNetReplication.DRStatus);

                db.AddInParameter(cmd, Dbstring + "iPRAccessed", DbType.AnsiString, drNetReplication.PRAccessed);
                db.AddInParameter(cmd, Dbstring + "iDRAccessed", DbType.AnsiString, drNetReplication.DRAccessed);

                db.AddInParameter(cmd, Dbstring + "iPRQueue", DbType.AnsiString, drNetReplication.PRQueue);
                db.AddInParameter(cmd, Dbstring + "iDRQueue", DbType.AnsiString, drNetReplication.DRQueue);

                db.AddInParameter(cmd, Dbstring + "iPRAuditedFileName", DbType.AnsiString, drNetReplication.PRAuditedFileName);
                db.AddInParameter(cmd, Dbstring + "iDRAuditDestination", DbType.AnsiString, drNetReplication.DRAuditDestination);

                db.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, drNetReplication.DataLag);

                int value = Database.ExecuteNonQuery(cmd);

                if (value > 0)
                {
                    return true;
                }
            }
            return false;
        }

        //public static bool UpdateDROperation(DROperation drOperation)
        //{
        //    const string sp = "DROperation_UpdtByStatusnEndTm";

        //    using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //    {
        //        Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, drOperation.Id);

        //        Database.AddInParameter(cmd, Dbstring+"iEndTime", DbType.DateTime, drOperation.EndTime);

        //        Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.AnsiString, drOperation.Status);

        //        var returnCode = Database.ExecuteNonQuery(cmd);

        //        return returnCode > 0;

        //    }
        //}

        //public static bool UpdateDROperationByConditional(int drId, int conditonal)
        //{
        //    const string sp = "DROperation_UpdtByConditional";

        //    using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //    {
        //        Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, drId);

        //        Database.AddInParameter(cmd, Dbstring+"iConditonal", DbType.Int32, conditonal);

        //        var returnCode = Database.ExecuteNonQuery(cmd);

        //        return returnCode > 0;

        //    }
        //}

        //public static DROperation GetLastDROperation(int groupId)
        //{

        //    DROperation drOperation = new DROperation();


        //    using (DbCommand cmd = Database.GetStoredProcCommand("DROperation_GetLastByInfraID"))
        //    {
        //        Database.AddInParameter(cmd, Dbstring+"iInfraObjectID", DbType.Int32, groupId);

        //        using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
        //        {
        //            if (myDROperationReader1.Read())
        //            {
        //                drOperation.Id = Convert.IsDBNull(myDROperationReader1["Id"]) ? 0 : Convert.ToInt32(myDROperationReader1["Id"]);
        //                drOperation.Type = Convert.IsDBNull(myDROperationReader1["Type"]) ? 0 : Convert.ToInt32(myDROperationReader1["Type"]);
        //                drOperation.StartTime = Convert.IsDBNull(myDROperationReader1["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["StartTime"]);
        //                drOperation.EndTime = Convert.IsDBNull(myDROperationReader1["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["EndTime"]);
        //                drOperation.Status = Convert.IsDBNull(myDROperationReader1["Status"]) ? string.Empty : Convert.ToString(myDROperationReader1["Status"]);
        //                drOperation.ConditionalOperation = Convert.IsDBNull(myDROperationReader1["ConditionalOperation"]) ? 0 : Convert.ToInt32(myDROperationReader1["ConditionalOperation"]);
        //                drOperation.WorkflowId = Convert.IsDBNull(myDROperationReader1["WorkflowId"]) ? 0 : Convert.ToInt32(myDROperationReader1["WorkflowId"]);
        //                drOperation.GroupId = Convert.IsDBNull(myDROperationReader1["InfraObjectId"]) ? 0 : Convert.ToInt32(myDROperationReader1["InfraObjectId"]);
        //                drOperation.Direction = Convert.IsDBNull(myDROperationReader1["Direction"]) ? 0 : Convert.ToInt32(myDROperationReader1["Direction"]);
        //                drOperation.ActionMode = (ActionMode)Convert.ToInt32(myDROperationReader1["ActionMode"]);

        //            }
        //            else
        //            {
        //                drOperation = null;
        //            }
        //        }

        //        return drOperation;
        //    }

        //}

        //public static DROperation GetDROperationById(int id)
        //{

        //    var drOperation = new DROperation();

        //    using (DbCommand cmd = Database.GetStoredProcCommand("DROperation_GetById"))
        //    {
        //        Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

        //        using (IDataReader myDROperationReader1 = Database.ExecuteReader(cmd))
        //        {
        //            if (myDROperationReader1.Read())
        //            {
        //                drOperation.Id = Convert.IsDBNull(myDROperationReader1["Id"]) ? 0 : Convert.ToInt32(myDROperationReader1["Id"]);
        //                drOperation.Type = Convert.IsDBNull(myDROperationReader1["Type"]) ? 0 : Convert.ToInt32(myDROperationReader1["Type"]);
        //                drOperation.StartTime = Convert.IsDBNull(myDROperationReader1["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["StartTime"]);
        //                drOperation.EndTime = Convert.IsDBNull(myDROperationReader1["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myDROperationReader1["EndTime"]);
        //                drOperation.Status = Convert.IsDBNull(myDROperationReader1["Status"]) ? string.Empty : Convert.ToString(myDROperationReader1["Status"]);
        //                drOperation.ConditionalOperation = Convert.IsDBNull(myDROperationReader1["ConditionalOperation"]) ? 0 : Convert.ToInt32(myDROperationReader1["ConditionalOperation"]);
        //                drOperation.WorkflowId = Convert.IsDBNull(myDROperationReader1["WorkflowId"]) ? 0 : Convert.ToInt32(myDROperationReader1["WorkflowId"]);
        //                drOperation.GroupId = Convert.IsDBNull(myDROperationReader1["InfraObjectId"]) ? 0 : Convert.ToInt32(myDROperationReader1["InfraObjectId"]);
        //                drOperation.Direction = Convert.IsDBNull(myDROperationReader1["Direction"]) ? 0 : Convert.ToInt32(myDROperationReader1["Direction"]);
        //                drOperation.ActionMode = (ActionMode)Convert.ToInt32(myDROperationReader1["ActionMode"]);

        //            }
        //            else
        //            {
        //                drOperation = null;
        //            }
        //        }

        //        return drOperation;
        //    }

        //}

    }
}