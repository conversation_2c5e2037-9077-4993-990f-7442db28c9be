﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using System.Data.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;


namespace Bcms.DataAccess
{
    public class MSSQLAlwaysonMonitorDataAccess : BaseDataAccess
    {
        #region Methods



        public static bool AddalwayasonLogDetails(MSSQLAlwaysonMonitor sqllognative)
        {
            try
            {
                // MSSQLAlwaysonlogs_create
                //  MSQLAlwaysonMonitorstatus_create
                using (DbCommand cmd = Database.GetStoredProcCommand("MSSQLAlwaysonlogs_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqllognative.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRInstanceName", DbType.AnsiString, sqllognative.PRInstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iDRInstanceName", DbType.AnsiString, sqllognative.DRInstanceName);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGroupname", DbType.AnsiString, sqllognative.PRAGroupname);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGroupname", DbType.AnsiString, sqllognative.DRAGroupname);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGrouprole", DbType.AnsiString, sqllognative.PRAGrouprole);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGrouprole", DbType.AnsiString, sqllognative.DRAGrouprole);

                    Database.AddInParameter(cmd, Dbstring + "iPRReplicaMode", DbType.AnsiString, sqllognative.PRReplicaMode);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicaMode", DbType.AnsiString, sqllognative.DRReplicaMode);

                    Database.AddInParameter(cmd, Dbstring + "iPRFailovermode", DbType.AnsiString, sqllognative.PRFailovermode);
                    Database.AddInParameter(cmd, Dbstring + "iDRFailovermode", DbType.AnsiString, sqllognative.DRFailovermode);

                    Database.AddInParameter(cmd, Dbstring + "iPRPSroleallowconn", DbType.AnsiString, sqllognative.PRPSroleallowconn);
                    Database.AddInParameter(cmd, Dbstring + "iDRPSroleallowconn", DbType.AnsiString, sqllognative.DRPSroleallowconn);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGOperationalstate", DbType.AnsiString, sqllognative.PRAGOperationalstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGOperationalstate", DbType.AnsiString, sqllognative.PRAGOperationalstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGconnectedstate", DbType.AnsiString, sqllognative.PRAGconnectedstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGconnectedstate", DbType.AnsiString, sqllognative.PRAGconnectedstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBSynchronizationState", DbType.AnsiString, sqllognative.PRDBSynchronizationState);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBSynchronizationState", DbType.AnsiString, sqllognative.DRDBSynchronizationState);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBSynchronizationhealth", DbType.AnsiString, sqllognative.PRDBSynchronizationhealth);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBSynchronizationhealth", DbType.AnsiString, sqllognative.PRDBSynchronizationhealth);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBstate", DbType.AnsiString, sqllognative.PRDBstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBstate", DbType.AnsiString, sqllognative.DRDBstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBsynchron_state_onavaildb", DbType.AnsiString, sqllognative.PRDBsynchron_state_onavaildatabase);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBsynchron_state_onavaildb", DbType.AnsiString, sqllognative.DRDBsynchron_state_onavaildatabase);

                    Database.AddInParameter(cmd, Dbstring + "iPREndpointPortNumber", DbType.AnsiString, sqllognative.PREndpointPortNumber);
                    Database.AddInParameter(cmd, Dbstring + "iDREndpointPortNumber", DbType.AnsiString, sqllognative.DREndpointPortNumber);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastSentLSN", DbType.AnsiString, sqllognative.PRLastSentLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastSentLSN", DbType.AnsiString, sqllognative.DRLastSentLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastReceivedLSN", DbType.AnsiString, sqllognative.PRLastReceivedLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastReceivedLSN", DbType.AnsiString, sqllognative.DRLastReceivedLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastRedoneLSN", DbType.AnsiString, sqllognative.PRLastRedoneLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastRedoneLSN", DbType.AnsiString, sqllognative.DRLastRedoneLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastCommitLSN", DbType.AnsiString, sqllognative.PRLastCommitLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastCommitLSN", DbType.AnsiString, sqllognative.DRLastCommitLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastSentTime", DbType.AnsiString, sqllognative.PRLastSentTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastSentTime", DbType.AnsiString, sqllognative.DRLastSentTime);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastReceivedTime", DbType.AnsiString, sqllognative.PRLastReceivedTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastReceivedTime", DbType.AnsiString, sqllognative.DRLastReceivedTime);


                    Database.AddInParameter(cmd, Dbstring + "iPRLastRedoneTime", DbType.AnsiString, sqllognative.PRLastRedoneTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastRedoneTime", DbType.AnsiString, sqllognative.DRLastRedoneTime);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastCommitTime", DbType.AnsiString, sqllognative.PRLastCommitTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastCommitTime", DbType.AnsiString, sqllognative.DRLastCommitTime);

                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, sqllognative.DataLag);

                    Database.AddInParameter(cmd, Dbstring + "iPRLogSendQueueSizeinKB", DbType.AnsiString, sqllognative.PRLogSendQueueSizeinKB);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSendQueueSizeinKB", DbType.AnsiString, sqllognative.DRLogSendQueueSizeinKB);

                    Database.AddInParameter(cmd, Dbstring + "iPRRedoQueueSizeinKB", DbType.AnsiString, sqllognative.PRRedoQueueSizeinKB);
                    Database.AddInParameter(cmd, Dbstring + "iDRRedoQueueSizeinKB", DbType.AnsiString, sqllognative.DRRedoQueueSizeinKB);

                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseSize", DbType.AnsiString, sqllognative.PRDatabaseSize);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseSize", DbType.AnsiString, sqllognative.DRDatabaseSize);

                    Database.AddInParameter(cmd, Dbstring + "iPRMSSQLEdition", DbType.AnsiString, sqllognative.PRMSSQLEdition);
                    Database.AddInParameter(cmd, Dbstring + "iDRMSSQLEdition", DbType.AnsiString, sqllognative.DRMSSQLEdition);

                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting AddalwayasonLogDetails entry ", exc);
            }
            return false;
        }

        public static bool AddalwayasonStatusDetails(MSSQLAlwaysonMonitor sqllognative)
        {
            try
            {
                // MSSQLAlwaysonlogs_create
                //  MSQLAlwaysonMonitorstatus_create
                using (DbCommand cmd = Database.GetStoredProcCommand("MSSQLAlwyonMonistatus_create"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, sqllognative.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iPRInstanceName", DbType.AnsiString, sqllognative.PRInstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iDRInstanceName", DbType.AnsiString, sqllognative.DRInstanceName);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGroupname", DbType.AnsiString, sqllognative.PRAGroupname);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGroupname", DbType.AnsiString, sqllognative.DRAGroupname);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGrouprole", DbType.AnsiString, sqllognative.PRAGrouprole);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGrouprole", DbType.AnsiString, sqllognative.DRAGrouprole);

                    Database.AddInParameter(cmd, Dbstring + "iPRReplicaMode", DbType.AnsiString, sqllognative.PRReplicaMode);
                    Database.AddInParameter(cmd, Dbstring + "iDRReplicaMode", DbType.AnsiString, sqllognative.DRReplicaMode);

                    Database.AddInParameter(cmd, Dbstring + "iPRFailovermode", DbType.AnsiString, sqllognative.PRFailovermode);
                    Database.AddInParameter(cmd, Dbstring + "iDRFailovermode", DbType.AnsiString, sqllognative.DRFailovermode);

                    Database.AddInParameter(cmd, Dbstring + "iPRPSroleallowconn", DbType.AnsiString, sqllognative.PRPSroleallowconn);
                    Database.AddInParameter(cmd, Dbstring + "iDRPSroleallowconn", DbType.AnsiString, sqllognative.DRPSroleallowconn);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGOperationalstate", DbType.AnsiString, sqllognative.PRAGOperationalstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGOperationalstate", DbType.AnsiString, sqllognative.PRAGOperationalstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRAGconnectedstate", DbType.AnsiString, sqllognative.PRAGconnectedstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRAGconnectedstate", DbType.AnsiString, sqllognative.PRAGconnectedstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBSynchronizationState", DbType.AnsiString, sqllognative.PRDBSynchronizationState);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBSynchronizationState", DbType.AnsiString, sqllognative.DRDBSynchronizationState);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBSynchronizationhealth", DbType.AnsiString, sqllognative.PRDBSynchronizationhealth);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBSynchronizationhealth", DbType.AnsiString, sqllognative.PRDBSynchronizationhealth);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBstate", DbType.AnsiString, sqllognative.PRDBstate);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBstate", DbType.AnsiString, sqllognative.DRDBstate);

                    Database.AddInParameter(cmd, Dbstring + "iPRDBsynchron_state_onavaildb", DbType.AnsiString, sqllognative.PRDBsynchron_state_onavaildatabase);
                    Database.AddInParameter(cmd, Dbstring + "iDRDBsynchron_state_onavaildb", DbType.AnsiString, sqllognative.DRDBsynchron_state_onavaildatabase);

                    Database.AddInParameter(cmd, Dbstring + "iPREndpointPortNumber", DbType.AnsiString, sqllognative.PREndpointPortNumber);
                    Database.AddInParameter(cmd, Dbstring + "iDREndpointPortNumber", DbType.AnsiString, sqllognative.DREndpointPortNumber);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastSentLSN", DbType.AnsiString, sqllognative.PRLastSentLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastSentLSN", DbType.AnsiString, sqllognative.DRLastSentLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastReceivedLSN", DbType.AnsiString, sqllognative.PRLastReceivedLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastReceivedLSN", DbType.AnsiString, sqllognative.DRLastReceivedLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastRedoneLSN", DbType.AnsiString, sqllognative.PRLastRedoneLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastRedoneLSN", DbType.AnsiString, sqllognative.DRLastRedoneLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastCommitLSN", DbType.AnsiString, sqllognative.PRLastCommitLSN);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastCommitLSN", DbType.AnsiString, sqllognative.DRLastCommitLSN);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastSentTime", DbType.AnsiString, sqllognative.PRLastSentTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastSentTime", DbType.AnsiString, sqllognative.DRLastSentTime);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastReceivedTime", DbType.AnsiString, sqllognative.PRLastReceivedTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastReceivedTime", DbType.AnsiString, sqllognative.DRLastReceivedTime);


                    Database.AddInParameter(cmd, Dbstring + "iPRLastRedoneTime", DbType.AnsiString, sqllognative.PRLastRedoneTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastRedoneTime", DbType.AnsiString, sqllognative.DRLastRedoneTime);

                    Database.AddInParameter(cmd, Dbstring + "iPRLastCommitTime", DbType.AnsiString, sqllognative.PRLastCommitTime);
                    Database.AddInParameter(cmd, Dbstring + "iDRLastCommitTime", DbType.AnsiString, sqllognative.DRLastCommitTime);

                    Database.AddInParameter(cmd, Dbstring + "iDataLag", DbType.AnsiString, sqllognative.DataLag);

                    Database.AddInParameter(cmd, Dbstring + "iPRLogSendQueueSizeinKB", DbType.AnsiString, sqllognative.PRLogSendQueueSizeinKB);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogSendQueueSizeinKB", DbType.AnsiString, sqllognative.DRLogSendQueueSizeinKB);

                    Database.AddInParameter(cmd, Dbstring + "iPRRedoQueueSizeinKB", DbType.AnsiString, sqllognative.PRRedoQueueSizeinKB);
                    Database.AddInParameter(cmd, Dbstring + "iDRRedoQueueSizeinKB", DbType.AnsiString, sqllognative.DRRedoQueueSizeinKB);

                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseSize", DbType.AnsiString, sqllognative.PRDatabaseSize);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseSize", DbType.AnsiString, sqllognative.DRDatabaseSize);

                    Database.AddInParameter(cmd, Dbstring + "iPRMSSQLEdition", DbType.AnsiString, sqllognative.PRMSSQLEdition);
                    Database.AddInParameter(cmd, Dbstring + "iDRMSSQLEdition", DbType.AnsiString, sqllognative.DRMSSQLEdition);

                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, sqllognative.IsActive);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, sqllognative.CreatorId);
                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value > 0)
                    {
                        return true;
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting alwayason Status Details entry ", exc);
            }
            return false;
        }

        #endregion Methods
    }
}
