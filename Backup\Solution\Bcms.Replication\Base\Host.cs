﻿using System;
using System.Collections.Generic;
using System.Text;
using Bcms.ExceptionHandler;
using Bcms.Replication.Shared;
using Jscape.Ssh;

namespace Bcms.Replication.Base
{
    public abstract class Host 
    {
        private string _hostName;

        private string _userName;

        private string _password;

        public string HostName
        {
            get { return _hostName; }
            set
            {
                if (value == String.Empty)
                {
                    throw new BcmsException(BcmsExceptionType.CommonArgumentNullException,"Host Name cannot be null value");
                }
                _hostName = value;
            }
        }

        public string UserName
        {
            get { return _userName; }
            set
            {
                if (value == String.Empty)
                {
                    throw new BcmsException(BcmsExceptionType.CommonArgumentNullException, "User Name cannot be null value");
                }
                _userName = value;
            }
        }

        //public string Password
        //{
        //    get { return _password; }
        //    set
        //    {
        //        if (value == String.Empty)
        //        {
        //            throw new BcmsException(BcmsExceptionType.CommonArgumentNullException, "Password cannot be null value");
        //        }
        //        _password = value;
        //    }
        //}

        public string Password
        {
            get;
            set;
        }

        public string PrivateKey
        {
            get;
            set;
        }

        public string PrivateKeyPasspharase
        {
            get;
            set;
        }

        public bool IsKeyAuthentication { get; set; }

        public string SudoUser { get; set; }

        public string DatabaseName { get; set; }

        public string DatabaseUserName { get; set; }

        public string DatabasePassword { get; set; }

        public bool IsWindows { get; set; }
       
        #region Constructor and Destructor
        
       

        #endregion

        internal abstract bool Connect(bool isConnectDatabase);

       // internal abstract bool ConnectWithDatabaseName(string databaseName);

        internal abstract void Disconnect();

        internal abstract bool VerifyDatabaseMode();

        internal abstract bool VerifyUserStatus();

        internal abstract string GetMaxSequenceNumber();

        internal abstract bool Exit();

        internal abstract bool MoveFiles(string sourcePath,string fileName,string targetPath);

        internal abstract void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt);
        
        protected string MoveFiles(SshSession session,string sourcePath,string fileName,string targetPath)
        {
          return RunSshCommandsWithWait(session, String.Format("mv {0}/{1} {2}", sourcePath,fileName,targetPath), "$");
        }

        protected void MoveFiles(SshSession session,string sourcePath,string targetPath,bool isSqlPrompt)
        {
            string command = isSqlPrompt ? "!cp " + sourcePath + "* " + targetPath :"cp" + sourcePath + "*" + targetPath;

            string prompt = isSqlPrompt ? Constants.QueryConstants.GreaterThan : Constants.QueryConstants.Dolar;

            RunSshCommandsWithWait(session, command, prompt);
        }
// surendra added
        protected bool ConnectToDatabase(SshSession session)
        {
            var sb = new StringBuilder();

            var commands = new List<string>
                {
                                   Constants.QueryConstants.SudoUser + SudoUser,
                                   Constants.QueryConstants.GlobalName,
                                   DatabaseName,
                                   Constants.QueryConstants.ConnectDbaSingle
                               };

            //Constants.QueryConstants.SudoUser+SudoUser,Constants.QueryConstants.ExportOracle+DatabaseName.ToUpper(),Constants.QueryConstants.ConnectDbaSingle
            //Constants.QueryConstants.SqlPlusNoLog, Constants.QueryConstants.ConnectSysDba, Constants.QueryConstants.ExpectPassword };

            foreach (var command in commands)
            {
                sb.Append(RunSshCommands(session, command));
            }

            LogHelper.LogHeaderAndMessage(DatabaseName.ToUpper()+" : CONNECTION OUTPUT", sb.ToString());
            
            var connectResult = sb.ToString().ToLower();

            if (connectResult.Contains("error") )
            {
                return false;
            }
            return connectResult.Contains("connected");
        }
// end surendra
        protected bool ConnectToDatabase(SshSession session, string databaseName)
        {
            try
            {
                var sb = new StringBuilder();

                session.SetShellPrompt("\\$|#|>", true);

                if (!String.IsNullOrEmpty(SudoUser) && !SudoUser.Contains("none"))
                {
                    sb.Append(RunSshCommands(session, Constants.QueryConstants.SudoUser + SudoUser));
                }

                sb.Append(RunSshCommands(session, Constants.QueryConstants.ExportOracle + databaseName));

                sb.Append(RunSshCommands(session, Constants.QueryConstants.ConnectDbaSingle));

                var connectResult = sb.ToString().ToLower();

                LogHelper.LogHeaderAndMessage(DatabaseName.ToUpper() + " : CONNECTION OUTPUT", sb.ToString());

                if (connectResult.Contains("error"))
                {
                    return false;
                }
                return connectResult.Contains("connected");
            }
            catch(SshException exc)
            {
                LogHelper.LogHeaderAndErrorMessage("CONNECT TO DATABASE SSH ERROR",exc);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled, "SshException occured while connecting Database - " + databaseName, exc);
 
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("CONNECT TO DATABASE ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occured while connecting Database - " + databaseName, exc);
            }
            
        }

        protected bool IsPrimaryHavingProcess(SshSession session)
        {
            try
            {
                string processCount = RunSshCommandsWithWait(session, Constants.QueryConstants.ProcessCount,Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY PROCESS COUNT OUTPUT ", processCount);

                if (processCount.Contains("name") && processCount.Contains("-") && processCount.Contains("sql>") && processCount.Contains("job_queue_processes") && processCount.Contains("value"))
                {
                    processCount = processCount.Replace("\r\n", "");
                    processCount = processCount.Replace("\t", "");
                    processCount = processCount.Replace("name", "");
                    processCount = processCount.Replace("job_queue_processes", "");
                    processCount = processCount.Replace("value", "");
                    processCount = processCount.Replace("-", "");
                    processCount = processCount.Replace("sql>", "").Trim();
                }

                return Convert.ToInt32(processCount) == 0;

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY PROCESS COUNT ERROR ", exc);

                return false;
               
            }
        }

        protected int VerifyPrimaryLogCount(SshSession session)
        {
            try
            {
              var logcount = RunSshCommandsWithWait(session,Constants.QueryConstants.LogCount,Constants.QueryConstants.GreaterThan);

              LogHelper.LogHeaderAndMessage("PRIMARY LOG COUNT OUTPUT ", logcount);

            if (logcount.Contains("count(*)") && logcount.Contains("-") && logcount.Contains("sql>"))
            {
                logcount = logcount.Replace("\r\n", "");
                logcount = logcount.Replace("\t", "");
                logcount = logcount.Replace("count(*)", "");
                logcount = logcount.Replace("-", "");
                logcount = logcount.Replace("sql>", "").Trim();
            }

            return  Convert.ToInt32(logcount);

            }
            catch (Exception)
            {

                return 0;
            }
          
        }

        protected bool IsKillAllPrimaryProcess(SshSession session)
        {
            try
            {
                LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS COMMAND : ", Constants.QueryConstants.KillProcess);

                string jobCount = RunSshCommandsWithWait(session, Constants.QueryConstants.KillProcess, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS OUTPUT ", jobCount);

                if (jobCount.Contains("name") && jobCount.Contains("-") && jobCount.Contains("sql>") && jobCount.Contains("job_queue_processes") && jobCount.Contains("value"))
                {
                    jobCount = jobCount.Replace("\r\n", "");
                    jobCount = jobCount.Replace("\t", "");
                    jobCount = jobCount.Replace("name", "");
                    jobCount = jobCount.Replace("Job_queue_processes", "");
                    jobCount = jobCount.Replace("value", "");
                    jobCount = jobCount.Replace("-", "");
                    jobCount = jobCount.Replace("SQL>", "").Trim().ToLower();
                }
                return jobCount.Contains("system altered");

            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("KILL PRIMARY PROCESS ERROR : ", exc);

                throw new BcmsException(BcmsExceptionType.ORAKillProcessFailed, "Exception occured killing primary process", exc);
            }
        }

        protected void ExitPromt(SshSession session)
        {
            RunSshCommands(session, "exit");
        }
        // surendra added
        protected string GetDatabaseRoleAndMode(SshSession session)
        {
            return session.Send(Constants.QueryConstants.DatabaseModeAndRole);
        }
        // end surendra
        protected string RunSshCommands(SshSession session, string command)
        {
            return session.Send(command, 60000).Replace(command, "");
        }

        protected string RunSshCommandsWithTimeOut(SshSession session, string command,int timeout)
        {
            return session.Send(command, timeout).Replace(command, "");
        }

        protected string RunSshCommandsWithWait(SshSession session, string command, string prompt)
        {
           return session.SendWait(command, prompt, true, 600000).Replace(command,"");
        }

        protected string RunSshCommandsWithWaitAndTimeOut(SshSession session, string command, string prompt, int timeout)
        {
            return session.SendWait(command, prompt, false, timeout).Replace(command, "");
        }

        protected void RunSshCommandsWithNoWait(SshSession session, string command)
        {
            session.SendNoWait(command);
        }
        public static string GetConnectionString(string ipAdress, string database, string password)
        {
            return String.Format("Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST={0})(PORT=1521)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME={1})));DBA PRIVILEGE=sysdba;User Id= {2};Password=***", ipAdress, database, "sys", password);
        }

        public static string GetConnectionWithoutDedicatedString(string ipAdress, string database, string password)
        {
           return "data source=" +"(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)" +
            "(HOST=" + ipAdress + ")(PORT=1521))(CONNECT_DATA=" +
            "(SERVICE_NAME=" + database + ")));user id=sys;password=" + password + ";DBA PRIVILEGE=sysdba";
            //return String.Format("Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={0})(PORT=1521))(CONNECT_DATA =(SERVER=DEDICATED)(SERVICE_NAME = {1}));DBA PRIVILEGE=sysdba;User Id= {2};Password=***", ipAdress, database, "sys", password);
        }

        
    }
}
