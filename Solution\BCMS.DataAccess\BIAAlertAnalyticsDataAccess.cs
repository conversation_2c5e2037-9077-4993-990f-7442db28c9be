﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using System.Collections.Generic;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Bcms.DataAccess
{
    public class BIAAlertAnalyticsDataAccess : BaseDataAccess
    {
        public static IList<BIAAlertAnalytic> GetAllAlertAnalytic()
        {
            IList<BIAAlertAnalytic> _BIAAlertAnalytic = new List<BIAAlertAnalytic>();

            const string sp = "Biaalert_Getall";

            try
            {
                using (var dbCommand = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (var reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            var BIAAlertAnalytic = new BIAAlertAnalytic();

                            BIAAlertAnalytic.AlertId = Convert.IsDBNull(reader["AlertId"]) ? 0 : Convert.ToInt32(reader["AlertId"]);
                            BIAAlertAnalytic.InfraobjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]);
                            BIAAlertAnalytic.BusinessService = Convert.IsDBNull(reader["BusinessService"]) ? string.Empty : Convert.ToString(reader["BusinessService"]);
                            BIAAlertAnalytic.BusinessFunction = Convert.IsDBNull(reader["BusinessFunction"]) ? string.Empty : Convert.ToString(reader["BusinessFunction"]);
                            BIAAlertAnalytic.BusinessService = Convert.IsDBNull(reader["BusinessService"]) ? string.Empty : Convert.ToString(reader["BusinessService"]);
                            BIAAlertAnalytic.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                            BIAAlertAnalytic.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]);
                            BIAAlertAnalytic.BusinessFunctionId = Convert.IsDBNull(reader["BusinessFunctionId"]) ? 0 : Convert.ToInt32(reader["BusinessFunctionId"]);
                            BIAAlertAnalytic.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
                            BIAAlertAnalytic.JobName = Convert.IsDBNull(reader["JobName"]) ? String.Empty : Convert.ToString(reader["JobName"]);
                            BIAAlertAnalytic.Severity = Convert.IsDBNull(reader["Severity"]) ? String.Empty : Convert.ToString(reader["Severity"]);
                            BIAAlertAnalytic.UserMessage = Convert.IsDBNull(reader["UserMessage"]) ? String.Empty : Convert.ToString(reader["UserMessage"]);
                            BIAAlertAnalytic.SystemMessage = Convert.IsDBNull(reader["SystemMessage"]) ? String.Empty : Convert.ToString(reader["SystemMessage"]);
                            BIAAlertAnalytic.AlertCreateDate = Convert.IsDBNull(reader["AlertCreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["AlertCreateDate"]);
                            _BIAAlertAnalytic.Add(BIAAlertAnalytic);
                        }
                    }
                }
                return _BIAAlertAnalytic;

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Fetching BIA Alert Analytic in BIAAlertAnalyticsDataAccess.GetAllAlertAnalytic", exc);

            }
        }
        // insert records into  Alert_analytic BIA table
        public static void Add_Alert_Analytic()
        {
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("Biaalert_Getall"))
                {

                    Database.ExecuteNonQuery(cmd);

                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting alert_analytic in Add_Alert_Analytic BIAAlertAnalyticsDataAccess", exc);
            }


        }
    }
}
