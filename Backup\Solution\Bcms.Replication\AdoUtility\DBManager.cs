﻿using System;
using System.Data;
using System.Data.SqlClient;
using Bcms.Common;
using Bcms.Replication.Interface;


namespace Bcms.Replication.AdoUtility
{
    public class DBManager : IDBManager, IDisposable
    {
        private IDbCommand _command;

        public Group CurrentInfraObject { get; set; }

        public Server CurrentServer { get; set; }

        public DBManager(Group group, Server server)
        {
            CurrentInfraObject = group;
            CurrentServer = server;
        }

        private IDbConnection GetConnectionString(DatabaseBase database, string type)
        {
            string connextionstring = string.Empty;

            switch (type)
            {
                case "Primary":
                    connextionstring = string.Format("Data Source={0};Initial Catalog={1};User Id={2};Password=***;", CurrentServer.PRIPAddress, database.DatabaseSql.PRDatabaseSID, database.DatabaseSql.PRUserName, database.DatabaseSql.PRPassword);
                    break;
                case "PrimaryMaster":
                    connextionstring = string.Format("Data Source={0};Initial Catalog={1};User Id={2};Password=***;", CurrentServer.PRIPAddress, "Master", database.DatabaseSql.PRUserName, database.DatabaseSql.PRPassword);
                    break;
                case "DR":
                    connextionstring = string.Format("Data Source={0};Initial Catalog={1};User Id={2};Password=***;", CurrentServer.DRIPAddress, database.DatabaseSql.DRDatabaseSID, database.DatabaseSql.DRUserName, database.DatabaseSql.DRPassword);
                    break;
                case "DRMaster":
                    connextionstring = string.Format("Data Source={0};Initial Catalog={1};User Id={2};Password=***;", CurrentServer.DRIPAddress, "Master", database.DatabaseSql.DRUserName, database.DatabaseSql.DRPassword);
                    break;
            }
            return new SqlConnection(connextionstring);
        }

        public IDbCommand Command
        {
            get { return _command ?? (_command = new SqlCommand()); }
        }

        public IDbCommand PrepareCommand(DatabaseBase database, string commandText, CommandType commandType, string type)
        {
            Command.CommandText = commandText;
            Command.Connection = GetConnectionString(database, type);
            Command.CommandType = commandType;

            if (Command.Connection.State != ConnectionState.Open)
            {
                Command.Connection.Open();
            }

            return Command;
        }

        public void AddCommandParameter(IDbCommand cmd, string paramName, DbType dbType, object paramValue)
        {
            IDbDataParameter param = cmd.CreateParameter();
            param.ParameterName = paramName;
            param.DbType = dbType;
            param.Value = paramValue ?? DBNull.Value;
            cmd.Parameters.Add(param);
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
            _command = null;
        }
    }
}