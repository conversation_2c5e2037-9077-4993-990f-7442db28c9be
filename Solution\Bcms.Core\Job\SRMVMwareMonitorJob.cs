﻿using System;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.Core.Job
{
    public class SRMVMwareMonitorJob : IJob
    {
        #region IJob Members

        public const string Infraobject = "group";
        private static readonly ILog Logger = LogManager.GetLogger(typeof(SRMVMwareMonitorJob));

        public void Execute(JobExecutionContext context)
        {
            JobDataMap data = context.JobDetail.JobDataMap;


            var group = data.Get(Infraobject) as InfraObject;

            if (group != null)
            {   
                InfraObject groupItem = InfraObjectDataAccess.GetInfraObjectById(group.Id);

                if (groupItem != null)
                {
                    if (groupItem.State == InfraObjectState.Maintenance.ToString())
                    {
                        Logger.InfoFormat("{0} : InfraObject in Maintenace mode skip SRM VMware Monitoring Job", groupItem);
                        return;
                    }
                    MonitorSRMVMware(groupItem);
                }
            }
            else
            {
                throw new ArgumentException(context.JobDetail.Name + " InfraObject Having null values While Perform Monitor SRM VMware Job ");
            }
        }

        public void MonitorSRMVMware(InfraObject group)
        {
            try
            {
                using (var client = new BcmsClient(group))
                {
                    client.MonitorSRMVMware();
                }

            }
            catch (BcmsException exc)
            {
                ExceptionManager.Manage(exc, JobName.SRMVMwareMonitor.ToString(), group.Id, group.Name);
            }
            catch (Exception exc)
            {
                var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, Constants.ValidationConstants.ErrorMessage.GetMessage(JobName.SRMVMwareMonitor.ToString(), group.Name, string.Empty, ExceptionType.UnHandled), exc);
                ExceptionManager.Manage(bcmsException, JobName.SRMVMwareMonitor.ToString(), group.Id, group.Name);
            }
        }

        #endregion        
    }
}
