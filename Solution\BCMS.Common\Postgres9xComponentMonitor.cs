﻿using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    public class Postgres9xComponentMonitor : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string DBVersionPR { get; set; }

        [DataMember]
        public string DBVersionDR { get; set; }

        [DataMember]
        public string DBServiceStatusPR { get; set; }

        [DataMember]
        public string DBServiceStatusDR { get; set; }

        [DataMember]
        public string DBClusterStatusPR { get; set; }

        [DataMember]
        public string DBClusterStatusDR { get; set; }

        [DataMember]
        public string DBRecoveryStatusDR { get; set; }
        #endregion
    }
}
