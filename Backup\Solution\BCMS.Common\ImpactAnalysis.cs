﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "ImpactAnalysis", Namespace = "http://www.ContinuityVault.com/types")]
    public class ImpactAnalysis : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int EntityId
        {
            get;
            set;
        }

        [DataMember]
        public string ImpactType
        {
            get;
            set;
        }

        [DataMember]
        public DateTime ImpactCreateDate
        {
            get;
            set;
        }

        [DataMember]
        public DateTime ImpactResolveDate
        {
            get;
            set;
        }

        [DataMember]
        public int Status
        {
            get;
            set;
        }

        [DataMember]
        public string ImpactMessage
        {
            get;
            set;
        }

        [DataMember]
        public string ResolveMessage
        {
            get;
            set;
        }

        [DataMember]
        public string TimeTakenToResolve
        {
            get;
            set;
        }


        #endregion

    }
}
