﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Exchange", Namespace = "http://www.BCMS.com/types")]
    public class ExchangeHealthStatus : BaseEntity
    {
        //table Exchange Services Status

        //[DataMember]
        //public int ServiceId
        //{
        //    get;
        //    set;
        //}
        // public Exchange() { }

        //[DataMember]
        //public int StorageGroupId
        //{
        //    get;
        //    set;
        //}
        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRStorageGroup
        {
            get;
            set;
        }

        [DataMember]
        public string PRMailboxDatabase
        {
            get;
            set;
        }

        [DataMember]
        public string PRMailboxDatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PREdbFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string PRLatestAvabLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string PRLastGenLogSequence
        {
            get;
            set;
        }

        [DataMember]
        public string DRStorageGroup
        {
            get;
            set;
        }

        [DataMember]
        public string DRMailboxDatabase
        {
            get;
            set;
        }

        [DataMember]
        public string DRMailboxDatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DREdbFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastCopiedLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastInspectedLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastReplayedLogTime
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastCopiedLogSequence
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastInspectedLogSequence
        {
            get;
            set;
        }

        [DataMember]
        public string DRLastReplayedLogSequence
        {
            get;
            set;
        }

        [DataMember]
        public string PREdbFileSize
        {
            get;
            set;
        }

        [DataMember]
        public string PREdbFileLastWriteTime
        {
            get;
            set;
        }

        [DataMember]
        public string DREdbFileSize
        {
            get;
            set;
        }

        [DataMember]
        public string DREdbFileLastWriteTime
        {
            get;
            set;
        }

        [DataMember]
        public string DataLagTime
        {
            get;
            set;
        }
    }
}