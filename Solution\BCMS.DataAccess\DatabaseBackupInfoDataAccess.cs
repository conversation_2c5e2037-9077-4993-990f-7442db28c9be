﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.DataAccess.Utility;

namespace Bcms.DataAccess
{
    public class DatabaseBackupInfoDataAccess:BaseDataAccess
    {
        public static IList<DatabaseBackupInfo> GetAllDatabaseBackupInfo()
        {

            IList<DatabaseBackupInfo> databaseBackup = new List<DatabaseBackupInfo>();

            try
            {
                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBackupInfo_GetAll"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_dbbackupInfo"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            var database = new DatabaseBackupInfo()
                            {
                                Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]),
                                Time = Convert.IsDBNull(myReader["Time"]) ? string.Empty : Convert.ToString(myReader["Time"]),
                                Server = Convert.IsDBNull(myReader["Server"]) ? string.Empty : Convert.ToString(myReader["Server"]),
                                UserName = Convert.IsDBNull(myReader["UserName"]) ? string.Empty : Convert.ToString(myReader["UserName"]),
                                Password = Convert.IsDBNull(myReader["Password"]) ? string.Empty : Convert.ToString(myReader["Password"]),
                                DatabaseName = Convert.IsDBNull(myReader["DatabaseName"]) ? string.Empty : Convert.ToString(myReader["DatabaseName"]),
                                BackupPath = Convert.IsDBNull(myReader["BackupPath"]) ? string.Empty : Convert.ToString(myReader["BackupPath"]),
                                MySqlBinDirectory = Convert.IsDBNull(myReader["MySqlBinDirectory"]) ? string.Empty : Convert.ToString(myReader["MySqlBinDirectory"])



                                //Id = Convert.ToInt32(myReader[0]),
                                //Time = myReader[1].ToString(),
                                //Server = myReader[2].ToString(),
                                //UserName = StringHelper.Md5Decrypt(myReader[3].ToString()),
                                //Password = StringHelper.Md5Decrypt(myReader[4].ToString()),
                                //DatabaseName = myReader[5].ToString(),
                                //BackupPath = myReader[6].ToString(),
                                //MySqlBinDirectory = myReader[7].ToString()
                            };


                            databaseBackup.Add(database);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", exc);
            }

            return databaseBackup;

        }

        public static DatabaseBackupInfo GetLastDatabaseBackupInfo()
        {
            var databaseBackup = new DatabaseBackupInfo();
            try
            {

                
                using (DbCommand dbCommand = Database.GetStoredProcCommand("DatabaseBackupInfo_GetLast"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_dbbackupInfo"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            databaseBackup = new DatabaseBackupInfo()
                            {

                                Id = Convert.IsDBNull(myReader["Id"]) ? 0 : Convert.ToInt32(myReader["Id"]),
                                Time = Convert.IsDBNull(myReader["Time"]) ? string.Empty : Convert.ToString(myReader["Time"]),
                                Server = Convert.IsDBNull(myReader["Server"]) ? string.Empty : Convert.ToString(myReader["Server"]),
                                UserName = Convert.IsDBNull(myReader["UserName"]) ? string.Empty : Convert.ToString(myReader["UserName"]),
                                Password = Convert.IsDBNull(myReader["Password"]) ? string.Empty : Convert.ToString(myReader["Password"]),
                                DatabaseName = Convert.IsDBNull(myReader["DatabaseName"]) ? string.Empty : Convert.ToString(myReader["DatabaseName"]),
                                BackupPath = Convert.IsDBNull(myReader["BackupPath"]) ? string.Empty : Convert.ToString(myReader["BackupPath"]),
                                MySqlBinDirectory = Convert.IsDBNull(myReader["MySqlBinDirectory"]) ? string.Empty : Convert.ToString(myReader["MySqlBinDirectory"])
                            };

                        }

                    }

                }

            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", ex);
            }
            return databaseBackup;
        }

    }
}