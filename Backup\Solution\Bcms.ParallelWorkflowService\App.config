﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="bcms" type="System.Configuration.NameValueSectionHandler, System, Version=1.0.5000.0,Culture=neutral, PublicKeyToken=b77a5c561934e089" />
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
    <sectionGroup name="common">
      <section name="logging" type="Common.Logging.ConfigurationSectionHandler, Common.Logging" />
    </sectionGroup>
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data"/>
  </configSections>
  <appSettings>
    <add key="SMTPFromAddress" value="<EMAIL>"/>
  </appSettings>
  <connectionStrings>
    <!--<add name="BcmsConnectionString" connectionString="server=************;uid=bcmsservice;pwd=******;database=bcms;Pooling=true;Min Pool Size=0;Max Pool Size=10000;Connection Lifetime=0;procedure bodies=false;allow user variables=true"
      providerName="MySql.Data.MySqlClient"/>-->
    <add name="CPConnectionString" connectionString="NtJK3X8ADpdUNUmyuLEK6svc7kIuXPYz+5ZulnbGVsRHUKsE8QHQsQ2WJubTsk3TDDT/F79RaUeKr8RH4qMFCVPeDDk85kwAQWq4TXYDGKU4urNHL2NnL+kwEOV20SLQR/wJCNhSu9K5OftUdEg/DRsI5j8oK8dCUcbCH0ouyb6PydOHOiSquGyZsujXC5T67g7IoM+87bzjUJUfJyg+xEO0Xfn1LDVtrNT9zLcEhvdBEkovVSV3hA=="
      providerName="Oracle.DataAccess.Client"/>
  </connectionStrings>
  <dataConfiguration defaultDatabase="CPConnectionString" />
  <common>
    <logging>
      <factoryAdapter type="Common.Logging.Simple.ConsoleOutLoggerFactoryAdapter, Common.Logging">
        <arg key="showLogName" value="true" />
        <arg key="showDataTime" value="true" />
        <arg key="level" value="DEBUG" />
        <arg key="dateTimeFormat" value="HH:mm:ss:fff" />
      </factoryAdapter>
    </logging>
  </common>
  <startup userLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <log4net>

    <appender name="BcmsParallelWorkflowLogs" type="log4net.Appender.RollingFileAppender">
      <file value="C:\\CP\CPParallelWorkflow.log" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="20" />
      <maximumFileSize value="10MB" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline" />
      </layout>
    </appender>

    <appender name="BcmsSQLNativeLog" type="log4net.Appender.RollingFileAppender">
      <file value="C:\\CP\CPServiceSQLNativeLog.log" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="20" />
      <maximumFileSize value="10MB" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline" />
      </layout>
    </appender>

    <appender name="BcmsEngineLog" type="log4net.Appender.RollingFileAppender">
      <file value="C:\\CP\CPParallelEngineLog.log" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="20" />
      <maximumFileSize value="10MB" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline" />
      </layout>
    </appender>
    <root>
      <level value="ALL"/>
      <appender-ref ref="BcmsParallelWorkflowLogs" />
    </root>
    <logger additivity="false" name="BcmsSQLNativeLog">
      <level value="ALL"/>
      <appender-ref ref="BcmsSQLNativeLog"/>
    </logger>
    <logger additivity="false" name="BcmsEngineLog">
      <level value="ALL"/>
      <appender-ref ref="BcmsEngineLog" />
    </logger>
  </log4net>
  <bcms>
    <!-- Configure Thread Pool -->
    <add key="bcms.scheduler.instanceName" value="BcmsScheduler" />
    <add key="bcms.scheduler.instanceId" value="bcms_instance"/>
    <add key="bcms.threadPool.type" value="Bcms.Simpl.SimpleThreadPool, Bcms" />
    <add key="bcms.threadPool.threadCount" value="100" />
    <add key="bcms.threadPool.threadPriority" value="Normal" />
    <add key="bcms.jobStore.misfireThreshold" value="600000" />
    <add key="bcms.jobStore.type" value="Bcms.Simpl.RAMJobStore, Bcms" />
  </bcms>


  <system.web>
    <compilation debug="false" />
  </system.web>
  <!-- When deploying the service library project, the content of the config file must be added to the host's 
  app.config file. System.Configuration does not support config files for libraries. -->
  <system.serviceModel>
    <services>
      <service behaviorConfiguration="Bcms.Service.Engine.BcmsSchedulerBehavior"
        name="Bcms.Service.Engine.BcmsScheduler">
        <endpoint address="" binding="netTcpBinding" bindingConfiguration=""
          contract="Bcms.Service.Engine.IBcmsScheduler">
          <identity>
            <dns value="localhost" />
          </identity>
        </endpoint>
        <endpoint address="mex" binding="mexTcpBinding" bindingConfiguration=""
          contract="IMetadataExchange" />
        <host>
          <baseAddresses>
            <add baseAddress="net.tcp://localhost:8731/Bcms.Service.Engine/BcmsScheduler/" />
          </baseAddresses>
        </host>
      </service>

      <service name="Bcms.Server.Service.BcmsServiceOperations"
              behaviorConfiguration="Bcms.Server.Service.BcmsServiceOperationsBehavior">
        <host>
          <baseAddresses>
            <add baseAddress="http://localhost:8000/BcmsOperationalService/service"/>
          </baseAddresses>
        </host>
        <!-- this endpoint is exposed at the base address provided by host: http://localhost:8000/ServiceModelSamples/service  -->
        <endpoint address=""
                  binding="wsHttpBinding"
                  contract="Bcms.Server.Service.IBcmsServiceOperation" />
        <!-- the mex endpoint is exposed at http://localhost:8000/ServiceModelSamples/service/mex -->
        <endpoint address="mex"
                  binding="mexHttpBinding"
                  contract="IMetadataExchange" />
      </service>


    </services>
    <behaviors>
      <serviceBehaviors>
        <behavior name="Bcms.Service.Engine.BcmsSchedulerBehavior">
          <!-- To avoid disclosing metadata information, 
          set the value below to false and remove the metadata endpoint above before deployment -->
          <serviceMetadata httpGetEnabled="false"/>
          <!-- To receive exception details in faults for debugging purposes, 
          set the value below to true.  Set to false before deployment 
          to avoid disclosing exception information -->
          <serviceDebug includeExceptionDetailInFaults="False" />
        </behavior>

        <behavior name="Bcms.Server.Service.BcmsServiceOperationsBehavior">
          <serviceMetadata httpGetEnabled="true"/>
          <serviceDebug includeExceptionDetailInFaults="False"/>
        </behavior>

      </serviceBehaviors>
    </behaviors>
  </system.serviceModel>

  <system.data>
    <DbProviderFactories>
      <!-- Remove in case this is already defined in machine.config -->
      <remove invariant="Oracle.DataAccess.Client" />
      <add name="Oracle Data Provider for .NET"
           invariant="Oracle.DataAccess.Client"
           description="Oracle Data Provider for .NET"
           type="Oracle.DataAccess.Client.OracleClientFactory, Oracle.DataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342"/>
    </DbProviderFactories>
  </system.data>

</configuration>