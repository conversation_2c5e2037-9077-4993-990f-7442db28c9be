using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Text;
using System.Threading;
using Bcms.ExceptionHandler;
using Bcms.Replication.Shared;
using Jscape.Ssh;
using Microsoft.Practices.EnterpriseLibrary.Data;
//using Oracle.DataAccess.Client;
using PGlobalMirror;
using PSSHConn;
using Devart.Data.Oracle;
using Rebex.TerminalEmulation;
using System.Text.RegularExpressions;

namespace Bcms.Replication.Base
{
    public static class CustomDatabaseFactory
    {

#if DEVART
        private static readonly DbProviderFactory DbProviderFactory =
          DbProviderFactories.GetFactory("Devart.Data.Oracle");

#else

        private static readonly DbProviderFactory DbProviderFactory =
            DbProviderFactories.GetFactory("Oracle.DataAccess.Client");
#endif

        public static Database CreateDatabase(string connectionString)
        {
            return new GenericDatabase(connectionString, DbProviderFactory);
        }
    }

    public sealed class PrimaryHost : Host
    {
        private readonly object _lockObject = new object();

        private int _maxattempt = 1;
        private VirtualTerminal terminal;

        //public PrimaryHost(string hostName, string userName, string password, int port)
        //{
        //    if (string.IsNullOrEmpty(hostName))
        //    {
        //        throw new ArgumentException("HostName cannot be null or empty");
        //    }
        //    if (string.IsNullOrEmpty(userName))
        //    {
        //        throw new ArgumentException("UserName cannot be null or empty");
        //    }
        //    if (string.IsNullOrEmpty(password))
        //    {
        //        throw new ArgumentException("Password cannot be null or empty");
        //    }
        //    HostName = hostName;
        //    UserName = userName;
        //    Password = password;
        //    PrivateKey = string.Empty;
        //    PrivateKeyPasspharase = string.Empty;
        //    Port = port;
        //}

        //public PrimaryHost(string hostName, string userName, int port, string privatekey, string privateKeyPasspharase)
        //{
        //    HostName = hostName;
        //    UserName = userName;
        //    Password = string.Empty;
        //    PrivateKey = privatekey;
        //    PrivateKeyPasspharase = privateKeyPasspharase;
        //    Port = port;
        //}


        public PrimaryHost(string hostName, string userName, string password, int port, List<PGlobalMirror.SubAuthentication> SubAuthlist)
        {
            if (string.IsNullOrEmpty(hostName))
            {
                throw new ArgumentException("HostName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(userName))
            {
                throw new ArgumentException("UserName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(password))
            {
                throw new ArgumentException("Password cannot be null or empty");
            }
            HostName = hostName;
            UserName = userName;
            Password = password;
            PrivateKey = string.Empty;
            PrivateKeyPasspharase = string.Empty;
            Port = port;
            SubAuthenticationlist = SubAuthlist;

            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public PrimaryHost(string hostName, string userName, int port, string privatekey, string privateKeyPasspharase, List<PGlobalMirror.SubAuthentication> SubAuthlist)
        {
            HostName = hostName;
            UserName = userName;
            Password = string.Empty;
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
            Port = port;

            SubAuthenticationlist = SubAuthlist;
            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public SshSession PrimarySession { get; set; }
        public Rebex.Net.Ssh PRSessionRebex { get; set; }

        internal override bool Connect(bool isConnectDatabase)
        {
            try
            {
                lock (_lockObject)
                {
                    //if (PrimarySession != null)
                    //{
                    //    if (PrimarySession.Ssh.Connected)
                    //    {
                    //        PrimarySession.Disconnect();
                    //        PrimarySession = null;
                    //    }
                    //}

                    if (string.IsNullOrEmpty(Password) && !string.IsNullOrEmpty(PrivateKey))
                    {
                        LogHelper.LogMessage("PRIMARY SERVER (" + HostName +
                                             ") is will connect via SSH KEY Based Authentication");
                        var parameters = new SshParameters(HostName, Port, UserName, Password);
                        parameters.SetPrivateKey(new FileInfo(PrivateKey), PrivateKeyPasspharase);
                        PrimarySession = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig());
                    }
                    else
                    {
                        LogHelper.LogMessage("PRIMARY SERVER (" + HostName +
                                             ") is will connect via PASSWORD Based Authentication");
                        var parameters = new SshParameters(HostName, Port, UserName, Password);
                        PrimarySession = new SshSession(parameters, Bcms.Replication.Shared.SshUtil.GetSshConfig());
                    }

                    PrimarySession.LicenseKey = Constants.QueryConstants.LicenseKey;
                    PrimarySession.SetShellPrompt("\\$|#|>", true);
                    PrimarySession.Connect(30000);
                    LogHelper.LogHeaderAndMessage("PRIMARY SERVER STATUS :",
                        string.Format(" PR Server {0} is connected", HostName));

                    if (isConnectDatabase)
                    {
                        //LogHelper.LogMessage("PRIMARY SERVER (" + HostName + "), DATABASE (" + DatabaseName +
                        //                     ") is connecting...");

                        //if (ConnectToDatabase(PrimarySession, DatabaseName))
                        //{
                        //    return true;
                        //}


                        if (!string.IsNullOrEmpty(OracleHomePath))
                        {
                            LogHelper.LogMessage("PRIMARY SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") OracleHomePath:" + OracleHomePath + " is connecting...");

                            return ConnectToDatabase(PrimarySession, DatabaseName, OracleHomePath);
                        }
                        else
                        {
                            LogHelper.LogMessage("PRIMARY SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") is connecting...");

                            return ConnectToDatabase(PrimarySession, DatabaseName);
                        }
                        throw new BcmsException(BcmsExceptionType.DatabaseConnectionFailed, "Primary Database (" + DatabaseName + ") connection is failed");
                    }
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER CONNECTION SSH ERROR", sshException);

                if (_maxattempt == 1)
                {
                    LogHelper.LogMessage("RETRY PRIMARY SERVER CONNECTION  : " + HostName);

                    _maxattempt++;

                    Connect(isConnectDatabase);
                }
                else
                {
                    throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                        "SshException occurred while connecting Primary host - " + HostName, sshException);
                }
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER CONNECTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while connecting Primary host - " + HostName, exc);
            }

            return true;
        }

        internal override bool ConnectRebex(bool isConnectDatabase)
        {
            try
            {

                PRSessionRebex = new Rebex.Net.Ssh();
                terminal = new VirtualTerminal(80, 500);
                PRSessionRebex.Connect(HostName, Port);
                PRSessionRebex.Login(UserName, Password);
                terminal.Bind(PRSessionRebex);
                LogHelper.LogHeaderAndMessage("PR SERVER STATUS  :",
                       string.Format(" PR Server {0} is connected Via Rebex", HostName));
            }

            catch (Rebex.Net.SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PR SERVER CONNECTION Via Rebex ERROR ", sshException);

                if (_maxattempt == 1)
                {
                    LogHelper.LogMessage("RETRY PR SERVER CONNECTION Via Rebex : " + HostName);

                    _maxattempt++;

                    ConnectRebex(isConnectDatabase);
                }
                else
                {
                    throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                        "Rebex SshException occurred while connecting PR host - " + HostName, sshException);
                }
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PR SERVER CONNECTION ERROR REBEX", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while connecting PR host Via Rebex- " + HostName, exc);
            }
            return true;
        }

        internal override void DisconnectRebex()
        {
            if (PRSessionRebex != null)
            {
                LogHelper.LogMessage("PRIMARY SERVER CONNECTION DISCONNECT VIA REBEX");
                PRSessionRebex.Disconnect();
                //  Thread.Sleep(10000);
            }
        }

        internal override void Disconnect()
        {
            if (PrimarySession != null)
            {
                LogHelper.LogMessage("PRIMARY SERVER CONNECTION DISCONNECT");
                PrimarySession.Disconnect();
                //  Thread.Sleep(10000);
            }
        }

        internal override bool VerifyDatabaseMode()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGVerifyDatabaseMode();
                //}
                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE COMMAND : ", Constants.QueryConstants.DatabaseModeAndRole);

                string primaryRole = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DatabaseModeAndRole, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE OUTPUT : ", primaryRole);

                return primaryRole.Contains("primary") && primaryRole.Contains("read write");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while verify primary database mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while verify primary database mode", exc);
            }
        }

        internal override bool VerifyUserStatus()
        {
            try
            {
                //if (IsWindows)
                //{
                //    string usercounts = WinDGVerifyUserStatus();

                //    LogHelper.LogHeaderAndMessage("PRIMARY DATABASE USER COUNT : ", usercounts);

                //    if (Convert.ToInt32(usercounts) > 0)
                //    {
                //        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound,
                //            usercounts + "Active user (" + usercounts + ") found logged in status in Primary Server (" +
                //            HostName + ")");
                //    }
                //    return true;
                //}

                LogHelper.LogHeaderAndMessage("PRIMARY USER STATUS COMMAND : ", Constants.QueryConstants.ActiveUserCount);

                string usercount =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.ActiveUserCount,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE USER COUNT :", usercount);

                if (usercount.Contains("count(*)") && usercount.Contains("-") && usercount.Contains("sql>"))
                {
                    usercount = usercount.Replace("\r\n", "");
                    usercount = usercount.Replace("\t", "");
                    usercount = usercount.Replace("count(*)", "");
                    usercount = usercount.Replace("-", "");
                    usercount = usercount.Replace("sql>", "").Trim();

                    if (Convert.ToInt32(usercount) > 0)
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound,
                            usercount + "Active user (" + usercount + ") found logged in status in Primary Server (" +
                            HostName + ")");
                    }
                    return true;
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while get Primary Active user count", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get Primary Active user count", exc);
            }
            return false;
        }

        internal override string GetMaxSequenceNumber()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGGetMaxSequenceNumber();
                //}

                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE MAX SEQUENCE COMMAND : ",
                    Constants.QueryConstants.MaxSequenceNo);

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.MaxSequenceNo,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MAX SEQUENCE OUTPUT :", output);

                return output;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Primary get Max Sequence No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get Primary Max Sequence No", exc);
            }
        }

        internal override bool Exit()
        {
            try
            {
                ExitPromt(PrimarySession);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal override bool MoveFiles(string sourcePath, string fileName, string targetPath)
        {
            string moveResult = MoveFiles(PrimarySession, sourcePath, fileName, targetPath);

            return moveResult == null || !moveResult.Contains("cannot move");
        }

        internal override void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt)
        {
            MoveFiles(PrimarySession, sourcePath, targetPath, true);
        }

        internal bool ConnectServer()
        {
            return IsWindows ? SSHHelper.Ping(HostName) : SSHHelper.Connect(HostName, UserName, Password);
        }

        internal bool IsDatabaseRunning()
        {
            if (IsWindows)
            {
                return IsWinDatabaseRunning();
            }
            return OracleDB.DBRunning(new SSHInfo(HostName, UserName, Password, Port,SubAuthenticationlist), SudoString, DatabaseName);
        }

        internal bool IsListenerRunning()
        {
            if (IsWindows)
            {
                return true;
                //return IsWinDatabaseListnerRunning();
            }
            return OracleDB.IsListnerRunning(new SSHInfo(HostName, UserName, Password, Port,SubAuthenticationlist), SudoString);
        }

        internal bool SwitchDatabaseToStandbyMode()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGSwitchDatabaseToStandbyMode();
                //}

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE COMMAND : ",
                    Constants.QueryConstants.SwitchToStandbyMode);

                string switchresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.SwitchToStandbyMode,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE TO STANDBY MODE OUTPUT:", switchresult);

                return switchresult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE SSH EXCEPTION ERROR",
                    sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Switch database Primary to standby mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while Switch database Primary to standby mode", exc);
            }
        }

        internal bool ShutdownPrimaryDatabase()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGShutdownPrimaryDatabase();
                //}
                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE COMMAND : ",
                    Constants.QueryConstants.ShutdownPrimary);

                string shutdownresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.ShutdownPrimary,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE OUTPUT :", shutdownresult);

                return shutdownresult.Contains("instance shut down") || shutdownresult.Contains("instance terminated");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while shutdown Primary Database", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while shutdown Primary Database", exc);
            }
        }

        internal bool MountStandby()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGMountStandby();
                //}

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY COMMAND : ", Constants.QueryConstants.MountStandby);

                string standbyresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.MountStandby,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY OUTPUT :", standbyresult);

                return standbyresult.Contains("database mounted");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while mount standby", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occurred while  mount standby" +
                                                                           "" +
                                                                           "", exc);
            }
        }

        internal bool StartStandbyRecovery()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGStartStandbyRecovery();
                //}

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY COMMAND : ",
                    Constants.QueryConstants.StandbyRecovery);

                string startStandbyresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.StandbyRecovery,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY :", startStandbyresult);

                return startStandbyresult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while start standby recovery", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while while start standby recovery", exc);
            }
        }

        internal bool VerifyPrimaryProcess()
        {
            try
            {
                //if (IsWindows)
                //{
                //    bool processCounts = WinDGVerifyPrimaryProcess();

                //    if (!processCounts)
                //    {
                //        KillAllWindowsPrimaryProcess();
                //    }
                //    return true;
                //}

                bool processCount = IsPrimaryHavingProcess(PrimarySession);

                if (processCount)
                {
                    PrimarySession.Send("exit");
                }
                else
                {
                    IsKillAllPrimaryProcess(PrimarySession);
                }

                return true;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.AuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while verify primary process", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while while verify primary process", exc);
            }
        }

        internal void GetDataFileCount()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE COUNT");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileCount,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileCount);

                LogHelper.LogHeaderAndMessage("DATA FILE COUNT OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DATA FILE COUNT ERROR", exc.Message);
            }
        }

        internal void CheckDataFileError()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE ERROR");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileError,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileError);

                LogHelper.LogHeaderAndMessage("DATA FILE ERROR OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("DATA FILE ERROR ERROR", exc.Message);
            }
        }

        internal void CheckRecoveryModeCount()
        {
            try
            {
                LogHelper.Header("CHECK RECOVERY MODE COUNT");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInRecoveryMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInRecoveryMode);

                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT ERROR", exc.Message);
            }
        }

        internal void CheckBackupMode()
        {
            try
            {
                LogHelper.Header("CHECK BACKUP MODE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInBackupMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInBackupMode);

                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE ERROR", exc.Message);
            }
        }

        internal void CheckDataFileStatus()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE STATUS");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileStatus,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileStatus);

                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS ERROR", exc.Message);
            }
        }

        internal void CheckDataFileMode()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE MODE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInReadMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInReadMode);

                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE ERROR", exc.Message);
            }
        }

        internal void IsCorruptedDataFile()
        {
            try
            {
                LogHelper.Header("IS CORRUPTED DATA FILE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInCorrupted,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInCorrupted);

                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE ERROR", exc.Message);
            }
        }

        internal void CheckPointChange()
        {
            try
            {
                LogHelper.Header("CHECK POINT CHANGE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckPointChange,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CheckPointChange);

                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE ERROR", exc.Message);
            }
        }

        internal void CheckCurrentSCN()
        {
            try
            {
                LogHelper.Header("CHECK CURRENT SCN");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CurrentSCNNumber,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CurrentSCNNumber);

                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN ERROR", exc.Message);
            }
        }

        internal void CheckNologgingOperation()
        {
            try
            {
                LogHelper.Header("CHECK NOLOGGING OPERATION");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.NoLoggingOperation,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.NoLoggingOperation);

                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION ERROR", exc.Message);
            }
        }

        internal void CheckPrDataGuardStatus()
        {
            try
            {
                LogHelper.Header("CHECK PR DATAGUARD STATUS");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.PRDataGuardStatus);

                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS ERROR", exc.Message);
            }
        }

        internal void CheckForceLogging()
        {
            try
            {
                LogHelper.Header("CHECK FORCE LOGGING");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckForceLogging,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.CheckForceLogging);

                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING ERROR", exc.Message);
            }
        }

        //internal void VerifyPrimaryDatabaseLogCount()
        //{
        //    int dbcountResult = VerifyPrimaryLogCount(PrimarySession);

        //    for (int i = 0; i <= 2 + dbcountResult; i++)
        //    {
        //        RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.AlterLogfiles,
        //                               Constants.QueryConstants.GreaterThan);
        //    }
        //}

        internal string VerifyRecoveryMode()
        {
            return
                RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataGuardInRecovery,
                    Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string VerifyArachiveLogGap()
        {
            return
                RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.VerifyArchiveGap,
                    Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string CheckPRDataGuard()
        {

            string result;

            string prdgResult = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus,
                Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.PRDataGuardStatus);

            LogHelper.LogHeaderAndMessage("COMMAND Output: ", prdgResult);

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive";
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        // added Kiran 22042015


        internal string DynamicPRDBName()
        {

            var discoverDBcmd = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.discoveryqueryondr, Constants.QueryConstants.GreaterThan);

            if (discoverDBcmd.ToLower().Contains("db_unique_name"))
            {
                string output = discoverDBcmd;
                string output1 = output.Replace("\r\n", ",");
                string[] result = output1.Split(',');
                string dbname = result[3];


                return dbname;

            }
            return "N/A";
        }

        internal string CheckPRDataGuardDynamic(string DBName)
        {
            string result = "N/A";

            if (DBName != "N/A")
            {
                string DGStatuscmd = Constants.QueryConstants.dataguardstatus + "('" + DBName + "','" + DBName.ToUpper() + "') and TARGET='STANDBY';";

                var prdgResult = RunSshCommandsWithWait(PrimarySession, DGStatuscmd, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("COMMAND : ", DGStatuscmd);

                LogHelper.LogHeaderAndMessage("COMMAND Outpu: ", prdgResult);

                if (prdgResult.Contains("valid"))
                {
                    result = "Running";
                }
                else if (prdgResult.Contains("deferred"))
                {
                    result = "Deferred";
                }
                else if (prdgResult.Contains("inactive"))
                {
                    result = "Inactive";
                }
                else if (prdgResult.Contains("error"))
                {
                    result = "Error";
                }
                else
                {
                    result = "N/A";
                }
            }
            return result;
        }

        internal string StartFastCopy(string hostname, string username, string password, string localDir, string remoteDir, string processcode)
        {
            var sb = new StringBuilder();

            var commands = new List<string>
            {
                Constants.QueryConstants.ChangeFastCopyDirectory,
                string.Format("{0} \"{1}_{2}_{3}_{4}_{5}\"",
                    Constants.QueryConstants.StartFastCopyReplication,
                    processcode,
                    hostname,
                    username,
                    localDir,
                    remoteDir),
                password
            };

            foreach (string command in commands)
            {
                sb.Append(RunSshCommands(PrimarySession, command));
            }
            string connectResult = sb.ToString().ToLower();

            return connectResult;
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover)
        {
            try
            {
                if (IsWindows)
                {
                    //Windows FastCopy Log Replication

                    string repicationstatus = ReplicateArchiveLogFastcopy(groupId);
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", repicationstatus);
                    return repicationstatus;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();
                var commands = new List<string>
                {
                    Constants.QueryConstants.ChangeFastCopyDirectory,
                    iswithover
                        ? string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-SO",
                            groupId, replicationId)
                        : string.Format("{0} {1} {2}", Constants.QueryConstants.StartFastCopyReplication, groupId, replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION  :", exc.Message);
                return exc.Message;
            }
        }

        internal string ReplicateArchiveLogFastcopy(int groupId)
        {
            string result = RunSshCommandsWithWait(PrimarySession, "cd C:\\FastCopy1", Constants.QueryConstants.GreaterThan).ToLower();
            if (result.Contains("c:\\fastcopy"))
            {
                return RunSshCommandsWithWait(PrimarySession, "FastCopy.exe " + groupId, Constants.QueryConstants.GreaterThan).ToLower();
            }
            return null;
        }

        internal string StartFastCopy(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for oracle linux datasync
                {
                    
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DBU", infraObjectId, replicationId, logSequence, logSequenceName) 
               
                    };
                    foreach (string command in commands)
                    {
                        LogHelper.LogMessage("Uploading command: " + command);
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopyReplicateRedoFiles(int workflowActionId, int replicationId, string fastCopyPath)
        {
            try
            {
                if (IsWindows)
                {
                    return string.Empty;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}", fastCopyPath),
                    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-WFU", workflowActionId, replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE REDO FILES RESULT: ", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);
                return string.Empty;
            }
        }

        internal string StartFastCopyReplicateArchiveFolder(string fastCopyPath, int workflowActionId, int replicationId, string sequence, string logfile)
        {
            try
            {
                if (IsWindows)
                {
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}", fastCopyPath),
                    string.Format("{0} -WDBU {1} {2} {3} {4}", Constants.QueryConstants.StartFastCopyReplication, workflowActionId, replicationId, sequence, logfile)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate ARCHIVE files ", exc.Message);
                return string.Empty;
            }
        }

        internal string CheckPRDataGuardWindows()
        {
            string result;

            string prdgResult = WinDGGetPrimaryDataGuardStatus().ToLower();

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive";
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        private bool IsWinDatabaseRunning()
        {
            try
            {
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB RUNNING EXCEPTION : ", exc);

                return false;
            }
        }

        private bool IsWinDatabaseListnerRunning()
        {
            try
            {
                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" +
                                   DatabaseName;

                using (var db = new OracleDatabase(constring))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB LISTNER RUNNING EXCEPTION : ", exc);

                return false;
            }
        }

        private string WinDGGetPrimaryDataGuardStatus()
        {
            try
            {
                string output = string.Empty;

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsPRDataGuardStatus))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();
                        }
                    }
                }

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATAGUARD STATUS : ", exc);

                return "INVALID";
            }
        }

        private bool WinDGVerifyDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsDatabaseModeAndRole);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDatabaseModeAndRole)
                    )
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string mode = myDatabaseReader[1].ToString();
                            string role = myDatabaseReader[2].ToString();

                            if (mode == "READ WRITE" && role == "PRIMARY")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION : ", exc);
                return false;
            }

            return false;
        }

        private string WinDGVerifyUserStatus()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsActiveUserCount);
                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsActiveUserCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();
                        }
                    }
                }
                return "0";
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY USER STATUS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGVerifyPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsProcessCount);
                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsProcessCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string count = myDatabaseReader[1].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY PROCESS COUNT :", count);

                            return Convert.ToInt32(count) == 0;
                        }
                    }
                }
                return false;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY PROCESS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool KillAllWindowsPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsKillProcess);

                LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS ", string.Empty);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsKillProcess))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string output = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS OUTPUT:", output);

                            if (output == "")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
                LogHelper.LogHeaderAndMessage("KILLED ALL PRIMARY PROCESS ", string.Empty);
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("KILL PRIMARY PROCESS EXCEPTION : ", exc);

                throw;
            }
        }

        private string WinDGGetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMaxSequenceNo);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsMaxSequenceNo))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string squenceno = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY MAX SEQUNCE NO OUTPUT : ", squenceno);

                            return squenceno;
                        }
                    }
                }

                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY MAX SEQUNCE NO EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGSwitchDatabaseToStandbyMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsSwitchToStandbyMode);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsSwitchToStandbyMode);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY DATABASE TO STANDBY MODE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGShutdownPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.ShutdownPrimary);
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.Shutdown(Devart.Data.Oracle.OracleShutdownMode.Immediate, true);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGMountStandby()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMountStandby);

                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" +
                                   DatabaseName;

                using (var db = new OracleDatabase(constring))
                {
                    db.Startup(Devart.Data.Oracle.OracleStartupMode.Default);

                    // db.Startup(Devart.Data.Oracle.OracleShutdownMode.reOracleDBStartupMode.NoRestriction, null, false);
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsMountStandby);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGStartStandbyRecovery()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsStandbyRecovery);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsStandbyRecovery);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION : ", exc);

                throw;
            }
        }

        internal List<string> GetPrimaryDRMaxLogSequnce(string sequnce)
        {
            try
            {
                var prsequnce = new List<string>();

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (
                    DbCommand dbCommand =
                        db.GetSqlStringCommand(Constants.QueryConstants.WinDRLogLatestSeqInPrimary + sequnce))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY DR LATEST LOG SEQUENCE EXCEPTION : ", exc);

                throw;
            }
        }

        internal List<string> GetPrimaryMaxLogSequnce()
        {
            try
            {
                var prsequnce = new List<string>();

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinPRLatestLogSeq))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY LATEST LOG SEQUENCE EXCEPTION : ", exc);

                throw;
            }
        }

        public string AmazonReplicationDataSync(string option, int infraObjectId, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Amazon Replication DataSync Started (client): ");
                LogHelper.LogMessage("Amazon Replication DataSync infraObjectId :" + infraObjectId);

                String shellPrompt = "\\$|>|>";


                string output = string.Empty;


                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("nohup java -jar AmazonS3_RHL.jar {1} {0} &", infraObjectId, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Amazon Replication : Command (" + command + ") is executed");

                    RunSshCommandsWithNoWait(PrimarySession, command);
                }


                LogHelper.LogMessage("Amazon Replication DataSync Output: " + output);


                LogHelper.LogMessage("Amazon Replication DataSync Ended (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Amazon DataSync files ", exc.Message);

                return string.Empty;
            }
        }

        public string WFAmazonReplicationDataSync(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Amazon Replication DataSync Started (client): ");
                LogHelper.LogMessage("Amazon Replication DataSync workflowactionid :" + workflowactionid);

                String shellPrompt = "\\$|>|>";


                string output = string.Empty;


                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("java -jar AmazonS3_RHL.jar {1} {0}", workflowactionid, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Amazon Replication : Command (" + command + ") is executed");

                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }


                LogHelper.LogMessage("Amazon Replication DataSync Output: " + output);

                LogHelper.LogMessage("Amazon Replication DataSync Ended (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Amazon DataSync files ", exc.Message);

                return string.Empty;
            }
        }

        public string WFSybaseReplicationDataSyncBCPTable(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Sybase Replication DataSync Started (client): ");
                LogHelper.LogMessage("Sybase Replication DataSync workflowactionid :" + workflowactionid);

                String shellPrompt = "\\$|>|>";


                string output = string.Empty;

                string datasync = Constants.QueryConstants.StartFastCopyReplication;
                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,                        
                         string.Format("{0} {1} {2}", datasync,option, workflowactionid)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Sybase Replication : Command (" + command + ") is executed");

                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }


                LogHelper.LogMessage("Sybase Replication DataSync Output: " + output);

                LogHelper.LogMessage("Sybase Replication DataSync Ended (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Sybase DataSync files ", exc.Message);

                return string.Empty;
            }
        }

        public string WFSybaseReplicationDataSync(string option, int workflowActionId, int replicationId, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Sybase Replication DataSync Started (client): ");
                LogHelper.LogMessage("Sybase Replication DataSync workflowActionId :" + workflowActionId);

                String shellPrompt = "\\$|>|>";


                string output = string.Empty;

                string datasync = Constants.QueryConstants.StartFastCopyReplication;

                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("{0} {1} {2} {3}", datasync,option, workflowActionId,replicationId)                        
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Sybase Replication : Command (" + command + ") is executed");

                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }


                LogHelper.LogMessage("Sybase Replication DataSync Output: " + output);

                LogHelper.LogMessage("Sybase Replication DataSync Ended (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Sybase DataSync files ", exc.Message);

                return string.Empty;
            }
        }

        public string WFBFLReplicationDataSync(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage(" Replication DataSync Started (client): ");
                LogHelper.LogMessage(" Replication DataSync workflowactionid :" + workflowactionid);

                String shellPrompt = "\\$|>|>";


                string output = string.Empty;


                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("DataSync.exe {1} {0}", workflowactionid, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage(" Replication : Command (" + command + ") is executed");

                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }


                LogHelper.LogMessage(" Replication DataSync Output: " + output);

                LogHelper.LogMessage(" Replication DataSync Ended (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate  DataSync files ", exc.Message);

                return string.Empty;
            }
        }

        public string WFReplicationDataSync(string option,string DatasyncPath, int workflowactionId)
        {
            try
            {


                LogHelper.LogMessage(" Replication DataSync Started (client): ");
                LogHelper.LogMessage(" Replication DataSync workflowactionid :" + workflowactionId);


                String shellPrompt = "\\$|>|>";
                string output = string.Empty;
                //string javaLocation = string.IsNullOrEmpty(jrePath) ? "java" : jrePath.TrimEnd('/') + "/java";

                // commented as per discussed with kiran sir 14.09.2015
                //var commands = new List<string>
                //{
                //    "cd " + datasyncPath,
                //    string.Format(javaLocation+" -jar DataSync.jar {2} {0} {1}", infraObjectId, fastcopyJobId, option)
                //};

                var commands = new List<string>
                {
                   
                     "rm -f nohup.out",
                                      "cd " + DatasyncPath,
                                       string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar {0} {1} &", option,workflowactionId)
                };

                foreach (string command in commands)
                {
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage("Application Replication DataSync Ended (client): ");
                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while WF Replication DataSync  ", exc.Message);

                return string.Empty;
            }
        }

    }

    public sealed class PrimaryPSSHHost : HostPSSH
    {
        private readonly object _lockObject = new object();

        private int _maxattempt = 1;
        private VirtualTerminal terminal;

        public PrimaryPSSHHost(string hostName, string userName, string password, int port, List<PSSHConn.SubAuthentication> SubAuthlist)
        {
            if (string.IsNullOrEmpty(hostName))
            {
                throw new ArgumentException("HostName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(userName))
            {
                throw new ArgumentException("UserName cannot be null or empty");
            }
            if (string.IsNullOrEmpty(password))
            {
                throw new ArgumentException("Password cannot be null or empty");
            }
            HostName = hostName;
            UserName = userName;
            Password = password;
            PrivateKey = string.Empty;
            PrivateKeyPasspharase = string.Empty;
            Port = port;
            SubAuthenticationlist = SubAuthlist;

            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public PrimaryPSSHHost(string hostName, string userName, int port, string privatekey, string privateKeyPasspharase, List<PSSHConn.SubAuthentication> SubAuthlist)
        {
            HostName = hostName;
            UserName = userName;
            Password = string.Empty;
            PrivateKey = privatekey;
            PrivateKeyPasspharase = privateKeyPasspharase;
            Port = port;

            SubAuthenticationlist = SubAuthlist;
            if (SubAuthenticationlist.Count > 0)
            {
                EnableSudoAccess = true;
            }
        }

        public dynamic PrimarySession { get; set; }

        internal override bool Connect(bool isConnectDatabase)
        {
            bool isConnected = false;
            string strOutdb = string.Empty;
            SSHServerInfo objSSHServer = null;
            string shellPrompt = "\\$|#|>";

            try
            {
                lock (_lockObject)
                {
                    objSSHServer = new SSHServerInfo(HostName, UserName, Password, !string.IsNullOrEmpty(PrivateKey), PrivateKey, PrivateKeyPasspharase, Port, SubAuthenticationlist);
                    PrimarySession = PSSH.CreateSSHSession(objSSHServer);

                    LogHelper.LogHeaderAndMessage("PRIMARY SERVER STATUS: ", string.Format(" PR Server {0} is connected", HostName));

                    if (isConnectDatabase)
                    {
                        if (!string.IsNullOrEmpty(OracleHomePath))
                        {
                            LogHelper.LogMessage("PRIMARY SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") OracleHomePath: " + OracleHomePath + " is connecting...");
                            if (PrODGInfo != null)

                                isConnected = _ConnectToDatabase(PrimarySession, PrODGInfo, DatabaseName, OracleHomePath);
                            else
                                isConnected = _ConnectToDatabase(PrimarySession, DatabaseName, OracleHomePath);

                            //return ConnectToDatabase(PrimarySession, DatabaseName, OracleHomePath);
                            return isConnected;
                        }
                        else
                        {
                            LogHelper.LogMessage("PRIMARY SERVER (" + HostName + "), DATABASE (" + DatabaseName + ") is connecting...");
                            if (PrODGInfo != null)
                                isConnected = _ConnectToDatabase(PrimarySession, PrODGInfo, DatabaseName);
                            else
                                isConnected = _ConnectToDatabase(PrimarySession, DatabaseName);




                            // isConnected= ConnectToDatabase(PrimarySession, DatabaseName);
                            //return ConnectToDatabase(PrimarySession, DatabaseName);
                            return isConnected;
                        }
                        throw new BcmsException(BcmsExceptionType.DatabaseConnectionFailed, "Primary Database (" + DatabaseName + ") connection is failed");
                    }
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER CONNECTION SSH ERROR", sshException);

                if (_maxattempt == 1)
                {
                    LogHelper.LogMessage("RETRY PRIMARY SERVER CONNECTION: " + HostName);
                    _maxattempt++;
                    Connect(isConnectDatabase);
                }
                else
                {
                    throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                        "SshException occurred while connecting Primary host - " + HostName, sshException);
                }
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY SERVER CONNECTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while connecting Primary host - " + HostName, exc);
            }
            return true;
        }

        internal override void Disconnect()
        {
            if (PrimarySession != null)
            {
                LogHelper.LogMessage("PRIMARY SERVER CONNECTION DISCONNECT");
                try
                {
                    PSSH.DisconnectAndRemoveSSHSession(PrimarySession);
                }
                catch (Exception ex)
                {
                    LogHelper.LogMessage($"Error disconnecting Primary SSH session for {HostName}: {ex.Message}");
                }
            }
            else
            {
                LogHelper.LogMessage($"Primary SSH session was null for {HostName}, no cleanup needed");
            }
        }

        internal override bool VerifyDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE COMMAND: ", Constants.QueryConstants.DatabaseModeAndRole);

                string primaryRole = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DatabaseModeAndRole, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MODE OUTPUT: ", primaryRole);
                return primaryRole.Contains("primary") && primaryRole.Contains("read write");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while verify primary database mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while verify primary database mode", exc);
            }
        }

        internal override bool VerifyUserStatus()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("PRIMARY USER STATUS COMMAND : ", Constants.QueryConstants.ActiveUserCount);

                string usercount =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.ActiveUserCount,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE USER COUNT: ", usercount);

                if (usercount.Contains("count(*)") && usercount.Contains("-") && usercount.Contains("sql>"))
                {
                    usercount = usercount.Replace("select count(*) from  v$session where status='active' and username not in('sys','system');", "");
                    usercount = usercount.Replace("\r\n", "");
                    usercount = usercount.Replace("\t", "");
                    usercount = usercount.Replace("count(*)", "");
                    usercount = usercount.Replace("-", "");
                    usercount = usercount.Replace("sql>", "").Trim();

                    if (Convert.ToInt32(usercount) > 0)
                    {
                        throw new BcmsException(BcmsExceptionType.WFORAActiveUserFound,
                            usercount + "Active user (" + usercount + ") found logged in status in Primary Server (" +
                            HostName + ")");
                    }
                    return true;
                }
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS AUTHENTICATION ERROR", sshAuthentication);
                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS SSH EXCEPTION ERROR", sshException);
                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while get Primary Active user count", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY USER STATUS EXCEPTION ERROR", exc);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get Primary Active user count", exc);
            }
            return false;
        }

        internal override string GetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE MAX SEQUENCE COMMAND: ",
                    Constants.QueryConstants.MaxSequenceNo);

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.MaxSequenceNo,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MAX SEQUENCE OUTPUT: ", output);
                return output;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Primary get Max Sequence No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER EXCEPTION ERROR", exc);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get Primary Max Sequence No", exc);
            }
        }

        internal override string GetMaxPRnDRSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE RESETLOG COMMAND: ",
                    Constants.QueryConstants._RESETLOG);
                int index = 1;
                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants._RESETLOG,
                    Constants.QueryConstants.GreaterThan);
                output = output.Replace(Constants.QueryConstants._RESETLOG, " ").Trim();

                if (string.IsNullOrEmpty(output) || output.Contains("no rows selected"))
                {
                    return "NA";
                }

                int strstr = output.IndexOf("-");
                //int strindex=output .IndexOf("\n",strstr);
                string resultstring = output.Substring(strstr);
                if (strstr != -1)
                {
                    resultstring = output.Substring(strstr);
                }
                else
                {
                    resultstring = output.Substring(strstr);
                }

                string[] resultarray = Regex.Split(resultstring, "\r\n");
                if (string.IsNullOrEmpty(resultarray[index].ToString().Trim()))
                {
                    return "NA";
                }

                output = resultarray[index].Trim();
                LogHelper.LogHeaderAndMessage("PRIMARY DATBASE RESETLOG Changes Value is: ", resultarray[index].Trim());
                if (!string.IsNullOrEmpty(output))
                {
                    string Log_sequence = Constants.QueryConstants._Log_sequences + "'" + output + "'" + " GROUP BY THREAD#;";
                    output = RunDatabaseCommandsWithWait(PrimarySession, Log_sequence,
                        Constants.QueryConstants.GreaterThan);
                    LogHelper.LogHeaderAndMessage("PRIMARY DATABASE MAX SEQUENCE OUTPUT: ", output);
                    return output;
                }


                return "NA";
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY GET MAX SEQUENCE NUMBER AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("GetMaxPRSequenceNumber EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Primary get Max Sequence No", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GetMaxPRSequenceNumberR EXCEPTION ERROR", exc);
                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while get Primary Max Sequence No", exc);
            }
        }

        internal override bool Exit()
        {
            try
            {
                ExitPromt(PrimarySession);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        internal override bool MoveFiles(string sourcePath, string fileName, string targetPath)
        {
            string moveResult = MoveFiles(PrimarySession, sourcePath, fileName, targetPath);

            return moveResult == null || !moveResult.Contains("cannot move");
        }

        internal override void MoveFiles(string sourcePath, string targetPath, bool isSqlPrompt)
        {
            MoveFiles(PrimarySession, sourcePath, targetPath, true);
        }

        internal bool ConnectServer()
        {
            return IsWindows ? SSHHelper.Ping(HostName) : SSHHelper.Connect(HostName, UserName, Password);
        }

        internal bool IsDatabaseRunning()
        {
            if (IsWindows)
            {
                return IsWinDatabaseRunning();
            }

            return OracleDB.DBRunning(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString, DatabaseName);
        }

        internal bool IsListenerRunning()
        {
            if (IsWindows)
            {
                return true;
                //return IsWinDatabaseListnerRunning();
            }

            return OracleDB.IsListnerRunning(new SSHInfo(HostName, UserName, Password, Port, GetSubAuthList(SubAuthenticationlist)), SudoString);
        }

        internal bool SwitchDatabaseToStandbyMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE COMMAND: ",
                    Constants.QueryConstants.SwitchToStandbyMode);

                string switchresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.SwitchToStandbyMode,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SWITCH DATABASE TO STANDBY MODE OUTPUT: ", switchresult);

                return switchresult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE AUTHENTICATION ERROR",
                    sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE SSH EXCEPTION ERROR",
                    sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while Switch database Primary to standby mode", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH DATABASE PRIMARY TO STANDBY MODE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while Switch database Primary to standby mode", exc);
            }
        }

        internal bool ShutdownPrimaryDatabase()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGShutdownPrimaryDatabase();
                //}
                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE COMMAND : ",
                    Constants.QueryConstants.ShutdownPrimary);

                string shutdownresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.ShutdownPrimary,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("SHUTDOWN PRIMARY DATABASE OUTPUT :", shutdownresult);

                return shutdownresult.Contains("instance shut down") || shutdownresult.Contains("instance terminated");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while shutdown Primary Database", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while shutdown Primary Database", exc);
            }
        }

        internal bool MountStandby()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGMountStandby();
                //}

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY COMMAND : ", Constants.QueryConstants.MountStandby);

                string standbyresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.MountStandby,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("MOUNT STANDBY OUTPUT :", standbyresult);

                return standbyresult.Contains("database mounted");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while mount standby", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY OUTPUT EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled, "Exception occurred while  mount standby" +
                                                                           "" +
                                                                           "", exc);
            }
        }

        internal bool StartStandbyRecovery()
        {
            try
            {
                //if (IsWindows)
                //{
                //    return WinDGStartStandbyRecovery();
                //}

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY COMMAND : ",
                    Constants.QueryConstants.StandbyRecovery);

                string startStandbyresult =
                    RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.StandbyRecovery,
                        Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("START STANDBY RECOVERY: ", startStandbyresult);

                return startStandbyresult.Contains("database altered");
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY AUTHENTICATION ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while start standby recovery", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while while start standby recovery", exc);
            }
        }

        internal bool VerifyPrimaryProcess()
        {
            try
            {
                bool processCount = IsPrimaryHavingProcess(PrimarySession);

                if (processCount)
                {
                    ExitPromt(PrimarySession);
                }
                else
                {
                    IsKillAllPrimaryProcess(PrimarySession);
                }

                return true;
            }
            catch (SshAuthenticationException sshAuthentication)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS ERROR", sshAuthentication);

                throw new BcmsException(BcmsExceptionType.PRAuthenticationFailed,
                    "Authentication Failed for host - " + HostName, sshAuthentication);
            }
            catch (SshException sshException)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS SSH EXCEPTION ERROR", sshException);

                throw new BcmsException(BcmsExceptionType.CommonSshUnhandled,
                    "SshException occurred while verify primary process", sshException);
            }
            catch (BcmsException)
            {
                throw;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("VERIFY PRIMARY PROCESS EXCEPTION ERROR", exc);

                throw new BcmsException(BcmsExceptionType.CommonUnhandled,
                    "Exception occurred while while verify primary process", exc);
            }
        }

        internal void GetDataFileCount()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE COUNT");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileCount,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileCount);

                LogHelper.LogHeaderAndMessage("DATA FILE COUNT OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("DATA FILE COUNT ERROR", exc.Message);
            }
        }

        internal void CheckDataFileError()
        {
            try
            {
                LogHelper.Header("VERIFY DATA FILE ERROR");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileError,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileError);

                LogHelper.LogHeaderAndMessage("DATA FILE ERROR OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("DATA FILE ERROR ERROR", exc.Message);
            }
        }

        internal void CheckRecoveryModeCount()
        {
            try
            {
                LogHelper.Header("CHECK RECOVERY MODE COUNT");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInRecoveryMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.DataFileInRecoveryMode);

                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK RECOVERY MODE COUNT ERROR", exc.Message);
            }
        }

        internal void CheckBackupMode()
        {
            try
            {
                LogHelper.Header("CHECK BACKUP MODE");
                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInBackupMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.DataFileInBackupMode);

                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK BACKUP MODE ERROR", exc.Message);
            }
        }

        internal void CheckDataFileStatus()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE STATUS");
                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileStatus,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.DataFileStatus);
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE STATUS ERROR", exc.Message);
            }
        }

        internal void CheckDataFileMode()
        {
            try
            {
                LogHelper.Header("CHECK DATAFILE MODE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInReadMode,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.DataFileInReadMode);

                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK DATAFILE MODE ERROR", exc.Message);
            }
        }

        internal void IsCorruptedDataFile()
        {
            try
            {
                LogHelper.Header("IS CORRUPTED DATA FILE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataFileInCorrupted,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.DataFileInCorrupted);
                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("IS CORRUPTED DATA FILE ERROR", exc.Message);
            }
        }

        internal void CheckPointChange()
        {
            try
            {
                LogHelper.Header("CHECK POINT CHANGE");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckPointChange,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.CheckPointChange);

                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK POINT CHANGE ERROR", exc.Message);
            }
        }

        internal void CheckCurrentSCN()
        {
            try
            {
                LogHelper.Header("CHECK CURRENT SCN");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CurrentSCNNumber,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.CurrentSCNNumber);

                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK CURRENT SCN ERROR", exc.Message);
            }
        }

        internal void CheckNologgingOperation()
        {
            try
            {
                LogHelper.Header("CHECK NOLOGGING OPERATION");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.NoLoggingOperation,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.NoLoggingOperation);

                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK NOLOGGING OPERATION ERROR", exc.Message);
            }
        }

        internal void CheckPrDataGuardStatus()
        {
            try
            {
                LogHelper.Header("CHECK PR DATAGUARD STATUS");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.PRDataGuardStatus);

                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK PR DATAGUARD STATUS ERROR", exc.Message);
            }
        }

        internal void CheckForceLogging()
        {
            try
            {
                LogHelper.Header("CHECK FORCE LOGGING");

                string output = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.CheckForceLogging,
                    Constants.QueryConstants.GreaterThan);

                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.CheckForceLogging);

                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING OUTPUT", output);
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndMessage("CHECK FORCE LOGGING ERROR", exc.Message);
            }
        }

        internal string VerifyRecoveryMode()
        {
            return
                RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.DataGuardInRecovery,
                    Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string VerifyArachiveLogGap()
        {
            return
                RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.VerifyArchiveGap,
                    Constants.QueryConstants.GreaterThan).ToLower();
        }

        internal string CheckPRDataGuard()
        {
            string result = string.Empty;

            string prdgResult = RunDatabaseCommandsWithWait(PrimarySession, Constants.QueryConstants.PRDataGuardStatus,
                Constants.QueryConstants.GreaterThan).ToLower();

            LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.PRDataGuardStatus);

            LogHelper.LogHeaderAndMessage("COMMAND Output: ", prdgResult);

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive";
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        internal string DynamicPRDBName()
        {

            var discoverDBcmd = RunSshCommandsWithWait(PrimarySession, Constants.QueryConstants.discoveryqueryondr, Constants.QueryConstants.GreaterThan);
            if (discoverDBcmd.ToLower().Contains("db_unique_name"))
            {
                string output = discoverDBcmd;
                string output1 = output.Replace("\r\n", ",");
                string[] result = output1.Split(',');
                string dbname = result[3];

                return dbname;
            }
            return "N/A";
        }

        internal string CheckPRDataGuardDynamic(string DBName)
        {
            string result = "N/A";

            if (DBName != "N/A")
            {
                string DGStatuscmd = Constants.QueryConstants.dataguardstatus + "('" + DBName + "','" + DBName.ToUpper() + "') and TARGET='STANDBY';";

                var prdgResult = RunSshCommandsWithWait(PrimarySession, DGStatuscmd, Constants.QueryConstants.GreaterThan).ToLower();

                LogHelper.LogHeaderAndMessage("COMMAND : ", DGStatuscmd);

                LogHelper.LogHeaderAndMessage("COMMAND Outpu: ", prdgResult);

                if (prdgResult.Contains("valid"))
                {
                    result = "Running";
                }
                else if (prdgResult.Contains("deferred"))
                {
                    result = "Deferred";
                }
                else if (prdgResult.Contains("inactive"))
                {
                    result = "Inactive";
                }
                else if (prdgResult.Contains("error"))
                {
                    result = "Error";
                }
                else
                {
                    result = "N/A";
                }
            }
            return result;
        }

        internal string StartFastCopy(string hostname, string username, string password, string localDir, string remoteDir, string processcode)
        {
            var sb = new StringBuilder();

            var commands = new List<string>
            {
                Constants.QueryConstants.ChangeFastCopyDirectory,
                string.Format("{0} \"{1}_{2}_{3}_{4}_{5}\"",
                    Constants.QueryConstants.StartFastCopyReplication,
                    processcode,
                    hostname,
                    username,
                    localDir,
                    remoteDir),
                password
            };

            foreach (string command in commands)
            {
                sb.Append(RunSshCommands(PrimarySession, command));
            }
            string connectResult = sb.ToString().ToLower();

            return connectResult;
        }

        internal string StartFastCopy(int groupId, int replicationId, bool iswithover)
        {
            try
            {
                if (IsWindows)
                {
                    //Windows FastCopy Log Replication

                    string repicationstatus = ReplicateArchiveLogFastcopy(groupId);
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", repicationstatus);
                    return repicationstatus;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();
                var commands = new List<string>
                {
                    Constants.QueryConstants.ChangeFastCopyDirectory,
                    iswithover
                        ? string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-SO",
                            groupId, replicationId)
                        : string.Format("{0} {1} {2}", Constants.QueryConstants.StartFastCopyReplication, groupId, replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT :", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION  :", exc.Message);
                return exc.Message;
            }
        }

        internal string ReplicateArchiveLogFastcopy(int groupId)
        {
            string result = RunSshCommandsWithWait(PrimarySession, "cd C:\\FastCopy1", Constants.QueryConstants.GreaterThan).ToLower();
            if (result.Contains("c:\\fastcopy"))
            {
                return RunSshCommandsWithWait(PrimarySession, "FastCopy.exe " + groupId, Constants.QueryConstants.GreaterThan).ToLower();
            }
            return null;
        }

        internal string StartFastCopy_OLD(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for oracle linux datasync
                {

                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DBU", infraObjectId, replicationId, logSequence, logSequenceName) 
               
                    };
                    foreach (string command in commands)
                    {
                        LogHelper.LogMessage("Uploading command: " + command);
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else    // for non-windows
                {
                    var commands = new List<string>
                    {
                        "rm -f nohup.out",
                        "cd " + path,
                        isSwithover 
                        ? string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar -SO {0} {1} {2} {3} &", infraObjectId, replicationId, logSequence, logSequenceName)
                        : string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar -DBU {0} {1} {2} {3} &", infraObjectId, replicationId, logSequence, logSequenceName)
                    };

                    foreach (string command in commands)
                    {
                        LogHelper.LogHeaderAndMessage("ARCHIVE LOG DATASYNC Command(in StartFastCopy - PrimaryHost): ", command);
                        RunSshCommandsWithNoWait(PrimarySession, command);
                    }
                    return "";
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("ARCHIVE LOG DATASYNC EXCEPTION(in StartFastCopy - PrimaryHost): ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path, string jrepath, bool jreFlag)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else    // for non-windows
                {

                    if (!string.IsNullOrEmpty(jrepath))
                    {
                        datasync = "nohup " + jrepath.TrimEnd('/') + "/java ";
                    }
                    else
                    {
                        datasync = "nohup java ";
                    }

                    var commands = new List<string>
                    {
                        "rm -f nohup.out",
                        "cd " + path,
                        isSwithover 
                        ? string.Format("{4} -Xms512m -Xmx1024m -jar DataSync.jar -SO {0} {1} {2} {3} &", infraObjectId, replicationId, logSequence, logSequenceName, datasync)
                        : string.Format("{4} -Xms512m -Xmx1024m -jar DataSync.jar -DBU {0} {1} {2} {3} &", infraObjectId, replicationId, logSequence, logSequenceName, datasync)
                    };

                    foreach (string command in commands)
                    {
                        LogHelper.LogHeaderAndMessage("ARCHIVE LOG DATASYNC Command(in StartFastCopy - PrimaryHost): ", command);
                        RunSshCommandsWithNoWait(PrimarySession, command);
                    }
                    return "";
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("ARCHIVE LOG DATASYNC EXCEPTION(in StartFastCopy - PrimaryHost): ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path, string sudoUser)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for oracle linux datasync
                {
                    var commands = new List<string>
                    {
                        "sudo su - " + sudoUser,
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DBU", infraObjectId, replicationId, logSequence, logSequenceName) 
               
                    };
                    foreach (string command in commands)
                    {
                        LogHelper.LogMessage("Uploading command: " + command);
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopy(int infraObjectId, int replicationId, bool isSwithover, string logSequence, string logSequenceName, string path, string sudoUser, string jrepath)
        {
            try
            {
                //Linux FastCopy Log Replication

                string datasync = IsWindows ? Constants.QueryConstants.FastCopyExe : Constants.QueryConstants.StartFastCopyReplication;
                var sb = new StringBuilder();

                if (IsWindows)   // for oracle win datasync
                {
                    var commands = new List<string>
                    {
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DB", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName) 
                    };

                    foreach (string command in commands)
                    {
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }

                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
                else  // for oracle linux datasync
                {
                    if (!string.IsNullOrEmpty(jrepath))
                    {
                        datasync = "nohup " + jrepath.TrimEnd('/') + "/java ";
                    }
                    else
                    {
                        datasync = "nohup java ";
                    }

                    var commands = new List<string>
                    {
                        "sudo su - " + sudoUser,
                        "cd " + path,
                        isSwithover
                            ? string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-SO", infraObjectId, replicationId, logSequence, logSequenceName)
                            : string.Format("{0} {1} {2} {3} {4} {5}", datasync,
                                "-DBU", infraObjectId, replicationId, logSequence, logSequenceName) 
               
                    };
                    foreach (string command in commands)
                    {
                        LogHelper.LogMessage("Uploading command: " + command);
                        sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                    }
                    LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                    return sb.ToString().ToLower();
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("REPLICATE ARCHIVE LOG EXCEPTION: ", exc.Message);
                return exc.Message;
            }
        }

        internal string StartFastCopyReplicateRedoFiles(int workflowActionId, int replicationId, string fastCopyPath)
        {
            try
            {
                if (IsWindows)
                {
                    return string.Empty;
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}", fastCopyPath),
                    string.Format("{0} {1} {2} {3}", Constants.QueryConstants.StartFastCopyReplication, "-WFU", workflowActionId, replicationId)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE REDO FILES RESULT: ", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate REDO files ", exc.Message);
                return string.Empty;
            }
        }

        internal string StartFastCopyReplicateArchiveFolder(string fastCopyPath, int workflowActionId, int replicationId, string sequence, string logfile)
        {
            try
            {
                if (IsWindows)
                {
                }

                //Linux FastCopy Log Replication

                var sb = new StringBuilder();

                var commands = new List<string>
                {
                    string.Format("cd {0}", fastCopyPath),
                    string.Format("{0} -WDBU {1} {2} {3} {4}", Constants.QueryConstants.StartFastCopyReplication, workflowActionId, replicationId, sequence, logfile)
                };

                foreach (string command in commands)
                {
                    sb.Append(RunSshCommandsWithTimeOut(PrimarySession, command, 1800000));
                }
                LogHelper.LogHeaderAndMessage("REPLICATE ARCHIVE LOG DR RESULT: ", sb.ToString());
                return sb.ToString().ToLower();
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate ARCHIVE files ", exc.Message);
                return string.Empty;
            }
        }

        internal string CheckPRDataGuardWindows()
        {
            string result;

            string prdgResult = WinDGGetPrimaryDataGuardStatus().ToLower();

            if (prdgResult.Contains("valid"))
            {
                result = "Running";
            }
            else if (prdgResult.Contains("deferred"))
            {
                result = "Deferred";
            }
            else if (prdgResult.Contains("inactive"))
            {
                result = "Inactive";
            }
            else if (prdgResult.Contains("error"))
            {
                result = "Error";
            }
            else
            {
                result = "N/A";
            }

            return result;
        }

        private bool IsWinDatabaseRunning()
        {
            try
            {
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB RUNNING EXCEPTION: ", exc);
                return false;
            }
        }

        private bool IsWinDatabaseListnerRunning()
        {
            try
            {
                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" + DatabaseName;

                using (var db = new OracleDatabase(constring))
                {
                    return true;
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("IS WIN DB LISTNER RUNNING EXCEPTION: ", exc);
                return false;
            }
        }

        private string WinDGGetPrimaryDataGuardStatus()
        {
            try
            {
                string output = string.Empty;

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsPRDataGuardStatus))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        if (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();
                        }
                    }
                }

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATAGUARD STATUS : ", exc);

                return "INVALID";
            }
        }

        private bool WinDGVerifyDatabaseMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsDatabaseModeAndRole);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsDatabaseModeAndRole)
                    )
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string mode = myDatabaseReader[1].ToString();
                            string role = myDatabaseReader[2].ToString();

                            if (mode == "READ WRITE" && role == "PRIMARY")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY DATABASE MODE EXCEPTION : ", exc);
                return false;
            }

            return false;
        }

        private string WinDGVerifyUserStatus()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsActiveUserCount);
                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsActiveUserCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            return myDatabaseReader[0].ToString();
                        }
                    }
                }
                return "0";
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY USER STATUS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGVerifyPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsProcessCount);
                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsProcessCount))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string count = myDatabaseReader[1].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY PROCESS COUNT :", count);

                            return Convert.ToInt32(count) == 0;
                        }
                    }
                }
                return false;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY PROCESS EXCEPTION : ", exc);

                throw;
            }
        }

        private bool KillAllWindowsPrimaryProcess()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsKillProcess);

                LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS ", string.Empty);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsKillProcess))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string output = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("KILL PRIMARY PROCESS OUTPUT: ", output);

                            if (output == "")
                            {
                                return true;
                            }
                            return false;
                        }
                    }
                }
                LogHelper.LogHeaderAndMessage("KILLED ALL PRIMARY PROCESS ", string.Empty);
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("KILL PRIMARY PROCESS EXCEPTION: ", exc);
                throw;
            }
        }

        private string WinDGGetMaxSequenceNumber()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND : ", Constants.QueryConstants.WindowsMaxSequenceNo);

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));
                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WindowsMaxSequenceNo))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            string squenceno = myDatabaseReader[0].ToString();

                            LogHelper.LogHeaderAndMessage("PRIMARY MAX SEQUNCE NO OUTPUT: ", squenceno);

                            return squenceno;
                        }
                    }
                }
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("PRIMARY MAX SEQUNCE NO EXCEPTION: ", exc);
                throw;
            }
        }

        private bool WinDGSwitchDatabaseToStandbyMode()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsSwitchToStandbyMode);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsSwitchToStandbyMode);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SWITCH PRIMARY DATABASE TO STANDBY MODE EXCEPTION: ", exc);
                throw;
            }
        }

        private bool WinDGShutdownPrimaryDatabase()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.ShutdownPrimary);
                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.Shutdown(Devart.Data.Oracle.OracleShutdownMode.Immediate, true);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("SHUTDOWN PRIMARY DATABASE EXCEPTION : ", exc);

                throw;
            }
        }

        private bool WinDGMountStandby()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsMountStandby);

                string constring = "dba privilege=sysdba;user id=sys;password=" + Password + ";data source=" +
                                   DatabaseName;

                using (var db = new OracleDatabase(constring))
                {
                    db.Startup(Devart.Data.Oracle.OracleStartupMode.Default);

                    // db.Startup(Devart.Data.Oracle.OracleShutdownMode.reOracleDBStartupMode.NoRestriction, null, false);
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsMountStandby);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("MOUNT STANDBY EXCEPTION: ", exc);
                throw;
            }
        }

        private bool WinDGStartStandbyRecovery()
        {
            try
            {
                LogHelper.LogHeaderAndMessage("COMMAND: ", Constants.QueryConstants.WindowsStandbyRecovery);

                using (var db = new OracleDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort)))
                {
                    db.ExecuteNonQuery(Constants.QueryConstants.WindowsStandbyRecovery);
                }
                return true;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("START STANDBY RECOVERY EXCEPTION: ", exc);
                throw;
            }
        }

        internal List<string> GetPrimaryDRMaxLogSequnce(string sequnce)
        {
            try
            {
                var prsequnce = new List<string>();

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (
                    DbCommand dbCommand =
                        db.GetSqlStringCommand(Constants.QueryConstants.WinDRLogLatestSeqInPrimary + sequnce))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY DR LATEST LOG SEQUENCE EXCEPTION: ", exc);
                throw;
            }
        }

        internal List<string> GetPrimaryMaxLogSequnce()
        {
            try
            {
                var prsequnce = new List<string>();

                Database db =
                    CustomDatabaseFactory.CreateDatabase(GetConnectionString(HostName, DatabaseName, DatabaseUserName, DatabasePassword, DatabasePort));

                using (DbCommand dbCommand = db.GetSqlStringCommand(Constants.QueryConstants.WinPRLatestLogSeq))
                {
                    using (IDataReader myDatabaseReader = db.ExecuteReader(dbCommand))
                    {
                        while (myDatabaseReader.Read())
                        {
                            prsequnce.Add(myDatabaseReader["name"].ToString());
                            prsequnce.Add(myDatabaseReader["sequence#"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(COMPLETION_TIME,'mm-dd-yyyyhh24:mi:ss')"].ToString());
                            prsequnce.Add(myDatabaseReader["to_char(first_Change#)"].ToString());
                        }
                    }
                }

                return prsequnce;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("GET PRIMARY LATEST LOG SEQUENCE EXCEPTION: ", exc);
                throw;
            }
        }

        public string AmazonReplicationDataSync(string option, int infraObjectId, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Amazon Replication DataSync Started (client): ");
                LogHelper.LogMessage("Amazon Replication DataSync infraObjectId: " + infraObjectId);

                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("nohup java -jar AmazonS3_RHL.jar {1} {0} &", infraObjectId, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Amazon Replication : Command (" + command + ") is executed");
                    RunSshCommandsWithNoWait(PrimarySession, command);
                }
                LogHelper.LogMessage("Amazon Replication DataSync Completed (client): ");
                return string.Empty;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Amazon DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        public string WFAmazonReplicationDataSync(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Amazon Replication DataSync Started (client): ");
                LogHelper.LogMessage("Amazon Replication DataSync workflowactionid: " + workflowactionid);

                string output = string.Empty;


                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("java -jar AmazonS3_RHL.jar {1} {0}", workflowactionid, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Amazon Replication : Command (" + command + ") is executed");
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage("Amazon Replication DataSync Output: " + output);
                LogHelper.LogMessage("Amazon Replication DataSync Completed (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Amazon DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        public string WFSybaseReplicationDataSyncBCPTable(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Sybase Replication DataSync Started (client): ");
                LogHelper.LogMessage("Sybase Replication DataSync workflowactionid: " + workflowactionid);

                string output = string.Empty;

                string datasync = Constants.QueryConstants.StartFastCopyReplication;
                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,                        
                         string.Format("{0} {1} {2}", datasync,option, workflowactionid)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Sybase Replication: Command (" + command + ") is executed");
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage("Sybase Replication DataSync Output: " + output);
                LogHelper.LogMessage("Sybase Replication DataSync Completed (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Sybase DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        public string WFSybaseReplicationDataSync(string option, int workflowActionId, int replicationId, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage("Sybase Replication DataSync Started (client): ");
                LogHelper.LogMessage("Sybase Replication DataSync workflowActionId: " + workflowActionId);

                string output = string.Empty;
                string datasync = Constants.QueryConstants.StartFastCopyReplication;

                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("{0} {1} {2} {3}", datasync,option, workflowActionId,replicationId)                        
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage("Sybase Replication: Command (" + command + ") is executed");
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage("Sybase Replication DataSync Output: " + output);
                LogHelper.LogMessage("Sybase Replication DataSync Completed (client): ");

                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate Sybase DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        public string WFBFLReplicationDataSync(string option, int workflowactionid, string datasyncPath)
        {
            try
            {
                LogHelper.LogMessage(" Replication DataSync Started (client): ");
                LogHelper.LogMessage(" Replication DataSync workflowactionid: " + workflowactionid);

                string output = string.Empty;

                var commands1 = new List<string>
                    {
                        "cd " + datasyncPath,
                        string.Format("DataSync.exe {1} {0}", workflowactionid, option)
                    };

                foreach (string command in commands1)
                {
                    LogHelper.LogMessage(" Replication : Command (" + command + ") is executed");
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage(" Replication DataSync Output: " + output);
                LogHelper.LogMessage(" Replication DataSync Completed (client): ");
                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while Replicate  DataSync files ", exc.Message);
                return string.Empty;
            }
        }

        public string WFReplicationDataSync(string option, string DatasyncPath, int workflowactionId)
        {
            try
            {
                LogHelper.LogMessage(" Replication DataSync Started (client): ");
                LogHelper.LogMessage(" Replication DataSync workflowactionid :" + workflowactionId);

                string output = string.Empty;

                var commands = new List<string>
                {                   
                    "rm -f nohup.out",
                    "cd " + DatasyncPath,
                    string.Format("nohup java -Xms512m -Xmx1024m -jar DataSync.jar {0} {1} &", option,workflowactionId)
                };

                foreach (string command in commands)
                {
                    output += RunSshCommandsWithTimeOut(PrimarySession, command, 300000);
                }

                LogHelper.LogMessage("Application Replication DataSync Completed (client): ");
                return output;
            }
            catch (Exception exc)
            {
                LogHelper.LogHeaderAndErrorMessage("Exception occurred while WF Replication DataSync  ", exc.Message);
                return string.Empty;
            }
        }

    }
}