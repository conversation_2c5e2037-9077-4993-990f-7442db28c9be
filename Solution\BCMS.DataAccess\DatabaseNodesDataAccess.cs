﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class DatabaseNodesDataAccess : BaseDataAccess
    {
        public static IList<DatabaseNodes> GetDatabaseNodesByDatabaseId(int iId)
        {
            try
            {
                IList<DatabaseNodes> node = new List<DatabaseNodes>();


                using (DbCommand cmd = Database.GetStoredProcCommand("DatabaseNode_GetByDbBaseId"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iDatabaseBaseId", DbType.Int32, iId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var databaseNode = new DatabaseNodes();

                            DatabaseNodes dbnode = new DatabaseNodes();
                            dbnode.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            ////node.BaseDatabaseId = reader.IsDBNull(FLD_BASEDATABASEID) ? 0 : reader.GetInt32(FLD_BASEDATABASEID);
                            dbnode.DatabaseId = Convert.IsDBNull(reader["DatabaseId"]) ? 0 : Convert.ToInt32(reader["DatabaseId"]);
                            dbnode.NodeId = Convert.IsDBNull(reader["NodeId"]) ? 0 : Convert.ToInt32(reader["NodeId"]);
                            node.Add(dbnode);

                        }
                    }
                }
                return node;
            }
            catch (Exception ex)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get DatabaseNode information By Id - " + iId, ex);
            }
           
        }
    }
}
