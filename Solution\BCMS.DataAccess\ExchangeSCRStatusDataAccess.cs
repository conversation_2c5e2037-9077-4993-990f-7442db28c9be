﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class ExchangeSCRStatusDataAccess : BaseDataAccess
    {
      public static ExchangeSCRStatus GetByGroupId(int groupid)
        {

            var scrGroup = new ExchangeSCRStatus();

            try
            {


                using (var dbCommand = Database.GetStoredProcCommand("EXCSCRSTATUS_GETBYINFRAOBJID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, groupid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            scrGroup.Id = myReader.IsDBNull(0) ? 0 : Convert.ToInt32(myReader[0]); 
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Parallel Server Information", exc);
            }
            return scrGroup;
        }
      public static ExchangeSCRStatus Update(ExchangeSCRStatus status)
        {
            const string SP = "EXCSCRSTATUS_UPDATEBYINFOBJID";

            using (var dbCommand = Database.GetStoredProcCommand(SP))
            {
                Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, status.Id);
                Database.AddInParameter(dbCommand, Dbstring+"iSummaryCopyStatus", DbType.AnsiString, status.SummaryCopyStatus);
                Database.AddInParameter(dbCommand, Dbstring+"iCopyQueueLenght", DbType.AnsiString, status.CopyQueueLenght);
                Database.AddInParameter(dbCommand, Dbstring+"iReplyQueueLenght", DbType.AnsiString, status.ReplyQueueLenght);
                Database.AddInParameter(dbCommand, Dbstring+"iSCRHealth", DbType.AnsiString, status.SCRHealth);
#if ORACLE
                dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader myDROperationResultReader = Database.ExecuteReader(dbCommand))
                {
                    if (myDROperationResultReader.Read())
                    {
                        status.Id = Convert.ToInt32(myDROperationResultReader[0]);
                    }
                    else
                    {
                        status = null;
                    }
                }

                return status;


            }
        }

      public static ExchangeSCRStatus Add(ExchangeSCRStatus status)
      {
          const string SP = "EXCHANGESCRLOG_CREATE";


          using (var dbCommand = Database.GetStoredProcCommand(SP))
          {
              Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, status.InfraObjectId);
              Database.AddInParameter(dbCommand, Dbstring+"iSummaryCopyStatus", DbType.AnsiString, status.SummaryCopyStatus);
              Database.AddInParameter(dbCommand, Dbstring+"iCopyQueueLenght", DbType.AnsiString, status.CopyQueueLenght);
              Database.AddInParameter(dbCommand, Dbstring+"iReplyQueueLenght", DbType.AnsiString, status.ReplyQueueLenght);
              Database.AddInParameter(dbCommand, Dbstring+"iSCRHealth", DbType.AnsiString, status.SCRHealth);


              using (IDataReader myDROperationResultReader = Database.ExecuteReader(dbCommand))
              {
                  if (myDROperationResultReader.Read())
                  {
                      status.Id = Convert.ToInt32(myDROperationResultReader[0]);

                  }
                  else
                  {
                      status = null;
                  }
              }
              return status;


          }
      }
    }
}
