﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Bcms.Common.Shared
{
    public sealed partial class Constants
    {
        public static class ValidationConstants
        {
            public static class ErrorMessage
            {
                public static string GetMessage(string jobName, string groupName,string entity, ExceptionType exceptionType)
                {
                    switch (exceptionType)
                    {
                      case  ExceptionType.Null:

                            return string.Format("{0} : Null argument while fetching {1} data from database", groupName, entity);

                        case ExceptionType.Invalid:
                            
                            return string.Format("{0} : Empty data found while validating {1} information",groupName,entity);

                        case ExceptionType.IndexOutOfRange:

                            return string.Format("{0} : Exception occured while parsing {1} Returnvalue", groupName,jobName);
                      
                        case ExceptionType.SSHUnHandled:

                            return string.Empty;
                           
                        case  ExceptionType.UnHandled:
                           
                            return string.Format("{0} : Error Occured While Executing {1}", groupName, jobName);
                            
                        case ExceptionType.NotSpecified:

                            return string.Format("{0} : {1} , {2} is not in specified format", groupName,jobName,entity);
                            
                    }
                    return null;
                }
            }


            public static class SuccessMessage
            {
                public static string GetMessage(string groupName,string userMessage,Success successMessage)
                {
                    switch (successMessage)
                    {
                        case Success.Updated:
                            return string.Format("{0} : {1} Status Updated Successfully",groupName,userMessage);
                    }
                    return null;
                }
            }

        }
    }
}
