﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;

using Bcms.Common.Shared;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;
using Bcms.Common;

namespace Bcms.DataAccess
{
    public class MimixMonitorDataAccess : BaseDataAccess
    {
        public static bool AddMimixManagerMonitorStatus(ApplicationMimix.MIMIXManager _MIMIXManager)
        {
            const string sp = "MimixManagerStatus_Create"; 
            try
            { 
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _MIMIXManager.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iSystemDefinition", DbType.AnsiString, _MIMIXManager.SystemDefinition);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, _MIMIXManager.Type);
                    Database.AddInParameter(cmd, Dbstring + "iSystem", DbType.AnsiString, _MIMIXManager.System);
                    Database.AddInParameter(cmd, Dbstring + "iJournal", DbType.AnsiString, _MIMIXManager.Journal);
                    Database.AddInParameter(cmd, Dbstring + "iClusterServices", DbType.AnsiString, _MIMIXManager.ClusterServices);
                 
                    int value = Database.ExecuteNonQuery(cmd);
                    #if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
                    #endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting Mimix Manager Status MimixManagerStatus_Create", exc);
            }
            return false;

        }

        public static bool AddMimiXAlertsMonitorStatus(ApplicationMimix.MIMIXAlerts _MIMIXAlerts)
        {
            const string sp = "MimixAlertStatus_Create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _MIMIXAlerts.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iAuditCount", DbType.AnsiString, _MIMIXAlerts.AuditCount);
                    Database.AddInParameter(cmd, Dbstring + "iRecoveryCount", DbType.AnsiString, _MIMIXAlerts.RecoveryCount);
                    Database.AddInParameter(cmd, Dbstring + "iNotificationCount", DbType.AnsiString, _MIMIXAlerts.NotificationCount);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting Mimix Alert Monitor Status MimixAlertStatus_Create", exc);
            }
            return false;

        }

        public static bool AddMimixHealthMonitorStatus(ApplicationMimix.MIMIXHealth _MIMIXHealth)
        {
            const string sp = "MimixHealthStatus_Create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _MIMIXHealth.InfraobjectId);

                    Database.AddInParameter(cmd, Dbstring + "iDataGroup", DbType.AnsiString, _MIMIXHealth.DataGroup);

                    Database.AddInParameter(cmd, Dbstring + "iSourceSystem", DbType.AnsiString, _MIMIXHealth.SourceSystem);
                    Database.AddInParameter(cmd, Dbstring + "iSourceMgr", DbType.AnsiString, _MIMIXHealth.SourceMgr);
                    Database.AddInParameter(cmd, Dbstring + "iSourceDB", DbType.AnsiString, _MIMIXHealth.SourceDB);
                    Database.AddInParameter(cmd, Dbstring + "iSourceObj", DbType.AnsiString, _MIMIXHealth.SourceObj);

                    Database.AddInParameter(cmd, Dbstring + "iTargetSystem", DbType.AnsiString, _MIMIXHealth.TargetSystem);
                    Database.AddInParameter(cmd, Dbstring + "iTargetMgr", DbType.AnsiString, _MIMIXHealth.TargetMgr);
                    Database.AddInParameter(cmd, Dbstring + "iTargetDB", DbType.AnsiString, _MIMIXHealth.TargetDB);
                    Database.AddInParameter(cmd, Dbstring + "iTargetObj", DbType.AnsiString, _MIMIXHealth.TargetObj);

                    Database.AddInParameter(cmd, Dbstring + "iErrorDB", DbType.AnsiString, _MIMIXHealth.ErrorDB);
                    Database.AddInParameter(cmd, Dbstring + "iErrorObj", DbType.AnsiString, _MIMIXHealth.ErrorObj);


                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting Mimix Alert Monitor Status MimixAlertStatus_Create", exc);
            }
            return false;

        }

        public static bool AddMimixAvailabilityMonitorStatus(ApplicationMimix.MIMIXAvilability _MIMIXAvilability)
        {
            const string sp = "MimixAvilabilityStatus_Create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _MIMIXAvilability.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iActivity", DbType.AnsiString, _MIMIXAvilability.Activity);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, _MIMIXAvilability.Status);
                   

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting Mimix Manager Status MimixManagerStatus_Create", exc);
            }
            return false;

        }

        public static bool AddMimixDatalagMonitorStatus(ApplicationMimix.MIMIXDatalag _MIMIXDatalag)
        {
            const string sp = "MimixDatalagStatus_Create";
            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, _MIMIXDatalag.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iDataGroup", DbType.AnsiString, _MIMIXDatalag.DataGroup);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseErrors", DbType.AnsiString, _MIMIXDatalag.DatabaseErrors);
                    Database.AddInParameter(cmd, Dbstring + "iElapsedTime", DbType.AnsiString, _MIMIXDatalag.ElapsedTime);
                    Database.AddInParameter(cmd, Dbstring + "iObjectInErrorAndActive", DbType.AnsiString, _MIMIXDatalag.ObjectInErrorAndActive);
                    Database.AddInParameter(cmd, Dbstring + "iTransferDefinition", DbType.AnsiString, _MIMIXDatalag.TransferDefinition);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.AnsiString, _MIMIXDatalag.State);
                    Database.AddInParameter(cmd, Dbstring + "iDatabase", DbType.AnsiString, _MIMIXDatalag.Database);
                    Database.AddInParameter(cmd, Dbstring + "iObject", DbType.AnsiString, _MIMIXDatalag.Object);
                    Database.AddInParameter(cmd, Dbstring + "iDBSourceReceiver", DbType.AnsiString, _MIMIXDatalag.DBSourceReceiver);
                    Database.AddInParameter(cmd, Dbstring + "iDBTargetReceiver", DbType.AnsiString, _MIMIXDatalag.DBTargetReceiver);
                    Database.AddInParameter(cmd, Dbstring + "iDBLastReadReceiver", DbType.AnsiString, _MIMIXDatalag.DBLastReadReceiver);
                    Database.AddInParameter(cmd, Dbstring + "iDBSourceSequence", DbType.AnsiString, _MIMIXDatalag.DBSourceSequence);
                    Database.AddInParameter(cmd, Dbstring + "iDBTargetSequence", DbType.AnsiString, _MIMIXDatalag.DBTargetSequence);
                    Database.AddInParameter(cmd, Dbstring + "iDBLastReadSequence", DbType.AnsiString, _MIMIXDatalag.DBLastReadSequence);
                    Database.AddInParameter(cmd, Dbstring + "iDBSourceDateTime", DbType.AnsiString, _MIMIXDatalag.DBSourceDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iDBTargetDateTime", DbType.AnsiString, _MIMIXDatalag.DBTargetDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iDBLastReadDateTime", DbType.AnsiString, _MIMIXDatalag.DBLastReadDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iDBSourceTransPerHour", DbType.AnsiString, _MIMIXDatalag.DBSourceTransPerHour);
                    Database.AddInParameter(cmd, Dbstring + "iDBTargetTransPerHour", DbType.AnsiString, _MIMIXDatalag.DBTargetTransPerHour);
                    Database.AddInParameter(cmd, Dbstring + "iDBLastReadTransPerHour", DbType.AnsiString, _MIMIXDatalag.DBLastReadTransPerHour);
                    Database.AddInParameter(cmd, Dbstring + "iDBEntrieNotRead", DbType.AnsiString, _MIMIXDatalag.DBEntrieNotRead);
                    Database.AddInParameter(cmd, Dbstring + "iDBEstimatedTimeToRead", DbType.AnsiString, _MIMIXDatalag.DBEstimatedTimeToRead);
                    Database.AddInParameter(cmd, Dbstring + "iObjCurrentReceiver", DbType.AnsiString, _MIMIXDatalag.ObjCurrentReceiver);
                    Database.AddInParameter(cmd, Dbstring + "iObjLastReadReceiver", DbType.AnsiString, _MIMIXDatalag.ObjLastReadReceiver);
                    Database.AddInParameter(cmd, Dbstring + "iObjCurrentSequence", DbType.AnsiString, _MIMIXDatalag.ObjCurrentSequence);
                    Database.AddInParameter(cmd, Dbstring + "iObjLastReadSequence", DbType.AnsiString, _MIMIXDatalag.ObjLastReadSequence);
                    Database.AddInParameter(cmd, Dbstring + "iObjCurrentDateTime", DbType.AnsiString, _MIMIXDatalag.ObjCurrentDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iObjLastReadDateTime", DbType.AnsiString, _MIMIXDatalag.ObjLastReadDateTime);
                    Database.AddInParameter(cmd, Dbstring + "iObjCurrentTransPerHour", DbType.AnsiString, _MIMIXDatalag.ObjCurrentTransPerHour);
                    Database.AddInParameter(cmd, Dbstring + "iObjLastReadTransPerHour", DbType.AnsiString, _MIMIXDatalag.ObjLastReadTransPerHour);
                    Database.AddInParameter(cmd, Dbstring + "iObjEntrieNotRead", DbType.AnsiString, _MIMIXDatalag.ObjEntrieNotRead);
                    Database.AddInParameter(cmd, Dbstring + "iObjEstimatedTimeToRead", DbType.AnsiString, _MIMIXDatalag.ObjEstimatedTimeToRead);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentDatalag", DbType.AnsiString, _MIMIXDatalag.CurrentDatalag);

                    int value = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (value == -1)
                    {
                        return true;
                    }
#endif
                    if (value >= 0)
                    {
                        return true;
                    }
                }
            }


            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while while inserting Mimix Manager Status MimixManagerStatus_Create", exc);
            }
            return false;

        }
       
    }
}
        

