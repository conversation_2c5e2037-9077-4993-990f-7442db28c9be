﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
using Bcms.Common.Shared;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "DatabaseSql", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseSql : BaseEntity
    {

        #region Properties

        //DatabaseBase _databaseBase = new DatabaseBase();

        [DataMember]
        public int PRBaseDatabaseId
        {
            get;
            set;
        }
          [DataMember]
        public int PRServerId
        {
            get;
            set;
        }

          [DataMember]
          public string PRDatabaseSID
        {
            get;
            set;
        }

        [DataMember]
          public string PRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string PRPassword
        {
            get;
            set;
        }

        [DataMember]
        public int PRPort
        {
            get;
            set;
        }

        [DataMember]
        public SqlAuthenticateType PRAuthenticationMode
        {
            get;
            set;
        }

        [DataMember]
        public string PRDataFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string PRTransLogPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRUndoFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string PRBackupRestorePath
        {
            get;
            set;
        }

        [DataMember]
        public string PRNetworkSharedPath
        {
            get;
            set;
        }

        [DataMember]
        public int DRBaseDatabaseId
        {
            get;
            set;
        }
        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseSID
        {
            get;
            set;
        }

        [DataMember]
        public string DRUserName
        {
            get;
            set;
        }

        [DataMember]
        public string DRPassword
        {
            get;
            set;
        }

        [DataMember]
        public int DRPort
        {
            get;
            set;
        }

        [DataMember]
        public SqlAuthenticateType DRAuthenticationMode
        {
            get;
            set;
        }

        [DataMember]
        public string DRDataFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string DRTransLogPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRUndoFilePath
        {
            get;
            set;
        }

        [DataMember]
        public string DRBackupRestorePath
        {
            get;
            set;
        }

        [DataMember]
        public string DRNetworkSharedPath
        {
            get;
            set;
        }

        //[DataMember]
        //public DatabaseBase DatabaseBase
        //{
        //    get { return _databaseBase; }
        //    set { _databaseBase = value; }
        //}

        #endregion

        #region Constructor

        public DatabaseSql()
            : base()
        {
        }

        #endregion
    }
}
