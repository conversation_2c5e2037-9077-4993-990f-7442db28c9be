﻿using System;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class ExchangeHealthDataAccess : BaseDataAccess
    {
       public static ExchangeHealthStatus GetByGroupId(int groupid)
        {

            var scrGroup = new ExchangeHealthStatus();

            try
            {


                using (var dbCommand = Database.GetStoredProcCommand("ExchangeHealth_GetById"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, groupid);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myReader.Read())
                        {
                            scrGroup.Id = myReader.IsDBNull(0) ? 0 : Convert.ToInt32(myReader[0]);
                           
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation,
                                        "Exception occurred while Get Parallel Server Information", exc);
            }
            return scrGroup;
        }
       public static ExchangeHealthStatus Update(ExchangeHealthStatus exchangeHealth)
        {
            const string sp = "ExchangeHelthstatus_UpdtGrupId";

            using (var dbCommand = Database.GetStoredProcCommand(sp))
            {
                Database.AddInParameter(dbCommand, Dbstring+"iId", DbType.Int32, exchangeHealth.Id);
                Database.AddInParameter(dbCommand, Dbstring+"iPRMailboxDatabaseStatus", DbType.AnsiString, exchangeHealth.PRMailboxDatabaseStatus);
                Database.AddInParameter(dbCommand, Dbstring+"iDRMailboxDatabaseStatus", DbType.AnsiString, exchangeHealth.DRMailboxDatabaseStatus);
                Database.AddInParameter(dbCommand, Dbstring+"iPRLatestAvabLogTime", DbType.AnsiString, exchangeHealth.PRLatestAvabLogTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastCopiedLogTime", DbType.AnsiString, exchangeHealth.DRLastCopiedLogTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastInspectedLogTime", DbType.AnsiString, exchangeHealth.DRLastInspectedLogTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastReplayedLogTime", DbType.AnsiString, exchangeHealth.DRLastReplayedLogTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastCopiedLogSequence", DbType.AnsiString, exchangeHealth.DRLastCopiedLogSequence);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastInspectedLogSequence", DbType.AnsiString, exchangeHealth.DRLastInspectedLogSequence);
                Database.AddInParameter(dbCommand, Dbstring+"iDRLastReplayedLogSequence", DbType.AnsiString, exchangeHealth.DRLastReplayedLogSequence);
                Database.AddInParameter(dbCommand, Dbstring+"iPREdbFilePath", DbType.AnsiString, exchangeHealth.PREdbFilePath);
                Database.AddInParameter(dbCommand, Dbstring+"iDREdbFilePath", DbType.AnsiString, exchangeHealth.DREdbFilePath);
                Database.AddInParameter(dbCommand, Dbstring+"iPREdbFileSize", DbType.AnsiString, exchangeHealth.PREdbFileSize);
                Database.AddInParameter(dbCommand, Dbstring+"iPREdbFileLastWriteTime", DbType.AnsiString, exchangeHealth.PREdbFileLastWriteTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDREdbFileSize", DbType.AnsiString, exchangeHealth.DREdbFileSize);
                Database.AddInParameter(dbCommand, Dbstring+"iDREdbFileLastWriteTime", DbType.AnsiString, exchangeHealth.DREdbFileLastWriteTime);
                Database.AddInParameter(dbCommand, Dbstring+"iDataLagTime", DbType.AnsiString, exchangeHealth.DataLagTime);
#if ORACLE
                dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader myDrOperationResultReader = Database.ExecuteReader(dbCommand))
                {
                    if (myDrOperationResultReader.Read())
                    {
                        exchangeHealth.Id = Convert.ToInt32(myDrOperationResultReader[0]);
                    }
                    else
                    {
                        exchangeHealth = null;
                    }
                }

                return exchangeHealth;


            }
        }
       public static ExchangeHealthStatus Add(ExchangeHealthStatus exchangeHealth)
       {
           const string sp = "EXCHANGEHEALTHLOG_CREATE";
           

           using (var dbCommand = Database.GetStoredProcCommand(sp))
           {
              // db.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, exchangeHealth.Id);

               Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, exchangeHealth.InfraObjectId);
               Database.AddInParameter(dbCommand, Dbstring+"iPRStorageGroupName", DbType.AnsiString, exchangeHealth.PRStorageGroup);
               Database.AddInParameter(dbCommand, Dbstring+"iPRStorageMailboxDBName", DbType.AnsiString, exchangeHealth.PRMailboxDatabase);
               Database.AddInParameter(dbCommand, Dbstring+"iDRStorageGroupName", DbType.AnsiString, exchangeHealth.DRStorageGroup);
               Database.AddInParameter(dbCommand, Dbstring+"iDRStorageMailboxDBName", DbType.AnsiString, exchangeHealth.DRMailboxDatabase);

               Database.AddInParameter(dbCommand, Dbstring+"iPRMailboxDatabaseStatus", DbType.AnsiString, exchangeHealth.PRMailboxDatabaseStatus);
               Database.AddInParameter(dbCommand, Dbstring+"iDRMailboxDatabaseStatus", DbType.AnsiString, exchangeHealth.DRMailboxDatabaseStatus);
               Database.AddInParameter(dbCommand, Dbstring+"iPRLatestAvabLogTime", DbType.AnsiString, exchangeHealth.PRLatestAvabLogTime);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastCopiedLogTime", DbType.AnsiString, exchangeHealth.DRLastCopiedLogTime);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastInspectedLogTime", DbType.AnsiString, exchangeHealth.DRLastInspectedLogTime);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastReplayedLogTime", DbType.AnsiString, exchangeHealth.DRLastReplayedLogTime);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastCopiedLogSequence", DbType.AnsiString, exchangeHealth.DRLastCopiedLogSequence);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastInspectedLogSequence", DbType.AnsiString, exchangeHealth.DRLastInspectedLogSequence);
               Database.AddInParameter(dbCommand, Dbstring+"iDRLastReplayedLogSequence", DbType.AnsiString, exchangeHealth.DRLastReplayedLogSequence);
               Database.AddInParameter(dbCommand, Dbstring+"iPREdbFilePath", DbType.AnsiString, exchangeHealth.PREdbFilePath);
               Database.AddInParameter(dbCommand, Dbstring+"iDREdbFilePath", DbType.AnsiString, exchangeHealth.DREdbFilePath);
               Database.AddInParameter(dbCommand, Dbstring+"iPREdbFileSize", DbType.AnsiString, exchangeHealth.PREdbFileSize);
               Database.AddInParameter(dbCommand, Dbstring+"iPREdbFileLastWriteTime", DbType.AnsiString, exchangeHealth.PREdbFileLastWriteTime);
               Database.AddInParameter(dbCommand, Dbstring+"iDREdbFileSize", DbType.AnsiString, exchangeHealth.DREdbFileSize);
               Database.AddInParameter(dbCommand, Dbstring+"iDREdbFileLastWriteTime", DbType.AnsiString, exchangeHealth.DREdbFileLastWriteTime);
               Database.AddInParameter(dbCommand, Dbstring+"iPRLastGenLogSequence", DbType.AnsiString, exchangeHealth.PRLastGenLogSequence);
               Database.AddInParameter(dbCommand, Dbstring+"iDataLagTime", DbType.AnsiString, exchangeHealth.DataLagTime);


               using (IDataReader myDrOperationResultReader = Database.ExecuteReader(dbCommand))
               {
                   if (myDrOperationResultReader.Read())
                   {
                       exchangeHealth.Id = Convert.ToInt32(myDrOperationResultReader[0]);
                   }
                   else
                   {
                       exchangeHealth = null;
                   }
               }

               return exchangeHealth;


           }
       }
    }
}
