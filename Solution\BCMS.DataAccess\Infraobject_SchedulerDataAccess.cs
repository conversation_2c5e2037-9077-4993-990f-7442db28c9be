﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;
using log4net;

namespace Bcms.DataAccess
{
    public class Infraobject_SchedulerDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(Infraobject_SchedulerDataAccess));

        public static IList<Infraobject_Scheduler> GelAllInfraobjectScheduler()
        {
            IList<Infraobject_Scheduler> Infraobject_Scheduler = new List<Infraobject_Scheduler>();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("infraobject_scheduler_All"))
                {
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                        while (myHostReader.Read())
                        {
                            var host = new Infraobject_Scheduler();

                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.InfraobjectId = Convert.ToInt32(myHostReader["InfraobjectId"]);
                            host.WorkFlowId = Convert.ToInt32(myHostReader["WorkFlowId"]);
                            host.WorkFlowName = Convert.ToString(myHostReader["WorkFlowName"]);
                            host.ScheduleType = Convert.ToInt32(myHostReader["ScheduleType"]);
                            host.ScheduleTime = Convert.ToString(myHostReader["ScheduleTime"]);
                            host.IsSchedule = Convert.ToInt32(myHostReader["IsSchedule"]);
                            Infraobject_Scheduler.Add(host);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while get Infraobject Workflow Schedule list", exc);
            }

            return Infraobject_Scheduler;
        }

        public static Infraobject_Scheduler GelInfraobjectSchedulerById(int id)
        {
            Infraobject_Scheduler host = new Infraobject_Scheduler();

            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("infraobject_scheduler_GetById"))
                {

                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myHostReader = Database.ExecuteReader(dbCommand))
                    {
                      
                        while (myHostReader.Read())
                        {


                            host.Id = Convert.ToInt32(myHostReader["Id"]);
                            host.InfraobjectId = Convert.ToInt32(myHostReader["InfraobjectId"]);
                            host.WorkFlowId = Convert.ToInt32(myHostReader["WorkFlowId"]);
                            host.WorkFlowName = Convert.ToString(myHostReader["WorkFlowName"]);
                            host.ScheduleType = Convert.ToInt32(myHostReader["ScheduleType"]);
                            host.ScheduleTime = Convert.ToString(myHostReader["ScheduleTime"]);
                            host.IsSchedule = Convert.ToInt32(myHostReader["IsSchedule"]);
                            host.IsEnable = Convert.ToInt32(myHostReader["IsEnable"]);
                            host.IsActive = Convert.ToBoolean(myHostReader["IsActive"]);
                        }
                    }
                }

            }
            catch (Exception exc)
            {

                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Error In DAL While Executing Function Signature Infraobject_SchedulerDataAccess.GelInfraobjectSchedulerById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

            return host;
        }

        public static bool UpdateInfraobjectScheduler(Infraobject_Scheduler Infraobject_Scheduler)
        {

            try
            {
                using (DbCommand cmd = Database.GetStoredProcCommand("infraobject_sch_Update_Ser"))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, Infraobject_Scheduler.Id);
                    Database.AddInParameter(cmd, Dbstring + "IsSchedule", DbType.Int32, Infraobject_Scheduler.IsSchedule);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return true;
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while Update Infraobject Scheduler Details", exc);

            }
        }
    }
}
