﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Data;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Utility;
using Bcms.DataAccess.Base;
using Bcms.Common;
using log4net;


namespace Bcms.DataAccess
{
    public class EC2S3DatasyncDataAccess : BaseDataAccess
    {
        private static readonly ILog Logger = LogManager.GetLogger(typeof(EC2S3DatasyncDataAccess));

        public static bool AddEC2S3DataSyncComponantMonitor(BCMS.Common.EC2S3Datasync ComponantMonitor)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand(DbRoleName + "EC2S3DATASYNC_MONLOGS_CREATE"))
                {                   
                    Database.AddInParameter(dbCommand, Dbstring+"iInfraobjectId", DbType.Int32, ComponantMonitor.InfraObjectId);
                    Database.AddInParameter(dbCommand, Dbstring+"iSourceDataPath", DbType.AnsiString, Convert.ToString(ComponantMonitor.SourceDataPath));
                    Database.AddInParameter(dbCommand, Dbstring+"iEc2InstanceId", DbType.AnsiString, Convert.ToString(ComponantMonitor.Ec2InstanceId));
                    Database.AddInParameter(dbCommand, Dbstring+"iEc2InstanceStatus", DbType.AnsiString, Convert.ToString(ComponantMonitor.Ec2InstanceStatus));
                    Database.AddInParameter(dbCommand, Dbstring+"iS3BucketLocation", DbType.AnsiString, Convert.ToString(ComponantMonitor.S3BucketLocation));
                    Database.AddInParameter(dbCommand, Dbstring+"iS3BucketTimeStamp", DbType.AnsiString, Convert.ToString(ComponantMonitor.S3BucketTimeStamp));
                   
                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occured while insert AddEC2S3DataSyncComponantMonitor information :" + exc.InnerException.Message, exc);
            }
            return false;
        }

        public static bool AddEC2S3DataSyncReplicationMonitor(BCMS.Common.EC2S3DatasyncReplicationStatus ReplicationMonitor)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand(DbRoleName + "EC2S3DATASYNC_MONREP_CREATE"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iReplicationId", DbType.Int32, ReplicationMonitor.ReplicationId);
                  
                    Database.AddInParameter(dbCommand, Dbstring+"iReplicationType", DbType.AnsiString, Convert.ToString(ReplicationMonitor.ReplicationType));
                    Database.AddInParameter(dbCommand, Dbstring+"iReplicationStatus", DbType.AnsiString, Convert.ToString(ReplicationMonitor.ReplicationStatus));

                    Database.AddInParameter(dbCommand, Dbstring+"iSourceServerPath", DbType.AnsiString, Convert.ToString(ReplicationMonitor.SourceServerPath));
                    Database.AddInParameter(dbCommand, Dbstring+"iDestinationS3Path", DbType.AnsiString, Convert.ToString(ReplicationMonitor.DestinationS3Path));

                    Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, Convert.ToInt32(ReplicationMonitor.InfraobjectId));
                    //Database.AddInParameter(dbCommand, Dbstring+"iSyncstatus", DbType.AnsiString, Convert.ToString(ReplicationMonitor.Syncstatus));
                   
                    int value = Database.ExecuteNonQuery(dbCommand);
                    if (value > 0)
                    {
                        return true;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occured while insert AddEC2S3DataSyncReplicationMonitor information :" + exc.InnerException.Message, exc);
            }
            return false;
        }

//        public static EC2S3DatasyncReplicationStatus GetEmcReplicationById(int id)
//        {


//            try
//            {


//                using (DbCommand dbCommand = Database.GetStoredProcCommand("EC2S3_REP_MON_GETBYINFRAID"))
//                {
//                    var emcreplication = new EmcReplication();

//                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
//#if ORACLE
//                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
//                    {
//                        while (reader.Read())
//                        {
//                            emcreplication.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
//                            emcreplication.ReplicationType = Convert.IsDBNull(reader["ReplicationId"]) ? string.Empty : Convert.ToString(reader["ReplicationId"]);
//                            emcreplication.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
//                            emcreplication.DGroupName = Convert.IsDBNull(reader["DGroupName"]) ? string.Empty : Convert.ToString(reader["DGroupName"]);
//                            emcreplication.DGType = Convert.IsDBNull(reader["DGType"]) ? string.Empty : Convert.ToString(reader["DGType"]);
//                            emcreplication.DGSummetrixID = Convert.IsDBNull(reader["DGSummetrixID"]) ? string.Empty : Convert.ToString(reader["DGSummetrixID"]);
//                            emcreplication.RemoteSymmetrixID = Convert.IsDBNull(reader["RemoteSymmetrixID"]) ? string.Empty : Convert.ToString(reader["RemoteSymmetrixID"]);
//                            emcreplication.RdfRaGroupNumber = Convert.IsDBNull(reader["RDF_RA_GroupNumber"]) ? string.Empty : Convert.ToString(reader["RDF_RA_GroupNumber"]);
//                            emcreplication.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
//                            emcreplication.PendingTracks = Convert.IsDBNull(reader["PendingTracks"]) ? string.Empty : Convert.ToString(reader["PendingTracks"]);
//                            emcreplication.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);
//                        }
//                        return emcreplication;
//                    }
//                }
//            }
//            catch (Exception exc)
//            {
//                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get Parallel Server Information", exc);
//            }
//        }


    }
}
