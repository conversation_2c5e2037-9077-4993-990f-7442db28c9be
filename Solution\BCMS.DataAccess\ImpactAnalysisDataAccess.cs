﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class ImpactAnalysisDataAccess : BaseDataAccess
    {
        public static bool AddImpactAnalysis(ImpactAnalysis impactAnalysis)
        {
            try
            {
               

                using (DbCommand cmd = Database.GetStoredProcCommand("ImpactAnalysis_Create"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, impactAnalysis.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iEntityId", DbType.Int32, impactAnalysis.EntityId);
                    Database.AddInParameter(cmd, Dbstring+"iImpactType", DbType.String, impactAnalysis.ImpactType);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, impactAnalysis.Status);
                    Database.AddInParameter(cmd, Dbstring+"iImpactMessage", DbType.String, impactAnalysis.ImpactMessage);
                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessInsertOperation, "Exception occurred while insert impactAnalysis information", exc);
            }

        }

        public static bool UpdateImpactAnalysis(ImpactAnalysis impactAnalysis)
        {
            try
            {


                using (DbCommand cmd = Database.GetStoredProcCommand("ImpactAnalysis_Update"))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, impactAnalysis.Id);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, impactAnalysis.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iEntityId", DbType.Int32, impactAnalysis.EntityId);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, impactAnalysis.Status);
                    Database.AddInParameter(cmd, Dbstring+"iResolveMessage", DbType.String, impactAnalysis.ResolveMessage);
                    int isuccess = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while Update impactAnalysis information", exc);
            }

        }
    }
}
