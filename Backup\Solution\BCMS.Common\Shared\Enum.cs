﻿namespace Bcms.Common.Shared
{
    public enum DatabaseType
    {
        Undefined = 0,
        Oracle = 1,
        Sql = 2,
        Exchange = 3,
        OracleRac = 4,
        DB2 = 5
    }
    public enum DatabaseMode
    {

        Undefined = 0,
        Up=1,
        Down = 2,
        ReadWrite = 3,
        Standby = 4,
     
    }

    public enum ServerStatus
    {
        Undefined = 0,
        Up = 1,
        Down = 2
    }

    public enum ListnerStatus
    {
        Undefined = 0,
        Running = 1,
        Stopped = 2
    }

    public enum FastCopyMode
    {

        Undefined = 0,

        DataSync = 1,

        VMWareDataSync = 2,

        OracleWithDataSync = 3
    }

    public enum SqlAuthenticateType
    {

        Undefined = 0,
        Windows = 1,
        SqlServer = 2
    }
    public enum ServerType
    {
        None = 0,
        PRServer = 1,
        DRServer = 2,
        DRServerNext = 3
    }
    public enum HeatmapType
    {
        Server = 1,
        Database = 2,
        Os = 3,
        Storage = 4,
        Network = 5,
        Replication = 6
    }

    public enum ImpactType
    {
        ServerNotReachable = 1,
        ServerAuthenticationFailed = 2,
        DatabaseDown = 3,
        DatabaseListenerDown = 4,
        DatalagExceededConfiguredRPO = 5,
        ArchiveGapFound = 6,
        DataGuardNotInManagedState = 7

    }

    public enum LogLevel
    {
        None = 0,
        Warning = 1,
        Error = 2,
        Info = 3,
        Fatal = 4,
        Success = 5,
        Debug = 6
    }

    public enum DROperationType
    {
        None = 0,
        SwitchOver = 1,
        SwitchBack = 2,
        FailOver = 3,
        FailBack = 4,
        Customized = 5,
        DataSynchronization = 6
    }

    public enum DataGuardMode
    {

        Undefined = 0,

        Sync = 1,

        Async = 2
    }
    public enum AlertType
    {
        ServerInvalidAuthentication,
        ServerNoResponse,
        ServerUnhandled,
        ServiceUnhandled,
        MySqlUnhandled,
        NormalCopyFailure
    }

    public enum ExceptionType
    {
        Null,
        IndexOutOfRange,
        UnHandled,
        SSHUnHandled,
        NotSpecified,
        Invalid
    }

    public enum JobName
    {
        None = 0,
        MonitorServer = 1,
        RetrieveDataLag = 2,
        MonitorDatabase = 3,
        MonitorGlobalMirror = 4,
        PerformReplication = 5,
        MonitorDataGuard = 6,
        Ping = 7,
        SwitchOver = 8,
        SwitchBack = 9,
        MonitorApplication = 10,
        MonitorLogVolume = 11,
        PerformDatabaseBackup = 12,
        TwentyFourHoursReport = 13,
        ParallelWorkflowJob = 14,
        MSSqlNativeLogdetails = 15,
        MSSqlNativeEnableLogShipping = 16,
        CustomStart = 17,
        MonitorSnapMirror = 18,
        DataGuradArchiveGap = 19,
        MonitorSRDF = 20,
        MonitorApplicationService = 21,
        MonitorSRDFDataLag = 22,
        MonitorVmWare = 23,
        MonitorHADR = 24,
        ArchivedLogDelete = 25,
        MSSqlLogGeneration = 26,
        MonitorComponent = 27,
        FastCopyApplicationReplication = 28,
        SqlNativeJobStatus = 29,
        MonitorHitachiHUR=30,
        ApplyIncrementalLogs=31,
        MonitorMsExchange=32,
        MonitorServices=33
        //endsurendra
    }

    public enum Success
    {
        Updated,
        ApplyLog,
        Mount,
        UnMount,
        Replicated
    }

    public enum ConditionalOperationType
    {
        None = 0,
        Skip = 1,
        Retry = 2,
        Abort = 3,
        Next = 4,
        FailedNext=5
    }

    public enum ActionMode
    {
        None = 0,
        Auto = 1,
        Manual = 2

    }

    public enum InfraObjectState
    {
        None,
        Active,
        Maintenance,
        Locked,
        InActive,
        Replicating
    }
     
    public enum ReplicationState
    {
        Running = 0,
        PausingGM = 1,
        PerformingDataSynchronization = 2,
        DataSynchronizationCompleted = 3,
        ResumingGM = 4,
        MountingVolume = 5,
        ApplyingIncrementalLogs = 6,
        UnMountingVolume = 7,
        CompletedSuccessfully = 8,
        CompletedWithError = 9,
        DataSynchronizationFailed = 10,
        GlobalMirrorFatalState = 11,
        Aborted = 12,
        ExecutingReplicationCycle = 13,
        NoLogsAvailable = 14,
        PerformingDataSync = 15,
        DataSyncCompleted = 16
    }

    public enum NetworkIPStatus
    {
        InActive = 0,
        Active = 1
    }

    public enum ReplicationType
    {
        None = 0,
        IBMGlobalMirror = 1,
        OracleDataGuard = 2,
        DataSync = 3,
        MSSQLServerNative = 4,
        MSSCR = 5,
        NetAppSnapMirror = 6,
        EMCSRDF = 7,
        HITACHITrueCopy = 8,
        HITACHIUR_OracleFullDB = 9,
        VMWareDataSync = 10,
        VMWareGlobalmirror = 11,
        VMWareSnapmirror = 12,
        Sql2000FastCopy = 13,
        OracleWithDataSync = 14,
        DB2HADR = 15,
        HITACHIUR_OracleLogShipping = 16,
        EMCSRDF_OracleFullDB = 17,
        EMCSRDF_OracleLogShipping = 18,
        VMWareHitachiUR = 19,
        MSExchangeDAG = 20,
        DB2HADR9X = 21
        
        // end surendra
    }

    public enum ThreadTaskStateType
    {
        Created,

        Started,

        Completed,

        Failed,

        FailedTimeout
    }

    public enum WorkflowActionType
    {
        None = 0,
        PauseGlobalMirror = 1,
        ResumeGlobalMirror = 2,
        //   PerformDataSynchronization = 3, //surendra commented suggested from shivraj
        PerformNormalReplication = 3,
        PausePPRC = 4,
        ResumePPRC = 5,
        FailBackPPRC = 6,
        MKFlashBC = 7,
        MKFlashBD = 8,
        RMFlashBC = 9,
        RMFlashBD = 10,
        FailOverPPRC = 11,
        ApplyIncrementalLogs = 12,
        MountVG = 13,
        UnMountVG = 14,
        StartDB = 15,
        ShutDB = 16,
        SwitchOver = 17,
        SwitchBack = 18,
        PerformNormalReflicatin = 19,
        DGConnector = 20,
        DGVerifyDBModeAndRole = 21,
        DGCheckUserStatus = 22,
        DGJobStatus = 23,
        DGVerifyMaxSequenceNumber = 24,
        DGSwitchPrimaryToStandBy = 25,
        DGSwitchStandByToPrimary = 26,
        DGShutDownPrimary = 27,
        DGMountStandBy = 28,
        DGStartPrimary = 29,
        DGSwitchLogFile = 30,
        DGRecoverStandBy = 31,
        PowerOnMachine = 32,
        PowerOffMachine = 33,
        ExecuteOSCommand = 34,
        MakeGMIR = 35,
        RemoveGMIR = 36,
        MakeSession = 37,
        ChangeSession = 38,
        MountVGS = 39,
        UnmountVGS = 40,
        ExecuteDBCommand = 41,
        ExecuteStorageCommand = 42,
        StartDatabaseNoMount = 43,
        StartDatabaseMount = 44,
        OpenDatabase = 45,
        AlterDatabaseOpen = 46,
        BackUpControlFile = 47,
        CreateStandbyControlFile = 48,
        ShutDownDataBase = 49,
        SCP = 50,
        RestoreStandbyControlFile = 51,
        RecoverStandbyDatabaseFile = 52,
        RecoverDatabase = 53,
        CreateControlFileByScript = 54,
        CreateControlFileScript = 55,
        ISDBRunning = 56,
        ISDBDown = 57,
        AlterDatabaseMount = 58,
        AlterSystemLog = 59,
        CreateTempFile = 60,
        CreateTempFileTableSpace = 61,
        IsCheckPointCountOne = 62,
        StartDBStandBy = 63,
        StartDBReadWrite = 64,
        ReplicateStandByControlFile = 65,
        MSSqlGenerateLog = 66,
        MSSqlGenerateLastLog = 67,
        MSSqlRestoreLog = 68,
        MSSqlRestoreLastLog = 69,
        MSSqlKillProcessPR = 70,
        MSSqlRestoreDBWithRecovery = 71,
        MSSqlDBOption = 72,
        MSSqlVerifyLogSequence = 73,
        FastcopyReplication = 74,
        SwitchShutPrimaryDB = 75,
        SetJobQueProcess = 76,
        CheckJobQueProcess = 77,
        DeleteArchiveLogs = 78,
        IsFileSystemMounted = 79,
        ExecuteRMANCommand = 80,
        IsCGFormed = 81,
        VerifyLogSequence = 82,
        ActiveDatabaseReadWrite = 83,
        StartDatabase = 84,
        ImportVG = 85,
        ReplicateStandByCtrFile = 86,
        ReplicateTraceFile = 87,
        StartListner = 88,
        StopListner = 89,
        ReStartListner = 90,
        CheckGroupSyncStatus = 91,
        ChangeDNS = 92,
        DisableBackupJobPrimary = 93,
        DisableCopyJobDr = 94,
        DisableRestoreJobDr = 95,
        StartCopyJobDr = 96,
        StartRestoreJobDr = 97,
        KillTransactionProcess = 98,
        GenerateLastLog = 99,
        DisableSecondaryDbJob = 100,
        MakeSingleUser = 101,
        RestoreLastLogNative = 102,
        RestoreLogShipping = 103,
        CheckNoLoggingOperation = 104,
        BackUpPrimaryDatabase = 105,
        RestoreSecondaryDatabase = 106,
        RestoreLogShippingPrimary = 107,
        AddSecondaryLog = 108,
        RestoreLogshipSecondary = 109,
        UpdateSecondaryServerCopyJob = 110,
        UpdateSecondaryServerRestoreJob = 111,
        MigrateLogingPR = 112,
        MonitorLogShipping = 113,
        RunBackJob = 114,
        RunCopyJob = 115,
        RunRestoreJob = 116,
        RestoreSecondaryWithRecovery = 117,
        AddDNS = 118,
        DeleteDNS = 119,
        StartOracleListner = 120,
        StopOracleListner = 121,
        PreShutRedoCtrlScript = 122,
        CopyRedoCtrFile = 123,
        Status = 124,
        CheckStorageMailboxPath = 125,
        SetSCRPrerequisite = 126,
        EnableSCR = 127,
        ExecuteSudoCommand = 128,
        UnMountNFSVolume = 129,
        MountNFSVolume = 130,
        CheckFileExist = 131,
        CheckDBStandBy = 132,
        CheckDBReadWrite = 133,
        ExecuteCheckOSCommand = 134,
        FastCopyReplicateFile = 135,
        FastCopySyncFolders = 136,
        CompareNewSGMailboxPath = 137,
        UpdateTargetStorageGroup = 138,
        ResumeSCR = 139,
        ChecktargetDBStatus = 140,
        DismountMailboxdatabase = 141,
        SCRStatusWithCopyQueueLength = 142,
        DisableSCRRestorelogs = 143,
        CheckTargetDBFileStatus = 144,
        AllowFileRestoreToMailboxDB = 145,
        MountDatabase = 146,
        GetMaiboxListCountbeforeswitch = 147,
        MoveMailboxConfiguration = 148,
        GetMaiboxListCount = 149,
        ChangeRedoArchivePermission = 150,
        RevertRedoArchivePermission = 151,
        FastCopySyncArchiveFolder = 152,
        FastCopySyncRedoArchiveFolder = 153,
        InitiateBCVCopy = 154,
        CreateASMDisks = 155,
        StartASMInstance = 156,
        MountASMDiskGroups = 157,
        ActivatePhysicalStbyDB = 158,
        StopSchedulerJobs = 159,
        UnMountASMDiskGroups = 160,
        ShutASMInstance = 161,
        //30/07/2012 
        MsSqlSwitchOver = 162,
        MsSqlSwitchBack = 163,
        //31/07/2012
        RSyncFileReplication = 164,
        VMCreateSnapShot = 165,
        VMRemoveSnapShot = 166,
        VMRevertToSnapShot = 167,
        VMRegisterMachine = 168,
        //09/08/2012(For Abhay)
        UpdateSecondaryServerBackUpJob = 169,
        //(Kiran:Exchange)
        CreateStorageGroup = 170,
        //end 
        NativeSetdbOption = 171,
        NativeDeleteLastLog = 172,
        NativeRemovePRDRLS = 173,
        NativeRemoveLSPri = 174,
        NativeRemoveLSSEC = 175,
        ASMActivatePhysicalStbyDB = 176,
        DisableSchedulerJobs = 177,
        CheckDataFileErrorCountNull = 178,
        CheckCheckPointChange = 179,
        BackUpControlFileLocal = 180,
        DeleteFile = 181,
        RenameFile = 182,
        CopyFileLocal = 183,
        MoveFileLocal = 184,
        ExecuteCheckSqlCommand = 185,
        IsDBReadWrite = 186,
        IsDBStandBy = 187,
        MigrateLogingDR = 188,
        MSSqlKillProcessDR = 189,
        IsMachineRunning = 190,
        OnRegisterVirtualMachine = 191,
        ReplicateSnapShot = 192,
        ReplicateSCP = 193,
        ReplicateSnapShotFile = 194,
        ReplicateVMConfigFile = 195,
        SnapMirror_Update = 196,
        SnapMirror_Break = 197,
        SnapMirror_Vloume_Online = 198,
        SnapMirror_Vloume_Restrict = 199,
        SnapMirror_Resync = 200,
        ApplyASMLog = 201,
        NativeSetFileNameFlag = 202,
        NativeChangeRolePri_Sec = 203,
        ReplicateVirtualMachine = 204,

        //6/11/2012
        MSSqlKillProcess_Sec = 205,
        MSSqlKillProcess_Prim = 206,
        MSSqlGenerateLastLog_FC = 207,
        FileReplaceText = 208,
        ExecuteCheckStroageCommand = 209,
        WriteToRouter = 210,
        ApplicationStart = 211,
        ApplicationStop = 212,
        ApplicationMonitor = 213,
        IsApplicationUp = 214,
        IsApplicationDown = 215,
        //26/11/2012(STORAGE FOR EMCSYMCLI SERVER)
        EMC_ISDGRESENT = 216,
        EMC_DISABLEDEVICEGROUP = 217,
        EMC_ENABLEDEVICEGROUP = 218,
        EMC_SETACPDEVICEMODE = 219,
        EMC_FAILOVERDEVICEGROUP = 220,
        EMC_SWAPPERSONALITY = 221,
        EMC_RESUMEDEVICEGROUP = 222,
        EMC_SETASYNCMODE = 223,
        //27/11/2012
        HPUX_MOUNT = 224,
        HPUX_UNMOUNT = 225,
        HPUX_VGCHANGE_ACTIVE = 226,
        HPUX_VGCHANGE_DEACTIVE = 227,
        //28/11/2012
        ExecuteSymcliCommand = 228,
        ExecuteCheckDB2Command = 229,
        //30/11/2012
        Wait = 230,
        HPUX_IsVGACTIVE = 231,
        HPUX_EXPORTVGTOMAPFILE = 232,
        HPUX_IMPORTVGTOMAPFILE = 233,
        ExecuteSSH = 234,
        HP_MOUNTVGS = 235,
        HP_UNMOUNTVGS = 236,
        EMC_SWITCHOVERDG = 237,
        EMC_SWITCHBACKDG = 238,

        EMC_ISDGCONSISTENT = 239,
        HP_MOUNTVGPARALLEL = 240,
        HP_UNMOUNTVGPARALLEL = 241,
        EMC_CHECKDGTRACKSZERO = 242,
        // surendra from Shivraj
        WAITFORWORKFLOWACTION = 243,
        Start_HADRPrimary = 244,
        Stop_HADR = 245,
        IsHADRAActive = 246,
        DB2_IsDBUp = 247,
        DB2_SwitchOverDB = 248,
        DB2_SwitchBackDB = 249,
        DB2_ActivateDB = 250,
        DB2_DeActivateDBStart = 251,
        IsHADRRolePrimary = 252,
        IsHADRRoleStandby = 253,
        DB2_IsDatabasePrimary = 254,
        DB2_IsDatabaseStandby = 255,
        IsHADRStatePEER = 256,
        DB2_QuiesceDatabase = 257,
        DB2_UnQuiesceDatabase = 258,
        DB2_IsDatabaseQuiesced = 259,
        DB2_Terminate = 260,
        DB2_Start = 261,
        TakeOverHADR = 262,
        DB2_DeActivateDBTerminate = 263,
        Start_HADRStandBy = 264,
        //1/3/2013 abhay
        WinDGVerifyMaxSequenceNumber = 265,
        WinDGCheckUserStatus = 266,
        WinDGVerifyDBModeAndRole = 267,
        MigrateLoging_PR = 268,
        MigrateLoging_DR = 269,
        FastCopySql2000 = 270,
        MigrateServer_roles_PR = 271,
        MigrateServer_roles_DR = 272,
        ReplicateFastCopyFileWin = 273,
        ReplicateFastCopyFilePosix = 274,
        CancelRecoverMode = 275,
        GenerateRedoCtrlBKPScriptWin = 276,
        SwitchShutPRDBWin = 277,
        ExecuteRedoCtrlBKPWin = 278,
        StartDBReadWriteWin = 279,
        StartDBStandByWin = 280,
        ReplicateFastCopyFoldersWin = 281,
        ReplicateFastCopyFoldersPosix = 282,
        SyncArchives = 283,
        ReveseReplicateFastCopyFoldersPosix = 284,
        CheckProcessCount = 285,
        ExecuteWindowsProcess = 286,
        StartHypervisorMachine = 287,
        StopHypervisorMachine = 288,
        StartWindowsService = 289,
        StopWindowsService = 290,
        KillWindowsProcess = 291,
        WaitForPing = 292,
        DS_failOver = 293,
        DS_RMFLASH = 294,
        DS_CHECKCONSISTENCY = 295,
        DS_REVERSEFLASH = 296,
        DS_MKFLASH = 297,
        AIX_MountVG = 298,
        AIX_UNMountVG = 299,
        NativeMigrateServerRole_PR = 300,
        NativeMigrateServerRole_DR = 301,
        NativeVerifyLastRestoreFileName = 302,
        NativeVerifyDBState = 303,
        AIX_MOUNT = 304,
        AIX_DISMOUNT = 305,
        Hitachi_Horcctakeover = 306,
        Hitachi_Pairsplit = 307,
        Hitachi_PairReSync = 308,
        Hitachi_IsVolSuspended=309,
       // Native_SetDBOptionSql2012=316

        // added by Kiran @ 06/12/2013

         ReplicateFileParallel=310,
    VerifySwitchoverStatusIsPrimary=311,
    VerifySwitchoverStatusIsStandby=312,
    SetArchiveLogDestination=313,
    ApplyJobQeueProcess=314,
    ExecuteCheckSymcliCommand=315,
    Native_SetDBOptionSql2012=316,
    AlterDatabaseFlashBackOn=317,
    CreateRestorePoint=318,
    AlterDatabaseActiveStby=319,
    FlashBackToRestorePoint=320,
    AlterDatabaseConvertPhysicalStby=321,
    StartUpMountForce=322,
    DropRestorePoint=323,
   
    AlterDatabaseFlashBackOff=324,
    AlterSystemSetLogArchiveDestDefer=325,
    AlterSystemSetLogArchiveDestEnable=326,
    AlterStbyDBtoMaxPerformance=327,
    AlterDBRecoverManagedStbyDBCancel=328,
    CheckFlashBackOn = 329,
    //Added By JP on 09/06/2014
        EMC_ISSPLIT = 330,
        EMC_ISISSYNCINPROGRESS = 331,
        EMC_SPLITDEVICEGROUP = 332,
        EMC_ESTABLISHDEVICEGROUP = 333,

        SendAlert = 334,
        ExecuteShellProcess = 335,
        ExecuteSwiftProcess = 336,
        ExecuteT24Process= 337,
        ChangeCIProperties = 338,
        Win_MontVol = 339,
        Win_DeMontVol = 340,
        VM_UnRegister = 341,
        VM_ShutDown = 342,
        VM_Mount = 343,
        VM_UnMount = 344,
        DetachLunFromESXI = 345,
        RemoveLunFromESXI = 346,
        UpdateVMNetworkInfo = 347,
        ExecutePowerShellScript = 348,
        VMGuestStartService = 349,
        VMGuestStopService = 350,
        Hitachi_IsPairSync = 351,
        Hitachi_IsPairSuspended = 352,
        DB2_IsDBDown = 353,
        ExecuteScript = 354,
        ExecuteScriptWithPassword = 355,
        Sleep = 356,
        ExecuteSqlScriptWithPassword= 357,
        ExecuteScriptwithEnvPassword= 358,
        ExecuteSqlscriptWithEnvPassword= 359,
        ExecuteScriptalongPassword= 360,
        StartDataBaseReadOnly= 361,
        KillUnixProcess= 362,
        Win_KillProcessById= 363,
        win_killProcessByName = 364,
        win_IsServiceRunning= 365,
        win_IsServiceDown= 366,
        win_IsProcessRunning= 367,
        win_IsProcessDown= 368,
        win_ExecuteProcess= 369,
        win_IsFileExist= 370,
        win_FileDelete= 371,
        SyncArchive= 372,
        IsFileExist= 373,
        IsFolderExist= 374,
        IsProcessRunning= 375,
        KillProcess= 376,
        ExecuteConsoleCommand= 377,
        ExecuteCheckConsoleCommand= 378,
        ExecuteDS_MKFlash= 379,
        ExecuteDS_ReSyncFlash= 380,
        ExecuteDS_CheckFlashZeroTracks= 381,
        ExecuteDSCommand= 382,
        ExecuteCheckDSCommand = 383,
        ExecuteDS_WaitforflashTracksZero= 384,
        ExecuteScriptWithUserPassword = 385,
        ExecuteDS_PausePPRC= 386,
        ExecuteDS_ResumePPRC= 387,
        ExecuteDS_FailOverPPRC= 388,
        ExecuteDS_RMFlash= 389,
        ExecuteDS_ReverseFlash = 390,
        ExecuteDS_FailBackPPRC = 391,
        ExecuteDS_RevertFlash = 392,
        ExecuteDS_CommitFlash = 393,
        ExecuteDS_ChangeSessionAdd= 394,
        ExecuteDS_ChangeSessionRemove = 395,
        ExecuteDS_MakeSession= 396,
        ExecuteDS_MakeGMIR= 397,
        ExecuteDS_RemoveGMIR = 398,
        ExecuteDS_PauseGMIR = 399,
        ExecuteDS_ResumeGMIR = 400,
        Execute3270= 401,
        ExecuteAIXProcess= 402,
        ExecutePowerCLICommand = 403,
        ExecutePowerCLITasks= 404,
        ExecuteCheckPowerCLICommand = 405,
        ExecuteSSHTasks= 406,
        ExecuteSVCTask= 407,
        ExecuteCheckSVCTask= 408,
        SVCWaitforRCState= 409,
        SVCStartReplication= 410,
        SVCStopReplication= 411,
        SVCStartReverseReplication = 412,
        SRMInitiateRecoveryPlan = 413,
        StartMSCluster = 414,
        StopMSCluster= 415,
        ChangeNodeIP= 416,
         ExecuteCPSL= 417,
    ExecuteNohup= 418,
    }

    public enum GroupType
    {
        ApplicationGroup = 1,

        DBGroup = 2,

        VirtualGroup = 3
    }

    public enum OperationType
    {
        AIXOperations = 1,

        GMOperations = 2,

        OracleDB = 3,

        DROperations = 4,

        StorageOperations = 5,

        DNSManager = 6,

        SRDFProcess = 7
    }

    public enum GroupWorkflowOperation : int
    {
        None = 0,

        StartSwitchOver = 1,

        SwitchOverCompleted = 2,

        SwitchOverError = 3,

        SwitchBackStart = 4,

        SwitchBackCompleted = 5,

        SwitchBackError = 6,

        FailOverStart = 7,

        FailOverCompleted = 8,

        FailOverError = 9,

        FailBackStart = 10,

        FailBackCompleted = 11,

        FailBackError = 12,

        CustomStart = 13,

        CustomCompleted = 14,

        CustomError = 15,

        StartPowerOn = 16,

        PowerOnCompleted = 17,

        PowerOnError = 18,

        StartPowerOff = 19,

        PowerOffCompleted = 20,

        PowerOffError = 21,

        SwitchOverProcessing = 22,

        SwitchBackProcessing = 23,

        CustomizedProcessing = 24,

        CustomActionStart = 25,

        CustomActionCompleted = 26,

        CustomActionError = 27,

        CustomActionProcessing = 28,

        FailOverProcessing = 29,

    }

    public enum AlertNotificationType
    {
        DataLag = 1,
        CGFormed = 2,
        NormalReplication = 3,
        DBcheck = 4,
        ApplyLogs = 5,
        Server = 6,
        ArchiveGap = 7,
        RecoveryMode = 8,
        DBListenerCheck = 9,
        PRServer = 10,
        DRServer = 11,
        PRDBcheck = 12,
        DRDBcheck = 13,
        PRDBListenerCheck = 14,
        DRDBListenerCheck = 15,
        HADRRole = 16,
        HADRState = 17,
        HADRConnectionStatus = 18,
        HADRSyncMode = 19,
        HADRLogGap = 20,
        PRBackUpJob=21,
        DRCopyJob=22,
        DRRestoreJob=23,
        ServiceCheck=24
    }

    public enum AlertCategory
    {
        Group = 1,
        GlobalMirror = 2,
        Application = 3,
        Service=4
    }
    // End:
    
    public enum NodeStatus
    {
      
        Undefined = 0,
       
        Up = 1,
      
        Down = 2
    }

    public enum GetWorkflowCondition
    {

        Undefined = 0,

        ActionId = 1,

        TargetId = 2,

        DirectionId=3
    }

    public enum GetWorkflowDirection
    {

        Undefined = 0,

        Up = 1,

        Down = 2
    }
}
