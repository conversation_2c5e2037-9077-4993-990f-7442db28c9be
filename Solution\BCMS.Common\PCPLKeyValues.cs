﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "PCPLKeyValues", Namespace = "http://www.Bcms.com/types")]
    public class PCPLKeyValues
    {
        #region Properties

        [DataMember]
        public int Id
        {
            get;
            set;
        }

        [DataMember]
        public string KeyName
        {
            get;
            set;
        }

        [DataMember]
        public string KeyValue
        {
            get;
            set;
        }

        [DataMember]
        public DateTime CreatedAt
        {
            get;
            set;
        }

        #endregion Properties
    }
}
