﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "NetworkIP", Namespace = "http://www.Bcms.com/types")]

    public class NetworkIP : BaseEntity
    {
         #region Properties

        [DataMember]
        public string Circle
        {
            get;
            set;
        }


        [DataMember]
        public string IP
        {
            get;
            set;
        }

        [DataMember]
        public string Switch
        {
            get;
            set;
        }

        [DataMember]
        public int Status
        {
            get;
            set;
        }

        #endregion

        #region Constructor

        public NetworkIP()
            : base()
        {
        }

        #endregion

    }
}
