﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;


namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "SqlnativeHealth", Namespace = "http://www.BCMS.com/types")]
    public class SqlnativeHealth : BaseEntity
    {
        #region Properties
        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string MSSQL_Database_State_PR { get; set; }

        [DataMember]
        public string MSSQL_Database_State_DR { get; set; }

        [DataMember]
        public string MSSQL_Database_Recovery_Model_PR { get; set; }

        [DataMember]
        public string MSSQL_Database_Recovery_Model_DR { get; set; }

        [DataMember]
        public string Transaction_Log_Shipping_status_PR { get; set; }

        [DataMember]
        public string Transaction_Log_Shipping_status_DR { get; set; }

        [DataMember]
        public string Database_Restrict_Access_Status_PR  { get; set; }

        [DataMember]
        public string Database_Restrict_Access_Status_DR  { get; set; }

        [DataMember]
        public string Backup_Job_Status { get; set; }

        [DataMember]
        public string Copy_Job_Status { get; set; }

        [DataMember]
        public string Restore_Job_Status { get; set; }

        [DataMember]
        public string Backup_Job_Execution_Status { get; set; }

        [DataMember]
        public string Copy_Job_Execution_Status { get; set; }

        [DataMember]
        public string Restore_Job_Execution_Status { get; set; }
               
        

        #endregion

        #region Constructor
        public SqlnativeHealth(): base()
        {
        }
        #endregion
    }
}
