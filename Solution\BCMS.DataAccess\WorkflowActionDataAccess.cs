﻿using System;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.ExceptionHandler;
using Bcms.DataAccess.Base;

namespace Bcms.DataAccess
{
    public class WorkflowActionDataAccess : BaseDataAccess
    {
        public static WorkflowAction GetWorkflowActionById(int id)
        {
            var workflowAction = new WorkflowAction();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOWACTION_GETBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iid", DbType.Int32, id);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTIONCur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {
                            workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflowAction.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            workflowAction.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : CryptographyHelper.Md5Decrypt(reader["Description"].ToString());
                            workflowAction.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                            workflowAction.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            workflowAction.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
                            workflowAction.ActionType = Convert.IsDBNull(reader["ActionType"]) ? 0 : Convert.ToInt32(reader["ActionType"]);
                            workflowAction.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
                            workflowAction.DatabaseId = Convert.IsDBNull(reader["DatabaseId"]) ? 0 : Convert.ToInt32(reader["DatabaseId"]);
                            workflowAction.ExceptPassword = Convert.IsDBNull(reader["ExceptPassword"]) ? string.Empty : Convert.ToString(reader["ExceptPassword"]);
                            workflowAction.SessionAddRemove = Convert.IsDBNull(reader["SessionAddRemove"]) ? 0 : Convert.ToInt32(reader["SessionAddRemove"]);
                            workflowAction.Session = Convert.IsDBNull(reader["SessionName"]) ? string.Empty : Convert.ToString(reader["SessionName"]);
                            workflowAction.Luns = Convert.IsDBNull(reader["Luns"]) ? string.Empty : Convert.ToString(reader["Luns"]);
                            workflowAction.MountPoint = Convert.IsDBNull(reader["MountPoint"]) ? string.Empty : Convert.ToString(reader["MountPoint"]);
                            workflowAction.VG = Convert.IsDBNull(reader["VG"]) ? string.Empty : Convert.ToString(reader["VG"]);
                            workflowAction.SwitchOverSession = Convert.IsDBNull(reader["SwitchOverSession"]) ? string.Empty : Convert.ToString(reader["SwitchOverSession"]);
                            workflowAction.Command = Convert.IsDBNull(reader["Command"]) ? string.Empty : Convert.ToString(reader["Command"]);
                            workflowAction.PrDr = Convert.IsDBNull(reader["PrDr"]) ? 0 : Convert.ToInt32(reader["PrDr"]);

                            workflowAction.StandByControlFile = Convert.IsDBNull(reader["StandByControlFile"]) ? string.Empty : Convert.ToString(reader["StandByControlFile"]);
                            workflowAction.File = Convert.IsDBNull(reader["SCPFile"]) ? string.Empty : Convert.ToString(reader["SCPFile"]);
                            workflowAction.ControlFile = Convert.IsDBNull(reader["ControlFile"]) ? string.Empty : Convert.ToString(reader["ControlFile"]);
                            workflowAction.TargetServer = Convert.IsDBNull(reader["TargetSession"]) ? 0 : Convert.ToInt32(reader["TargetSession"]);
                            workflowAction.ScriptFile = Convert.IsDBNull(reader["ScriptFile"]) ? string.Empty : Convert.ToString(reader["ScriptFile"]);

                            workflowAction.TempFile = Convert.IsDBNull(reader["TempFile"]) ? string.Empty : Convert.ToString(reader["TempFile"]);
                            workflowAction.JobQueue = Convert.IsDBNull(reader["JobQueue"]) ? string.Empty : Convert.ToString(reader["JobQueue"]);
                            workflowAction.Fastcopy = Convert.IsDBNull(reader["FastCopyReplication"]) ? 0 : Convert.ToInt32(reader["FastCopyReplication"]);
                            workflowAction.Listener = Convert.IsDBNull(reader["Listner"]) ? string.Empty : Convert.ToString(reader["Listner"]);

                            workflowAction.DNSServer = Convert.IsDBNull(reader["DNSServer"]) ? 0 : Convert.ToInt32(reader["DNSServer"]);
                            workflowAction.ExistingHost = Convert.IsDBNull(reader["ExistingHost"]) ? string.Empty : Convert.ToString(reader["ExistingHost"]);
                            workflowAction.ExistingIp = Convert.IsDBNull(reader["ExistingIp"]) ? string.Empty : Convert.ToString(reader["ExistingIp"]);
                            workflowAction.NewHost = Convert.IsDBNull(reader["NewHost"]) ? string.Empty : Convert.ToString(reader["NewHost"]);
                            workflowAction.NewIp = Convert.IsDBNull(reader["NewIp"]) ? string.Empty : Convert.ToString(reader["NewIp"]);
                            workflowAction.DomainName = Convert.IsDBNull(reader["DomainName"]) ? string.Empty : Convert.ToString(reader["DomainName"]);
                            ////26062012
                            workflowAction.LocalMountPoints = Convert.IsDBNull(reader["LocalMountPoints"]) ? string.Empty : Convert.ToString(reader["LocalMountPoints"]);
                            workflowAction.RemoteMountPoints = Convert.IsDBNull(reader["RemoteMountPoints"]) ? string.Empty : Convert.ToString(reader["RemoteMountPoints"]);
                            workflowAction.HostName = Convert.IsDBNull(reader["HostName"]) ? string.Empty : Convert.ToString(reader["HostName"]);
                            ////02/07/2012 
                            workflowAction.IsUseSudo = Convert.IsDBNull(reader["IsUseSudo"]) ? 0 : Convert.ToInt32(reader["IsUseSudo"]);
                            workflowAction.CheckOutput = Convert.IsDBNull(reader["CheckOutput"]) ? string.Empty : Convert.ToString(reader["CheckOutput"]);
                            workflowAction.IsReturn = Convert.IsDBNull(reader["IsReturn"]) ? 0 : Convert.ToInt32(reader["IsReturn"]);
                            ////03/07/2012            
                            workflowAction.FastCopyPath = Convert.IsDBNull(reader["FastCopyPath"]) ? string.Empty : Convert.ToString(reader["FastCopyPath"]);
                            workflowAction.SourceFile = Convert.IsDBNull(reader["SourceFile"]) ? string.Empty : Convert.ToString(reader["SourceFile"]);
                            workflowAction.SourceFolder = Convert.IsDBNull(reader["SourceFolder"]) ? string.Empty : Convert.ToString(reader["SourceFolder"]);
                            workflowAction.TargetFolder = Convert.IsDBNull(reader["TargetFolder"]) ? string.Empty : Convert.ToString(reader["TargetFolder"]);
                            ////25/07/2012
                            workflowAction.DiskGroup = Convert.IsDBNull(reader["DiskGroup"]) ? string.Empty : Convert.ToString(reader["DiskGroup"]);
                            workflowAction.DiskName = Convert.IsDBNull(reader["DiskName"]) ? string.Empty : Convert.ToString(reader["DiskName"]);
                            //workflowAction.PrStorageImageId = reader.IsDBNull(FLD_PRSTORAGEIMAGEID) ? string.Empty : reader.GetString(FLD_PRSTORAGEIMAGEID);
                            //workflowAction.DrStorageImageId = reader.IsDBNull(FLD_DRSTORAGEIMAGEID) ? 0 : reader.GetInt32(FLD_DRSTORAGEIMAGEID);
                            ////31/07/2012
                            workflowAction.RsyncPath = Convert.IsDBNull(reader["RsyncPath"]) ? string.Empty : Convert.ToString(reader["RsyncPath"]);
                            workflowAction.TargetFile = Convert.IsDBNull(reader["TargetFile"]) ? string.Empty : Convert.ToString(reader["TargetFile"]);
                            workflowAction.MachineName = Convert.IsDBNull(reader["MachineName"]) ? string.Empty : Convert.ToString(reader["MachineName"]);
                            workflowAction.SnapShotName = Convert.IsDBNull(reader["SnapShotName"]) ? string.Empty : Convert.ToString(reader["SnapShotName"]);
                            workflowAction.TimeOut = Convert.IsDBNull(reader["TimeOut"]) ? string.Empty : Convert.ToString(reader["TimeOut"]);
                            workflowAction.VirtualMachine = Convert.IsDBNull(reader["VirtualMachine"]) ? string.Empty : Convert.ToString(reader["VirtualMachine"]);
                            ////17/08/2012          
                            workflowAction.DestinationPath = Convert.IsDBNull(reader["DestinationPath"]) ? string.Empty : Convert.ToString(reader["DestinationPath"]);
                            //workflowAction.SnapMirrorVolume = reader.IsDBNull(FLD_SNAPMIRRORVOLUME) ? string.Empty : reader.GetString(FLD_SNAPMIRRORVOLUME);
                            workflowAction.TargetMachine = Convert.IsDBNull(reader["TargetMachine"]) ? string.Empty : Convert.ToString(reader["TargetMachine"]);

                            workflowAction.VmPath = Convert.IsDBNull(reader["VmPath"]) ? string.Empty : Convert.ToString(reader["VmPath"]);
                            workflowAction.OriginalText = Convert.IsDBNull(reader["OriginalText"]) ? string.Empty : Convert.ToString(reader["OriginalText"]);
                            workflowAction.NewText = Convert.IsDBNull(reader["NewText"]) ? string.Empty : Convert.ToString(reader["NewText"]);

                            //16/11/2012
                            workflowAction.RouterConfiguration = Convert.IsDBNull(reader["RouterConfiguration"]) ? string.Empty : Convert.ToString(reader["RouterConfiguration"]);
                            //20/11/2012
                            workflowAction.InterfacePassword = Convert.IsDBNull(reader["InterfacePassword"]) ? string.Empty : Convert.ToString(reader["InterfacePassword"]);
                            //26/11/2012
                            workflowAction.DeviceGroup = Convert.IsDBNull(reader["DeviceGroup"]) ? string.Empty : Convert.ToString(reader["DeviceGroup"]);
                            //27/11/2012
                            workflowAction.FileSystem = Convert.IsDBNull(reader["FileSystem"]) ? string.Empty : Convert.ToString(reader["FileSystem"]);
                            //27/11/2012
                            workflowAction.WaitTime = Convert.IsDBNull(reader["WaitTime"]) ? string.Empty : Convert.ToString(reader["WaitTime"]);
                            //08/12/2012
                            workflowAction.ApplicationName = Convert.IsDBNull(reader["ApplicationName"]) ? string.Empty : Convert.ToString(reader["ApplicationName"]);
                            //09/12/2012
                            workflowAction.MapFile = Convert.IsDBNull(reader["MapFile"]) ? string.Empty : Convert.ToString(reader["MapFile"]);
                            workflowAction.Task = Convert.IsDBNull(reader["Task"]) ? string.Empty : Convert.ToString(reader["Task"]);
                            //12/12/2012
                            workflowAction.FileSystemMountPoint = Convert.IsDBNull(reader["FileSystemMountPoint"]) ? string.Empty : Convert.ToString(reader["FileSystemMountPoint"]);
                            //03/01/2013
                            workflowAction.WorkFlowId = Convert.IsDBNull(reader["WorkFlowId"]) ? 0 : Convert.ToInt32(reader["WorkFlowId"]);
                            workflowAction.WorkFlowActionId = Convert.IsDBNull(reader["WorkFlowActionId"]) ? string.Empty : Convert.ToString(reader["WorkFlowActionId"]);
                            workflowAction.DependencyType = Convert.IsDBNull(reader["DependencyType"]) ? 0 : Convert.ToInt32(reader["DependencyType"]);
                            workflowAction.Time = Convert.IsDBNull(reader["WFTime"]) ? string.Empty : Convert.ToString(reader["WFTime"]);
                            //24/01/2013
                            workflowAction.TargetDatabase = Convert.IsDBNull(reader["TargetDatabase"]) ? 0 : Convert.ToInt32(reader["TargetDatabase"]);
                            //01/04/2013
                            workflowAction.BackUpFile = Convert.IsDBNull(reader["BackUpFile"]) ? string.Empty : Convert.ToString(reader["BackUpFile"]);
                            workflowAction.TraceFile = Convert.IsDBNull(reader["TraceFile"]) ? string.Empty : Convert.ToString(reader["TraceFile"]);
                            //15/04/2013
                            workflowAction.Expression = Convert.IsDBNull(reader["Expression"]) ? 0 : Convert.ToInt32(reader["Expression"]);
                            workflowAction.ProcessCount = Convert.IsDBNull(reader["ProcessCount"]) ? 0 : Convert.ToInt32(reader["ProcessCount"]);
                            workflowAction.RTO = Convert.IsDBNull(reader["RTO"]) ? string.Empty : Convert.ToString(reader["RTO"]);
                            //12/06/2013
                            workflowAction.HDisc = Convert.IsDBNull(reader["HDisc"]) ? string.Empty : Convert.ToString(reader["HDisc"]);
                            workflowAction.HitachiHorComInstance = Convert.IsDBNull(reader["HitachihorcomInstance"]) ? string.Empty : Convert.ToString(reader["HitachihorcomInstance"]);

                            workflowAction.RestorePoint = Convert.IsDBNull(reader["RestorePoint"]) ? string.Empty : Convert.ToString(reader["RestorePoint"]);

                            //JP added
                            workflowAction.AlertText = Convert.IsDBNull(reader["AlertText"])
                                ? string.Empty
                                : Convert.ToString(reader["AlertText"]);
                            workflowAction.AlertModeType = Convert.IsDBNull(reader["AlertModeType"])
                                ? 0
                                : Convert.ToInt32(reader["AlertModeType"]);

                            workflowAction.VMIsClustered = Convert.IsDBNull(reader["VMIsClustered"])
                               ? 0
                               : Convert.ToInt32(reader["VMIsClustered"]);

                            workflowAction.ProcessFlow = Convert.IsDBNull(reader["ProcessFlow"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessFlow"]);

                            workflowAction.VolName = Convert.IsDBNull(reader["VolName"])
                                ? string.Empty
                                : Convert.ToString(reader["VolName"]);
                            workflowAction.Drive = Convert.IsDBNull(reader["Drive"]) ? string.Empty : Convert.ToString(reader["Drive"]);
                            workflowAction.SubnetMask = Convert.IsDBNull(reader["SubnetMask"])
               ? string.Empty
               : Convert.ToString(reader["SubnetMask"]);
                            workflowAction.GateWay = Convert.IsDBNull(reader["GateWay"])
                                ? string.Empty
                                : Convert.ToString(reader["GateWay"]);
                            workflowAction.PrimaryDNS = Convert.IsDBNull(reader["PrimaryDNS"])
                                ? string.Empty
                                : Convert.ToString(reader["PrimaryDNS"]);
                            workflowAction.SecondaryDNS = Convert.IsDBNull(reader["SecoundaryDNS"])
                                ? string.Empty
                                : Convert.ToString(reader["SecoundaryDNS"]);
                            workflowAction.ServiceName = Convert.IsDBNull(reader["ServiceName"])
                                ? string.Empty
                                : Convert.ToString(reader["ServiceName"]);
                            workflowAction.Appuser = Convert.IsDBNull(reader["Appuser"]) ?
                                 string.Empty
                                : Convert.ToString(reader["Appuser"]);
                            workflowAction.ProcessID = Convert.IsDBNull(reader["ProcessId"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessId"]);
                            workflowAction.Shellprompt = Convert.IsDBNull(reader["Shellprompt"])
                                ? string.Empty
                                : Convert.ToString(reader["Shellprompt"]);
                            workflowAction.DSCSLIServer = Convert.IsDBNull(reader["DSCSLISERVER"]) ? 0 : Convert.ToInt32(reader["DSCSLISERVER"]);
                            workflowAction.HMCServer = Convert.IsDBNull(reader["hmcserver"]) ? 0 : Convert.ToInt32(reader["hmcserver"]);
                            workflowAction.LSSID = Convert.IsDBNull(reader["LSSID"]) ? string.Empty : Convert.ToString(reader["LSSID"]);

                            workflowAction.RelationshipId = Convert.IsDBNull(reader["RelationshipId"]) ? string.Empty : Convert.ToString(reader["RelationshipId"]);
                            workflowAction.CheckState = Convert.IsDBNull(reader["CheckState"]) ? string.Empty : Convert.ToString(reader["CheckState"]);

                            workflowAction.RecoveryPlan = Convert.IsDBNull(reader["RecoveryPlan"]) ? string.Empty : Convert.ToString(reader["RecoveryPlan"]);
                            workflowAction.ClusterName = Convert.IsDBNull(reader["ClusterName"]) ? string.Empty : Convert.ToString(reader["ClusterName"]);
                            workflowAction.ClusterGroupResource = Convert.IsDBNull(reader["ClusterGroupResource"]) ? string.Empty : Convert.ToString(reader["ClusterGroupResource"]);

                            workflowAction.EmailSuccess = Convert.IsDBNull(reader["EmailSuccess"]) ? string.Empty : Convert.ToString(reader["EmailSuccess"]);
                            workflowAction.EmailFail = Convert.IsDBNull(reader["EmailFail"]) ? string.Empty : Convert.ToString(reader["EmailFail"]);
                            workflowAction.SmsSuccess = Convert.IsDBNull(reader["SmsSuccess"]) ? string.Empty : Convert.ToString(reader["SmsSuccess"]);
                            workflowAction.SmsFail = Convert.IsDBNull(reader["SmsFail"]) ? string.Empty : Convert.ToString(reader["SmsFail"]);
                            workflowAction.AlertMechanismType = Convert.IsDBNull(reader["AlertMechanismType"]) ? string.Empty : Convert.ToString(reader["AlertMechanismType"]);
                            workflowAction.ScriptBlock = Convert.IsDBNull(reader["ScriptBlock"]) ? string.Empty : Convert.ToString(reader["ScriptBlock"]);
                            //Url & HtmlContent Uncommented by Mridul and required fields in Hurl and Hit Chech Url Actions
                            workflowAction.URL = Convert.IsDBNull(reader["URL"]) ? string.Empty : Convert.ToString(reader["URL"]);
                            workflowAction.HTMLCONTENTS = Convert.IsDBNull(reader["HTMLContents"]) ? string.Empty : Convert.ToString(reader["HTMLContents"]);
                            workflowAction.ControllerType = Convert.IsDBNull(reader["ControllerType"]) ? string.Empty : Convert.ToString(reader["ControllerType"]);
                            workflowAction.ControllerLocation = Convert.IsDBNull(reader["ControllerLocation"]) ? string.Empty : Convert.ToString(reader["ControllerLocation"]);
                            workflowAction.DefineValue = Convert.IsDBNull(reader["DefineValue"]) ? string.Empty : Convert.ToString(reader["DefineValue"]);
                            workflowAction.SharingOption = Convert.IsDBNull(reader["SharingOption"]) ? string.Empty : Convert.ToString(reader["SharingOption"]);
                            workflowAction.ClusterSharedVolumeStatus = Convert.IsDBNull(reader["ClusterSharedVolumeStatus"]) ? string.Empty : Convert.ToString(reader["ClusterSharedVolumeStatus"]);
                            workflowAction.ZoneName = Convert.IsDBNull(reader["ZoneName"]) ? string.Empty : Convert.ToString(reader["ZoneName"]);
                            
                            workflowAction.CellNo = Convert.IsDBNull(reader["CellNo"]) ? string.Empty : Convert.ToString(reader["CellNo"]);
                            workflowAction.EmailId = Convert.IsDBNull(reader["EmailId"]) ? string.Empty : Convert.ToString(reader["EmailId"]);
                            workflowAction.Resource = Convert.IsDBNull(reader["ClusterResource"]) ? string.Empty : Convert.ToString(reader["ClusterResource"]);
                            workflowAction.AlertUsers = Convert.IsDBNull(reader["AlertUsers"]) ? string.Empty : Convert.ToString(reader["AlertUsers"]);
                            workflowAction.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            workflowAction.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            workflowAction.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            workflowAction.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());

                            return workflowAction;
                        }

                        return workflowAction;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get WorkflowAction information by Id - " + id, exc);

            }

        }

        public static WorkflowAction GetWorkflowActionByDROperaionId(int droperationId)
        {
            var workflowAction = new WorkflowAction();
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOWACTION_GETBYDROPRID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iid", DbType.Int32, droperationId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(dbCommand))
                    {
                        while (reader.Read())
                        {

                            workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            workflowAction.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            workflowAction.Description = Convert.IsDBNull(reader["Description"]) ? string.Empty : CryptographyHelper.Md5Decrypt(reader["Description"].ToString());
                            workflowAction.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);
                            workflowAction.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            workflowAction.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
                            workflowAction.ActionType = Convert.IsDBNull(reader["ActionType"]) ? 0 : Convert.ToInt32(reader["ActionType"]);
                            workflowAction.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
                            workflowAction.DatabaseId = Convert.IsDBNull(reader["DatabaseId"]) ? 0 : Convert.ToInt32(reader["DatabaseId"]);
                            workflowAction.ExceptPassword = Convert.IsDBNull(reader["ExceptPassword"]) ? string.Empty : Convert.ToString(reader["ExceptPassword"]);
                            workflowAction.SessionAddRemove = Convert.IsDBNull(reader["SessionAddRemove"]) ? 0 : Convert.ToInt32(reader["SessionAddRemove"]);
                            workflowAction.Session = Convert.IsDBNull(reader["SessionName"]) ? string.Empty : Convert.ToString(reader["SessionName"]);
                            workflowAction.Luns = Convert.IsDBNull(reader["Luns"]) ? string.Empty : Convert.ToString(reader["Luns"]);
                            workflowAction.MountPoint = Convert.IsDBNull(reader["MountPoint"]) ? string.Empty : Convert.ToString(reader["MountPoint"]);
                            workflowAction.VG = Convert.IsDBNull(reader["VG"]) ? string.Empty : Convert.ToString(reader["VG"]);
                            workflowAction.SwitchOverSession = Convert.IsDBNull(reader["SwitchOverSession"]) ? string.Empty : Convert.ToString(reader["SwitchOverSession"]);
                            workflowAction.Command = Convert.IsDBNull(reader["Command"]) ? string.Empty : Convert.ToString(reader["Command"]);
                            workflowAction.PrDr = Convert.IsDBNull(reader["PrDr"]) ? 0 : Convert.ToInt32(reader["PrDr"]);

                            workflowAction.StandByControlFile = Convert.IsDBNull(reader["StandByControlFile"]) ? string.Empty : Convert.ToString(reader["StandByControlFile"]);
                            workflowAction.File = Convert.IsDBNull(reader["SCPFile"]) ? string.Empty : Convert.ToString(reader["SCPFile"]);
                            workflowAction.ControlFile = Convert.IsDBNull(reader["ControlFile"]) ? string.Empty : Convert.ToString(reader["ControlFile"]);
                            workflowAction.TargetServer = Convert.IsDBNull(reader["TargetSession"]) ? 0 : Convert.ToInt32(reader["TargetSession"]);
                            workflowAction.ScriptFile = Convert.IsDBNull(reader["ScriptFile"]) ? string.Empty : Convert.ToString(reader["ScriptFile"]);

                            workflowAction.TempFile = Convert.IsDBNull(reader["TempFile"]) ? string.Empty : Convert.ToString(reader["TempFile"]);
                            workflowAction.JobQueue = Convert.IsDBNull(reader["JobQueue"]) ? string.Empty : Convert.ToString(reader["JobQueue"]);
                            workflowAction.Fastcopy = Convert.IsDBNull(reader["FastCopyReplication"]) ? 0 : Convert.ToInt32(reader["FastCopyReplication"]);
                            workflowAction.Listener = Convert.IsDBNull(reader["Listner"]) ? string.Empty : Convert.ToString(reader["Listner"]);

                            workflowAction.DNSServer = Convert.IsDBNull(reader["DNSServer"]) ? 0 : Convert.ToInt32(reader["DNSServer"]);
                            workflowAction.ExistingHost = Convert.IsDBNull(reader["ExistingHost"]) ? string.Empty : Convert.ToString(reader["ExistingHost"]);
                            workflowAction.ExistingIp = Convert.IsDBNull(reader["ExistingIp"]) ? string.Empty : Convert.ToString(reader["ExistingIp"]);
                            workflowAction.NewHost = Convert.IsDBNull(reader["NewHost"]) ? string.Empty : Convert.ToString(reader["NewHost"]);
                            workflowAction.NewIp = Convert.IsDBNull(reader["NewIp"]) ? string.Empty : Convert.ToString(reader["NewIp"]);
                            workflowAction.DomainName = Convert.IsDBNull(reader["DomainName"]) ? string.Empty : Convert.ToString(reader["DomainName"]);
                            ////26062012
                            workflowAction.LocalMountPoints = Convert.IsDBNull(reader["LocalMountPoints"]) ? string.Empty : Convert.ToString(reader["LocalMountPoints"]);
                            workflowAction.RemoteMountPoints = Convert.IsDBNull(reader["RemoteMountPoints"]) ? string.Empty : Convert.ToString(reader["RemoteMountPoints"]);
                            workflowAction.HostName = Convert.IsDBNull(reader["HostName"]) ? string.Empty : Convert.ToString(reader["HostName"]);
                            ////02/07/2012 
                            workflowAction.IsUseSudo = Convert.IsDBNull(reader["IsUseSudo"]) ? 0 : Convert.ToInt32(reader["IsUseSudo"]);
                            workflowAction.CheckOutput = Convert.IsDBNull(reader["CheckOutput"]) ? string.Empty : Convert.ToString(reader["CheckOutput"]);
                            workflowAction.IsReturn = Convert.IsDBNull(reader["IsReturn"]) ? 0 : Convert.ToInt32(reader["IsReturn"]);
                            ////03/07/2012            
                            workflowAction.FastCopyPath = Convert.IsDBNull(reader["FastCopyPath"]) ? string.Empty : Convert.ToString(reader["FastCopyPath"]);
                            workflowAction.SourceFile = Convert.IsDBNull(reader["SourceFile"]) ? string.Empty : Convert.ToString(reader["SourceFile"]);
                            workflowAction.SourceFolder = Convert.IsDBNull(reader["SourceFolder"]) ? string.Empty : Convert.ToString(reader["SourceFolder"]);
                            workflowAction.TargetFolder = Convert.IsDBNull(reader["TargetFolder"]) ? string.Empty : Convert.ToString(reader["TargetFolder"]);
                            ////25/07/2012
                            workflowAction.DiskGroup = Convert.IsDBNull(reader["DiskGroup"]) ? string.Empty : Convert.ToString(reader["DiskGroup"]);
                            workflowAction.DiskName = Convert.IsDBNull(reader["DiskName"]) ? string.Empty : Convert.ToString(reader["DiskName"]);
                            //workflowAction.PrStorageImageId = reader.IsDBNull(FLD_PRSTORAGEIMAGEID) ? string.Empty : reader.GetString(FLD_PRSTORAGEIMAGEID);
                            //workflowAction.DrStorageImageId = reader.IsDBNull(FLD_DRSTORAGEIMAGEID) ? 0 : reader.GetInt32(FLD_DRSTORAGEIMAGEID);
                            ////31/07/2012
                            workflowAction.RsyncPath = Convert.IsDBNull(reader["RsyncPath"]) ? string.Empty : Convert.ToString(reader["RsyncPath"]);
                            workflowAction.TargetFile = Convert.IsDBNull(reader["TargetFile"]) ? string.Empty : Convert.ToString(reader["TargetFile"]);
                            workflowAction.MachineName = Convert.IsDBNull(reader["MachineName"]) ? string.Empty : Convert.ToString(reader["MachineName"]);
                            workflowAction.SnapShotName = Convert.IsDBNull(reader["SnapShotName"]) ? string.Empty : Convert.ToString(reader["SnapShotName"]);
                            workflowAction.TimeOut = Convert.IsDBNull(reader["TimeOut"]) ? string.Empty : Convert.ToString(reader["TimeOut"]);
                            workflowAction.VirtualMachine = Convert.IsDBNull(reader["VirtualMachine"]) ? string.Empty : Convert.ToString(reader["VirtualMachine"]);
                            ////17/08/2012          
                            workflowAction.DestinationPath = Convert.IsDBNull(reader["DestinationPath"]) ? string.Empty : Convert.ToString(reader["DestinationPath"]);
                            //workflowAction.SnapMirrorVolume = reader.IsDBNull(FLD_SNAPMIRRORVOLUME) ? string.Empty : reader.GetString(FLD_SNAPMIRRORVOLUME);
                            workflowAction.TargetMachine = Convert.IsDBNull(reader["TargetMachine"]) ? string.Empty : Convert.ToString(reader["TargetMachine"]);

                            workflowAction.VmPath = Convert.IsDBNull(reader["VmPath"]) ? string.Empty : Convert.ToString(reader["VmPath"]);
                            workflowAction.OriginalText = Convert.IsDBNull(reader["OriginalText"]) ? string.Empty : Convert.ToString(reader["OriginalText"]);
                            workflowAction.NewText = Convert.IsDBNull(reader["NewText"]) ? string.Empty : Convert.ToString(reader["NewText"]);

                            //16/11/2012
                            workflowAction.RouterConfiguration = Convert.IsDBNull(reader["RouterConfiguration"]) ? string.Empty : Convert.ToString(reader["RouterConfiguration"]);
                            //20/11/2012
                            workflowAction.InterfacePassword = Convert.IsDBNull(reader["InterfacePassword"]) ? string.Empty : Convert.ToString(reader["InterfacePassword"]);
                            //26/11/2012
                            workflowAction.DeviceGroup = Convert.IsDBNull(reader["DeviceGroup"]) ? string.Empty : Convert.ToString(reader["DeviceGroup"]);
                            //27/11/2012
                            workflowAction.FileSystem = Convert.IsDBNull(reader["FileSystem"]) ? string.Empty : Convert.ToString(reader["FileSystem"]);
                            //27/11/2012
                            workflowAction.WaitTime = Convert.IsDBNull(reader["WaitTime"]) ? string.Empty : Convert.ToString(reader["WaitTime"]);
                            //08/12/2012
                            workflowAction.ApplicationName = Convert.IsDBNull(reader["ApplicationName"]) ? string.Empty : Convert.ToString(reader["ApplicationName"]);
                            //09/12/2012
                            workflowAction.MapFile = Convert.IsDBNull(reader["MapFile"]) ? string.Empty : Convert.ToString(reader["MapFile"]);
                            workflowAction.Task = Convert.IsDBNull(reader["Task"]) ? string.Empty : Convert.ToString(reader["Task"]);
                            //12/12/2012
                            workflowAction.FileSystemMountPoint = Convert.IsDBNull(reader["FileSystemMountPoint"]) ? string.Empty : Convert.ToString(reader["FileSystemMountPoint"]);
                            //03/01/2013
                            workflowAction.WorkFlowId = Convert.IsDBNull(reader["WorkFlowId"]) ? 0 : Convert.ToInt32(reader["WorkFlowId"]);
                            workflowAction.WorkFlowActionId = Convert.IsDBNull(reader["WorkFlowActionId"]) ? string.Empty : Convert.ToString(reader["WorkFlowActionId"]);
                            workflowAction.DependencyType = Convert.IsDBNull(reader["DependencyType"]) ? 0 : Convert.ToInt32(reader["DependencyType"]);
                            workflowAction.Time = Convert.IsDBNull(reader["WFTime"]) ? string.Empty : Convert.ToString(reader["WFTime"]);
                            //24/01/2013
                            workflowAction.TargetDatabase = Convert.IsDBNull(reader["TargetDatabase"]) ? 0 : Convert.ToInt32(reader["TargetDatabase"]);
                            //01/04/2013
                            workflowAction.BackUpFile = Convert.IsDBNull(reader["BackUpFile"]) ? string.Empty : Convert.ToString(reader["BackUpFile"]);
                            workflowAction.TraceFile = Convert.IsDBNull(reader["TraceFile"]) ? string.Empty : Convert.ToString(reader["TraceFile"]);
                            //15/04/2013
                            workflowAction.Expression = Convert.IsDBNull(reader["Expression"]) ? 0 : Convert.ToInt32(reader["Expression"]);
                            workflowAction.ProcessCount = Convert.IsDBNull(reader["ProcessCount"]) ? 0 : Convert.ToInt32(reader["ProcessCount"]);
                            workflowAction.RTO = Convert.IsDBNull(reader["RTO"]) ? string.Empty : Convert.ToString(reader["RTO"]);
                            //12/06/2013
                            workflowAction.HDisc = Convert.IsDBNull(reader["HDisc"]) ? string.Empty : Convert.ToString(reader["HDisc"]);
                            workflowAction.HitachiHorComInstance = Convert.IsDBNull(reader["HitachihorcomInstance"]) ? string.Empty : Convert.ToString(reader["HitachihorcomInstance"]);
                            workflowAction.RestorePoint = Convert.IsDBNull(reader["RestorePoint"]) ? string.Empty : Convert.ToString(reader["RestorePoint"]);

                            workflowAction.VMIsClustered = Convert.IsDBNull(reader["VMIsClustered"])
                               ? 0
                               : Convert.ToInt32(reader["VMIsClustered"]);
                            workflowAction.AlertText = Convert.IsDBNull(reader["AlertText"])
                                ? string.Empty
                                : Convert.ToString(reader["AlertText"]);
                            workflowAction.AlertModeType = Convert.IsDBNull(reader["AlertModeType"])
                                ? 0
                                : Convert.ToInt32(reader["AlertModeType"]);
                            workflowAction.ProcessFlow = Convert.IsDBNull(reader["ProcessFlow"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessFlow"]);

                            workflowAction.VolName = Convert.IsDBNull(reader["VolName"])
                                ? string.Empty
                                : Convert.ToString(reader["VolName"]);
                            workflowAction.Drive = Convert.IsDBNull(reader["Drive"]) ? string.Empty : Convert.ToString(reader["Drive"]);
                            workflowAction.SubnetMask = Convert.IsDBNull(reader["SubnetMask"])
               ? string.Empty
               : Convert.ToString(reader["SubnetMask"]);
                            workflowAction.GateWay = Convert.IsDBNull(reader["GateWay"])
                                ? string.Empty
                                : Convert.ToString(reader["GateWay"]);
                            workflowAction.PrimaryDNS = Convert.IsDBNull(reader["PrimaryDNS"])
                                ? string.Empty
                                : Convert.ToString(reader["PrimaryDNS"]);
                            workflowAction.SecondaryDNS = Convert.IsDBNull(reader["SecoundaryDNS"])
                                ? string.Empty
                                : Convert.ToString(reader["SecoundaryDNS"]);
                            workflowAction.ServiceName = Convert.IsDBNull(reader["ServiceName"])
                                ? string.Empty
                                : Convert.ToString(reader["ServiceName"]);
                            workflowAction.Appuser = Convert.IsDBNull(reader["Appuser"]) ?
                                 string.Empty
                                : Convert.ToString(reader["Appuser"]);
                            workflowAction.ProcessID = Convert.IsDBNull(reader["ProcessId"])
                                ? string.Empty
                                : Convert.ToString(reader["ProcessId"]);
                            workflowAction.Shellprompt = Convert.IsDBNull(reader["Shellprompt"])
                                ? string.Empty
                                : Convert.ToString(reader["Shellprompt"]);
                            workflowAction.DSCSLIServer = Convert.IsDBNull(reader["DSCSLISERVER"]) ? 0 : Convert.ToInt32(reader["DSCSLISERVER"]);
                            workflowAction.HMCServer = Convert.IsDBNull(reader["hmcserver"]) ? 0 : Convert.ToInt32(reader["hmcserver"]);
                            workflowAction.LSSID = Convert.IsDBNull(reader["LSSID"]) ? string.Empty : Convert.ToString(reader["LSSID"]);

                            workflowAction.RelationshipId = Convert.IsDBNull(reader["RelationshipId"]) ? string.Empty : Convert.ToString(reader["RelationshipId"]);
                            workflowAction.CheckState = Convert.IsDBNull(reader["CheckState"]) ? string.Empty : Convert.ToString(reader["CheckState"]);

                            workflowAction.RecoveryPlan = Convert.IsDBNull(reader["RecoveryPlan"]) ? string.Empty : Convert.ToString(reader["RecoveryPlan"]);
                            workflowAction.ClusterName = Convert.IsDBNull(reader["ClusterName"]) ? string.Empty : Convert.ToString(reader["ClusterName"]);
                            workflowAction.ClusterGroupResource = Convert.IsDBNull(reader["ClusterGroupResource"]) ? string.Empty : Convert.ToString(reader["ClusterGroupResource"]);

                            workflowAction.EmailSuccess = Convert.IsDBNull(reader["EmailSuccess"]) ? string.Empty : Convert.ToString(reader["EmailSuccess"]);
                            workflowAction.EmailFail = Convert.IsDBNull(reader["EmailFail"]) ? string.Empty : Convert.ToString(reader["EmailFail"]);
                            workflowAction.SmsSuccess = Convert.IsDBNull(reader["SmsSuccess"]) ? string.Empty : Convert.ToString(reader["SmsSuccess"]);
                            workflowAction.SmsFail = Convert.IsDBNull(reader["SmsFail"]) ? string.Empty : Convert.ToString(reader["SmsFail"]);
                            workflowAction.AlertMechanismType = Convert.IsDBNull(reader["AlertMechanismType"]) ? string.Empty : Convert.ToString(reader["AlertMechanismType"]);
                            workflowAction.ScriptBlock = Convert.IsDBNull(reader["ScriptBlock"]) ? string.Empty : Convert.ToString(reader["ScriptBlock"]);
                            //Url & HtmlContent Uncommented by Mridul and required fields in Hurl and Hit Chech Url Actions
                            workflowAction.URL = Convert.IsDBNull(reader["URL"]) ? string.Empty : Convert.ToString(reader["URL"]);
                            workflowAction.HTMLCONTENTS = Convert.IsDBNull(reader["HTMLContents"]) ? string.Empty : Convert.ToString(reader["HTMLContents"]);
                            workflowAction.ZoneName = Convert.IsDBNull(reader["ZoneName"]) ? string.Empty : Convert.ToString(reader["ZoneName"]);
                            workflowAction.CellNo = Convert.IsDBNull(reader["CellNo"]) ? string.Empty : Convert.ToString(reader["CellNo"]);
                            workflowAction.EmailId = Convert.IsDBNull(reader["EmailId"]) ? string.Empty : Convert.ToString(reader["EmailId"]);
                            workflowAction.Resource = Convert.IsDBNull(reader["ClusterResource"]) ? string.Empty : Convert.ToString(reader["ClusterResource"]);
                            workflowAction.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            workflowAction.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
                            workflowAction.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            workflowAction.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());

                            return workflowAction;
                        }

                        return workflowAction;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessFetchOperation, "Exception occurred while Get WorkflowAction information by DRoperation Id - " + droperationId, exc);

            }

        }

        public static bool UpdateWorkflowActionByworkflowActionId(int workflowactionid, string checkstate)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOWACTION_UPDATEBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, workflowactionid);
                    Database.AddInParameter(dbCommand, Dbstring + "icheckstate", DbType.Int32, checkstate);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update WorkflowAction Details by Workflowactionid ", exc);
            }
        }

        public static bool UpdateImplementationStatus(int id, int status)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("WorkflowAction_UpdateImpStatus"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring + Dbstring+"iImplementStatus", DbType.Int32, status);

                    int returnCode = Database.ExecuteNonQuery(dbCommand);

# if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
                //                    int isSuccess = Database.ExecuteNonQuery(dbCommand);
                //#if ORACLE
                //                    return isSuccess < 0;
                //#endif
                //                    return isSuccess > 0;
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update Workflow Action Details by Implementation Status ", exc);
            }

        }

        public static bool UpdateWorkflowActionAlertByworkflowActionId(int workflowactionid, string alertText)
        {
            try
            {

                using (DbCommand dbCommand = Database.GetStoredProcCommand("WORKFLOWACTIONA_UPDATEBYID"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, workflowactionid);
                    Database.AddInParameter(dbCommand, Dbstring + "iAlertText", DbType.String, alertText);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
#if ORACLE
                    return isuccess < 0;
#endif
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new BcmsException(BcmsExceptionType.DataAccessUpdateOperation, "Exception occurred while update WorkflowAction Alert Details by Workflowactionid ", exc);
            }
        }
    }
}