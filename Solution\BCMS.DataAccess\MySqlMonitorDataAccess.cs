﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Bcms.Common;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
   public class MySqlMonitorDataAccess : BaseDataAccess
    {
       public static bool AddMysqlMonitorLogs(MySqlMonitor mysqlmonitor)
       {
           try
           {
               string sp = DbRoleName + "MYSQLMONITORLOG_CREATE";

               using (DbCommand cmd = Database.GetStoredProcCommand(sp))
               {
                   Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, mysqlmonitor.GroupId);
                   Database.AddInParameter(cmd, Dbstring + "iPRSERVERSTATUS", DbType.AnsiString, mysqlmonitor.PRSERVERSTATUS);
                   Database.AddInParameter(cmd, Dbstring + "iDRSERVERSTATUS", DbType.AnsiString, mysqlmonitor.DRSERVERSTATUS);
                   Database.AddInParameter(cmd, Dbstring + "iPRSERVERVERSION", DbType.AnsiString, mysqlmonitor.PRSERVERVERSION);
                   Database.AddInParameter(cmd, Dbstring + "iDRSERVERVERSION", DbType.AnsiString, mysqlmonitor.DRSERVERVERSION);
                   Database.AddInParameter(cmd, Dbstring + "iPRDAEMONSTATUS", DbType.AnsiString, mysqlmonitor.PRDAEMONSTATUS);
                   Database.AddInParameter(cmd, Dbstring + "iDRDAEMONSTATUS", DbType.AnsiString, mysqlmonitor.DRDAEMONSTATUS);
                   Database.AddInParameter(cmd, Dbstring + "iMASTERBINLOG", DbType.AnsiString, mysqlmonitor.MASTERBINLOG);
                   Database.AddInParameter(cmd, Dbstring + "iSLAVEBINLOG", DbType.AnsiString, mysqlmonitor.SLAVEBINLOG);
                   Database.AddInParameter(cmd, Dbstring + "iMASTERPOSITION", DbType.AnsiString, mysqlmonitor.MASTERPOSITION);
                   Database.AddInParameter(cmd, Dbstring + "iSLAVEPOSITION", DbType.AnsiString, mysqlmonitor.SLAVEPOSITION);
                   int value = Database.ExecuteNonQuery(cmd);


                   if (value > 0)
                   {
                       return true;
                   }

               }

           }
           catch (Exception exe)
           {
               string error = "Exception :" + exe.Message;
               throw;
           }

           return true;
       }


    }
}
