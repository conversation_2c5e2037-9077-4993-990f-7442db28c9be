﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Bcms.Common.Base;

namespace Bcms.Common
{
  
   [Serializable]
   [DataContract(Name = "MimixReplication", Namespace = "http://www.BCMS.com/types")]
    public class MimixReplication : BaseEntity
    {
       
        [DataMember]
       public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public string DataGroup
        {
            get;
            set;
        }

        [DataMember]
        public string MimixLibrary
        {
            get;
            set;
        }

        [DataMember]
        public string MIMIXManager
        {
            get;
            set;
        }

        [DataMember]
        public string MIMIXHealth
        {
            get;
            set;
        }

        [DataMember]
        public string MIMIXAvilability
        {
            get;
            set;
        }

        [DataMember]
        public string MIMIXAlerts
        {
            get;
            set;
        }

        [DataMember]
        public string MIMIXDatalag
        {
            get;
            set;
        }
    }
}
