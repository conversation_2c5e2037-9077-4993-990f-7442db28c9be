﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using Bcms.Common;
using Bcms.Common.Shared;
using Bcms.Core.Client;
using Bcms.Core.Job;
using Bcms.DataAccess;
using Bcms.ExceptionHandler;
using Bcms.Impl;
using log4net;
using Reportslib = ReportsLib;
using Bcms.Core.Job;
using System.Diagnostics;
using System.IO;
using Bcms.Core.Workflows;
using System.Linq;
using Serilog;
using Quartz;


namespace Bcms.Core
{
    public class BcmsCore : IBcmsCore
    {
        #region Variables

        public readonly ILog _logger = LogManager.GetLogger(typeof(BcmsCore));
        public static int jobTrigger = 1;
        private ISchedulerFactory _schedulerFactory;
        private IScheduler _scheduler;
        //private ISchedulerFactory _parallelSchedulerFactory;
        //private IScheduler _parallelScheduler;
        private ISchedulerFactory _dataSyncSchedulerFactory;
        private IScheduler _dataSyncScheduler;
        private IList<InfraObject> _infraObjects;
        private IList<GlobalMirror> _globalMirrors;
        private IList<DatabaseBackupInfo> _databaseBackups;
        public List<string> listJobName = new List<string>();
        public Quartz.IScheduler scheduler = Quartz.Impl.StdSchedulerFactory.GetDefaultScheduler();
        //Added by Karthick B for ITIT-10566
        private Quartz.ISchedulerFactory _parallelSchedulerFactory;
        private Quartz.IScheduler _parallelScheduler;
        private readonly object _schedulerLock = new object();
        //Added by Karthick B for ITIT-10566
        #endregion Variables

        #region Properties

        public IList<InfraObject> ActiveInfraObjects
        {
            get { return _infraObjects ?? (_infraObjects = new List<InfraObject>()); }
            set
            {
                if (value == null || value.Count == 0)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.DataAccessArgumentNull, " All InfraObjects are Maintenance Mode or Getting Null values");

                    ExceptionManager.Manage(bcmsException);
                }
                _infraObjects = value;
            }
        }

        public IList<GlobalMirror> ActiveGlobalMirror
        {
            get { return _globalMirrors ?? (_globalMirrors = new List<GlobalMirror>()); }
            set
            {
                if (value == null || value.Count == 0)
                {
                    var bcmsException = new BcmsException(BcmsExceptionType.DataAccessArgumentNull, " Getting Null values while get GlobalMirror job initialization");

                    ExceptionManager.Manage(bcmsException);
                }
                _globalMirrors = value;
            }
        }

        public IList<DatabaseBackupInfo> CurrentDatabaseBackup
        {
            get { return _databaseBackups ?? (_databaseBackups = new List<DatabaseBackupInfo>()); }
            set
            {
                if (value == null || value.Count == 0)
                {
                    //var bcmsException = new BcmsException(BcmsExceptionType.DataAccessArgumentNull, " get Database backup having null value");

                    //ExceptionManager.Manage(bcmsException);


                    //_logger.Error();
                }
                _databaseBackups = value;
            }
        }

        #endregion Properties

        #region Public Method
        //Added by Karthick B for ITIT-10566
        public void InitializeParallel()
        {
            Serilog.Log.Logger = new LoggerConfiguration()
                        .Enrich.WithThreadId()
                        .WriteTo.Map("FileName", "Log", (name, wt) => wt.File("C:\\CP\\Parallel\\" + name + "-.txt", rollingInterval: RollingInterval.Day, outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [ThreadId:{ThreadId}] {Level:u3}  -   {Message:lj}{NewLine}{Exception}"))
                       .CreateLogger();
            try
            {
                _parallelSchedulerFactory = CreateParallelSchedulerFactory();
                _parallelScheduler = GetParallelScheduler();
                //_parallelScheduler = _parallelSchedulerFactory.GetScheduler();

                //_parallelScheduler = GetParallelScheduler();



                if (true)
                {
                    CleanUpParallelScheduler(_parallelScheduler);
                }

                // ExecuteParallelWorkflow();

                _logger.Info("Initializing Parallel CP Job Server");

                _logger.Info("Starting Parallel service");

                var thread = new Thread(_parallelScheduler.Start);

                thread.Start();

                _logger.Info("Parallel Service Started");
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                    _logger.ErrorFormat("Parallel CP Job Server initialization failed:" + e.InnerException);
                else
                    _logger.ErrorFormat("Parallel CP Job Server initialization failed:" + e.Message);
            }
        }    

        private void CleanUpParallelScheduler(Quartz.IScheduler _parallelScheduler)
        {
            _parallelScheduler.Clear();
        }
        //Added by Karthick B for ITIT-10566
        public virtual void CleanUp_Q(Quartz.IScheduler inScheduler)
        {
            _logger.Info("***** Deleting existing jobs/triggers *****");
            inScheduler.Clear();

        }
        protected virtual Quartz.ISchedulerFactory CreateParallelSchedulerFactory()
        {
            return new Quartz.Impl.StdSchedulerFactory();
        }
        public void Initialize(bool isScheduled)
        {
            try
            {
                _schedulerFactory = CreateSchedulerFactory();
                _scheduler = GetScheduler();
                if (true)
                {
                    CleanUp(_scheduler);
                    CleanUp_Q(scheduler);
                }

                _logger.Info("Initializing CP Job server");

                if (isScheduled)
                {
                    _logger.Info("call Initializing PrepareActiveInfraObjects");

                    PrepareActiveInfraObjects();
                    _logger.Info("call InitializeRescheduleMonitorServiceJob");
                    InitializeRescheduleMonitorServiceJob();
                    //need to add

                    PrepareActiveBusinessService();

                    PrepareMonitorService();

                    PrepareMonitorApplicationWorkflow();

                    PrepareMonitorStructure();

                    DoPingJob();

                    InitializeMonitorGlobalMirrorJob();

                    PreparingReport();

                    PreparingScheduleWorkflow();

                    PrepareMonitorInfraobjectDiskspace();

                    SchedulingDatbaseBackup();

                    //InitializeBIAWorkflowAnlyticJob();

                    //InitializeBIAWorkflowTrendsDataJob();

                    //InitializeBIAActionAnlyticJob();

                    //InitializeBIAActionTrendsDataJob();

                    //InitializeBIAAlertAnlyticJob();





                    //PrepareActiveInfraObjects();

                    //PrepareActiveBusinessService();

                    //PrepareMonitorService();

                    //PrepareMonitorApplicationWorkflow();

                    //PrepareMonitorStructure();

                    //DoPingJob();

                    //InitializeMonitorGlobalMirrorJob();

                    //PreparingReport();
                    //  ExecuteBackup();

                    //ApplicationMonitoring();

                    //  IntializingMonitorApplicationHealth();

                    // PrepareMonitoringGlobalMirror();

                    //   SchedulingDatbaseBackup();

                    //Initializing24HoursReport(); // surendra Added
                }
                //var thread = new Thread(_scheduler.Start);

                //thread.Start();
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                    _logger.ErrorFormat("CP Job Server initialization failed:" + e.InnerException);
                else
                    _logger.ErrorFormat("CP Job Server initialization failed:" + e.Message);
            }
        }

        #region Dynamic Service

        public void PrepareDyanamicDatabase(int id, int reptype)
        {
            _logger.Info("Dynamic job started");
            Thread.Sleep(1000);
            var jobInfo = GroupJobInfoDataAccess.GetAllInfraJobInfo();

            foreach (var item in jobInfo)
            {
                var ActiveInfraObjects = InfraObjectDataAccess.GetInfraObjectById(item.InfraObjectId);
                // _logger.Info("InfraId " + ActiveInfraObjects.Id);

                if (item.IsSchedule == 1)
                {
                    _logger.Info("Oracle job started");
                    InitializeRescheduleOracleMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName, item.BcmsClassName);
                }
                if (item.IsSchedule == 2)
                {
                    _logger.Info("SqlNative Info...");

                    InitializeRescheduleMSSqlNative2008MonitorComponents(ActiveInfraObjects, item.CronExpression, item.BcmsClassName);
                }
                if (item.IsSchedule == 3)
                {
                    _logger.Info("DB2 Info...");

                    InitializeRescheduleDB2MonitorComponents(ActiveInfraObjects, item.CronExpression, item.BcmsClassName);
                }
                if (item.IsSchedule == 4)
                {
                    _logger.Info("Applicatin Replication Info...");

                    InitializeRescheduleAppRepDataSyncMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName, item.BcmsClassName);
                }
                if (item.IsSchedule == 5)
                {
                    _logger.Info("SqlServer2000 Info...");

                    InitializeRescheduleSqlServer2000MonitorComponents(ActiveInfraObjects, item.CronExpression, item.BcmsClassName);
                }
                if (item.IsSchedule == 6)
                {
                    _logger.Info("ExchangeDAG Info...");

                    InitializeRescheduleExchangeDAGMonitorComponents(ActiveInfraObjects, item.CronExpression, item.BcmsClassName);
                }
                if (item.IsSchedule == 7)
                {
                    _logger.Info("Oracle DataSync Info...");

                    InitializeRescheduleOracleDataSyncMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName, item.BcmsClassName);
                }
                if (item.IsSchedule == 8)
                {
                    _logger.Info("Postgre9X Info...");

                    InitializeReschedulePostgre9XMonitorComponents(ActiveInfraObjects, item.CronExpression);
                }
                if (item.IsSchedule == 9)
                {
                    _logger.Info("MSSqlNetAppSnapMirror Info...");

                    InitializeRescheduleMSSqlNetAppSnapMirrorMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 10)
                {
                    _logger.Info("MSSqlNetAppSnapMirror Info...");

                    InitializeRescheduleOracleFullDBNetAppSnapMirrorMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 11)
                {
                    _logger.Info("MSSqlNativeLog Info...");

                    InitializeRescheduleMSSqlNativeLogMonitorComponents(ActiveInfraObjects, item.CronExpression);
                }
                if (item.IsSchedule == 12)
                {
                    _logger.Info("MySqlNativeLog Info...");

                    InitializeRescheduleMySqlNativeLogMonitorComponents(ActiveInfraObjects, item.CronExpression, item.BcmsClassName);
                }
                if (item.IsSchedule == 13)
                {
                    _logger.Info("MSSqlNativeLog Info...");
                    InitializeRescheduleEMCOracleFullDBMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 14)
                {
                    _logger.Info("HitachiOracleFullDb Info...");
                    InitializeRescheduleHirtachiOracleFullDBMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 14)
                {
                    _logger.Info("MSSqlDbMirroing Info...");
                    InitializeRescheduleMSSqlDbMirroingMonitorComponents(ActiveInfraObjects, item.CronExpression);
                }
                if (item.IsSchedule == 15)
                {
                    _logger.Info("MSSqlDbMirroing Info...");
                    InitializeRescheduleMSSqlDBMirrorMonitorComponents1(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 16)
                {
                    _logger.Info("VMWare Info...");
                    InitializeRescheduleVMWareMonitorComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 17)
                {
                    _logger.Info("EMCMSSQLFULLDB Info...");
                    InitializeRescheduleEMCMSSQLFULLDBComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 18)
                {
                    _logger.Info("HITACHIMSSQLFULLDB Info...");
                    InitializeRescheduleHitachiMSSqlFullDbComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 18)
                {
                    _logger.Info("HITACHIMSSQLFULLDB Info...");
                    InitializeRescheduleHitachiMSSqlFullDbComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 19)
                {
                    _logger.Info("EMCORACLEFULL DB RAC Info...");
                    InitializeRescheduleEMCORacleFullDbRacComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
                if (item.IsSchedule == 20)
                {
                    _logger.Info("EMCMySql DB RAC Info...");
                    InitializeRescheduleEMCMySqlComponents(ActiveInfraObjects, item.CronExpression, item.JobName);
                }
            }
        }
        #endregion

        public void PreparingScheduleWorkflowonclick()
        {
            _logger.Info("Workflow Job ReSchedule is Started");
            Thread.Sleep(1000);
            IList<Infraobject_Scheduler> ScheduleWFList = Infraobject_SchedulerDataAccess.GelAllInfraobjectScheduler();

            if (ScheduleWFList != null && ScheduleWFList.Count > 0)
            {
                foreach (var item in ScheduleWFList)
                {
                    //  _logger.Info("Workflow Job ReSchedule Id is :" + item.IsSchedule);
                    if (item.IsSchedule == 0)
                    {
                        _logger.Info("New Workflow Job Schedule is Started for :" + item.Id);
                        Infraobject_Scheduler updateschedular = new Infraobject_Scheduler();
                        updateschedular.Id = item.Id;
                        updateschedular.IsSchedule = 1;
                        bool updateinfrawfschedule = Infraobject_SchedulerDataAccess.UpdateInfraobjectScheduler(updateschedular);
                        if (updateinfrawfschedule)
                        {
                            var infraobject = InfraObjectDataAccess.GetInfraObjectById(item.InfraobjectId);
                            InitializingScheduleWorkflow(item.Id, item.WorkFlowName, item.ScheduleTime, infraobject);
                        }
                        else
                        {
                            _logger.Error("Schedule Workflow for Infraobject onclick unable to update for id :" + item.Id);
                        }

                    }
                    else if (item.IsSchedule == 2)
                    {
                        _logger.Info("Old Workflow Job ReSchedule is Started  for :" + item.Id);
                        Infraobject_Scheduler updateschedular = new Infraobject_Scheduler();
                        updateschedular.Id = item.Id;
                        updateschedular.IsSchedule = 1;
                        bool updateinfrawfschedule = Infraobject_SchedulerDataAccess.UpdateInfraobjectScheduler(updateschedular);
                        if (updateinfrawfschedule)
                        {
                            var infraobject = InfraObjectDataAccess.GetInfraObjectById(item.InfraobjectId);
                            InitializingScheduleWorkflowOnclick(item.Id, item.WorkFlowName, item.ScheduleTime, infraobject);
                        }
                        else
                        {
                            _logger.Error("Schedule Workflow for Infraobject onclick unable to update for id :" + item.Id);
                        }
                    }
                    else
                    {
                        _logger.Info("Workflow Job already Schedule .");
                    }

                }
            }
            else
            {
                _logger.Error("Schedule Workflow for Infraobject list onclick is null ..!");
            }

        }

        public void PrepareMonitorInfraobjectDiskspace()
        {
            _logger.Info("PrepareMonitorInfraobjectDiskspace start");

            ActiveInfraObjects = InfraObjectDataAccess.GetAllInfraObjects();

            if (ActiveInfraObjects.Count > 0)
            {
                foreach (var infraObject in ActiveInfraObjects)
                {
                    var getInfraobjectDiskMonitor = InfraobjectDiskMonitorDataAccess.GetInfraobjectDiskMonitorByInfraobjectId(infraObject.Id);

                    if (getInfraobjectDiskMonitor != null)
                    {
                        if (getInfraobjectDiskMonitor.VolumeNamePR != "")
                        {
                            _logger.Info("PrepareMonitorInfraobjectDiskspace start for PR");
                            InitializeMonitorDiskspace(getInfraobjectDiskMonitor, infraObject, "PR");
                        }
                        if (getInfraobjectDiskMonitor.VolumeNameDR != "")
                        {
                            _logger.Info("PrepareMonitorInfraobjectDiskspace start for DR");
                            InitializeMonitorDiskspace(getInfraobjectDiskMonitor, infraObject, "DR");
                        }

                    }
                    else
                    {
                        _logger.Debug("============== Diskspace Monitoring is not enabled for Infraobject: " + infraObject.Name + " in PrepareMonitorInfraobjectDiskspace Jobs ================");
                    }
                }
            }

        }

        public void PreparingScheduleWorkflow()
        {
            IList<Infraobject_Scheduler> ScheduleWFList = Infraobject_SchedulerDataAccess.GelAllInfraobjectScheduler();

            if (ScheduleWFList != null && ScheduleWFList.Count > 0)
            {
                foreach (var item in ScheduleWFList)
                {
                    Infraobject_Scheduler updateschedular = new Infraobject_Scheduler();
                    updateschedular.Id = item.Id;
                    updateschedular.IsSchedule = 1;
                    bool updateinfrawfschedule = Infraobject_SchedulerDataAccess.UpdateInfraobjectScheduler(updateschedular);
                    _logger.Info("Update vaule is :" + updateinfrawfschedule);
                    if (updateinfrawfschedule)
                    {
                        var infraobject = InfraObjectDataAccess.GetInfraObjectById(item.InfraobjectId);
                        InitializingScheduleWorkflow(item.Id, item.WorkFlowName, item.ScheduleTime, infraobject);
                    }
                    else
                    {
                        _logger.Error("Schedule Workflow for Infraobject unable to update for id :" + item.Id);
                    }

                }
            }
            else
            {
                _logger.Error("Schedule Workflow for Infraobject list is null ..!");
            }

        }

        public void ScheduleDiscoveryJobs()
        {
            Thread.Sleep(1000);

            var ScheduledDiscoveryProfileDetails = ScheduleDiscoveryProfDetailsDataAccess.GetAllScheduledDiscoveryProfileDetailsByIsScheduled();

            if (ScheduledDiscoveryProfileDetails != null)
            {
                if (ScheduledDiscoveryProfileDetails.Count > 0)
                {
                    foreach (var appDisc in ScheduledDiscoveryProfileDetails)
                    {
                        //once application discovery profile has scheduled,update isScheduled to 1.
                        bool isUpdated = ScheduleDiscoveryProfDetailsDataAccess.UpdateIsSchedule(appDisc.ScheDiscProfileName);

                        if (isUpdated)
                            _logger.Info("IsScheduled updated to 1 for profile" + appDisc.ScheDiscProfileName);

                        //InitializingScheduleDiscoveryjob(appDisc.ScheDiscProfileName);
                        var client = new BcmsClient();
                        //InitializingScheduleDiscoveryjob(appDisc.ScheDiscProfileName);
                        client.PerformAppDiscovery(appDisc.ScheDiscProfileName);


                    }
                }
            }
            else
            {
                _logger.Info("Scheduled Discovery ProfileDetailsList is empty or null");
            }
        }

        private void PreparingReport()
        {
            IList<Reportslib.ReportSchedule> sentList = Reportslib.ReportScheduleDataAccess.GetAllReportSchedule();

            if (sentList != null && sentList.Count > 0)
            {
                foreach (var item in sentList)
                {
                    InitializingReport(item.ReportName, item.ScheduleTime);
                }
            }
            else
            {
                _logger.Error("Report list is null while Preparing Report ");
            }

        }

        public void InitializeDataSync()
        {
            try
            {
                _logger.Info("============== Initialize Scheduler Factory ============");
                _dataSyncSchedulerFactory = CreateDataSyncSchedulerFactory();

                _logger.Info("============== Get Scheduler instance ============");
                _dataSyncScheduler = GetDataSyncScheduler();

                if (true)
                {
                    _logger.Info("============== Clear Scheduler/Triggers ============");

                    CleanUp(_dataSyncScheduler);
                }

                _logger.Info("============== Initialize Schedule Jobs ============");

                PrepareMonitoringGlobalMirror();

                //PrepareReplicationActiveGroups();

                var thread = new Thread(_dataSyncScheduler.Start);

                thread.Start();
            }
            catch (Exception e)
            {
                if (e.InnerException != null)
                    _logger.ErrorFormat("CP DataSync Server initialization failed:" + e.InnerException);
                else
                    _logger.ErrorFormat("CP DataSync Server initialization failed:" + e.Message);
            }
        }

        public virtual void Start()
        {
            try
            {
                _scheduler.Start();
                 //scheduler.Start();
            }
            catch (Exception ex)
            {
                _logger.Fatal(string.Format("Scheduler start failed: {0}", ex.Message), ex);
                throw;
            }

            _logger.Info("Scheduler started successfully");
        }

        public void StopParallelService()
        {
            try
            {
                if (_parallelScheduler != null)
                    _parallelScheduler.Shutdown(true);
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Scheduler stop failed: {0}", ex.Message), ex);
                throw;
            }

            _logger.Info("Scheduler shutdown complete");
        }

        public void Stop()
        {
            try
            {
                _scheduler.Shutdown(true);
            }
            catch (Exception ex)
            {
                _logger.Error(string.Format("Scheduler stop failed: {0}", ex.Message), ex);
                throw;
            }

            _logger.Info("Scheduler shutdown complete");
        }

        public virtual void DataSyncStart()
        {
            _dataSyncScheduler.Start();

            try
            {
                Thread.Sleep(40000);
            }
            catch (ThreadInterruptedException)
            {
            }
            _logger.Info("CP JobScheduler started successfully");
        }

        public void DataSyncStop()
        {
            _dataSyncScheduler.Shutdown(true);
        }

        public void Pause(string jobName, string groupName)
        {
            _scheduler.PauseJob(jobName, groupName);
        }

        public void Resume(string jobName, string groupName)
        {
            _scheduler.ResumeJob(jobName, groupName);
        }

        public void PauseGroup(string groupName)
        {
            _scheduler.PauseJobGroup(groupName);
        }

        public void ResumeGroup(string groupName)
        {
            _scheduler.ResumeJobGroup(groupName);
        }

        public void PauseAll()
        {
            _scheduler.PauseAll();
        }

        public void ResumeAll()
        {
            _scheduler.ResumeAll();
        }

        public void Delete(string jobName, string groupName)
        {
            _scheduler.DeleteJob(jobName, groupName);
        }

        public bool IsGroupPaused(string groupName)
        {
            return _scheduler.IsJobGroupPaused(groupName);
        }

        public void Ping()
        {
            //  var ping = new PerformPing();
            //ping.PerformJob();
        }

        public void CustomAction()
        {
            try
            {
                _logger.Info("==========   CUSTOM ACTION WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.CustomStart);

                if (group != null && group.Id > 0)
                {
                    InfraObjectDataAccess.UpdateWorkflowOperation(group.Id, (int)GroupWorkflowOperation.CustomizedProcessing, group.DROperationId);

                    _logger.Info(group.Name + " : Custom Action Started");

                    var client = new BcmsClient(group);

                    client.PerformCustomAction();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform custom action. ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform custom action. ERROR MESSAGE :" + exc.Message);
            }
        }

        //public void EventWorkFlow()
        //{
        //    _logger.Info("==========   EVENT WORKFLOW STARTED  =============" + Environment.NewLine);

        //    var event dr = EventDrOperationDataAccess.GetDROperationByStatus();
        //    Group group = GroupDataAccess.GetGroupById(eventdr.GroupId);
        //    var workflow = WorkflowDataAccess.GetWorkflowById(eventdr.WorkflowId);
        //    var client = new BcmsClient(group);
        //    EventDrOperationDataAccess.UpdateDROperationStatus(eventdr.Id, "Running");
        //    client.EventWorkFlow(workflow);
        //    EventDrOperationDataAccess.UpdateDROperationStatus(eventdr.Id, "Completed");
        //}

        public void SwitchOver()
        {
            try
            {
                _logger.Info("==========   SWITCH OVER WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.StartSwitchOver);

                if (group != null && group.Id > 0)
                {
                    _logger.Info(group.Name + " : switch over started");

                    var client = new BcmsClient(group);

                    client.PerformSwitchOver();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform switch over. ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform switch over. ERROR MESSAGE :" + exc.Message);
            }
        }

        public void SqlNativeJobStatus()
        {
            _logger.Info("Start SqlNative Job Status");

            InfraObject group = InfraObjectDataAccess.GetInfraObjectByDataSyncEnable(1);

            if (group != null && group.Id > 0)
            {
                var client = new BcmsClient(group);
                client.PerformSqlNativeJob();
                InfraObjectDataAccess.UpdateByEnableDataSync(group.Id, false);
                _logger.Info("Completed SqlNative Job Status");
            }
        }

        public void Custom()
        {
            try
            {
                _logger.Info("==========   CUSTOM WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.CustomStart);

                if (group != null && group.Id > 0)
                {
                    _logger.Info(group.Name + " : custom started");

                    var client = new BcmsClient(group);

                    client.PerformCustom();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform custom workflow . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform custom workflow . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void SwitchBack()
        {
            try
            {
                _logger.Info("==========   SWITCH BACK WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.SwitchBackStart);

                if (group != null && group.Id > 0)
                {
                    _logger.Info(group.Name + " : switch back started");

                    var client = new BcmsClient(group);

                    client.PerformSwitchBack();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform switch back workflow . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform switch back workflow . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void SwitchOverAction()
        {
            try
            {
                _logger.Info("==========   SWITCH OVER ACTION WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.StartSwitchOver);

                if (group != null && group.Id > 0)
                {
                    _logger.Info(group.Name + " : switch over action Started");

                    InfraObjectDataAccess.UpdateWorkflowOperation(group.Id, (int)GroupWorkflowOperation.SwitchOverProcessing, group.DROperationId);

                    var client = new BcmsClient(group);

                    client.PerformCustomAction();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform switch over action workflow . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform switch over action workflow . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void SwitchBackAction()
        {
            try
            {
                _logger.Info("==========   SWITCH BACK ACTION WF STARTED  =============" + Environment.NewLine);

                InfraObject group = InfraObjectDataAccess.GetInfraObjectByWorkflowEnable((int)GroupWorkflowOperation.SwitchBackStart);

                if (group != null && group.Id > 0)
                {
                    _logger.Info(group.Name + ": Custom Action Started");

                    InfraObjectDataAccess.UpdateWorkflowOperation(group.Id, (int)GroupWorkflowOperation.SwitchBackProcessing, group.DROperationId);

                    var client = new BcmsClient(group);

                    client.PerformCustomAction();
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform switch back action workflow . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform switch back action workflow . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void ExecuteBackup()
        {
            CurrentDatabaseBackup = DatabaseBackupInfoDataAccess.GetAllDatabaseBackupInfo();

            if (CurrentDatabaseBackup.Count > 0)
            {
                foreach (var databaseBackup in CurrentDatabaseBackup)
                {
                    var job = new DatabaseBackupJob();

                    job.PerformDatabaseBackup(databaseBackup, true);
                }
            }
        }

        public virtual void CleanUp(IScheduler inScheduler)
        {
            _logger.Info("***** Deleting existing jobs/triggers *****");

            // unschedule jobs
            var groups = inScheduler.TriggerGroupNames;
            foreach (var group in groups)
            {
                var names = inScheduler.GetTriggerNames(group);
                foreach (var name in names)
                {
                    inScheduler.UnscheduleJob(name, group);
                }
            }

            // delete jobs
            groups = inScheduler.JobGroupNames;
            foreach (var group in groups)
            {
                var names = inScheduler.GetJobNames(group);
                foreach (var name in names)
                {
                    inScheduler.DeleteJob(name, group);
                }
            }
        }

        public virtual void Dispose()
        {
            _infraObjects = null;
            _globalMirrors = null;
        }

        public void TestConnectionAction()
        {
            try
            {
                _logger.Info("==========   Test Connection Action STARTED  =============" + Environment.NewLine);

                var client = new BcmsClient();

                client.TestConnection();
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform Test Connection Action . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform Test Connection Action . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void SrdfDiscoveryAction()
        {
            try
            {
                _logger.Info("========== Srdf Discovery Action STARTED  =============" + Environment.NewLine);

                var client = new BcmsClient();

                client.SrdfDiscovery();
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while perform Test Connection Action . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while perform Test Connection Action . ERROR MESSAGE :" + exc.Message);
            }
        }

        public void GetVCenterServiceStatus()
        {
            try
            {
                _logger.Info("=========== Get VCenter Service Started================");


                var Client = new VCenterClient();
                Client.GetVcenterSelectedProfile();


                _logger.Info("=========== Get VCenter Service Completed================");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while Get VCenter Service Status . ERROR MESSAGE :" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while Get VCenter Service Status . ERROR MESSAGE :" + exc.Message);
            }

        }

        #endregion Public Method

        #region CustomMethod

        protected virtual IScheduler GetScheduler()
        {
            return _schedulerFactory.GetScheduler();
        }

        protected virtual IScheduler GetDataSyncScheduler()
        {
            return _dataSyncSchedulerFactory.GetScheduler();
        }
        protected virtual Quartz.IScheduler GetParallelScheduler()
        {
            //try
            //{
            //    return _parallelSchedulerFactory.GetScheduler();
            //}
            //catch (Exception ex)
            //{
            return _parallelSchedulerFactory.GetScheduler();
            // }
        }
        private ISchedulerFactory CreateDataSyncSchedulerFactory()
        {
            return new StdSchedulerFactory();
        }

        public void PrepareMonitorService()
        {
            IList<MonitorService> monitorServices = MonitorServiceDataAccess.GetAllMonitorService();

            if (monitorServices.Count > 0)
            {
                foreach (var monitorService in monitorServices)
                {
                    InitializeMonitorServiceJob(monitorService);
                }
            }
        }

        private void PrepareMonitorStructure()
        {
            IList<Infrastructure> infraList = InfrastructureDataAccess.GetAll();

            if (infraList.Count > 0)
            {
                foreach (var infra in infraList)
                {
                    if (!string.IsNullOrEmpty(infra.UserNames))
                    {
                        InitializeMonitorPassword(infra);
                    }
                    if (!string.IsNullOrEmpty(infra.FileName))
                    {
                        InitializeMonitorFile(infra);
                    }
                }
            }
        }

        // Added By Jeyapandi
        public void PrepareMonitorApplicationWorkflow()
        {
            _logger.Info("PrepareMonitorApplicationWorkflow start");

            ActiveInfraObjects = InfraObjectDataAccess.GetAllInfraObjects();

            if (ActiveInfraObjects.Count > 0)
            {
                foreach (var activeInfraObject in ActiveInfraObjects)
                {
                    if (activeInfraObject.MonitoringWorkflow > 0)
                    {
                        InitializeMonitorApplicationWorkflow(activeInfraObject);
                    }
                    else
                    {
                        _logger.Debug("==============" + activeInfraObject.Name + "No Workflow attached in MonitorApplicationWorkflow Jobs ================");
                    }
                }
            }
        }

        //private void ExecuteParallelWorkflow()
        //{
        //    try
        //    {
        //        IList<ParallelDROperation> parallelDRs = ParallelDROperationDataAccess.GetByStatus("Pending");

        //        if (parallelDRs != null && parallelDRs.Count > 0)
        //        {
        //            foreach (var parallelDR in parallelDRs)
        //            {
        //                ParallelDROperationDataAccess.UpdateByStatus(parallelDR.Id, "Running");

        //                var activeParallelJob = ParallelGroupWorkflowDataAccess.GetByParallelDROperationId(parallelDR.Id);

        //                foreach (var parallelInfo in activeParallelJob)
        //                {
        //                    InitializingParallelWorkflow(parallelInfo);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        if (exc.InnerException != null)
        //            _logger.Error("Error occurred while executing Parallel workflow /r/n" + exc.InnerException);
        //        else
        //            _logger.Error("Error occurred while executing Parallel workflow /r/n" + exc.Message);
        //    }
        //}
        //Added by Karthick B for ITIT-10566
        public void ExecuteParallelWorkflow()
        {
            try
            {
                IList<ParallelDROperation> parallelDRs = ParallelDROperationDataAccess.GetByStatus("Pending");
                //var parallelDR = ParallelDROperationDataAccess.GetByParallelLastId(2083);

                _logger.Info("ExecuteParallelWorkflow :Pending state count from ParallelDR: " + parallelDRs.Count);
                if (parallelDRs != null && parallelDRs.Count > 0)
                {
                    foreach (var parallelDR in parallelDRs)
                    {
                        _logger.Info("ExecuteParallelWorkflow: Get Pending state Ids from ParallelDR: " + parallelDR.Id);

                        ParallelDROperationDataAccess.UpdateByStatus(parallelDR.Id, "Running");

                        _logger.Info("Update the ParallelDR status from pending to Running" + parallelDR.Id);

                        var activeParallelJob = ParallelGroupWorkflowDataAccess.GetByParallelDROperationId(parallelDR.Id);

                        _logger.Info("ExecuteParallelWorkflow: Get the count from Parallel Groupworkflow: " + activeParallelJob.Count);

                        System.Threading.Tasks.Parallel.ForEach(activeParallelJob,
                            x => InitializingParallelWorkflow(x));
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error occurred while executing Parallel workflow method /r/n" + exc.InnerException);
                else
                    _logger.Error("Error occurred while executing Parallel workflow method/r/n" + exc.Message);
            }
        }
        //Added by Karthick B for ITIT-10566
        private void PrepareMonitoringGlobalMirror()
        {
            ActiveGlobalMirror = GlobalMirrorDataAccess.GetGlobalMirrorGroupByStorageImageId();

            if (ActiveGlobalMirror.Count > 0)
            {
                foreach (var activeGlobalMiror in ActiveGlobalMirror)
                {
                    InitializeMonitorGlobalMirrorJob(activeGlobalMiror);
                }
            }
        }

        private void PrepareActiveBusinessService()
        {
            try
            {
                IList<BusinessService> services = BusinessServiceDataAccess.GetActiveBusinessServices();

                if (services.Count > 0)
                {
                    foreach (var service in services)
                    {
                        InitializeMonitorBusinessService(service);
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.ErrorFormat("Exception occurred while initializing active BusinessService " + exc.InnerException);
                else
                    _logger.ErrorFormat("Exception occurred while initializing active BusinessService " + exc.Message);
            }
        }

        public void PrepareActiveInfraObjects()
        {
            try
            {
                ActiveInfraObjects = InfraObjectDataAccess.GetAllInfraObjects();
               // ActiveInfraObjects = ActiveInfraObjects.Where(AI => AI.Id == 384).ToList();

                if (ActiveInfraObjects.Count > 0)
                {
                    foreach (var activeInfraObject in ActiveInfraObjects)
                    {
                        switch (activeInfraObject.ReplicationType)
                        {
                            case (int)ReplicationType.OracleDataGuard:

                               // InitializeOracleMonitorComponents(activeInfraObject);

                                //// added by kiran
                                //InitializArchivedLogDeleteJob(activeInfraObject);
                                //InitializeMonitorReplicationLog(activeInfraObject);

                                _InitializeOracleMonitorComponents(activeInfraObject);

                                _InitializArchivedLogDeleteJob(activeInfraObject);

                                _InitializeMonitorReplicationLog(activeInfraObject);

                                break;

                            case (int)ReplicationType.MSSCR:

                                InitializeMonitorExchangeSCRJob(activeInfraObject);

                                break;

                            case (int)ReplicationType.MSSQLServerNative:

                                InitializeMonitorSqlNative(activeInfraObject);

                                break;

                            case (int)ReplicationType.Enterprisedb:

                                IntailizingPostGreSqlMonitoring(activeInfraObject);

                                break;

                            case (int)ReplicationType.Sql2000FastCopy:

                                IntailizingMSSqlLogGeneration(activeInfraObject);

                                break;

                            case (int)ReplicationType.OracleWithDataSync:

                                //InitializeOracleMonitorComponents(activeInfraObject);

                                _InitializeOracleMonitorComponents(activeInfraObject);
                                //InitializeArchivedLogApplyJob(activeInfraObject);

                                //InitializeOracleFastCopyJob(activeInfraObject);


                                InitializeOracleFastCopyJob(activeInfraObject);

                                InitializeArchivedLogApplyJob(activeInfraObject);

                                //   InitializeOracleFastCopyJob(activeInfraObject);

                                break;


                            case (int)ReplicationType.DataSync:

                                InitializeApplicationReplicationJob(activeInfraObject);

                                InitializeMonitorApplicationJob(activeInfraObject);

                                InitializeApplicationDeletionJob(activeInfraObject);

                                break;

                            case (int)ReplicationType.DB2HADR:
                            case (int)ReplicationType.DB2HADR9X:
                                InitializeMonitorHADRJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.EMCSRDFOracleFullDB:

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                //InitializeGroupMonitorSRDF(activeInfraObject);

                                InitializeGroupMonitorSRDFDatLag(activeInfraObject);

                                break;

                            case (int)ReplicationType.OracleFullDbGlobalMirror:

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                break;

                            case (int)ReplicationType.EMCSRDFMSSQLFullDB:

                                InitializeMSSQLEMCSRDFFULLDBMonitorComponents(activeInfraObject);

                                //  InitializeGroupMonitorSRDF(activeInfraObject);

                                InitializeGroupMonitorSRDFDatLag(activeInfraObject);

                                break;


                            case (int)ReplicationType.NetAppSnapMirror:

                                InitializeSnapMirrorMonitor(activeInfraObject);

                                break;

                            case (int)ReplicationType.VMWareSnapmirror:

                                InitializMonitorVmWareJob(activeInfraObject);

                                InitializeSnapMirrorMonitor(activeInfraObject);

                                break;

                            case (int)ReplicationType.VMWareHitachiUR:

                                InitializMonitorVmWareJob(activeInfraObject);

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializMonitorHitachiHUROracleFullDBJob(activeInfraObject);


                                break;

                            case (int)ReplicationType.MSExchangeDAG:

                                InitializeExchangeDagJob(activeInfraObject);

                                break;

                            case (int)ReplicationType.EC2S3DataSync:

                                InitializeEC2S3DataSyncJob(activeInfraObject);

                                InitializeEC2S3DataSyncReplicationJob(activeInfraObject);

                                break;

                            case (int)ReplicationType.Postgre9x:
                                InitializePostgre9xMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.SQLNative2008:
                                //  InitializeMonitorMSSqlNative2008(activeInfraObject);
                                _InitializeMonitorMSSqlNative2008(activeInfraObject);
                                break;

                            case (int)ReplicationType.MSSQLAlwaysOn:
                                InitializeMonitorMSSqlAlwayson(activeInfraObject);
                                break;

                            case (int)ReplicationType.OracleFullDB_NetAppSnapMirror:
                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializeSnapMirrorMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.MSSqlNetAppSnapMirror:
                                //InitializeMSSQLEMCSRDFFULLDBMonitorComponents(activeInfraObject);
                                IntailizingMSSqlNetAppJob(activeInfraObject);
                                InitializeSnapMirrorMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.MSSQLDataBaseMirror:
                                InitializeMSSQLEMCSRDFFULLDBMonitorComponents(activeInfraObject);
                                InitializeMSSQLMirrorMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.MSSQLDoubleTakeFullDB:
                                InitializeMonitordoubleTake(activeInfraObject);
                                break;
                            case (int)ReplicationType.ApplicationDoubleTake:
                                InitializeApplicationMonitordoubleTake(activeInfraObject);
                                break;

                            case (int)ReplicationType.SYBASEDataBase:
                                InitializeSybaseMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.SybaseWithSRS:
                                InitializeSybaseWithSRSMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.HITACHIUROracleFullDB:

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializMonitorHitachiHUROracleFullDBJob(activeInfraObject);
                                break;
                            case (int)ReplicationType.HITACHITrueCopy:

                                break;

                            case (int)ReplicationType.HitachiOracleFullDBRac:

                                _InitializeOracleMonitorComponents(activeInfraObject);
                                //InitializeOracleMonitorComponents(activeInfraObject);
                                InitializMonitorHitachiHUROracleFullDBJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.EmcsrfdOracleFullDBRac:

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializeGroupMonitorSRDFDatLag(activeInfraObject);

                                break;
                            case (int)ReplicationType.HyperV:
                                InitializeHyperVMonitorComponents(activeInfraObject);
                                break;

                            case (int)ReplicationType.MySqlNativeLogShipping:
                                _logger.Info("Mysql Native Initialized ");
                                InitializeMySqlNativeMonitor(activeInfraObject);
                                break;

                            case (int)ReplicationType.OracleFullDBSVCGlobalMirror:
                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializeSVCGlobalMirrorMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.MSSQLDBMirroring:

                                InitializeDBMSSQLMirrorMonitor(activeInfraObject);
                                break;
                            case (int)ReplicationType.SVCMSSQLFullDB:

                                InitializeMSSQLSVCFULLDBMonitorComponents(activeInfraObject);
                                InitializeSVCGlobalMirrorMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.AppHitachiUr:
                                InitializMonitorHitachiHUROracleFullDBJob(activeInfraObject);
                                InitializeMonitorApplicationService(activeInfraObject);
                                // InitializePasswordFileMonitor(activeInfraObject);
                                break;

                            case (int)ReplicationType.DB2IBMGlobalMirror:
                                InitializeMonitorHADRJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.VMWareWithSVC:

                                InitializMonitorVmWareJob(activeInfraObject);
                                InitializeSVCGlobalMirrorMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.HitachiURMSSQLFullDB:
                                InitializeMSSQLDBMonitorComponents(activeInfraObject);
                                //InitializMonitorHitachiHUROracleFullDBJob(activeInfraObject);
                                InitializMonitorHitachiHURMSSQLFullDBJob(activeInfraObject);
                                break;
                            case (int)ReplicationType.GlobalMirrorMssqlFullDB:
                                InitializeMSSQLDBMonitorComponents(activeInfraObject);
                                PrepareMonitoringGlobalMirror();
                                break;

                            case (int)ReplicationType.NetAppSnapMirrorPostgresFullDB:
                                InitializeSnapMirrorMonitor(activeInfraObject);
                                InitializePostgre9xMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.SVCGlobalMirrorOracleFullDBRac:

                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                InitializeSVCGlobalMirrorMonitorJob(activeInfraObject);

                                break;
                            case (int)ReplicationType.SRMVMware:

                                InitializeSRMVMwareMonitorJob(activeInfraObject);

                                break;

                            case (int)ReplicationType.EMCSRDFMysqlFullDB:
                                InitializeMysqlDBMonitor(activeInfraObject);
                                InitializeGroupMonitorSRDFDatLag(activeInfraObject);
                                break;


                            case (int)ReplicationType.MAXDBWithDataSync:

                                //InitializeApplicationReplicationJob(activeInfraObject);
                                // InitializeMonitorApplicationJob(activeInfraObject);
                                InitializeMAXDBMonitorComponents(activeInfraObject);

                                break;

                            case (int)ReplicationType.MIMIX:
                                InitializeApplicationMimix(activeInfraObject);
                                break;

                            case (int)ReplicationType.ApplicationeBDR:

                                InitializeApplicationeBDRMonitorJob(activeInfraObject);

                                break;
                            case (int)ReplicationType.DRNET:
                                InitializeDrNetMonitorJob(activeInfraObject);
                                break;

                            case (int)ReplicationType.TPCR:

                                InitializeTPCRMonitor(activeInfraObject);

                                break;

                            case (int)ReplicationType.MySQLGlobalMirrorFullDB:
                                InitializeMysqlDBMonitor(activeInfraObject);
                                break;

                            case (int)ReplicationType.RecoverPoint:

                                InitializeRecoverPointMonitor(activeInfraObject);

                                break;
                            case (int)ReplicationType.RecoverPointOracleFULLDB:

                                InitializeRecoverPointMonitor(activeInfraObject);
                                //InitializeOracleMonitorComponents(activeInfraObject);
                                _InitializeOracleMonitorComponents(activeInfraObject);
                                break;

                            case (int)ReplicationType.RecoverPointMSSQLFULLDB:
                                InitializeRecoverPointMonitor(activeInfraObject);
                                InitializeMSSQLEMCSRDFFULLDBMonitorComponents(activeInfraObject);

                                break;

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.ErrorFormat("Exception occurred while initializing active InfraObject " + exc.InnerException);
                else
                    _logger.ErrorFormat("Exception occurred while initializing active InfraObject " + exc.Message);
            }

            //ActiveGroups = GroupDataAccess.GetAllGroups();

            //if (ActiveGroups.Count > 0)
            //{
            //    foreach (var activeGroup in ActiveGroups)
            //    {
            //        switch (activeGroup.ReplicationType)
            //        {
            //            case (int)ReplicationType.MSSQLServerNative:

            //                IntailizingMonitorsqlNative(activeGroup);

            //                break;

            //            case (int)ReplicationType.OracleDataGuard:

            //                //InitializeMonitorComponents(activeGroup);

            //                //InitializMonitorDataGuardJob(activeGroup);

            //                //InitializMonitorArchiveGapAndRecovery(activeGroup);

            //                // added by kiran

            //               // InitializeOracleMonitorComponents(activeGroup);

            //                // added by kiran
            //              InitializArchivedLogDeleteJob(activeGroup);

            //                //    InitializArchivedLogReplicationJob(activeGroup);

            //                //   InitializeMonitorReplicationLog(activeGroup);

            //                break;

            //            case (int)ReplicationType.MSSCR:
            //              //  InitializMonitorExchangeSCRJob(activeGroup);
            //                break;

            //            case (int)ReplicationType.VMWareSnapmirror:
            //                InitializeSnapMirrorMonitor(activeGroup);
            //                break;
            //            case (int)ReplicationType.VMWareDataSync:

            //                InitializMonitorVmWareJob(activeGroup);
            //                break;

            //            case (int)ReplicationType.DB2HADR:
            //            case (int)ReplicationType.DB2HADR9X:
            //                InitializMonitorHADRJob(activeGroup);
            //                break;

            //            case (int)ReplicationType.IBMGlobalMirror:
            //                InitializeMonitorComponents(activeGroup);
            //                InitializExecuteDataSynchronizationJob(activeGroup);
            //                InitializeMonitorReplicationLog(activeGroup);

            //                break;

            //            case (int)ReplicationType.Sql2000FastCopy:
            //                IntailizingMSSqlLogGeneration(activeGroup);
            //                break;

            //            case (int)ReplicationType.OracleWithDataSync:

            //                InitializeMonitorComponents(activeGroup);

            //                //InitializMonitorArchiveGapAndRecovery(activeGroup);

            //                IntializingOracleFastCopyJob(activeGroup);

            //                InitializeMonitorReplicationLog(activeGroup);

            //                InitializArchivedLogDeleteJob(activeGroup);

            //               InitializArchivedLogApplyJob(activeGroup);

            //                break;
            //            case (int)ReplicationType.HITACHIUR_OracleFullDB:
            //                InitializMonitorHitachiHUROracleFullDBJob(activeGroup);

            //                break;
            //        }

            //    }
            //}
        }

        private void SchedulingDatbaseBackup()
        {
            //try
            //{
            //    CurrentDatabaseBackup = DatabaseBackupInfoDataAccess.GetAllDatabaseBackupInfo();

            //    if (CurrentDatabaseBackup.Count > 0)
            //    {
            //        foreach (var databaseBackup in CurrentDatabaseBackup)
            //        {
            //            IntializingDatabaseBackup(databaseBackup);
            //        }

            //    }

            //}
            //catch (Exception)
            //{
            //}

            _logger.Debug(" Start Scheduling DatabaseBackup");

            CurrentDatabaseBackup = DatabaseBackupInfoDataAccess.GetAllDatabaseBackupInfo();
            if (CurrentDatabaseBackup.Count != 0)
            {
                if (CurrentDatabaseBackup.Count > 0)
                {
                    foreach (var databaseBackup in CurrentDatabaseBackup)
                    {
                        _logger.Debug(" Start Initializing DatabaseBackup");
                        InitializeDatabaseBackup(databaseBackup);
                    }
                }
            }
            else
            {
                _logger.Debug("Database backup is not scheduled");
            }
        }

        //private void ApplicationMonitoring()
        //{
        //    try
        //    {
        //        IList<Application> applications = ApplicationDataAccess.GetAllApplication();

        //        if (applications.Count > 0)
        //        {
        //            foreach (var activeApplication in applications)
        //            {
        //                //if (activeApplication.IsReplication == 1)
        //                //{
        //                //    InitializeMonitorSRDF(activeApplication);

        //                //    InitializeMonitorSRDFDatLag(activeApplication);

        //                //    InitializeMonitorApplicationService(activeApplication);

        //                //}
        //            }
        //        }
        //    }
        //    catch (Exception)
        //    {
        //    }

        //}
        //Added by Karthick B for ITIT-10566
        protected virtual Quartz.ISchedulerFactory CreateParallelSchedulerFactory1()
        {

            return new Quartz.Impl.StdSchedulerFactory();
        }
       
        //Added by Karthick B for ITIT-10566
        protected virtual ISchedulerFactory CreateSchedulerFactory()
        {
            return new StdSchedulerFactory();
        }

        protected virtual IScheduler Scheduler
        {
            get { return _scheduler; }
        }

        #endregion CustomMethod


        #region Dynamic Re-Schedule

        private void InitializeRescheduleMSSqlNative2008MonitorComponents(InfraObject infraObject, string Cron, string classname)
        {
            try
            {
                _logger.Info("MonitorMSSqlNative2008 Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup");

                if (job != null)
                {
                    _logger.Info("Sql Rescheduling Started");

                    if (_scheduler.DeleteJob(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup"))
                    {
                        _logger.Info("Schedule MSSqlNative 2008 Job Delete Successfully for :" + infraObject.Name);
                    }

                    // var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", infraObject.Name + "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));
                    var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));
                    sqlnativ2008eJobDetail.JobDataMap.Put(MSSqlNative2008Job.InfraObject, infraObject);
                    sqlnativ2008eJobDetail.JobDataMap.Put("classname", classname);

                    var sqlnative2008Trigger = new CronTrigger(infraObject.Name + "_sqlnative2008jobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(sqlnativ2008eJobDetail, sqlnative2008Trigger);

                    _logger.Info(sqlnativ2008eJobDetail.Name + " MSSql Native 2008 Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        private void InitializeRescheduleDB2MonitorComponents(InfraObject infraObject, string Cron, string classname)
        {
            try
            {
                _logger.Info("MonitorDb2 Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorHADR", "MonitorHADRGroup");

                if (job != null)
                {
                    _logger.Info("Sql Rescheduling Started");

                    if (_scheduler.DeleteJob(infraObject.Name + "_MonitorHADR", "MonitorHADRGroup"))
                    {
                        _logger.Info("Schedule DB2 Job Delete Successfully for :" + infraObject.Name);
                    }

                    var hadrJobDetail = new JobDetail(infraObject.Name + "_MonitorHADR", infraObject.Name + "MonitorHADRGroup", typeof(MonitorHADRJob));

                    // monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                    hadrJobDetail.JobDataMap.Put(MonitorHADRJob.InfraObject, infraObject);
                    hadrJobDetail.JobDataMap.Put("classname", classname);

                    var hadrTrigger = new CronTrigger(infraObject.Name + "hadrTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(hadrJobDetail, hadrTrigger);

                    _logger.Info(hadrJobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleSqlServer2000MonitorComponents(InfraObject group, string Cron, string classname)
        {
            try
            {
                _logger.Info("SqlServer200 Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(group.Name + "sqlloggeneration", "sqlloggenerationGroup");

                if (job != null)
                {
                    _logger.Info("SqlServer2000 Rescheduling Started");

                    if (_scheduler.DeleteJob(group.Name + "sqlloggeneration", "sqlloggenerationGroup"))
                    {
                        _logger.Info("Schedule SqlServer200 Job Delete Successfully for :" + group.Name);
                    }

                    var sqlloggeneration = new JobDetail(group.Name + "sqlloggeneration", group.Name + "sqlloggenerationGroup", typeof(MSSqlServer2000Job));

                    // monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                    sqlloggeneration.JobDataMap.Put(MSSqlServer2000Job.Group, group);
                    sqlloggeneration.JobDataMap.Put("classname", classname);

                    var sqlloggenTrigger = new CronTrigger(group.Name + "sqlloggenTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(sqlloggeneration, sqlloggenTrigger);

                    _logger.Info(group.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleExchangeDAGMonitorComponents(InfraObject infraObject, string Cron, string classname)
        {
            try
            {
                _logger.Info("ExchangeDAG Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorExchangeDag", "MonitorExchangeDagGroup");

                if (job != null)
                {

                    if (_scheduler.DeleteJob(infraObject.Name + "_MonitorExchangeDag", "MonitorExchangeDagGroup"))
                    {
                        _logger.Info("Schedule ExchangeDAG Job Delete Successfully for :" + infraObject.Name);
                    }

                    var exchangeDAGJobDetail = new JobDetail(infraObject.Name + "_MonitorExchangeDag", infraObject.Name + "MonitorExchangeDagGroup", typeof(MonitorExchangeDagJob));

                    // monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                    exchangeDAGJobDetail.JobDataMap.Put(MonitorExchangeDagJob.InfrabojectId, infraObject.Id);
                    exchangeDAGJobDetail.JobDataMap.Put("classname", classname);

                    var exchangeDAGTrigger = new CronTrigger(infraObject.Name + "sqlloggenTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(exchangeDAGJobDetail, exchangeDAGTrigger);

                    _logger.Info(infraObject.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleAppRepDataSyncMonitorComponents(InfraObject infraObject, string Cron, string jobname, string classname)
        {
            try
            {
                _logger.Info("Applicatin DataSync Rescheduling Started");

                if (jobname == "ApplicationReplicationJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "FastCopyApplicationReplication", "ApplicationReplicationFastCopy");

                    if (job != null)
                    {
                        _logger.Info("Applicatin Replication Rescheduling Started");

                        if (_scheduler.DeleteJob(infraObject.Name + "FastCopyApplicationReplication", "ApplicationReplicationFastCopy"))
                        {
                            _logger.Info("Schedule Applicatin Replication Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail1 = new JobDetail(infraObject.Name + "FastCopyApplicationReplication", infraObject.Name + "ApplicationReplicationFastCopy", typeof(ApplicationReplicationJob));

                        applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.InfraObject, infraObject);

                        var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraObject.PRReplicationId);

                        applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.FastCopyId, replicationinfo.FastCopy.Id);

                        applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.ScheduleTime, replicationinfo.FastCopy.ScheduleTime);

                        applicationjobDetail1.JobDataMap.Put("classname", classname);

                        var applicationTrigger1 = new CronTrigger(infraObject.Name + "applicationTrigger1")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                        _logger.Info(applicationjobDetail1.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorApplicationJob")
                {
                    _logger.Info("Monitor Application Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "MonitorAppInfraObject", "MonitorAppInfraObject");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "MonitorAppInfraObject", "MonitorAppInfraObject"))
                        {
                            _logger.Info("Schedule Monitor Application Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail = new JobDetail(infraObject.Name + "MonitorAppInfraObject", "MonitorAppInfraObject", typeof(MonitorApplicationJob));

                        // monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                        applicationjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);
                        applicationjobDetail.JobDataMap.Put("classname", classname);

                        var applicationTrigger = new CronTrigger(infraObject.Name + "applicationTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail, applicationTrigger);

                        _logger.Info(applicationjobDetail.Name + " Job is Scheduled");

                    }
                }
                if (jobname == "ApplicationDeleteFilesJob")
                {
                    _logger.Info("Monitor ApplicationDeleteFilesJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "MonitorAppInfraObject", "MonitorAppInfraObject");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "MonitorAppInfraObject", "MonitorAppInfraObject"))
                        {
                            _logger.Info("Schedule ApplicationDeleteFilesJob Delete Successfully for :" + infraObject.Name);
                        }
                        string cronString = ConfigurationManager.AppSettings["DeleteApplicationJobCronString"];

                        var applicationjobDetail1 = new JobDetail(infraObject.Name + "ApplicationFileDeletionJob", "ApplicationFileDeletionJob", typeof(ApplicationDeleteFilesJob));

                        applicationjobDetail1.JobDataMap.Put(ApplicationDeleteFilesJob.InfraObjectId, infraObject.Id);

                        var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraObject.PRReplicationId);

                        applicationjobDetail1.JobDataMap.Put(ApplicationDeleteFilesJob.FastCopyId, replicationinfo.FastCopy.Id);
                        applicationjobDetail1.JobDataMap.Put("classname", classname);

                        var applicationTrigger1 = new CronTrigger(infraObject.Name + "ApplicationFileDeletionJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                        _logger.Debug(infraObject.Name + " Application Deletion Job is Scheduled at " + applicationTrigger1.CronExpressionString);
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeReschedulePostgre9XMonitorComponents(InfraObject infraObject, string Cron)
        {
            try
            {
                _logger.Info("Postgre9X Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "MonitorPostgres9xDB", "MonitorPostgres9xDBGroup");

                if (job != null)
                {

                    if (_scheduler.DeleteJob(infraObject.Name + "MonitorPostgres9xDB", "MonitorPostgres9xDBGroup"))
                    {
                        _logger.Info("Schedule Postgre9X Job Delete Successfully for :" + infraObject.Name);
                    }

                    var dbpostgrejob = new JobDetail(infraObject.Name + "MonitorPostgres9xDB", "MonitorPostgres9xDBGroup", typeof(Postgre9XJob));


                    dbpostgrejob.JobDataMap.Put(Postgre9XJob.InfraObject, infraObject);

                    var Postgre9xdbTrigger = new CronTrigger(infraObject.Name + "Postgre9xdbTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(dbpostgrejob, Postgre9xdbTrigger);

                    _logger.Debug(infraObject.Name + " InfraObject Monitor Postgres9x Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleMSSqlNativeLogMonitorComponents(InfraObject infraObject, string Cron)
        {
            try
            {
                _logger.Info("MSSqlNativeLog Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnativejob", "sqlnativejobGroup");

                if (job != null)
                {

                    if (_scheduler.DeleteJob(infraObject.Name + "_Sqlnativejob", "sqlnativejobGroup"))
                    {
                        _logger.Info("Schedule MSSqlNativeLog Job Delete Successfully for :" + infraObject.Name);
                    }

                    var sqlnativeJobDetail = new JobDetail(infraObject.Name + "_Sqlnativejob", infraObject.Name + "sqlnativejobGroup", typeof(MSSqlNativeLogjob));

                    sqlnativeJobDetail.JobDataMap.Put(MSSqlNativeLogjob.InfraObject, infraObject);

                    var sqlnativeTrigger = new CronTrigger(infraObject.Name + "_sqlnativejobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(sqlnativeJobDetail, sqlnativeTrigger);

                    _logger.Info(sqlnativeJobDetail.Name + " Sql Native Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        private void InitializeRescheduleMySqlNativeLogMonitorComponents(InfraObject infraobject, string Cron, string classname)
        {
            try
            {
                _logger.Info("MySqlNativeLog Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_MySqlNative", "MySqlNativeMonitor");

                if (job != null)
                {

                    if (_scheduler.DeleteJob(infraobject.Name + "_MySqlNative", "MySqlNativeMonitor"))
                    {
                        _logger.Info("Schedule MySqlNativeLog Job Delete Successfully for :" + infraobject.Name);
                    }

                    var mysqlNativeMonitorDetail = new JobDetail(infraobject.Name + "_MySqlNative", "MySqlNativeMonitor",
                                                        typeof(MySqlNativeJob));

                    mysqlNativeMonitorDetail.JobDataMap.Put(MySqlNativeJob.InfraObject, infraobject);
                    mysqlNativeMonitorDetail.JobDataMap.Put("classname", classname);

                    var mysqlMonitorJobTrigger = new CronTrigger(infraobject.Name + "_mysqlNativeTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(mysqlNativeMonitorDetail, mysqlMonitorJobTrigger);

                    _logger.Info(mysqlNativeMonitorDetail.Name + " MySql Native Monitor Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleMSSqlDbMirroingMonitorComponents(InfraObject infraobject, string Cron)
        {
            try
            {
                _logger.Info("MSSqlDbMirroing Rescheduling Started");

                JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_mssqlDBMirror", "MSSQLDBMirrorDetailGroup");

                if (job != null)
                {

                    if (_scheduler.DeleteJob(infraobject.Name + "_mssqlDBMirror", "MSSQLDBMirrorDetailGroup"))
                    {
                        _logger.Info("Schedule MSSqlDbMirroing Job Delete Successfully for :" + infraobject.Name);
                    }

                    var mssqlDBMirrorDetail = new JobDetail(infraobject.Name + "_mssqlDBMirror", "MSSQLDBMirrorDetailGroup",
                                                            typeof(SqlDBMirroringJob));

                    mssqlDBMirrorDetail.JobDataMap.Put(SqlDBMirroringJob.InfraObject, infraobject);

                    var mssqlDBMirrorJobTrigger = new CronTrigger(infraobject.Name + "_mssqlDBMirrorTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = Cron
                    };

                    _scheduler.ScheduleJob(mssqlDBMirrorDetail, mssqlDBMirrorJobTrigger);

                    _logger.Info(mssqlDBMirrorDetail.Name + " MSSQL Database Mirror Monitor Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleOracleMonitorComponents(InfraObject infraObject, string Cron, string jobname, string classname)
        {
            try
            {
                _logger.Info("Rescheduling Started");
                if (jobname == "MonitorOracleDgJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule MonitorOracleDgJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        // monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);
                        monitorjobDetail.JobDataMap.Put("classname", classname);

                        var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");

                    }
                }
                if (jobname == "ArchivedLogDeleteJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_DeleteArchiveLog", "ArchivedLogDeleted");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_DeleteArchiveLog", "ArchivedLogDeleted"))
                        {
                            _logger.Info("Schedule ArchivedLogDeleteJob Job Delete Successfully for :" + infraObject.Name);
                        }
                        string cronString = ConfigurationManager.AppSettings["DeleteArchiveJobCronString"];

                        var dataGuardJobDetail = new JobDetail(infraObject.Name + "_DeleteArchiveLog", "ArchivedLogDeleted", typeof(ArchivedLogDeleteJob));

                        dataGuardJobDetail.JobDataMap.Put(ArchivedLogDeleteJob.Group, infraObject);
                        dataGuardJobDetail.JobDataMap.Put("classname", classname);

                        var dataGuardTrigger = new CronTrigger(infraObject.Name + "_ArchivedLogDeletedTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = string.IsNullOrEmpty(cronString) ? "0 0 0/6 * * ?" : cronString
                        };

                        _scheduler.ScheduleJob(dataGuardJobDetail, dataGuardTrigger);

                        _logger.Info(dataGuardJobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorReplicateLogVolumeJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorReplicationLog", "MonitorReplicationLogGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorReplicationLog", "MonitorReplicationLogGroup"))
                        {
                            _logger.Info("Schedule MonitorReplicateLogVolumeJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var replicationLogJobDetail = new JobDetail(infraObject.Name + "_MonitorReplicationLog", "MonitorReplicationLogGroup", typeof(MonitorReplicateLogVolumeJob));



                        replicationLogJobDetail.JobDataMap.Put(MonitorReplicateLogVolumeJob.Group, infraObject);

                        var replicationLogTrigger = new CronTrigger(infraObject.Name + "_MonitorReplicationLogTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(replicationLogJobDetail, replicationLogTrigger);

                        _logger.Info(replicationLogJobDetail.Name + " Job is Scheduled");
                    }
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleOracleDataSyncMonitorComponents(InfraObject group, string Cron, string jobname, string classname)
        {
            try
            {
                _logger.Info("ApplyArchiveLog Rescheduling Started");

                if (jobname == "ApplyArchiveLogsJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_ApplyArchiveLog", "ArchivedLogApply");

                    if (job != null)
                    {
                        _logger.Info("Applicatin Replication Rescheduling Started");

                        if (_scheduler.DeleteJob(group.Name + "_ApplyArchiveLog", "ArchivedLogApply"))
                        {
                            _logger.Info("Schedule Applicatin Replication Job Delete Successfully for :" + group.Name);
                        }

                        var archiveJobDetail = new JobDetail(group.Name + "_ApplyArchiveLog", "ArchivedLogApply", typeof(ApplyArchiveLogsJob));

                        archiveJobDetail.JobDataMap.Put(ApplyArchiveLogsJob.Group, group);
                        archiveJobDetail.JobDataMap.Put("classname", classname);

                        var archiveTrigger = new CronTrigger(group.Name + "_ArchivedLogApplyTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(archiveJobDetail, archiveTrigger);

                        _logger.Info(archiveJobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "FastCopyOracleSyncJob")
                {
                    _logger.Info("FastCopyOracleSyncJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "OracleFastCopy", "OracleFastcopyGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "OracleFastCopy", "OracleFastcopyGroup"))
                        {
                            _logger.Info("Schedule FastCopyOracleSyncJob Job Delete Successfully for :" + group.Name);
                        }

                        var fastCopy = ReplicationBaseDataAccess.GetReplicationById(group.PRReplicationId);

                        if (fastCopy != null)
                        {
                            var oracleFastCopyJobDetails = new JobDetail(group.Name + "OracleFastCopy", "OracleFastcopyGroup", typeof(FastCopyOracleSyncJob));


                            oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.InfraObject, group);

                            oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.FastCopyId, fastCopy.FastCopy.Id);

                            oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.ScheduleTime, fastCopy.FastCopy.ScheduleTime);

                            oracleFastCopyJobDetails.JobDataMap.Put("classname", classname);

                            var oracleFastCopyTrigger = new CronTrigger(group.Name + "_OracleFastCopyTrigger")
                            {
                                StartTimeUtc = DateTime.UtcNow,

                                CronExpressionString = Cron
                            };

                            _scheduler.ScheduleJob(oracleFastCopyJobDetails, oracleFastCopyTrigger);

                            _logger.Info(oracleFastCopyJobDetails.Name + " Job is Scheduled. The Scheduled time interval is" + fastCopy.FastCopy.ScheduleTime);
                        }
                    }
                }
                if (jobname == "MonitorOracleDgJob")
                {
                    _logger.Info("MonitorOracleDgJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule MonitorOracleDgJob Job Delete Successfully for :" + group.Name);
                        }

                        var monitorjobDetail = new JobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        monitorjobDetail.JobDataMap.Put("InfraObjectJob", "0");
                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, group);
                        monitorjobDetail.JobDataMap.Put("classname", classname);

                        var monitorOracleDGtrigger = new CronTrigger(group.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleMSSqlNetAppSnapMirrorMonitorComponents(InfraObject group, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MSSqlNetAppSnapMirror Rescheduling Started");

                if (jobname == "MSSqlNetAppSnapMirror")
                {
                    JobDetail job = _scheduler.GetJobDetail(group.Name + "MSSqlNetAppSnapMirror", "MSSqlNetAppSnapMirror");

                    if (job != null)
                    {
                        _logger.Info("MSSqlNetAppSnapMirror Rescheduling Started");

                        if (_scheduler.DeleteJob(group.Name + "MSSqlNetAppSnapMirror", "MSSqlNetAppSnapMirror"))
                        {
                            _logger.Info("Schedule MSSqlNetAppSnapMirror Job Delete Successfully for :" + group.Name);
                        }

                        var sqlloggeneration = new JobDetail(group.Name + "MSSqlNetAppSnapMirror", group.Name + "MSSqlNetAppSnapMirror", typeof(MSSqlNetAppSnapMirror));


                        sqlloggeneration.JobDataMap.Put(MSSqlNetAppSnapMirror.Group, group);

                        var sqlloggenTrigger = new CronTrigger(group.Name + "_MSSqlNetAppSnapMirrorTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(sqlloggeneration, sqlloggenTrigger);

                        _logger.Info(sqlloggeneration.Name + " MSSql NetApp SnapMirror Job is Scheduled");
                    }
                }
                if (jobname == "SnapMirrorJob")
                {
                    _logger.Info("SnapMirrorJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "_snapMirror", "SnapMirrorDetailGroup"))
                        {
                            _logger.Info("Schedule SnapMirrorJob Delete Successfully for :" + group.Name);
                        }

                        var snapMirrorDetail = new JobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup",
                                                            typeof(SnapMirrorJob));

                        snapMirrorDetail.JobDataMap.Put(SnapMirrorJob.Group, group);

                        var snapMirrorJobTrigger = new CronTrigger(group.Name + "_snapMirrorTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(snapMirrorDetail, snapMirrorJobTrigger);

                        _logger.Info(snapMirrorDetail.Name + " Snap Mirror Monitor Job is Scheduled");

                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleOracleFullDBNetAppSnapMirrorMonitorComponents(InfraObject group, string Cron, string jobname)
        {
            try
            {
                _logger.Info("OracleFullDbSnapMirrorJob Rescheduling Started");

                if (jobname == "SnapMirrorJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup");

                    if (job != null)
                    {
                        _logger.Info("OracleFullDbSnapMirrorJob Rescheduling Started");

                        if (_scheduler.DeleteJob(group.Name + "_snapMirror", "SnapMirrorDetailGroup"))
                        {
                            _logger.Info("Schedule OracleFullDbSnapMirrorJob Job Delete Successfully for :" + group.Name);
                        }

                        var snapMirrorDetail = new JobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup",
                                                          typeof(SnapMirrorJob));

                        snapMirrorDetail.JobDataMap.Put(SnapMirrorJob.Group, group);

                        var snapMirrorJobTrigger = new CronTrigger(group.Name + "_snapMirrorTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(snapMirrorDetail, snapMirrorJobTrigger);

                        _logger.Info(snapMirrorDetail.Name + " Snap Mirror Monitor Job is Scheduled");
                    }
                }
                if (jobname == "MonitorOracleDgJob")
                {
                    _logger.Info("OracleFullDbNetAppMonitorOracleDgJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule OracleFullDbNetAppMonitorOracleDg Job Delete Successfully for :" + group.Name);
                        }

                        var monitorjobDetail = new JobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, group);

                        var monitorOracleDGtrigger = new CronTrigger(group.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleEMCOracleFullDBMonitorComponents(InfraObject infraObject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("OracleFullDb Rescheduling Started");

                if (jobname == "MonitorSRDFDataLagJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup");

                    if (job != null)
                    {
                        _logger.Info("OracleFullDb Rescheduling Started");

                        if (_scheduler.DeleteJob(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup"))
                        {
                            _logger.Info("Schedule OracleFullDbSnapMirrorJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                        applicationjobDetail2.JobDataMap.Put(MonitorSRDFDataLagJob.InfraObject, infraObject);

                        var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                        _logger.Debug(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
                    }
                }
                if (jobname == "MonitorOracleDgJob")
                {
                    _logger.Info("OracleFullDb OracleDgJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule OracleFullDb MonitorOracleDg Job Delete Successfully for :" + infraObject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);

                        var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleHirtachiOracleFullDBMonitorComponents(InfraObject group, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MonitorHitachiHURJob Rescheduling Started");

                if (jobname == "MonitorHitachiHURJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup");

                    if (job != null)
                    {
                        _logger.Info("MonitorHitachiHURJob Rescheduling Started");

                        if (_scheduler.DeleteJob(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup"))
                        {
                            _logger.Info("Schedule MonitorHitachiHURJob Job Delete Successfully for :" + group.Name);
                        }

                        var hitachiHURJobDetailJobDetail = new JobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup", typeof(MonitorHitachiHURJob));

                        hitachiHURJobDetailJobDetail.JobDataMap.Put(MonitorHitachiHURJob.Group, group);

                        var hitachiHURTrigger = new CronTrigger(group.Name + "_MonitorHitachiHURTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(hitachiHURJobDetailJobDetail, hitachiHURTrigger);

                        _logger.Info(hitachiHURJobDetailJobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorOracleDgJob")
                {
                    _logger.Info("Hitachi OracleDgJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule Hitachi OracleFullDb MonitorOracleDg Job Delete Successfully for :" + group.Name);
                        }

                        var monitorjobDetail = new JobDetail(group.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, group);

                        var monitorOracleDGtrigger = new CronTrigger(group.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleMSSqlDBMirrorMonitorComponents1(InfraObject infraobject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MSSQLMirroringMonitoringJob Rescheduling Started");

                if (jobname == "MSSQLMirroringMonitoringJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_mssqlMirror", "MSSQLMirrorDetailGroup");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraobject.Name + "_mssqlMirror", "MSSQLMirrorDetailGroup"))
                        {
                            _logger.Info("Schedule MSSQLMirroringMonitoringJob Job Delete Successfully for :" + infraobject.Name);
                        }

                        var mssqlMirrorDetail = new JobDetail(infraobject.Name + "_mssqlMirror", "MSSQLMirrorDetailGroup",
                                                    typeof(MSSQLMirroringMonitoringJob));

                        mssqlMirrorDetail.JobDataMap.Put(MSSQLMirroringMonitoringJob.InfraObject, infraobject);

                        var mssqlMirrorJobTrigger = new CronTrigger(infraobject.Name + "_mssqlMirrorTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(mssqlMirrorDetail, mssqlMirrorJobTrigger);

                        _logger.Info(mssqlMirrorDetail.Name + " MSSQL Database Mirror Monitor Job is Scheduled");
                    }
                }
                if (jobname == "MSSqlEmcSrdfFullDbJob")
                {
                    _logger.Info("MSSqlEmcSrdfFullDbJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraobject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob"))
                        {
                            _logger.Info("Schedule MSSqlEmcSrdfFullDbJob Job Delete Successfully for :" + infraobject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraobject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob", typeof(MSSqlEmcSrdfFullDbJob));


                        monitorjobDetail.JobDataMap.Put(MSSqlEmcSrdfFullDbJob.InfraObject, infraobject);

                        var MSSqlEmcSrdfFullDbtrigger = new CronTrigger(infraobject.Name + "_MSSqlEmcSrdfFullDbJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, MSSqlEmcSrdfFullDbtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraobject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleVMWareMonitorComponents(InfraObject group, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MonitorVmWareJob Rescheduling Started");

                if (jobname == "MonitorVmWareJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorVmWare", "MonitorVmWareGroup");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(group.Name + "_MonitorVmWare", "MonitorVmWareGroup"))
                        {
                            _logger.Info("Schedule MonitorVmWareJob Job Delete Successfully for :" + group.Name);
                        }

                        var vmwareJobDetail = new JobDetail(group.Name + "_MonitorVmWare", "MonitorVmWareGroup", typeof(MonitorVmWareJob));


                        vmwareJobDetail.JobDataMap.Put(MonitorVmWareJob.Group, group);

                        var vmWareTrigger = new CronTrigger(group.Name + "_MonitorVmWareTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(vmwareJobDetail, vmWareTrigger);

                        _logger.Info(vmwareJobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "SnapMirrorJob")
                {
                    _logger.Info("SnapMirrorJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(group.Name + "_snapMirror", "SnapMirrorDetailGroup"))
                        {
                            _logger.Info("Schedule SnapMirrorJob Job Delete Successfully for :" + group.Name);
                        }

                        var snapMirrorDetail = new JobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup",
                                                            typeof(SnapMirrorJob));


                        snapMirrorDetail.JobDataMap.Put(SnapMirrorJob.Group, group);

                        var snapMirrorJobTrigger = new CronTrigger(group.Name + "_snapMirrorTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(snapMirrorDetail, snapMirrorJobTrigger);

                        _logger.Info(snapMirrorDetail.Name + " Snap Mirror Monitor Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleEMCMSSQLFULLDBComponents(InfraObject infraObject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MSSqlEmcSrdfFullDbJob Rescheduling Started");

                if (jobname == "MSSqlEmcSrdfFullDbJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob"))
                        {
                            _logger.Info("Schedule MSSqlEmcSrdfFullDbJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob", typeof(MSSqlEmcSrdfFullDbJob));


                        monitorjobDetail.JobDataMap.Put(MSSqlEmcSrdfFullDbJob.InfraObject, infraObject);

                        var MSSqlEmcSrdfFullDbtrigger = new CronTrigger(infraObject.Name + "_MSSqlEmcSrdfFullDbJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, MSSqlEmcSrdfFullDbtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorSRDFDataLagJob")
                {
                    _logger.Info("MonitorSRDFDataLagJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup"))
                        {
                            _logger.Info("Schedule MonitorSRDFDataLagJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                        applicationjobDetail2.JobDataMap.Put(MonitorSRDFDataLagJob.InfraObject, infraObject);

                        var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                        _logger.Debug(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleHitachiMSSqlFullDbComponents(InfraObject infraObject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MssqlSVCFullDbJob Rescheduling Started");

                if (jobname == "MssqlSVCFullDbJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMSSQLDBJob", "MSSqlDbJob");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorMSSQLDBJob", "MSSqlDbJob"))
                        {
                            _logger.Info("Schedule MssqlSVCFullDbJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMSSQLDBJob", "MSSqlDbJob", typeof(MssqlSVCFullDbJob));

                        monitorjobDetail.JobDataMap.Put(MssqlSVCFullDbJob.InfraObject, infraObject);

                        var MSSqlDbtrigger = new CronTrigger(infraObject.Name + "_MSSqlDbJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, MSSqlDbtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorHitachiHURJob")
                {
                    _logger.Info("MonitorSRDFDataLagJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup"))
                        {
                            _logger.Info("Schedule MonitorSRDFDataLagJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var hitachiHURJobDetailJobDetail = new JobDetail(infraObject.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup", typeof(MonitorHitachiHURJob));


                        hitachiHURJobDetailJobDetail.JobDataMap.Put(MonitorHitachiHURJob.Group, infraObject);

                        var hitachiHURTrigger = new CronTrigger(infraObject.Name + "_MonitorHitachiHURTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(hitachiHURJobDetailJobDetail, hitachiHURTrigger);

                        _logger.Info(hitachiHURJobDetailJobDetail.Name + " Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        private void InitializeRescheduleEMCORacleFullDbRacComponents(InfraObject infraObject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("EMCORacleFullDbRac Rescheduling Started");

                if (jobname == "MonitorOracleDgJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup"))
                        {
                            _logger.Info("Schedule EMCORacleFullDbRac Job Delete Successfully for :" + infraObject.Name);
                        }

                        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);

                        var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorOracleDGJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                    }
                }
                if (jobname == "MonitorSRDFDataLagJob")
                {
                    _logger.Info("MonitorSRDFDataLagJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup"))
                        {
                            _logger.Info("Schedule MonitorSRDFDataLagJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                        applicationjobDetail2.JobDataMap.Put(MonitorSRDFDataLagJob.InfraObject, infraObject);

                        var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                        _logger.Debug(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeRescheduleEMCMySqlComponents(InfraObject infraObject, string Cron, string jobname)
        {
            try
            {
                _logger.Info("MonitorMySqlDBJob Rescheduling Started");

                if (jobname == "MonitorMySqlDBJob")
                {
                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMySqlDBJob", "MonitorMySqlDBMonitor");

                    if (job != null)
                    {

                        if (_scheduler.DeleteJob(infraObject.Name + "_MonitorMySqlDBJob", "MonitorMySqlDBMonitor"))
                        {
                            _logger.Info("Schedule MonitorMySqlDBJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var mysqlNativeMonitorDetail = new JobDetail(infraObject.Name + "_MonitorMySqlDBJob", "MonitorMySqlDBMonitor",
                                                        typeof(MonitorMySqlDBJob));

                        mysqlNativeMonitorDetail.JobDataMap.Put(MonitorMySqlDBJob.InfraObject, infraObject);

                        var mysqlMonitorJobTrigger = new CronTrigger(infraObject.Name + "_MonitorMySqlDBTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(mysqlNativeMonitorDetail, mysqlMonitorJobTrigger);

                        _logger.Info(mysqlNativeMonitorDetail.Name + " MySql DB Monitor Job is Scheduled");
                    }
                }
                if (jobname == "MonitorSRDFDataLagJob")
                {
                    _logger.Info("MonitorSRDFDataLagJob Rescheduling Started");

                    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup");

                    if (job != null)
                    {
                        if (_scheduler.DeleteJob(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup"))
                        {
                            _logger.Info("Schedule MonitorSRDFDataLagJob Job Delete Successfully for :" + infraObject.Name);
                        }

                        var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                        applicationjobDetail2.JobDataMap.Put(MonitorSRDFDataLagJob.InfraObject, infraObject);

                        var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = Cron
                        };

                        _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                        _logger.Debug(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        #region reschedule ODG

        public void InitializeRescheduleOracleMonitorComponents(InfraObject infraObject)
        {
            try
            {
                string Cron = "0 0/15 * * * ?";
                //JobDetail job = scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");
                IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup"));
                if (job == null)
                {



                    if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup")))
                    {
                        _logger.Info("ReSchedule _MonitorOracleDGJob Deleted Successfully for :" + infraObject.Name);
                    }

                    var _ntnxpd = JobBuilder.Create<MonitorOracleDgJob>().WithIdentity(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup").UsingJobData(MonitorOracleDgJob.InfraObject, infraObject.Id).Build();

                    var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_MonitorOracleDGJobTrigger", infraObject.Id + "MonitorOracleDGGroupJobTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                    scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                    _logger.Info(infraObject.Name + " :Job is MonitorOracleDGJob Scheduled ");


                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        public void InitializRescheduleArchivedLogDeleteJob(InfraObject infraObject)
        {

            IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted"));
            if (job == null)
            {

                string Cron = ConfigurationManager.AppSettings["DeleteArchiveJobCronString"];
                if (Cron == null)
                {
                    Cron = "0 0 0/6 * * ?";
                }

                if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted")))
                {
                    _logger.Info("ReSchedule ArchivedLogDeleted Deleted Successfully for :" + infraObject.Name);
                }

                var _ntnxpd = JobBuilder.Create<ArchivedLogDeleteJob>().WithIdentity(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted").UsingJobData(ArchivedLogDeleteJob.InfraObject, infraObject.Id).Build();

                var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_DeleteArchiveLogJobTrigger", infraObject.Id + "ArchivedLogDeletedJobTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                _logger.Info(infraObject.Name + " :Job is  Scheduled ");


            }




        }

        public void InitializeRescheduleMonitorReplicationLog(InfraObject infraObject)
        {


            IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup"));
            if (job == null)
            {

                string Cron = "0 55 0/23 * * ?";


                if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup")))
                {
                    _logger.Info("ReSchedule MonitorReplicationLogGroup Deleted Successfully for :" + infraObject.Name);
                }

                var _ntnxpd = JobBuilder.Create<MonitorReplicateLogVolumeJob>().WithIdentity(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup").UsingJobData(MonitorReplicateLogVolumeJob.InfraObject, infraObject.Id).Build();

                var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_MonitorReplicationLogJobTrigger", infraObject.Id + "MonitorReplicationLogGroupTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                _logger.Info(infraObject.Name + " :Job is  Scheduled ");



            }
        }

        public void InitializeRescheduleMonitorMSSqlNative2008(InfraObject infraObject)
        {
            try
            {
                string Cron = "0 0/15 * * * ?";

                //JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup");

                //if (job == null)
                //{
                //    _logger.InfoFormat("Initialized MonitorMSSqlNative2008...");
                //    // var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", infraObject.Name + "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                //    var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                //    sqlnativ2008eJobDetail.JobDataMap.Put(MSSqlNative2008Job.InfraObject, infraObject);

                //    var sqlnative2008Trigger = new CronTrigger(infraObject.Name + "_sqlnative2008jobTrigger")
                //    {
                //        StartTimeUtc = DateTime.UtcNow,

                //        CronExpressionString = "0 0/15 * * * ?"
                //    };

                //    _scheduler.ScheduleJob(sqlnativ2008eJobDetail, sqlnative2008Trigger);

                //    _logger.Info(sqlnativ2008eJobDetail.Name + " MSSql Native 2008 Job is Scheduled");
                //}



                //JobDetail job = scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");

                IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup"));
                if (job == null)
                {
                    _logger.InfoFormat("Initialized MonitorMSSqlNative2008...");


                    if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup")))
                    {
                        _logger.Info("ReSchedule MonitorMSSqlNative2008 Deleted Successfully for :" + infraObject.Name);
                    }

                    var _ntnxpd = JobBuilder.Create<MSSqlNative2008Job>().WithIdentity(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup").UsingJobData(MSSqlNative2008Job.InfraObject, infraObject.Id).Build();

                    var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_Sqlnative2008jobTrigger", infraObject.Id + "sqlnative2008jobGroupTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                    scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                    _logger.Info(infraObject.Name + " :Job is Sqlnative2008job Scheduled ");


                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize sqlnativ2008 component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initializesqlnativ2008 component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }
        #endregion



        #endregion


        #region InitializeMethod

        private void InitializeSRMVMwareMonitorJob(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_SRMVMwareMonitorJob", "SRMVMwareMonitorJobGroup");
                if (job == null)
                {

                    var monitorjobDetail = new JobDetail(infraObject.Name + "_SRMVMwareMonitorJob", "SRMVMwareMonitorJobGroup", typeof(SRMVMwareMonitorJob));

                    monitorjobDetail.JobDataMap.Put(SRMVMwareMonitorJob.Infraobject, infraObject);

                    var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_SRMVMwareMonitorJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize SRM VMware Monitor Component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize SRM VMware Monitor Component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        public void InitializingScheduleWorkflowOnclick(int Id, string scheduleworkflowname, string cron, InfraObject infraobject)
        {
            try
            {
                try
                {

                    //JobDetail job = _scheduler.GetJobDetail(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id);
                    //if (job != null)
                    //{
                    //    var ScheduleWorkflowJobTrigger1 = new CronTrigger(scheduleworkflowname + "_ScheduleWorkflowJob" + Id)
                    //    {
                    //        StartTimeUtc = DateTime.UtcNow,
                    //        CronExpressionString = cron
                    //    };
                    //    ScheduleWorkflowJobTrigger1.JobName = scheduleworkflowname + "_ScheduleWorkflowJob" + Id;
                    //    _scheduler.RescheduleJob(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id, ScheduleWorkflowJobTrigger1);

                    //    _logger.Info("workflow Name :" + scheduleworkflowname + " Job is ReScheduled for Infraobject :" + infraobject.Name + "with Cron Expressions" + cron);
                    //}
                    //else
                    //{
                    //    _logger.Info("workflow Job is not available in DB for :" + Id);
                    //}

                    ////if (_scheduler.UnscheduleJob(scheduleworkflowname + "_ScheduleWorkflowJobTrigger" + Id, "_ScheduleWorkflowJob" + Id))
                    ////{
                    ////    _logger.Info("Schedule Workflow Job Unschedule Successfully for :" + Id);
                    ////}

                    if (_scheduler.DeleteJob(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id))
                    {
                        _logger.Info("Schedule Workflow Job Delete Successfully for :" + Id);
                    }

                    JobDetail job = _scheduler.GetJobDetail(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id);
                    if (job == null)
                    {
                        var scheduleDetail = new JobDetail(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id, typeof(Infraobject_SchedulerJob));

                        scheduleDetail.JobDataMap.Put(Infraobject_SchedulerJob.SchedulerWfID, Id);

                        var ScheduleWorkflowJobTrigger = new CronTrigger(scheduleworkflowname + "_ScheduleWorkflowJobTrigger" + Id)
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = cron
                        };

                        _scheduler.ScheduleJob(scheduleDetail, ScheduleWorkflowJobTrigger);

                        _logger.Info("workflow Name :" + scheduleworkflowname + " Job is ReScheduled for Infraobject :" + infraobject.Name + "with Cron Expressions" + cron);
                    }

                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        _logger.Error("Error Occurred while initialize ReSchedule Workflow :(" + scheduleworkflowname + ") for Infraobject : " + infraobject.Name + " . /r/n" + exc.InnerException);
                    else
                        _logger.Error("Error Occurred while initialize ReSchedule Workflow : (" + scheduleworkflowname + ") for Infraobject: " + infraobject.Name + ". /r/n" + exc.Message);
                }


            }
            catch (Exception exc)
            {
                _logger.ErrorFormat("Exception occured while initializing Schedule Workflow : " + scheduleworkflowname + Environment.NewLine + "ERROR" + exc.Message);
            }

        }


        public void InitializingScheduleWorkflow(int Id, string scheduleworkflowname, string cron, InfraObject infraobject)
        {
            try
            {
                try
                {
                    JobDetail job = _scheduler.GetJobDetail(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id);
                    if (job == null)
                    {
                        var scheduleDetail = new JobDetail(scheduleworkflowname + "_ScheduleWorkflowJob" + Id, "_ScheduleWorkflowJob" + Id, typeof(Infraobject_SchedulerJob));

                        scheduleDetail.JobDataMap.Put(Infraobject_SchedulerJob.SchedulerWfID, Id);

                        var ScheduleWorkflowJobTrigger = new CronTrigger(scheduleworkflowname + "_ScheduleWorkflowJobTrigger" + Id)
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = cron
                        };

                        _scheduler.ScheduleJob(scheduleDetail, ScheduleWorkflowJobTrigger);

                        _logger.Info("workflow Name :" + scheduleworkflowname + " Job is Scheduled for Infraobject :" + infraobject.Name + "with Cron Expressions" + cron);
                    }

                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        _logger.Error("Error Occurred while initialize Schedule Workflow :(" + scheduleworkflowname + ") for Infraobject : " + infraobject.Name + " . /r/n" + exc.InnerException);
                    else
                        _logger.Error("Error Occurred while initialize Schedule Workflow : (" + scheduleworkflowname + ") for Infraobject: " + infraobject.Name + ". /r/n" + exc.Message);
                }


            }
            catch (Exception exc)
            {
                _logger.ErrorFormat("Exception occured while initializing Schedule Workflow : " + scheduleworkflowname + Environment.NewLine + "ERROR" + exc.Message);
            }

        }

        public void InitializeMonitorDiskspace(InfraobjectDiskMonitor infraobjectDiskMonitor, InfraObject infraobject, string isPRDR)
        {
            try
            {
                _logger.Info("Initializing MonitorDiskspace");
                Server server = null;
                string hostName = string.Empty;
                string cron = string.Empty;
                if (isPRDR == "PR")
                {
                    server = ServerDataAccess.GetServersById(infraobject.PRServerId);
                    //hostName = server.PRHostName;
                    //cron = infraobjectDiskMonitor.ScheduleTimePR;
                }
                else if (isPRDR == "DR")
                {
                    server = ServerDataAccess.GetServersById(infraobject.DRServerId);
                    //hostName = server.PRHostName;
                    //cron = infraobjectDiskMonitor.ScheduleTimeDR;
                }
                hostName = server.PRHostName;
                cron = infraobjectDiskMonitor.ScheduleTimePR;
                _logger.Info("Cron Value :" + cron);

                string jobName = hostName + "_MonitorDiskspaceJob" + server.Id;
                string groupName = "_MonitorDiskspaceJob" + infraobjectDiskMonitor.Id;
                string triggerName = hostName + "_" + isPRDR + "_InfraobjectDiskMonitorJobTrigger" + infraobjectDiskMonitor.Id;

                JobDetail job = _scheduler.GetJobDetail(jobName, groupName);
                if (job == null)
                {
                    var scheduleDetail = new JobDetail(jobName, groupName, typeof(InfraobjectDiskMonitorJob));

                    scheduleDetail.JobDataMap.Put(InfraobjectDiskMonitorJob.CronExpression, cron);
                    scheduleDetail.JobDataMap.Put(InfraobjectDiskMonitorJob.InfraobjectDiskMonitorId, infraobjectDiskMonitor.Id);

                    CronTrigger InfraobjectDiskMonitorJobTrigger = new CronTrigger(triggerName, groupName)
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = cron
                    };

                    _scheduler.ScheduleJob(scheduleDetail, InfraobjectDiskMonitorJobTrigger);

                    _logger.Info("Host Name :" + hostName + " (" + isPRDR + ") Job is Scheduled for Infraobject: " + infraobject.Name + " with Cron Expressions " + cron);
                }
                else
                {
                    _logger.Info("Job (" + job.Name + ") is already present. ");
                }
            }
            catch (Exception exc)
            {
                _logger.Error("Exception: (in InitializeMonitorDiskspace) " + exc.Message);
                _logger.ErrorFormat("Exception occured while initializing InitializeMonitorDiskspace (" + isPRDR + "): " + infraobject.Name + Environment.NewLine + "ERROR" + exc.Message);
            }
        }

        public void InitializingReport(string reportName, string cron)
        {
            try
            {
                try
                {
                    JobDetail job = _scheduler.GetJobDetail(reportName + "_ReportJobTrigger", "_ReportJobTrigger");
                    if (job == null)
                    {
                        var repoertDetail = new JobDetail(reportName + "_ReportJobTrigger", "_ReportJobTrigger", typeof(GenerateReportJob));

                        repoertDetail.JobDataMap.Put(GenerateReportJob.ReportName, reportName);

                        var reportrigger = new CronTrigger(reportName + "_ReportJobTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,
                            CronExpressionString = cron
                        };

                        _scheduler.ScheduleJob(repoertDetail, reportrigger);

                        _logger.Info(reportName + " Job is Scheduled");
                    }
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + reportName + "). /r/n" + exc.InnerException);
                    else
                        _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + reportName + "). /r/n" + exc.Message);
                }


            }
            catch (Exception exc)
            {
                _logger.ErrorFormat("Exception occured while initializing report job for : " + reportName + Environment.NewLine + "ERROR" + exc.Message);
            }

        }

        private void InitializeApplicationDeletionJob(InfraObject infraobject)
        {
            JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "ApplicationFileDeletionJob", "ApplicationFileDeletionJob");

            if (job == null)
            {
                string cronString = ConfigurationManager.AppSettings["DeleteApplicationJobCronString"];

                var applicationjobDetail1 = new JobDetail(infraobject.Name + "ApplicationFileDeletionJob", "ApplicationFileDeletionJob", typeof(ApplicationDeleteFilesJob));

                applicationjobDetail1.JobDataMap.Put(ApplicationDeleteFilesJob.InfraObjectId, infraobject.Id);

                var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraobject.PRReplicationId);

                applicationjobDetail1.JobDataMap.Put(ApplicationDeleteFilesJob.FastCopyId, replicationinfo.FastCopy.Id);

                var applicationTrigger1 = new CronTrigger(infraobject.Name + "ApplicationFileDeletionJobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = string.IsNullOrEmpty(cronString) ? "0 0/5 * * * ?" : cronString
                };

                _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                _logger.Debug(infraobject.Name + " Application Deletion Job is Scheduled at " + applicationTrigger1.CronExpressionString);
            }
        }

        private void InitializeOracleMonitorComponents(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");
                if (job == null)
                {

                    var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

                    monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);
                    //var DBInfo = DatabaseBaseDataAccess.GetDatabaseById(infraObject.PRDatabaseId);
                    //var CronTime = ConfigurationManager.AppSettings["MonitorCycleForActiveODG"].ToString();
                    var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorOracleDGJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        // CronExpressionString = (DBInfo.PRVersion == "11g" || DBInfo.PRVersion == "12c") ? CronTime : "0 0/5 * * * ?"

                        CronExpressionString = "0 0/15 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMAXDBMonitorComponents(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMAXDBJob", "MonitorMAXDBJobGroup");
                if (job == null)
                {

                    var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMAXDBJob", "MonitorMAXDBGroup", typeof(MonitorMAXDBJob));

                    monitorjobDetail.JobDataMap.Put(MonitorMAXDBJob.InfraObject, infraObject);

                    var monitorMaxDbtrigger = new CronTrigger(infraObject.Name + "_MonitorMAXDBJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, monitorMaxDbtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize maxdb monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize maxdb monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeApplicationMimix(InfraObject infraObject)
        {
            try
            {
                _logger.InfoFormat("Initialize Application Mimix Monitor Job");
                var mimixJob = new JobDetail(infraObject.Name + "ApplicationMimixjob", infraObject.Name + "ApplicationMimixjobGroup", typeof(ApplicationMimixJob));

                mimixJob.JobDataMap.Put(ApplicationMimixJob.InfraObject, infraObject);

                var mimixJobTrigger = new CronTrigger(infraObject.Name + "_applicationMimixJobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(mimixJob, mimixJobTrigger);

                _logger.Info(mimixJobTrigger.Name + "Application Mimix Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Application for Mimix InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Application for Mimix InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }
        private void InitializeApplicationeBDRMonitorJob(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_ApplicationeBDRMonitorJob", "ApplicationeBDRMonitorJobGroup");
                if (job == null)
                {

                    var monitorjobDetail = new JobDetail(infraObject.Name + "_ApplicationeBDRMonitorJob", "ApplicationeBDRMonitorJobGroup", typeof(ApplicationeBDRJob));

                    monitorjobDetail.JobDataMap.Put(ApplicationeBDRJob.InfraObject, infraObject);

                    var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_ApplicationeBDRMonitorJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/10 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize Appication eBDR Monitor Component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize Application eBDR Monitor Component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeHyperVMonitorComponents(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorHyperVJob", "MonitorHyperVGroup");
                if (job == null)
                {


                    var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorHyperVJob", "MonitorHyperVGroup", typeof(MonitorHyperVJob));

                    monitorjobDetail.JobDataMap.Put(MonitorHyperVJob.InfraObject, infraObject);

                    var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorHyperVJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize HyperV monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize HyperV monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }
        private void InitializeMonitorPassword(Infrastructure infrastructure)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infrastructure.Id + "_MonitorInfra", "MonitorInfraDGGroup");

                if (job == null)
                {

                    var monitorinfrajobDetail = new JobDetail(infrastructure.Id + "_MonitorInfra", "MonitorInfraDGGroup", typeof(MonitorPasswordChangeJob));

                    monitorinfrajobDetail.JobDataMap.Put(MonitorPasswordChangeJob.InfraObjectId, infrastructure.InfraObjectId);
                    monitorinfrajobDetail.JobDataMap.Put(MonitorPasswordChangeJob.ServerId, infrastructure.ServerId);
                    monitorinfrajobDetail.JobDataMap.Put(MonitorPasswordChangeJob.UserNames, infrastructure.UserNames);

                    var monitorOracleDGtrigger = new CronTrigger(infrastructure.Id + "_MonitorInfraTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorinfrajobDetail, monitorOracleDGtrigger);

                    _logger.Info(monitorinfrajobDetail.Name + " Password monitor Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Password (" + infrastructure.Id + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Password  (" + infrastructure.Id + "). /r/n" + exc.Message);
            }
        }


        private void InitializeMonitorFile(Infrastructure infrastructure)
        {
            try
            {
                var monitorinfrajobDetail = new JobDetail(infrastructure.Id + "_MonitorFileInfra", "MonitorFileIDGGroup", typeof(MonitorFileChangeJob));

                monitorinfrajobDetail.JobDataMap.Put(MonitorFileChangeJob.InfraObjectId, infrastructure.InfraObjectId);
                monitorinfrajobDetail.JobDataMap.Put(MonitorFileChangeJob.ServerId, infrastructure.ServerId);
                monitorinfrajobDetail.JobDataMap.Put(MonitorFileChangeJob.FileName, infrastructure.FileName);

                var monitorOracleDGtrigger = new CronTrigger(infrastructure.Id + "_MonitorFileITrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(monitorinfrajobDetail, monitorOracleDGtrigger);

                _logger.Info(monitorinfrajobDetail.Name + " File changes monitor Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor File changes (" + infrastructure.Id + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor File changes  (" + infrastructure.Id + "). /r/n" + exc.Message);
            }
        }


        private void InitializeMSSQLEMCSRDFFULLDBMonitorComponents(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob");
                if (job == null)
                {
                    var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMSSQLEMCSRDFFULLDBJob", "MSSqlEmcSrdfFullDbJob", typeof(MSSqlEmcSrdfFullDbJob));

                    monitorjobDetail.JobDataMap.Put(MSSqlEmcSrdfFullDbJob.InfraObject, infraObject);

                    var MSSqlEmcSrdfFullDbtrigger = new CronTrigger(infraObject.Name + "_MSSqlEmcSrdfFullDbJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, MSSqlEmcSrdfFullDbtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize MSSQL EMC SRDF FULL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize MSSQL EMC SRDF FULL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitorBusinessService(BusinessService service)
        {
            try
            {
                var monitorBusinessServiceJobDetail = new JobDetail(service.Name + "_MonitorBusinessService", "MonitorBusinessServiceGroup", typeof(MonitorBusinessServiceAvailbility));

                monitorBusinessServiceJobDetail.JobDataMap.Put(MonitorBusinessServiceAvailbility.BusinessServiceId, service.Id);

                var monitorBusinessService = new CronTrigger(service.Name + "_MonitorBusinessServiceTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(monitorBusinessServiceJobDetail, monitorBusinessService);

                _logger.Info(monitorBusinessServiceJobDetail.Name + "  monitor business Service Avilability Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor business service for Business Service (" + service.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor business service for Business Service (" + service.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitorGlobalMirrorJob(GlobalMirror globalMirror)
        {
            try
            {
                var globalMirrorjobDetail = new JobDetail(globalMirror.PRStorageImageId + "_GlobalMirror", "GlobalMirrorGroup", typeof(GlobalMirrorJob));

                globalMirrorjobDetail.JobDataMap.Put(GlobalMirrorJob.GlobalMirror, globalMirror);

                var globalMirrorTrigger = new CronTrigger(globalMirror.PRStorageImageId + "_GlobalMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(globalMirrorjobDetail, globalMirrorTrigger);

                _logger.Info(globalMirror.PRStorageImageId + "_Monitor Global Mirror Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor global mirror for Storage imageid (" + globalMirror.PRStorageImageId + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor global mirror for Storage imageid (" + globalMirror.PRStorageImageId + "). /r/n" + exc.Message);
            }
        }


        private void InitializeBIAWorkflowAnlyticJob()
        {
            try
            {
                var BIAAnalyticsJobbDetail = new JobDetail("BIAAnalytic", "BIAAnalytic", typeof(BIAAnalyticsJob));

                var BIAAnalyticTrigger = new CronTrigger("BIAAnalyticTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(BIAAnalyticsJobbDetail, BIAAnalyticTrigger);

                _logger.Info("BIA Workflow Analytic Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize BIA Analytic /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize BIA Analytic /r/n" + exc.Message);
            }
        }

        private void InitializeBIAWorkflowTrendsDataJob()
        {
            try
            {
                var BIAWorkflowTrendsDataJobbDetail = new JobDetail("BIAWorkflowTrendData", "BIAWorkflowDATA", typeof(BIATrendDatajob));

                var BIAWorkflowTrendsData = new CronTrigger("BIAWorkflowTrendsDataTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(BIAWorkflowTrendsDataJobbDetail, BIAWorkflowTrendsData);

                _logger.Info("BIA WorkflowTrendsData Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize BIA WorkflowTrendsData /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize BIA WorkflowTrendsData /r/n" + exc.Message);
            }
        }

        private void InitializeBIAActionTrendsDataJob()
        {
            try
            {
                var BIAActionTrendsDataJobbDetail = new JobDetail("BIAActionTrendsData", "BIAActionDATA", typeof(BIAActionTrendDataJob));

                var BIAActionTrendsData = new CronTrigger("BIAActionTrendsDataTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(BIAActionTrendsDataJobbDetail, BIAActionTrendsData);

                _logger.Info("BIA ActionTrendsData Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize BIA ActionTrendsData /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize BIA ActionTrendsData /r/n" + exc.Message);
            }
        }

        private void InitializeBIAActionAnlyticJob()
        {
            try
            {
                var BIAActionAnalyticsJobbDetail = new JobDetail("BIAActionAnalytic", "BIAActionAnalytic", typeof(BIAActionAnalyticsJob));

                var BIAActionAnalyticTrigger = new CronTrigger("BIAActionAnalyticTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(BIAActionAnalyticsJobbDetail, BIAActionAnalyticTrigger);

                _logger.Info("BIA Action Analytic Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize BIA Action Analytic /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize BIA Action Analytic /r/n" + exc.Message);
            }
        }

        private void InitializeBIAAlertAnlyticJob()
        {
            try
            {
                var BIAAlertAnalyticsJobbDetail = new JobDetail("BIAAlertAnalytic", "BIAAlertAnalytic", typeof(BIAActionAnalyticsJob));

                var BIAAlertAnalyticTrigger = new CronTrigger("BIAAlertAnalyticTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(BIAAlertAnalyticsJobbDetail, BIAAlertAnalyticTrigger);

                _logger.Info("BIA Alert Analytic Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize BIA Alert Analytic /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize BIA Alert Analytic /r/n" + exc.Message);
            }
        }



        private void InitializeMonitorGlobalMirrorJob()
        {
            try
            {
                var globalMirrorjobDetail = new JobDetail("GlobalMirror", "GlobalMirrorGroup", typeof(GlobalMirrorJob));

                var globalMirrorTrigger = new CronTrigger("GlobalMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(globalMirrorjobDetail, globalMirrorTrigger);

                _logger.Info("Monitor Global Mirror Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor global mirror /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor global mirror /r/n" + exc.Message);
            }
        }



        private void InitializeMonitorApplicationJob(InfraObject application)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(application.Name + "MonitorAppInfraObject", "MonitorAppInfraObject");

                if (job == null)
                {
                    var applicationjobDetail = new JobDetail(application.Name + "MonitorAppInfraObject", "MonitorAppInfraObject", typeof(MonitorApplicationJob));

                    applicationjobDetail.JobDataMap.Put(MonitorApplicationJob.InfraObject, application);

                    var applicationTrigger = new CronTrigger(application.Name + "applicationTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(applicationjobDetail, applicationTrigger);

                    _logger.Info(application.Name + " Application Monitor Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Application for Application (" + application.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Application for Application (" + application.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeArchivedLogApplyJob(InfraObject group)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(group.Name + "_ApplyArchiveLog", "ArchivedLogApply");

                if (job == null)
                {
                    var archiveJobDetail = new JobDetail(group.Name + "_ApplyArchiveLog", "ArchivedLogApply", typeof(ApplyArchiveLogsJob));

                    archiveJobDetail.JobDataMap.Put(ApplyArchiveLogsJob.Group, group);

                    var archiveTrigger = new CronTrigger(group.Name + "_ArchivedLogApplyTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(archiveJobDetail, archiveTrigger);

                    _logger.Info(archiveJobDetail.Name + "  apply archive log Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize  archive log for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize  archive log for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeGroupMonitorSRDF(InfraObject group)
        {
            try
            {
                var applicationjobDetail1 = new JobDetail(group.Name + "monitorSRDF", "monitorSRDFGroup", typeof(MonitorSRDFJob));

                applicationjobDetail1.JobDataMap.Put(MonitorSRDFJob.InfraObject, group);

                var applicationTrigger1 = new CronTrigger(group.Name + "monitorSRDFTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                _logger.Debug(group.Name + " InfraObject SRDF Monitor Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor SRFD for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor SRFD for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitorServiceJob(MonitorService monitorService)
        {
            try
            {
                var monitorjobDetail1 = new JobDetail(monitorService.ServicePath + monitorService.Id + "monitorService", "monitorServiceGroup", typeof(MonitorsServiesJob));

                monitorjobDetail1.JobDataMap.Put(MonitorsServiesJob.MonitorService, monitorService);

                var monitorTrigger1 = new CronTrigger(monitorService.ServicePath + monitorService.Id + "monitorServiceTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(monitorjobDetail1, monitorTrigger1);

                _logger.Debug(monitorService.ServicePath + " MonitorService Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor service for Service (" + monitorService.ServicePath + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor service for Service (" + monitorService.ServicePath + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitordoubleTake(InfraObject infraObject)
        {
            try
            {
                _logger.InfoFormat("Initialized doubleTake component...");
                var doubleTakeJobDetail = new JobDetail(infraObject.Name + "DoubleTakejob", infraObject.Name + "DoubleTakejobGroup", typeof(MssqldoubleTakeJob));

                doubleTakeJobDetail.JobDataMap.Put(MssqldoubleTakeJob.InfraObject, infraObject);

                var doubletakeTrigger = new CronTrigger(infraObject.Name + "_doubletakejobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(doubleTakeJobDetail, doubletakeTrigger);

                _logger.Info(doubleTakeJobDetail.Name + " doubleTake Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor doubleTake for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor doubleTake for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeApplicationMonitordoubleTake(InfraObject infraObject)
        {
            try
            {
                _logger.InfoFormat("Initialized Application Replication doubleTake component...");
                var doubleTakeJobDetail = new JobDetail(infraObject.Name + "ApplicationDoubleTakejob", infraObject.Name + "ApplicationDoubleTakejobGroup", typeof(ApplicationDTJob));

                doubleTakeJobDetail.JobDataMap.Put(ApplicationDTJob.InfraObject, infraObject);

                var doubletakeTrigger = new CronTrigger(infraObject.Name + "_doubletakejobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(doubleTakeJobDetail, doubletakeTrigger);

                _logger.Info(doubleTakeJobDetail.Name + "Application doubleTake Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Application replication for doubleTake InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Application replication for doubleTake InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        [SuppressMessage("StyleCop.CSharp.ReadabilityRules", "SA1101:PrefixLocalCallsWithThis", Justification = "Reviewed. Suppression is OK here.")]
        private void InitializeGroupMonitorSRDFDatLag(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup");
                if (job == null)
                {
                    var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                    applicationjobDetail2.JobDataMap.Put(MonitorSRDFDataLagJob.InfraObject, infraObject);

                    var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                    _logger.Debug(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    _logger.Error("Error Occurred while initialize monitor SRDF Datalag for InfraObject (" +
                                  infraObject.Name + "). /r/n" + exc.InnerException);
                }
                else
                {
                    _logger.Error("Error Occurred while initialize monitor SRDF Datalag for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
                }

            }
        }

        private void InitializeMonitorHADRJob(InfraObject infraObject)
        {
            try
            {
                JobDetail hadrJobDetail;
                if (infraObject.ReplicationType == (int)ReplicationType.DB2IBMGlobalMirror)
                {
                    hadrJobDetail = new JobDetail(infraObject.Name + "_MonitorDB2_IBMGlobalMirror", "MonitorDB2_IBMGlobalMirror", typeof(MonitorHADRJob));
                }
                else
                    hadrJobDetail = new JobDetail(infraObject.Name + "_MonitorHADR", "MonitorHADRGroup", typeof(MonitorHADRJob));

                hadrJobDetail.JobDataMap.Put(MonitorHADRJob.InfraObject, infraObject);

                var hadrTrigger = new CronTrigger(infraObject.Name + "_MonitorHADRTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                    // CronExpressionString = "0 0/1 * * * ?"
                };

                _scheduler.ScheduleJob(hadrJobDetail, hadrTrigger);

                _logger.Info(hadrJobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor HADR for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor HADR for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitorExchangeSCRJob(InfraObject infraObject)
        {
            try
            {
                var exchangeSCRJobDetail = new JobDetail(infraObject.Name + "_MonitorExchangeSCR", "MonitorExchangeSCRGroup", typeof(MonintorExchangeJob));

                exchangeSCRJobDetail.JobDataMap.Put(MonintorExchangeJob.InfraObject, infraObject);

                var exchangeSCRTrigger = new CronTrigger(infraObject.Name + "_MonitorExchangeSCRTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/20 * * * ?"
                };

                _scheduler.ScheduleJob(exchangeSCRJobDetail, exchangeSCRTrigger);

                _logger.Info(exchangeSCRJobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Exchange SCR for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Exchange SCR for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeDatabaseBackup(DatabaseBackupInfo databaseBackup)
        {
            try
            {
                //_logger.Debug(" Start DB Backup");
                //_logger.Debug("BackupTime:" + databaseBackup.Time);
                //var databaseBackupjobDetail = JobBuilder.Create<DatabaseBackupJob>()
                // .WithIdentity(databaseBackup.DatabaseName + "_BackupDatabase", "BackupDatabaseGroup")
                // .UsingJobData(DatabaseBackupJob.DatabaseBackup, databaseBackup.Id)
                // .Build();

                //var databaseBackupjobDetailtrigger = (ICronTrigger)TriggerBuilder.Create()
                //                                     .WithIdentity(databaseBackup.DatabaseName + "_DatabaseBackupTrigger", "_DatabaseBackupTrigger")
                //                                     .WithCronSchedule(databaseBackup.Time, x => x.WithMisfireHandlingInstructionFireAndProceed())
                //                                     .Build();

                //_scheduler.ScheduleJob(databaseBackupjobDetail, databaseBackupjobDetailtrigger);

                //_logger.Info(databaseBackup.DatabaseName + "DatabaseBackupJob is Scheduled");

                var databaseBackupjobDetail = new JobDetail(databaseBackup.DatabaseName + "_BackupDatabase", "BackupDatabaseGroup", typeof(DatabaseBackupJob));

                databaseBackupjobDetail.JobDataMap.Put(DatabaseBackupJob.DatabaseBackup, databaseBackup);

                var databaseBackupjobDetailtrigger = new CronTrigger(databaseBackup.DatabaseName + "_DatabaseBackupTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = databaseBackup.Time
                    //CronExpressionString = "0 0 0/1 * * ?"
                };

                _scheduler.ScheduleJob(databaseBackupjobDetail, databaseBackupjobDetailtrigger);

                _logger.Info(databaseBackupjobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing DatabaseBackupJob for : " + databaseBackup.DatabaseName + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing DatabaseBackupJob for : " + databaseBackup.DatabaseName + Environment.NewLine + "ERROR" + exc.Message);
            }
        }

        private void InitializeMonitorSqlNative(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnativejob", "sqlnativejobGroup");
                if (job == null)
                {
                    var sqlnativeJobDetail = new JobDetail(infraObject.Name + "_Sqlnativejob", infraObject.Name + "sqlnativejobGroup", typeof(MSSqlNativeLogjob));

                    sqlnativeJobDetail.JobDataMap.Put(MSSqlNativeLogjob.InfraObject, infraObject);

                    var sqlnativeTrigger = new CronTrigger(infraObject.Name + "_sqlnativejobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(sqlnativeJobDetail, sqlnativeTrigger);

                    _logger.Info(sqlnativeJobDetail.Name + " Sql Native Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor sql native for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor sql native for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void IntailizingPostGreSqlMonitoring(InfraObject infraObject)
        {
            var postgresqlreplication = new JobDetail(infraObject.Name + "postgresql", infraObject.Name + "postgresqlGroup", typeof(PostgreMonitoringJob));

            postgresqlreplication.JobDataMap.Put(PostgreMonitoringJob.InfraObject, infraObject);

            var postgresqlTrigger = new CronTrigger(infraObject.Name + "_postgresqlTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(postgresqlreplication, postgresqlTrigger);

            _logger.Debug(postgresqlreplication.Name + " EneterpriseDBMonitoring Job is Scheduled");
        }

        //private void InitializingParallelWorkflow(ParallelGroupWorkflow parallelGroup)
        //{
        //    try
        //    {
        //        var parallelJobDetail = new JobDetail(parallelGroup.WorkflowName + "ParallelJob", parallelGroup.WorkflowName + "ParallelJobGroup", typeof(ParallelWorkflowJob));

        //        parallelJobDetail.JobDataMap.Put(ParallelWorkflowJob.ParallelGroupWorkflow, parallelGroup);

        //        var simpleTrigger = new SimpleTrigger(parallelGroup.WorkflowName + "ParallelJobTrigger", DateTime.UtcNow);

        //        simpleTrigger.RepeatCount = 0;
        //        simpleTrigger.RepeatInterval = TimeSpan.Zero;

        //        _parallelScheduler.ScheduleJob(parallelJobDetail, simpleTrigger);

        //        _logger.Info(parallelJobDetail.Name + "Parallel workflow execution job is scheduled");

        //    }
        //    catch (Exception exc)
        //    {
        //        if (exc.InnerException != null)
        //            _logger.Error("Exception occurred while initializing Parallel Group Workflow : " + parallelGroup.WorkflowName + Environment.NewLine + "ERROR" + exc.InnerException);
        //        else
        //            _logger.Error("Exception occurred while initializing oracle fast copy job for : " + parallelGroup.WorkflowName + Environment.NewLine + "ERROR" + exc.Message);
        //    }
        //}

        //Added by Karthick B for ITIT-10566
        private void InitializingParallelWorkflow(ParallelGroupWorkflow parallelGroup)
        {
            _logger.Info("Entered into InitializingParallelWorkflow");
            ILogger _workflowLogger = Serilog.Log.ForContext("FileName", parallelGroup.WorkflowName);
            _logger.Info("Serilog processing into InitializingParallelWorkflow:" + _workflowLogger);
            try
            {
                if (parallelGroup.IsResume == 1)
                {
                    _logger.Info("IsResume matched with value 1");
                    ParallelGroupWorkflowDataAccess.UpdateResumeStatusGrpId(parallelGroup.Id, 2, 0);
                    _workflowLogger.Information(
                        "InitializingParallelWorkflow Is Resume Status updated as 2 for ParallelGroupId:" +
                        parallelGroup.Id);
                }

                lock (_schedulerLock)
                {
                    _workflowLogger.Information("InitializingParallelWorkflow Name:" + parallelGroup.WorkflowName);

                    string jobName = parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJob" + "_" +
                                     parallelGroup.JobName;
                    _workflowLogger.Information("InitializingParallelWorkflow jobName:" + jobName);
                    string groupName = parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJobGroup" + "_" +
                                       parallelGroup.JobName;
                    _workflowLogger.Information("Parallelworkflow groupName:" + groupName);

                    string triggerGroupName = parallelGroup.WorkflowName + "_" + parallelGroup.Id +
                                              "ParallelTriggerGroup" + "_" + parallelGroup.JobName;

                    IJobDetail parallelJobDetail = JobBuilder.Create<ParallelWorkflowJob>()
                        .WithIdentity(parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJob" + "_" +
                                      parallelGroup.JobName, parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJobGroup" + "_" +
                                                             parallelGroup.JobName)
                        .StoreDurably(false)
                        .Build();

                    parallelJobDetail.JobDataMap[ParallelWorkflowJob.ParallelGroupWorkflow] = parallelGroup;
                    parallelJobDetail.JobDataMap[ParallelWorkflowJob.ParallelGroupWorkflowLogger] = _workflowLogger;


                    ITrigger trigger = TriggerBuilder.Create()
                        .WithIdentity(parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJobTrigger")
                        .StartAt(DateTimeOffset.UtcNow.AddSeconds(2))
                        .WithSimpleSchedule(x => x
                            .WithRepeatCount(0)
                            .WithMisfireHandlingInstructionFireNow())
                        .Build();


                    _workflowLogger.Information("ParallelScheduler JobGroupNames list count before Resume: " + _parallelScheduler.GetJobGroupNames().Count);

                    _parallelScheduler.Start();

                    //_parallelScheduler.AddJob(parallelJobDetail, true);

                    // _parallelScheduler.TriggerJob(new JobKey(jobName, groupName));

                    _parallelScheduler.ScheduleJob(parallelJobDetail, trigger);

                    _workflowLogger.Information(parallelJobDetail.Key.Name +
                                                "Parallel workflow execution job is scheduled");
                    _workflowLogger.Information("ParallelScheduler JobGroupNames list count after Resume: " + _parallelScheduler.GetJobGroupNames().Count);

                    _workflowLogger.Information("ParallelScheduler Trigger Name:" + parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJobTrigger. Trigger State: " + _parallelScheduler.GetTriggerState(new TriggerKey(parallelGroup.WorkflowName + "_" + parallelGroup.Id + "ParallelJobTrigger")));

                }
            }

            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _workflowLogger.Error("Exception occurred while initializing Parallel Group Workflow : " + parallelGroup.WorkflowName + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _workflowLogger.Error("Exception occurred while initializing Parallel Group Workflow: " + parallelGroup.WorkflowName + Environment.NewLine + "ERROR" + exc.Message);
            }

        }
        #endregion Quartz
        //Added by Karthick B for ITIT-10566

        private void InitializeApplicationReplicationJob(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "FastCopyApplicationReplication", "ApplicationReplicationFastCopy");

                if (job == null)
                {
                    var applicationjobDetail1 = new JobDetail(infraObject.Name + "FastCopyApplicationReplication", "ApplicationReplicationFastCopy", typeof(ApplicationReplicationJob));

                    applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.InfraObject, infraObject);

                    var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraObject.PRReplicationId);

                    applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.FastCopyId, replicationinfo.FastCopy.Id);

                    applicationjobDetail1.JobDataMap.Put(ApplicationReplicationJob.ScheduleTime, replicationinfo.FastCopy.ScheduleTime);

                    var applicationTrigger1 = new CronTrigger(infraObject.Name + "ApplicationReplicationTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = replicationinfo.FastCopy.ScheduleTime
                    };

                    _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                    _logger.Info(infraObject.Name + " Application Replication DataSync Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing Application Replication : " + infraObject.Name + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing Application Replication : " + infraObject.Name + Environment.NewLine + "ERROR" + exc.Message);
            }

        }

        #region For Upload files from PR to DR (-RU)
        private void InitializeCFLApplicationReplicationUploadJob(InfraObject infraObject)
        {
            try
            {
                var applicationjobDetail1 = new JobDetail(infraObject.Name + "FastCopyCFLApplicationReplicationUpload", "ApplicationCFLReplicationUploadFastCopy", typeof(ApplicationReplicationUploadCFLJob));

                applicationjobDetail1.JobDataMap.Put(ApplicationReplicationUploadCFLJob.InfraObject, infraObject);

                var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraObject.PRReplicationId);

                applicationjobDetail1.JobDataMap.Put(ApplicationReplicationUploadCFLJob.FastCopyId, replicationinfo.FastCopy.Id);

                applicationjobDetail1.JobDataMap.Put(ApplicationReplicationUploadCFLJob.ScheduleTime, replicationinfo.FastCopy.ScheduleTime);

                var applicationTrigger1 = new CronTrigger(infraObject.Name + "ApplicationCFLReplicationTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = replicationinfo.FastCopy.ScheduleTime
                };

                _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                _logger.Info(infraObject.Name + " Application Replication CFL Upload DataSync Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing CFL Application Replication Upload: " + infraObject.Name + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing CFL Application Replication Upload: " + infraObject.Name + Environment.NewLine + "ERROR" + exc.Message);
            }

        }
        #endregion

        public void InitializeOracleFastCopyJob(InfraObject activeGroup)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(activeGroup.Name + "OracleFastCopy", "OracleFastcopyGroup");

                if (job == null)
                {
                    var fastCopy = ReplicationBaseDataAccess.GetReplicationById(activeGroup.PRReplicationId);

                    if (fastCopy != null)
                    {
                        var oracleFastCopyJobDetails = new JobDetail(activeGroup.Name + "OracleFastCopy", "OracleFastcopyGroup", typeof(FastCopyOracleSyncJob));

                        oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.InfraObject, activeGroup);

                        oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.FastCopyId, fastCopy.FastCopy.Id);

                        oracleFastCopyJobDetails.JobDataMap.Put(FastCopyOracleSyncJob.ScheduleTime, fastCopy.FastCopy.ScheduleTime);

                        var oracleFastCopyTrigger = new CronTrigger(activeGroup.Name + "_OracleFastCopyTrigger")
                        {
                            StartTimeUtc = DateTime.UtcNow,

                            CronExpressionString = fastCopy.FastCopy.ScheduleTime
                        };

                        _scheduler.ScheduleJob(oracleFastCopyJobDetails, oracleFastCopyTrigger);

                        _logger.Info(oracleFastCopyJobDetails.Name + " Job is Scheduled. The Scheduled time interval is" + fastCopy.FastCopy.ScheduleTime);
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing oracle fast copy job for : " + activeGroup.Name + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing oracle fast copy job for : " + activeGroup.Name + Environment.NewLine + "ERROR" + exc.Message);
            }
        }

        private void InitializeMonitorApplicationWorkflow(InfraObject infraObject)
        {
            try
            {
                var applicationWorkflowjob = new JobDetail(infraObject.Name + "MonitorApplicationWorkflow", "MonitorApplicationWorkflowGroup", typeof(MonitorApplicationWorkflowJob));

                applicationWorkflowjob.JobDataMap.Put(MonitorApplicationWorkflowJob.InfraObject, infraObject);

                var applicationWorkflowTrigger = new CronTrigger(infraObject.Name + "MonitorApplicationWorkflowTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(applicationWorkflowjob, applicationWorkflowTrigger);

                _logger.Debug(infraObject.Name + " InfraObject Monitor Application Workflow Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing monitoring application workflow :" + infraObject.Name + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing monitoring application workflow : " + infraObject.Name + Environment.NewLine + "ERROR" + exc.Message);
            }
        }

        private void InitializeExchangeDagJob(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorExchangeDag", "MonitorExchangeDagGroup");

                if (job == null)
                {
                    var exchangeDAGJobDetail = new JobDetail(infraObject.Name + "_MonitorExchangeDag", "MonitorExchangeDagGroup", typeof(MonitorExchangeDagJob));

                    exchangeDAGJobDetail.JobDataMap.Put(MonitorExchangeDagJob.InfrabojectId, infraObject.Id);

                    var exchangeDAGTrigger = new CronTrigger(infraObject.Name + "_MonitorExchangeDagTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(exchangeDAGJobDetail, exchangeDAGTrigger);

                    _logger.Info(exchangeDAGJobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Exchange DAG for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Exchange DAG for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeEC2S3DataSyncJob(InfraObject infraObject)
        {
            try
            {
                var EC2S3DataSyncJobDetail = new JobDetail(infraObject.Name + "_MonitorEC2S3DataSync", "MonitorEC2S3DataSyncGroup", typeof(EC2S3DataSyncJob));

                EC2S3DataSyncJobDetail.JobDataMap.Put(EC2S3DataSyncJob.InfrabojectId, infraObject.Id);

                var EC2S3DataSyncTrigger = new CronTrigger(infraObject.Name + "_MonitorEC2S3DataSyncTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(EC2S3DataSyncJobDetail, EC2S3DataSyncTrigger);

                _logger.Info(EC2S3DataSyncJobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor EC2S3DataSync for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor EC2S3DataSync for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeEC2S3DataSyncReplicationJob(InfraObject infraObject)
        {
            try
            {
                var EC2S3DataSyncReplicationJobDetail = new JobDetail(infraObject.Name + "_EC2S3DataSyncReplication", "EC2S3DataSyncReplicationGroup", typeof(EC2S3DataSyncReplicationJob));

                EC2S3DataSyncReplicationJobDetail.JobDataMap.Put(EC2S3DataSyncJob.InfrabojectId, infraObject.Id);

                var EC2S3DataSyncReplicationTrigger = new CronTrigger(infraObject.Name + "_EC2S3DataSyncReplicationTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(EC2S3DataSyncReplicationJobDetail, EC2S3DataSyncReplicationTrigger);

                _logger.Info(EC2S3DataSyncReplicationJobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor EC2S3DataSync replication for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor EC2S3DataSync replication for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializePostgre9xMonitorJob(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "MonitorPostgres9xDB", "MonitorPostgres9xDBGroup");
                if (job == null)
                {
                    var dbpostgrejob = new JobDetail(infraObject.Name + "MonitorPostgres9xDB", "MonitorPostgres9xDBGroup", typeof(Postgre9XJob));

                    dbpostgrejob.JobDataMap.Put(Postgre9XJob.InfraObject, infraObject);

                    var Postgre9xdbTrigger = new CronTrigger(infraObject.Name + "Postgre9xdbTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(dbpostgrejob, Postgre9xdbTrigger);

                    _logger.Debug(infraObject.Name + " InfraObject Monitor Postgres9x Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Exception occurred while initializing Monitor Postgres9x :" + infraObject.Name + Environment.NewLine + "ERROR" + exc.InnerException);
                else
                    _logger.Error("Exception occurred while initializing Monitor Postgres9x : " + infraObject.Name + Environment.NewLine + "ERROR" + exc.Message);
            }

        }

        private void InitializeMonitorMSSqlNative2008(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup");

                if (job == null)
                {
                    _logger.InfoFormat("Initialized MonitorMSSqlNative2008...");
                    // var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", infraObject.Name + "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                    var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                    sqlnativ2008eJobDetail.JobDataMap.Put(MSSqlNative2008Job.InfraObject, infraObject);

                    var sqlnative2008Trigger = new CronTrigger(infraObject.Name + "_sqlnative2008jobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = "0 0/15 * * * ?"
                    };

                    _scheduler.ScheduleJob(sqlnativ2008eJobDetail, sqlnative2008Trigger);

                    _logger.Info(sqlnativ2008eJobDetail.Name + " MSSql Native 2008 Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor MSsql native 2008 for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor sql native 2008 for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializeMonitorMSSqlAlwayson(InfraObject group)
        {
            try
            {
                _logger.InfoFormat("Initialized Monitor Mssql Alwayson job for " + group.Name);

                var applicationjobDetail1 = new JobDetail(group.Name + "monitorAlwayson", "monitorAlwaysonGroup", typeof(MSSqlAlwaysONJob));

                applicationjobDetail1.JobDataMap.Put(MSSqlAlwaysONJob.InfraObject, group);

                var applicationTrigger1 = new CronTrigger(group.Name + "monitorAlwaysonTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

                _logger.Debug(group.Name + " InfraObject Alwayson Monitor Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor Alwayson for InfraObject (" + group.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor Alwayson for InfraObject (" + group.Name + "). /r/n" + exc.Message);
            }
        }
        // SVC Global Mirror

        private void InitializeSVCGlobalMirrorMonitorJob(InfraObject infraObject)
        {
            try
            {
                var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorSVCGlobalMirrorJob", "MonitorSVCGlobalMirrorJob", typeof(MonitorSVCGlobalMirrorJob));

                monitorjobDetail.JobDataMap.Put(MonitorSVCGlobalMirrorJob.InfraObject, infraObject);

                var monitorSVCGlobalMirrortrigger = new CronTrigger(infraObject.Name + "_MonitorSVCGlobalMirrorJobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(monitorjobDetail, monitorSVCGlobalMirrortrigger);

                _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize SVC monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize SVC monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        // Monitor Mssql Database
        private void InitializeMSSQLSVCFULLDBMonitorComponents(InfraObject infraObject)
        {
            try
            {
                var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMSSQLSVCFULLDBJob", "MSSqlSVCFullDbJob", typeof(MssqlSVCFullDbJob));

                monitorjobDetail.JobDataMap.Put(MssqlSVCFullDbJob.InfraObject, infraObject);

                var MSSqlSVCFullDbtrigger = new CronTrigger(infraObject.Name + "_MSSqlSVCFullDbJobTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(monitorjobDetail, MSSqlSVCFullDbtrigger);

                _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize MSSQL SVC FULL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize MSSQL SVC FULL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        private void InitializeMSSQLDBMonitorComponents(InfraObject infraObject)
        {
            try
            {
                JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorMSSQLDBJob", "MSSqlDbJob");
                if (job == null)
                {
                    var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorMSSQLDBJob", "MSSqlDbJob", typeof(MssqlSVCFullDbJob));

                    monitorjobDetail.JobDataMap.Put(MssqlSVCFullDbJob.InfraObject, infraObject);

                    var MSSqlDbtrigger = new CronTrigger(infraObject.Name + "_MSSqlDbJobTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,
                        CronExpressionString = "0 0/5 * * * ?"
                    };

                    _scheduler.ScheduleJob(monitorjobDetail, MSSqlDbtrigger);

                    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize MSSQL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize MSSQL DB monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }


        private void InitializeDrNetMonitorJob(InfraObject infraObject)
        {
            var drNetMonitorJobDetail = new JobDetail(infraObject.Name + "monitorDrNet", "monitorDrNetGroup", typeof(DRNetMonitorJob));

            drNetMonitorJobDetail.JobDataMap.Put(DRNetMonitorJob.InfraObject, infraObject);

            var drNetappTrigger = new CronTrigger(infraObject.Name + "monitorDRNetTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(drNetMonitorJobDetail, drNetappTrigger);

            _logger.Info(infraObject.Name + " InfraObject DR-NET Monitor Job is Scheduled");
        }

        private void InitializeTPCRMonitor(InfraObject infraObject)
        {
            try
            {
                var jobdetails = new JobDetail(infraObject.Name + "monitorTPCR", "monitorTPCRGroup", typeof(MonitorTPCRJob));

                jobdetails.JobDataMap.Put(MonitorTPCRJob.InfraObject, infraObject);

                var TPCRTrigger = new CronTrigger(infraObject.Name + "monitorTPCRTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(jobdetails, TPCRTrigger);

                _logger.Debug(infraObject.Name + " InfraObject TPCR Monitor Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    _logger.Error("Error Occurred while initialize monitor TPCR Jbb for InfraObject (" +
                                  infraObject.Name + "). /r/n" + exc.InnerException);
                }
                else
                {
                    _logger.Error("Error Occurred while initialize monitor TPCR Jbb for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
                }

            }
        }

        private void InitializeRecoverPointMonitor(InfraObject infraObject)
        {
            try
            {
                var jobdetails = new JobDetail(infraObject.Name + "monitorRecoverPoint", "monitorRecoverPointGroup", typeof(MonitorRecoverPointJob));

                jobdetails.JobDataMap.Put(MonitorTPCRJob.InfraObject, infraObject);

                var RecoverPointTrigger = new CronTrigger(infraObject.Name + "monitorRecoverPointTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/3 * * * ?"
                };

                _scheduler.ScheduleJob(jobdetails, RecoverPointTrigger);

                _logger.Debug(infraObject.Name + " InfraObject RecoverPoint Monitor Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    _logger.Error("Error Occurred while initialize monitor RecoverPoint Jbb for InfraObject (" +
                                  infraObject.Name + "). /r/n" + exc.InnerException);
                }
                else
                {
                    _logger.Error("Error Occurred while initialize monitor RecoverPoint Jbb for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
                }

            }
        }

        //New Method using Quartz
        //public void _InitializeOracleMonitorComponents(InfraObject infraObject)
        //{
        //    try
        //    {
        //        string Cron = "0 0/5 * * * ?";
        //        //JobDetail job = scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");
        //        IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup"));
        //        if (job == null)
        //        {



        //            if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup")))
        //            {
        //                _logger.Info("ReSchedule _MonitorOracleDGJob Deleted Successfully for :" + infraObject.Name);
        //            }

        //            var _ntnxpd = JobBuilder.Create<MonitorOracleDgJob>().WithIdentity(infraObject.Id + "_MonitorOracleDGJob", infraObject.Id + "MonitorOracleDGGroup").UsingJobData(MonitorOracleDgJob.InfraObject, infraObject.Id).Build();

        //            var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_MonitorOracleDGJobTrigger", infraObject.Id + "MonitorOracleDGGroupJobTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

        //            scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);


        //            _logger.Info(infraObject.Name + " :Job is MonitorOracleDGJob Scheduled ");


        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        if (exc.InnerException != null)
        //            _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
        //        else
        //            _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
        //    }
        //}
        private void _InitializeOracleMonitorComponents(InfraObject infraobject)
        {
            #region OLD
            //try
            //{
            //    JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");
            //    if (job == null)
            //    {

            //        var monitorjobDetail = new JobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup", typeof(MonitorOracleDgJob));

            //        monitorjobDetail.JobDataMap.Put(MonitorOracleDgJob.InfraObject, infraObject);
            //        //var DBInfo = DatabaseBaseDataAccess.GetDatabaseById(infraObject.PRDatabaseId);
            //        //var CronTime = ConfigurationManager.AppSettings["MonitorCycleForActiveODG"].ToString();
            //        var monitorOracleDGtrigger = new CronTrigger(infraObject.Name + "_MonitorOracleDGJobTrigger")
            //        {
            //            StartTimeUtc = DateTime.UtcNow,

            //            // CronExpressionString = (DBInfo.PRVersion == "11g" || DBInfo.PRVersion == "12c") ? CronTime : "0 0/5 * * * ?"
            //            // CronExpressionString = "0 0/5 * * * ?"
            //            CronExpressionString = "0 0/5 * * * ?"

            //        };

            //        _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);

            //        _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
            //    }
            //}
            //catch (Exception exc)
            //{
            //    if (exc.InnerException != null)
            //        _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
            //    else
            //        _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            //}
            #endregion OLD

            try
            {
               //string Cron = "0 0/15 * * * ?";
                IJobDetail job = scheduler.GetJobDetail(new JobKey(infraobject.Id + "_MonitorOracleDGJob", infraobject.Id + "_MonitorOracleDGJob"));
                if (job == null)
                {
                    
                    var _MonitorODG = JobBuilder.Create<MonitorOracleDgJob>().WithIdentity(infraobject.Id + "_MonitorOracleDgJob", infraobject.Id + "_MonitorOracleDgJob").UsingJobData(MonitorOracleDgJob.InfraObject, infraobject.Id).Build();

                    var _monitorODGTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraobject.Id + "_MonitorOracleDGJobTrigger", infraobject.Id + "_MonitorOracleDGJobTriggerGrp").WithCronSchedule("0 0/15 * * * ?", x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                    scheduler.ScheduleJob(_MonitorODG, _monitorODGTrigger);                 
                    
                    _logger.Info(infraobject.Name + ": MonitorOracleDG Monitor Job is Scheduled");
                }

            }
            catch (Exception exc)
            {
                _logger.Error("Error Occurred while initialize _MonitorOracleDGJob for InfraObject (" + infraobject.Name + "). /r/n" + exc.Message);
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize _MonitorOracleDGJob for InfraObject (" + infraobject.Name + "). /r/n" + exc.InnerException.Message);

            }
        }

        public void _InitializArchivedLogDeleteJob(InfraObject infraObject)
        {

            IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted"));
            if (job == null)
            {

                string Cron = ConfigurationManager.AppSettings["DeleteArchiveJobCronString"];
                if (Cron == null)
                {
                    Cron = "0 0 0/6 * * ?";
                }

                if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted")))
                {
                    _logger.Info("ReSchedule ArchivedLogDeleted Deleted Successfully for :" + infraObject.Name);
                }

                var _ntnxpd = JobBuilder.Create<ArchivedLogDeleteJob>().WithIdentity(infraObject.Id + "_DeleteArchiveLog", infraObject.Id + "ArchivedLogDeleted").UsingJobData(ArchivedLogDeleteJob.InfraObject, infraObject.Id).Build();

                var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_DeleteArchiveLogJobTrigger", infraObject.Id + "ArchivedLogDeletedJobTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                _logger.Info(infraObject.Name + " :Job is  Scheduled ");
            }
        }

        public void _InitializeMonitorReplicationLog(InfraObject infraObject)
        {
            IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup"));
            if (job == null)
            {
                string Cron = "0 55 0/23 * * ?";

                if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup")))
                {
                    _logger.Info("ReSchedule MonitorReplicationLogGroup Deleted Successfully for :" + infraObject.Name);
                }

                var _ntnxpd = JobBuilder.Create<MonitorReplicateLogVolumeJob>().WithIdentity(infraObject.Id + "_MonitorReplicationLog", infraObject.Id + "MonitorReplicationLogGroup").UsingJobData(MonitorReplicateLogVolumeJob.InfraObject, infraObject.Id).Build();

                var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_MonitorReplicationLogJobTrigger", infraObject.Id + "_MonitorReplicationLogGroupTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                _logger.Info(infraObject.Name + " :Job is  Scheduled ");
            }
        }

        public void _InitializeMonitorMSSqlNative2008(InfraObject infraObject)
        {
            try
            {
                string Cron = "0 0/15 * * * ?";

                //JobDetail job = _scheduler.GetJobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup");

                //if (job == null)
                //{
                //    _logger.InfoFormat("Initialized MonitorMSSqlNative2008...");
                //    // var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", infraObject.Name + "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                //    var sqlnativ2008eJobDetail = new JobDetail(infraObject.Name + "_Sqlnative2008job", "sqlnative2008jobGroup", typeof(MSSqlNative2008Job));

                //    sqlnativ2008eJobDetail.JobDataMap.Put(MSSqlNative2008Job.InfraObject, infraObject);

                //    var sqlnative2008Trigger = new CronTrigger(infraObject.Name + "_sqlnative2008jobTrigger")
                //    {
                //        StartTimeUtc = DateTime.UtcNow,

                //        CronExpressionString = "0 0/15 * * * ?"
                //    };

                //    _scheduler.ScheduleJob(sqlnativ2008eJobDetail, sqlnative2008Trigger);

                //    _logger.Info(sqlnativ2008eJobDetail.Name + " MSSql Native 2008 Job is Scheduled");
                //}

                //JobDetail job = scheduler.GetJobDetail(infraObject.Name + "_MonitorOracleDGJob", "MonitorOracleDGGroup");
                IJobDetail job = scheduler.GetJobDetail(new JobKey(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup"));
                if (job == null)
                {
                    _logger.InfoFormat("Initialized MonitorMSSqlNative2008...");

                    if (scheduler.DeleteJob(new JobKey(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup")))
                    {
                        _logger.Info("ReSchedule MonitorMSSqlNative2008 Deleted Successfully for :" + infraObject.Name);
                    }

                    var _ntnxpd = JobBuilder.Create<MSSqlNative2008Job>().WithIdentity(infraObject.Id + "_Sqlnative2008job", infraObject.Id + "sqlnative2008jobGroup").UsingJobData(MSSqlNative2008Job.InfraObject, infraObject.Id).Build();

                    var _ntnxpdTrigger = (ICronTrigger)TriggerBuilder.Create().WithIdentity(infraObject.Id + "_Sqlnative2008jobTrigger", infraObject.Id + "sqlnative2008jobGroupTriggerGrp").WithCronSchedule(Cron, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                    scheduler.ScheduleJob(_ntnxpd, _ntnxpdTrigger);

                    _logger.Info(infraObject.Name + " :Job is Sqlnative2008job Scheduled ");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize sqlnativ2008 component for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initializesqlnativ2008 component for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

       

        #region CommentedMethod

        //public void InitializeMonitorComponents(InfraObject group)
        //{
        //    var monitorjobDetail = new JobDetail(group.Name + "_MonitoringComponent", "MonitoringComponentGroup", typeof(MonitorComponentJob));

        //    monitorjobDetail.JobDataMap.Put(MonitorComponentJob.Group, group);

        //    var monitorComponenttrigger = new CronTrigger(group.Name + "_MonitorComponentTrigger")
        //    {
        //        StartTimeUtc = DateTime.UtcNow,
        //        CronExpressionString = "0 0/5 * * * ?"
        //    };

        //    _scheduler.ScheduleJob(monitorjobDetail, monitorComponenttrigger);

        //    _logger.Info(monitorjobDetail.Name + " Job is Scheduled");
        //}

        // new added by kiran for Oracle DG Monitoring

        private void InitializeMonitorReplicationLog(InfraObject group)
        {

            JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorReplicationLog", "MonitorReplicationLogGroup");

            if (job == null)
            {
                var replicationLogJobDetail = new JobDetail(group.Name + "_MonitorReplicationLog", "MonitorReplicationLogGroup", typeof(MonitorReplicateLogVolumeJob));

                replicationLogJobDetail.JobDataMap.Put(MonitorReplicateLogVolumeJob.Group, group);

                var replicationLogTrigger = new CronTrigger(group.Name + "_MonitorReplicationLogTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 55 0/23 * * ?"
                };

                _scheduler.ScheduleJob(replicationLogJobDetail, replicationLogTrigger);

                _logger.Info(replicationLogJobDetail.Name + " Job is Scheduled");
            }
        }

        private void InitializeMonitorApplicationService(InfraObject application)
        {
            var applicationServicejobDetail = new JobDetail(application.Name + "ApplicationService", "ApplicationServiceGroup",
                                                    typeof(MonitorApplicationServiceJob));

            applicationServicejobDetail.JobDataMap.Put(MonitorApplicationServiceJob.InfraObject, application);

            var applicationServiceTrigger = new CronTrigger(application.Name + "applicationServiceTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(applicationServicejobDetail, applicationServiceTrigger);

            _logger.Info(application.Name + " Application Service Monitor Job is Scheduled");
        }

        private void InitializeMonitorSRDF(InfraObject infraObject)
        {
            var applicationjobDetail1 = new JobDetail(infraObject.Name + "monitorSRDF", "monitorSRDFGroup", typeof(MonitorSRDFJob));

            applicationjobDetail1.JobDataMap.Put(MonitorApplicationJob.InfraObject, infraObject);

            var applicationTrigger1 = new CronTrigger(infraObject.Name + "monitorSRDFTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(applicationjobDetail1, applicationTrigger1);

            _logger.Info(infraObject.Name + " InfraObject SRDF Monitor Job is Scheduled");
        }

        private void InitializeMonitorSRDFDatLag(InfraObject infraObject)
        {
            try
            {
                var applicationjobDetail2 = new JobDetail(infraObject.Name + "monitorSRDFDataLag", "monitorSRDFDataLagGroup", typeof(MonitorSRDFDataLagJob));

                applicationjobDetail2.JobDataMap.Put(MonitorApplicationJob.InfraObject, infraObject);

                var applicationTrigger2 = new CronTrigger(infraObject.Name + "monitorSRDFDataLagTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(applicationjobDetail2, applicationTrigger2);

                _logger.Info(infraObject.Name + " InfraObject SRDF Monitor DataLag Job is Scheduled");
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize monitor SRDF Datalag for InfraObject (" + infraObject.Name + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize monitor SRDF Datalag for InfraObject (" + infraObject.Name + "). /r/n" + exc.Message);
            }
        }

        private void InitializExecuteDataSynchronizationJob(InfraObject group)
        {
            var dataSynchronizationDetail = new JobDetail(group.Name + "_DataSynchronization", "DataSynchronizationGroup",
                                                    typeof(DataSynchronizationJob));

            dataSynchronizationDetail.JobDataMap.Put(DataSynchronizationJob.Group, group);

            var dataSynchronizationTrigger = new CronTrigger(group.Name + "_DataSynchronizationTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,
                CronExpressionString = "0 0/5 * * * ?",
                Priority = 10
            };

            _scheduler.ScheduleJob(dataSynchronizationDetail, dataSynchronizationTrigger);

            _logger.Info(dataSynchronizationDetail.Name + " DataSynchronization Job is Scheduled");
        }

        private void InitializeSnapMirrorMonitor(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup");
            if (job == null)
            {
                var snapMirrorDetail = new JobDetail(group.Name + "_snapMirror", "SnapMirrorDetailGroup",
                                                       typeof(SnapMirrorJob));

                snapMirrorDetail.JobDataMap.Put(SnapMirrorJob.Group, group);

                var snapMirrorJobTrigger = new CronTrigger(group.Name + "_snapMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/10 * * * ?",
                };

                _scheduler.ScheduleJob(snapMirrorDetail, snapMirrorJobTrigger);

                _logger.Info(snapMirrorDetail.Name + " Snap Mirror Monitor Job is Scheduled");
            }
        }

        private void InitializMonitorVmWareJob(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorVmWare", "MonitorVmWareGroup");
            if (job == null)
            {
                var vmwareJobDetail = new JobDetail(group.Name + "_MonitorVmWare", "MonitorVmWareGroup", typeof(MonitorVmWareJob));

                vmwareJobDetail.JobDataMap.Put(MonitorVmWareJob.Group, group);

                var vmWareTrigger = new CronTrigger(group.Name + "_MonitorVmWareTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/10 * * * ?"
                };

                _scheduler.ScheduleJob(vmwareJobDetail, vmWareTrigger);

                _logger.Info(vmwareJobDetail.Name + " Job is Scheduled");
            }
        }

        //private void IntializingDatabaseBackup(DatabaseBackupInfo databaseBackup)
        //{
        //    var databaseBackupjobDetail = new JobDetail(databaseBackup.DatabaseName + "_BackupDatabase", "BackupDatabaseGroup", typeof(DatabaseBackupJob));

        //    databaseBackupjobDetail.JobDataMap.Put(DatabaseBackupJob.DatabaseBackup, databaseBackup);

        //    var databaseBackupjobDetailtrigger = new CronTrigger(databaseBackup.DatabaseName + "_DatabaseBackupTrigger")
        //    {
        //        StartTimeUtc = DateTime.UtcNow,

        //        CronExpressionString = "0 0 0/1 * * ?"
        //    };

        //    _scheduler.ScheduleJob(databaseBackupjobDetail, databaseBackupjobDetailtrigger);

        //    _logger.Info(databaseBackupjobDetail.Name + " Job is Scheduled");

        //}

        private void InitializArchivedLogDeleteJob(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "_DeleteArchiveLog", "ArchivedLogDeleted");

            if (job == null)
            {

                string cronString = ConfigurationManager.AppSettings["DeleteArchiveJobCronString"];

                var dataGuardJobDetail = new JobDetail(group.Name + "_DeleteArchiveLog", "ArchivedLogDeleted", typeof(ArchivedLogDeleteJob));

                dataGuardJobDetail.JobDataMap.Put(ArchivedLogDeleteJob.Group, group);

                var dataGuardTrigger = new CronTrigger(group.Name + "_ArchivedLogDeletedTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = string.IsNullOrEmpty(cronString) ? "0 0 0/6 * * ?" : cronString
                };

                _scheduler.ScheduleJob(dataGuardJobDetail, dataGuardTrigger);

                _logger.Info(dataGuardJobDetail.Name + " Job is Scheduled");
            }
        }

        private void InitializArchivedLogReplicationJob(InfraObject group)
        {
            var dataGuardJobDetail = new JobDetail(group.Name + "_ReplicateArchiveLog", "ArchivedLogReplication", typeof(ArchivedLogReplicationJob));

            dataGuardJobDetail.JobDataMap.Put(ArchivedLogReplicationJob.InfraObject, group);

            var dataGuardTrigger = new CronTrigger(group.Name + "_ArchivedLogReplicationTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(dataGuardJobDetail, dataGuardTrigger);

            _logger.Info(dataGuardJobDetail.Name + " Job is Scheduled");
        }

        private void IntializingMonitorApplicationHealth()
        {
            var applicationHealthjobDetail = new JobDetail("ApplicationHealth", "ApplicationHealthGroup", typeof(MonitorBusinessServiceAvailbility));

            var applicationHealthtrigger = new CronTrigger("ApplicationHealthTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/10 * * * ?"
            };

            _scheduler.ScheduleJob(applicationHealthjobDetail, applicationHealthtrigger);

            _logger.Info(applicationHealthjobDetail.Name + " Job is Scheduled");
        }

        // surendra added
        private void DoPingJob()
        {
            var pingjobDetail = new JobDetail("Ping", "PingGroup", typeof(PingJob));

            var pinglMirrorTrigger = new CronTrigger("PingTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(pingjobDetail, pinglMirrorTrigger);

            _logger.Info("Ping Job is Scheduled");

        }
        // end surendra

        //private void InitializArchivedLogFastCopyReplicationJob(Group group)
        //{
        //    var dataGuardJobDetail = new JobDetail(group.Name + "_ReplicateArchiveLog", "ArchivedLogReplication", typeof(FastCopyOracleSyncJob));

        //    dataGuardJobDetail.JobDataMap.Put(FastCopyOracleSyncJob.Group, group);

        //    var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(group.PRReplicationId);

        //    var dataGuardTrigger = new CronTrigger(group.Name + "_ArchivedLogReplicationTrigger")
        //    {
        //        StartTimeUtc = DateTime.UtcNow,

        //        CronExpressionString = "0 0/5 * * * ?"

        //    };

        //    _scheduler.ScheduleJob(dataGuardJobDetail, dataGuardTrigger);

        //    _logger.Info(dataGuardJobDetail.Name + " Job is Scheduled");

        private void InitializMonitorHitachiHUROracleFullDBJob(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup");
            if (job == null)
            {
                var hitachiHURJobDetailJobDetail = new JobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup", typeof(MonitorHitachiHURJob));

                hitachiHURJobDetailJobDetail.JobDataMap.Put(MonitorHitachiHURJob.Group, group);

                var hitachiHURTrigger = new CronTrigger(group.Name + "_MonitorHitachiHURTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(hitachiHURJobDetailJobDetail, hitachiHURTrigger);

                _logger.Info(hitachiHURJobDetailJobDetail.Name + " Job is Scheduled");
            }
        }

        private void InitializMonitorHitachiHURMSSQLFullDBJob(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup");
            if (job == null)
            {
                var hitachiHURJobDetailJobDetail = new JobDetail(group.Name + "_MonitorHitachiHUR", "MonitorHitachiHURGroup", typeof(MonitorHitachiHURJob));

                hitachiHURJobDetailJobDetail.JobDataMap.Put(MonitorHitachiHURJob.Group, group);

                var hitachiHURTrigger = new CronTrigger(group.Name + "_MonitorHitachiHURTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(hitachiHURJobDetailJobDetail, hitachiHURTrigger);

                _logger.Info(hitachiHURJobDetailJobDetail.Name + " Job is Scheduled");
            }
        }

        private void IntailizingMSSqlLogGeneration(InfraObject group)
        {
            var sqlloggeneration = new JobDetail(group.Name + "sqlloggeneration", group.Name + "sqlloggenerationGroup", typeof(MSSqlServer2000Job));

            sqlloggeneration.JobDataMap.Put(MSSqlServer2000Job.Group, group);

            var sqlloggenTrigger = new CronTrigger(group.Name + "_sqlloggenerationTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,

                CronExpressionString = "0 0/5 * * * ?"
            };

            _scheduler.ScheduleJob(sqlloggeneration, sqlloggenTrigger);

            _logger.Info(sqlloggeneration.Name + " SqlLogGeneration Job is Scheduled");
        }

        private void IntailizingMSSqlNetAppJob(InfraObject group)
        {
            JobDetail job = _scheduler.GetJobDetail(group.Name + "MSSqlNetAppSnapMirror", "MSSqlNetAppSnapMirror");

            if (job == null)
            {
                var sqlloggeneration = new JobDetail(group.Name + "MSSqlNetAppSnapMirror", group.Name + "MSSqlNetAppSnapMirror", typeof(MSSqlNetAppSnapMirror));

                sqlloggeneration.JobDataMap.Put(MSSqlNetAppSnapMirror.Group, group);

                var sqlloggenTrigger = new CronTrigger(group.Name + "_MSSqlNetAppSnapMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,

                    CronExpressionString = "0 0/5 * * * ?"
                };

                _scheduler.ScheduleJob(sqlloggeneration, sqlloggenTrigger);

                _logger.Info(sqlloggeneration.Name + " MSSql NetApp SnapMirror Job is Scheduled");
            }
        }

        //}
        // for MSSQLDatabaseMirror
        private void InitializeMSSQLMirrorMonitor(InfraObject infraobject)
        {
            JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_mssqlMirror", "MSSQLMirrorDetailGroup");

            if (job == null)
            {
                var mssqlMirrorDetail = new JobDetail(infraobject.Name + "_mssqlMirror", "MSSQLMirrorDetailGroup",
                                                       typeof(MSSQLMirroringMonitoringJob));

                mssqlMirrorDetail.JobDataMap.Put(MSSQLMirroringMonitoringJob.InfraObject, infraobject);

                var mssqlMirrorJobTrigger = new CronTrigger(infraobject.Name + "_mssqlMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/10 * * * ?",
                };

                _scheduler.ScheduleJob(mssqlMirrorDetail, mssqlMirrorJobTrigger);

                _logger.Info(mssqlMirrorDetail.Name + " MSSQL Database Mirror Monitor Job is Scheduled");
            }
        }

        //MSSQLDBonitor
        private void InitializeDBMSSQLMirrorMonitor(InfraObject infraobject)
        {
            JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_mssqlDBMirror", "MSSQLDBMirrorDetailGroup");
            if (job == null)
            {
                var mssqlDBMirrorDetail = new JobDetail(infraobject.Name + "_mssqlDBMirror", "MSSQLDBMirrorDetailGroup",
                                                       typeof(SqlDBMirroringJob));

                mssqlDBMirrorDetail.JobDataMap.Put(SqlDBMirroringJob.InfraObject, infraobject);

                var mssqlDBMirrorJobTrigger = new CronTrigger(infraobject.Name + "_mssqlDBMirrorTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/5 * * * ?",
                };

                _scheduler.ScheduleJob(mssqlDBMirrorDetail, mssqlDBMirrorJobTrigger);

                _logger.Info(mssqlDBMirrorDetail.Name + " MSSQL Database Mirror Monitor Job is Scheduled");
            }
        }
        private void InitializeSybaseMonitor(InfraObject infraobject)
        {
            var sybaseMonitorDetail = new JobDetail(infraobject.Name + "_sybase", "SybaseDetailGroup",
                                                   typeof(SybaseMonitoringJob));

            sybaseMonitorDetail.JobDataMap.Put(SybaseMonitoringJob.InfraObject, infraobject);

            var sybaseMonitorJobTrigger = new CronTrigger(infraobject.Name + "_sybaseTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,
                CronExpressionString = "0 0/10 * * * ?",
            };

            _scheduler.ScheduleJob(sybaseMonitorDetail, sybaseMonitorJobTrigger);

            _logger.Info(sybaseMonitorDetail.Name + " SYBASE Database Monitor Job is Scheduled");
        }

        private void InitializeSybaseWithSRSMonitor(InfraObject infraobject)
        {
            var sybaseWithSRSMonitorDetail = new JobDetail(infraobject.Name + "_sybaseWithSRS", "SybaseWithSRSDetailGroup",
                                                   typeof(SybaseWithSRSMonitoringJob));

            sybaseWithSRSMonitorDetail.JobDataMap.Put(SybaseWithSRSMonitoringJob.InfraObject, infraobject);

            var sybaseMonitorJobTrigger = new CronTrigger(infraobject.Name + "_sybaseWithSRSTrigger")
            {
                StartTimeUtc = DateTime.UtcNow,
                CronExpressionString = "0 0/5 * * * ?",
            };

            _scheduler.ScheduleJob(sybaseWithSRSMonitorDetail, sybaseMonitorJobTrigger);

            _logger.Info(sybaseWithSRSMonitorDetail.Name + " Sybase With SRS Database Monitor Job is Scheduled");
        }

        private void InitializeMySqlNativeMonitor(InfraObject infraobject)
        {
            JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_MySqlNative", "MySqlNativeMonitor");
            if (job == null)
            {
                var mysqlNativeMonitorDetail = new JobDetail(infraobject.Name + "_MySqlNative", "MySqlNativeMonitor",
                                                       typeof(MySqlNativeJob));

                mysqlNativeMonitorDetail.JobDataMap.Put(MySqlNativeJob.InfraObject, infraobject);

                var mysqlMonitorJobTrigger = new CronTrigger(infraobject.Name + "_mysqlNativeTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/5 * * * ?",
                };

                _scheduler.ScheduleJob(mysqlNativeMonitorDetail, mysqlMonitorJobTrigger);

                _logger.Info(mysqlNativeMonitorDetail.Name + " MySql Native Monitor Job is Scheduled");
            }
        }

        private void InitializeMysqlDBMonitor(InfraObject infraobject)
        {
            JobDetail job = _scheduler.GetJobDetail(infraobject.Name + "_MonitorMySqlDBJob", "MonitorMySqlDBMonitor");
            if (job == null)
            {
                var mysqlNativeMonitorDetail = new JobDetail(infraobject.Name + "_MonitorMySqlDBJob", "MonitorMySqlDBMonitor",
                                                       typeof(MonitorMySqlDBJob));

                mysqlNativeMonitorDetail.JobDataMap.Put(MonitorMySqlDBJob.InfraObject, infraobject);

                var mysqlMonitorJobTrigger = new CronTrigger(infraobject.Name + "_MonitorMySqlDBTrigger")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = "0 0/10 * * * ?",
                };

                _scheduler.ScheduleJob(mysqlNativeMonitorDetail, mysqlMonitorJobTrigger);

                _logger.Info(mysqlNativeMonitorDetail.Name + " MySql DB Monitor Job is Scheduled");
            }
        }
        #endregion CommentedMethod

        #region Re-Schedule Jobs

        public void PrepareInitializeJob()
        {
            try
            {
                var jobs = GroupJobInfoDataAccess.GetAllInfraJobInfo();

                if (jobs != null)
                {
                    foreach (GroupJob job in jobs)
                    {
                        if (job.CronExpressionChange == 1)
                        {
                            InitializeJob(job);
                        }
                    }
                }

            }
            catch (Exception)
            {
            }
        }

        public void GetValue(GroupJob groupJob, object objPara, string paraName, string paraName1, string paraName2, string paraName3, Type type, string Name, string cronvalue, int infraid, string classname)
        {
            if (Name == "MonitorsServiesJob")
            {
                IList<MonitorService> monitorServices = MonitorServiceDataAccess.GetAllMonitorService();
                foreach (var monitorService in monitorServices)
                {
                    var monitorjobDetail1 = new JobDetail(monitorService.ServicePath + monitorService.Id + "monitorService", groupJob.BcmsClassName, type);
                    monitorjobDetail1.JobDataMap.Put(paraName1, cronvalue);
                    monitorjobDetail1.JobDataMap.Put(paraName2, infraid);
                    monitorjobDetail1.JobDataMap.Put("MonitorService", monitorService);

                    var monitorTrigger1 = new CronTrigger(monitorService.ServicePath + monitorService.Id + "monitorServiceTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = groupJob.CronExpression
                    };
                    _scheduler.ScheduleJob(monitorjobDetail1, monitorTrigger1);
                    _logger.Debug(monitorService.ServicePath + " MonitorService Job is Scheduled");
                }
            }
            else if (Name == "GlobalMirrorJob")
            {
                IList<GlobalMirror> ActiveGlobalMirror = GlobalMirrorDataAccess.GetGlobalMirrorGroupByStorageImageId();
                var getValue4 = GroupJobInfoDataAccess.GetInfraObjectJobByJobId(groupJob.JobId);
                foreach (var activeGlobalMiror in ActiveGlobalMirror)
                {
                    var monitorjobDetail1 = new JobDetail(activeGlobalMiror.PRStorageImageId + "globalmirror", groupJob.BcmsClassName, type);
                    monitorjobDetail1.JobDataMap.Put(paraName1, cronvalue);
                    monitorjobDetail1.JobDataMap.Put(paraName2, infraid);
                    monitorjobDetail1.JobDataMap.Put("globalmirror", activeGlobalMiror);

                    var monitorTrigger1 = new CronTrigger(activeGlobalMiror.PRStorageImageId + "globalmirrorTrigger")
                    {
                        StartTimeUtc = DateTime.UtcNow,

                        CronExpressionString = groupJob.CronExpression
                    };
                    _scheduler.ScheduleJob(monitorjobDetail1, monitorTrigger1);
                    // _logger.Debug(monitorService.ServicePath + " MonitorService Job is Scheduled");

                }
            }
            else
            {
                var monitorjobDetail = new JobDetail(Name + "_MonitorOracleDGJob", groupJob.BcmsClassName, type);
                monitorjobDetail.JobDataMap.Put(paraName1, cronvalue);
                monitorjobDetail.JobDataMap.Put(paraName2, infraid);
                monitorjobDetail.JobDataMap.Put(paraName3, classname);

                // monitorjobDetail.JobDataMap.Put(type.GetProperty("InfraObject"),infraObject);            
                monitorjobDetail.JobDataMap.Put(paraName, objPara);

                if (Name == "ApplicationReplicationJob")
                {
                    var infraobject1 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                    var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraobject1.PRReplicationId);
                    monitorjobDetail.JobDataMap.Put("fastcopy", replicationinfo.FastCopy.Id);
                    monitorjobDetail.JobDataMap.Put("scheduleTime", replicationinfo.FastCopy.ScheduleTime);
                }
                if (Name == "FastCopyOracleSyncJob")
                {
                    var infraobject1 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                    var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraobject1.PRReplicationId);
                    monitorjobDetail.JobDataMap.Put("fastcopy", replicationinfo.FastCopy.Id);
                    monitorjobDetail.JobDataMap.Put("scheduleTime", replicationinfo.FastCopy.ScheduleTime);
                }
                if (Name == "ApplicationDeleteFilesJob")
                {
                    var infraobject1 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                    var replicationinfo = ReplicationBaseDataAccess.GetReplicationById(infraobject1.PRReplicationId);
                    monitorjobDetail.JobDataMap.Put("fastcopy", replicationinfo.FastCopy.Id);
                }

                //JobDetail oldJob = _scheduler.GetJobDetail(Name + "_MonitorOracleDGJobTrigger", groupJob.BcmsClassName);



                _logger.Info("Trigger Name in Initialize" + Name + "_MonitorOracleDGJobTrigger");



                var monitorOracleDGtrigger = new CronTrigger(groupJob.TriggerName, "TriggerGroupJob")
                {
                    StartTimeUtc = DateTime.UtcNow,
                    CronExpressionString = groupJob.CronExpression
                };

                _logger.Info("Infra JobManagement Started");


                _scheduler.ScheduleJob(monitorjobDetail, monitorOracleDGtrigger);
                // Initialize(true);

                _logger.Info("Infra JobManagement End");

                _logger.Info(monitorjobDetail.Name + " : " + type + " Job is Scheduled");
            }
        }

        public void InitializeJob(GroupJob groupJob)
        {
            try
            {
                _logger.Info("JobManagement Started");
                Type type = Type.GetType(groupJob.BcmsClassName);

                switch (groupJob.ParatmeterType)
                {
                    case "infraObject":
                        #region Group
                        List<string> lst = new List<string>();
                        lst.Add("Bcms.Core.Job.SnapMirrorJob");
                        lst.Add("Bcms.Core.Job.MonitorDatabaseJob");
                        lst.Add("Bcms.Core.Job.ApplyArchiveLogsJob");
                        lst.Add("Bcms.Core.Job.ArchivedLogDeleteJob");
                        lst.Add("Bcms.Core.Job.DataSynchronizationJob");
                        lst.Add("Bcms.Core.Job.MSSqlNativeEnableLogshipping");
                        lst.Add("Bcms.Core.Job.MSSqlServer2000Job");
                        lst.Add("Bcms.Core.Job.MonitorComponentJob");
                        lst.Add("Bcms.Core.Job.MonitorDataGuardJob");
                        lst.Add("Bcms.Core.Job.MonitorHitachiHURJob");
                        lst.Add("Bcms.Core.Job.MonitorReplicateLogVolumeJob");
                        lst.Add("Bcms.Core.Job.MonitorVmWareJob");
                        lst.Add("Bcms.Core.Job.MSSqlNativeEnableLogshipping");
                        #endregion
                        if (lst.Contains(groupJob.BcmsClassName))
                        {
                            var infraobject2 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                            var getValue3 = GroupJobInfoDataAccess.TestGetInfraObjectJobById(groupJob.InfraObjectId, groupJob.BcmsClassName);
                            GetValue(groupJob, infraobject2, "group", "CronExpression", "InfraObjectJob", "ClassName", type, infraobject2.Name, getValue3.CronExpression, getValue3.InfraObjectId, getValue3.BcmsClassName);
                        }
                        else
                        {
                            var infraobject2 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                            var cronvalue = GroupJobInfoDataAccess.TestGetInfraObjectJobById(groupJob.InfraObjectId, groupJob.BcmsClassName);

                            GetValue(groupJob, infraobject2, "infraObject", "CronExpression", "InfraObjectJob", "ClassName", type, infraobject2.Name, cronvalue.CronExpression, cronvalue.InfraObjectId, cronvalue.BcmsClassName);
                        }
                        break;
                    case "BusinessServiceId":
                        //var BusinessServiceId = BusinessServiceDataAccess.GetBusinessById(groupJob.BusinessServiceId);
                        //var getValue = GroupJobInfoDataAccess.GetInfraObjectJobByBusinessId(groupJob.BusinessServiceId);
                        //GetValue(groupJob, BusinessServiceId.Id, "BusinessServiceId", "CronExpression", "BuisnessServiceJob", "", type, BusinessServiceId.Name, getValue.CronExpression, getValue.BusinessServiceId, "");
                        break;
                    case "infraObject,fastcopy,scheduleTime":
                        var infraobject1 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                        //  var getValue1 = GroupJobInfoDataAccess.GetInfraObjectJobById(groupJob.InfraObjectId);
                        var getValue1 = GroupJobInfoDataAccess.TestGetInfraObjectJobById(groupJob.InfraObjectId, groupJob.BcmsClassName);
                        if (getValue1.JobName == "ApplicationReplicationJob")
                        {
                            GetValue(groupJob, infraobject1, "InfraObject", "CronExpression", "InfraObjectJob", "ClassName", type, "ApplicationReplicationJob", getValue1.CronExpression, getValue1.InfraObjectId, getValue1.BcmsClassName);
                        }
                        else
                        {
                            GetValue(groupJob, infraobject1, "InfraObject", "CronExpression", "InfraObjectJob", "ClassName", type, "FastCopyOracleSyncJob", getValue1.CronExpression, getValue1.InfraObjectId, getValue1.BcmsClassName);
                        }
                        break;
                    case "infraObjectId,fastcopy":
                        var infraobject3 = InfraObjectDataAccess.GetInfraObjectById(groupJob.InfraObjectId);
                        //  var getValue2 = GroupJobInfoDataAccess.GetInfraObjectJobById(groupJob.InfraObjectId);
                        var getValue2 = GroupJobInfoDataAccess.TestGetInfraObjectJobById(groupJob.InfraObjectId, groupJob.BcmsClassName);
                        GetValue(groupJob, infraobject3, "InfraObject", "CronExpression", "InfraObjectJob", "ClassName", type, "ApplicationDeleteFilesJob", getValue2.CronExpression, getValue2.InfraObjectId, getValue2.BcmsClassName);
                        break;

                    case "":
                        if (groupJob.BcmsClassName == "Bcms.Core.Job.MonitorsServiesJob")
                        {
                            IList<MonitorService> monitorServices = MonitorServiceDataAccess.GetAllMonitorService();
                            var getValue3 = GroupJobInfoDataAccess.GetInfraObjectJobByJobId(groupJob.JobId);
                            foreach (var monitorService in monitorServices)
                            {
                                //   GetValue(groupJob, monitorService.ServicePath + monitorService.Id, "MonitorService", "CronExpression", "JobInfo", type, "MonitorsServiesJob", getValue3.CronExpression, getValue3.JobId);
                            }
                        }
                        else if (groupJob.BcmsClassName == "Bcms.Core.Job.GlobalMirrorJob")
                        {
                            IList<GlobalMirror> ActiveGlobalMirror = GlobalMirrorDataAccess.GetGlobalMirrorGroupByStorageImageId();
                            var getValue4 = GroupJobInfoDataAccess.GetInfraObjectJobByJobId(groupJob.JobId);
                            foreach (var activeGlobalMiror in ActiveGlobalMirror)
                            {
                                // GetValue(groupJob, activeGlobalMiror.PRStorageImageId, "globalmirror", "CronExpression", "JobInfo", type, "GlobalMirrorJob", getValue4.CronExpression, getValue4.JobId);
                            }
                        }

                        break;

                    default:
                        break;
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + groupJob.JobName + "). /r/n" + exc.InnerException);
                else
                    _logger.Error("Error Occurred while initialize oracle monitor component for InfraObject (" + groupJob.JobName + "). /r/n" + exc.Message);
            }
        }

        #endregion
        //Added by Karthick B for ITIT-10566
        #region WF Pause/Resume Jobs
        public void PrepareResumeWorkflow()
        {

            try
            {
                _logger.Info("Enter Prepare Resume Workflow method");

                var pgWorkflow = ParallelGroupWorkflowDataAccess.GetGroupWorkflowByReExcute(1);

                if (pgWorkflow.Count < 1)
                {
                    _logger.Info("RESUME : * pgworkflow is getting null");
                }
                else
                {

                    _logger.Info("RESUME : *******************GroupWorkflowByResume Count :*" + pgWorkflow.Count + "******************");

                    System.Threading.Tasks.Parallel.ForEach(pgWorkflow, x => InitializingParallelWorkflow(x));

                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error occurred while executing resume Parallel workflow /r/n" + exc.InnerException);
                else
                    _logger.Error("Error occurred while executing resume Parallel workflow /r/n" + exc.Message);
            }
        }



        public void TerminateJob()
        {
            _logger.Info("Terminate===Job");

            if (_parallelScheduler != null)
            {

                _logger.Info("Terminate===GetAllPause");

                IList<ParallelGroupWorkflow> lstGrpWF = ParallelGroupWorkflowDataAccess.GetAllPause();

                _logger.Info("Terminate===GetAllPauseCount====" + lstGrpWF.Count);

                if (lstGrpWF.Count > 0)
                {
                    System.Threading.Tasks.Parallel.ForEach(lstGrpWF, (x) => PauseWorkflowAction(x));
                }
            }
            else
            {
                _logger.Info("Current Thread ID :" + Convert.ToInt32(Thread.CurrentThread.ManagedThreadId));
                _logger.Error("_parallelScheduler1 object getting null, Unable to identify running Thread");

            }
        }



        private void PauseWorkflowAction(ParallelGroupWorkflow item)
        {
            int cnt = 0;
            int _cnt = 0;
            _logger.Info("PauseWorkflowAction is Started");
            try
            {
                if (!string.IsNullOrEmpty(item.ProgressStatus.ToString()))
                {
                    _logger.Info("Terminate===Job progress is completed: " + item.ProgressStatus.ToString());
                    var progressstatus = item.ProgressStatus.Split('/');

                    if (progressstatus[0].ToString() == progressstatus[1].ToString())
                    {

                        ParallelGroupWorkflowDataAccess.UpdatePauseStatusGrpId(item.Id, 0, 0);
                        ParallelGroupWorkflowDataAccess.UpdateStatusById(item.Id, "Completed");
                        _logger.Info("Terminate===Job progress is completed: " + item.Id);
                        return;
                    }
                    if (item.Status.ToString() == "Success" || item.Status.ToString() == "Retry")
                    {
                        _logger.Info("Terminate===Job Current Status is : " + item.Status);
                        return;
                    }
                }

                bool isJobNameFound = false;

                var runningJobs = _parallelScheduler.GetCurrentlyExecutingJobs();

                if (runningJobs.Count > 0)
                {
                    _logger.Info("Terminate===runningJobs: " + runningJobs.Count);

                    string jobName = item.WorkflowName + "_" + item.Id + "ParallelJob" + "_" + item.JobName;

                    string groupName = item.WorkflowName + "_" + item.Id + "ParallelJobGroup" + "_" + item.JobName;

                    _logger.Info("Terminate===Job jobName: " + jobName);

                    _logger.Info("Terminate===Job GroupName: " + groupName);

                    IList<ParallelWorkflowActionResult> lstActions = ParallelWorkflowActionResultDataAccess.GetCurrentDRGroupWorkflowActionsResult(item);
                    _logger.Info("Terminate===Job ParallelWorkflowActionResult count :" + lstActions.Count);
                    foreach (IJobExecutionContext executionContext in runningJobs)
                    {
                        var jobDetail = executionContext.JobDetail;

                        var jobKey = new JobKey(jobName, groupName);

                        if (jobKey.Equals((object)jobDetail.Key))
                        {
                            IList<ParallelWorkflowActionResult> lstRunnigActions = lstActions.Where(x => x.Status.Trim().Equals("Running")).ToList();

                            _logger.Info("Terminate===Job GetCurrentDRGroupWorkflowActionsResult count :" + lstActions.Count);
                            _logger.Info("Terminate===Job Running workflow action result count :" + lstRunnigActions.Count);

                            if (lstRunnigActions.Count > 0)
                            {
                                loop:
                                ParallelGroupWorkflowDataAccess.UpdatePauseStatusGrpId(item.Id, 2, 0);
                                _logger.Info("ISPause updated to 2");
                                if (Interrupt(executionContext))
                                {
                                    _logger.Info("Interrupt===Job context.JobDeta :" + executionContext.JobDetail.Key.Name);

                                    isJobNameFound = true;
                                    _loop:
                                    Thread.Sleep(1000);

                                    bool isDelete = _parallelScheduler.DeleteJob(new JobKey(jobName, groupName));

                                    if (isDelete)
                                    {
                                        _logger.Info("Successfully Delete Job name: " + jobName + " group Jobname :" + groupName);

                                        _logger.Info("Terminate===Parallel Scheduler Jobs Group Count : " + _parallelScheduler.GetJobGroupNames().Count);

                                        foreach (var item1 in _parallelScheduler.GetJobGroupNames())
                                        {
                                            _logger.Info("Terminate===Parallel Scheduler  Jobs Group Name : " + item1);
                                        }

                                        _logger.Info("Terminate===Parallel Scheduler Execution Jobs Count : " + _parallelScheduler.GetCurrentlyExecutingJobs().Count);

                                        foreach (IJobExecutionContext context in _parallelScheduler.GetCurrentlyExecutingJobs())
                                        {
                                            _logger.Info("Terminate===Parallel Scheduler Execution Jobs Name : " + context.JobDetail.Key.Name);
                                            _logger.Info(
                                                "Terminate===Parallel Scheduler Execution Jobs Name End Time : " +
                                                context.Scheduler.GetTriggerState(context.Trigger.Key));
                                        }


                                        _logger.Info(
                                            "ParallelScheduler JobGroupNames list count after delete thread: " +
                                            _parallelScheduler.GetCurrentlyExecutingJobs().Count);
                                        ParallelGroupWorkflowDataAccess
                                            .UpdatePauseStatusGrpId(item.Id, 3, 1); //Update the Pause Status as 3 for Success
                                        var value = UpdatePauseStatus(item);
                                        _logger.Info("ActionResult id is :*" + value.Id);
                                        _logger.Info("ActionResult currentActionId is:*" + value.ActionId);
                                        if (value.ActionId > 0)
                                        {
                                            item.CurrentActionId = value.ActionId;
                                            _logger.Info(" item.CurrentActionId  is:*" + item.CurrentActionId);
                                            ParallelGroupWorkflowDataAccess.UpdateByStatusAndMessage(item);

                                        }
                                    }
                                    else
                                    {
                                        _logger.Error("ERROR: Cannot Delete Job name: " + jobName + " group Jobname :" +
                                                      groupName);
                                        ParallelGroupWorkflowDataAccess
                                            .UpdatePauseStatusGrpId(item.Id, 4,
                                                0); //Update the Pause Status as 4 for Failed
                                        UpdatePauseStatus(item, "Cannot Pause the Workflow,Retry After Sometime");
                                        ParallelGroupWorkflowDataAccess.UpdateStatusById(item.Id, "Error");
                                        _logger.Error("Job:" + jobName + "Updated to Error");

                                        if (_cnt <= 3)   //Not Deleted For First Time , So trying it for 3 times...
                                        {
                                            Thread.Sleep(200);
                                            _logger.Info(" Error: ERROR: Cannot Delete Job name " + jobName + " group Jobname :" +
                                                    groupName + "  So Reattempting for :" + _cnt + " Time.");

                                            _cnt++;
                                            goto _loop;
                                        }

                                        return;
                                    }
                                }
                                else
                                {
                                    _logger.Error("ERROR: Cannot Interrupt Job name: " + jobName + " group Jobname :" +
                                                  groupName);

                                    ParallelGroupWorkflowDataAccess
                                        .UpdatePauseStatusGrpId(item.Id, 4,
                                            0); //Update the Pause Status as 4 for Failed
                                    UpdatePauseStatus(item, "Cannot Pause the Workflow,Retry After Sometime");

                                    ParallelGroupWorkflowDataAccess.UpdateStatusById(item.Id, "Error");

                                    if (cnt <= 3)      //Not Interrupted For First Time , So trying it for 3 times...
                                    {
                                        Thread.Sleep(200);
                                        _logger.Info(" Error : ERROR: Cannot Interrupt Job name: " + jobName + " group Jobname :" +
                                                groupName + "  So Reattempting for :" + cnt + " Time.");
                                        cnt++;
                                        goto loop;
                                    }


                                }
                            }
                            else
                            {
                                var lstNotRunningActions = lstActions.Where(x => x.Status.Trim().Equals("Error") || x.Status.Trim().Equals("Success")).LastOrDefault();

                                ParallelGroupWorkflowDataAccess
                                    .UpdatePauseStatusGrpId(item.Id, 0, 0); //Update the Pause Status as 3 for Success
                                _logger.Error("ERROR: Cannot Pause the workflow because no action is running state");
                                _logger.Info("Terminate===Job lstNotRunningActions count :" + lstNotRunningActions.Status.ToString());
                                ParallelGroupWorkflowDataAccess.UpdateStatusById(item.Id, lstNotRunningActions.Status);
                                return;
                            }
                        }
                    }

                    if (!isJobNameFound)
                    {
                        var lstNotRunningActions = lstActions.Where(x => x.Status.Trim().Equals("Error") || x.Status.Trim().Equals("Success")).LastOrDefault();

                        ParallelGroupWorkflowDataAccess
                            .UpdatePauseStatusGrpId(item.Id, 0, 0); //Update the Pause Status as 3 for Success
                        _logger.Error("ERROR: Cannot Pause the workflow because no action is running state");
                        _logger.Info("Terminate===Job lstNotRunningActions count :" + lstNotRunningActions.Status.ToString());
                        ParallelGroupWorkflowDataAccess.UpdateStatusById(item.Id, lstNotRunningActions.Status);

                        _logger.Error("Terminate===Job context.JobDetail Key Name Not Found :" + jobName);

                    }
                }
                else
                {
                    _logger.Error("Terminate===Job Running job count is empty");
                }
            }
            catch (Exception exc)
            {
                _logger.Error("Terminate===Job throws exception " + exc.Message);

            }

        }

        public virtual bool Interrupt(IJobExecutionContext executionContext)
        {
            bool interruptedAny = false;
            var interruptableJob = executionContext.JobInstance as Quartz.IInterruptableJob;
            if (interruptableJob != null)
            {
                _logger.Info("Interrupt===Job context.JobDeta :" + executionContext.JobDetail.Key.Name);
                interruptableJob.Interrupt();
                _logger.Info("Interrupt===Successfully context.JobDeta :" + executionContext.JobDetail.Key.Name);
                interruptedAny = true;
            }
            else
            {
                _logger.Info("Interrupt===Job context.JobDeta failed :" + executionContext.JobDetail.Key.Name);
                return interruptedAny;
            }
            return interruptedAny;
        }
        private ParallelWorkflowActionResult UpdatePauseStatus(ParallelGroupWorkflow item, string message)
        {
            ParallelWorkflowActionResult actionresult = new ParallelWorkflowActionResult();
            try
            {
                IList<ParallelWorkflowActionResult> lstActions = ParallelWorkflowActionResultDataAccess.GetCurrentDRGroupWorkflowActionsResult(item);

                IList<ParallelWorkflowActionResult> lstRunnigActions = lstActions.Where(x => x.Status.Trim().Equals("Running")).ToList();


                if (lstRunnigActions != null)
                {
                    foreach (var item1 in lstRunnigActions)
                    {
                        _logger.Info("Enter UpdateParallelWorkflowActionResultStatus starts for Pause");

                        _logger.Info("ParallelWorkflowActionResult Id:" + item1.Id);
                        _logger.Info("ParallelWorkflowActionResult ActionId:" + item1.ActionId);

                        actionresult.Id = item1.Id;
                        actionresult.StartTime = DateTime.Now;
                        actionresult.Message = message;
                        actionresult.EndTime = DateTime.Now;
                        actionresult.Status = "Error";
                        actionresult.Direction = "";
                        actionresult.ConditionActionId = 0;
                        actionresult.ActionId = item1.ActionId;
                        actionresult = ParallelWorkflowActionResultDataAccess.Update(actionresult);

                        _logger.Info("Enter UpdateParallelWorkflowActionResultStatus Ends for Pause");


                    }
                }
                else
                {
                    _logger.Info("No Records in Running Mode");
                }

            }
            catch (Exception exc)
            {
                _logger.Error("Exception Occured While  UpdatePauseStatus: " + exc.Message);
            }

            return actionresult;
        }

        private ParallelWorkflowActionResult UpdatePauseStatus(ParallelGroupWorkflow item)
        {
            return UpdatePauseStatus(item, "User Pause the Workflow Manually, so status changed Running to Error");

        }



        #endregion
        //Added by Karthick B for ITIT-10566
        #region Reschedule

        public void InitializeRescheduleMonitorServiceJob()
        {
            try
            {
                _logger.Info("Initialize Reschedule ");
                IJobDetail job = scheduler.GetJobDetail(new JobKey("InitializeRescheduleMonitorService", "InitializeRescheduleMonitorServiceGroup"));
                if (job == null)
                {
                    string cronExpression = "0 0 0/2 1/1 * ? *";

                    var monitorjobDetail1 = JobBuilder.Create<RescheduleMonitorService>().WithIdentity("InitializeRescheduleMonitorService", "InitializeRescheduleMonitorServiceGroup").Build();

                    var monitorTrigger1 = (ICronTrigger)TriggerBuilder.Create().WithIdentity("InitializeRescheduleMonitorServiceTrigger", "InitializeRescheduleMonitorServiceGroup").WithCronSchedule(cronExpression, x => x.WithMisfireHandlingInstructionFireAndProceed()).Build();

                    scheduler.ScheduleJob(monitorjobDetail1, monitorTrigger1);

                    _logger.Info("Initialize Reschedule  Completed.");
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    _logger.Error("Error Occurred while Reschedule ." + exc.InnerException);
                else
                    _logger.Error("Error Occurred while Reschedule ." + exc.Message);
            }
        }

        #endregion Reschedule 

    }

    class RescheduleMonitorService : BcmsCore, Quartz.IJob, Quartz.IInterruptableJob
    {
        private volatile Thread thisThread;
        public void Execute(IJobExecutionContext context)
        {
            _logger.Info("Executing Re-schedule NTNX Job.");
            thisThread = Thread.CurrentThread;

            #region Reshedule

            IList<InfraObject> ActiveInfraObjects_node = InfraObjectDataAccess.GetAllInfraObjects();

            if (ActiveInfraObjects_node != null && ActiveInfraObjects_node.Count >= 0)
                ActiveInfraObjects = ActiveInfraObjects_node;

            if (ActiveInfraObjects.Count > 0)
            {
                //string CronPD = ConfigurationManager.AppSettings["PDJobInterval"];
              

                foreach (var activeInfraObject in ActiveInfraObjects)
                {

                    if (activeInfraObject.ReplicationType == 2)
                    {
                        _logger.Info("Reschdule odg Job"+DateTime.Now);
                        // InitializeRescheduleMonitorNutanixJob(activeInfraObject, "0 0/15 * * * ?", "MonitorNutanixJob");
                        // InitializeRescheduleMonitorNutanixJob(activeInfraObject, CronPD, "MonitorNutanixJob");


                        InitializeRescheduleOracleMonitorComponents(activeInfraObject);
                        InitializRescheduleArchivedLogDeleteJob(activeInfraObject);
                        InitializeRescheduleMonitorReplicationLog(activeInfraObject);
                    }

                    if (activeInfraObject.ReplicationType == 28)
                    {
                        _logger.Info("Reschdule mssql Job" + DateTime.Now);
                        InitializeRescheduleMonitorMSSqlNative2008(activeInfraObject);
                        //  InitializeRescheduleMonitorLeapNutanixJob(activeInfraObject, "0 0/15 * * * ?", "MonitorLeapNutanixJob");
                        //InitializeRescheduleMonitorLeapNutanixJob(activeInfraObject, CronLeap, "MonitorLeapNutanixJob");
                    }

                }
            }

            #endregion Reshedulentnx

        }

        void Quartz.IInterruptableJob.Interrupt()
        {
            if (thisThread != null)
            {
                thisThread.Interrupt();
            }
        }
       
    }   
}