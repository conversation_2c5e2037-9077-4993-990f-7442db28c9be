﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace BCMS.Common
{
    public class ExchangeDAGServiceMonitoring : Bcms.Common.Base.BaseEntity
    {
        public ExchangeDAGServiceMonitoring() : base()
        {

        }

        #region Properties

        [DataMember]
        public int Id
        {
            get;
            set;
        }

        [DataMember]
        public int GroupId
        {
            get;
            set;
        }
        [DataMember]
        public string ActiveDirectoryTopology_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ActiveDirectoryTopology_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string ActiveDirectoryTopology_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ActiveDirectoryTopology_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string AddressBook_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AddressBook_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string AddressBook_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AddressBook_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string AntispamUpdate_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AntispamUpdate_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string AntispamUpdate_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AntispamUpdate_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string EdgeSync_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string EdgeSync_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string EdgeSync_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string EdgeSync_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string FileDistribution_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string FileDistribution_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string FileDistribution_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string FileDistribution_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string AuthenticationService_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AuthenticationService_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string AuthenticationService_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string AuthenticationService_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string IMAP4_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string IMAP4_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string IMAP4_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string IMAP4_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string InformationStore_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string InformationStore_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string InformationStore_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string InformationStore_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string MailSubmission_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailSubmission_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string MailSubmission_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailSubmission_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string MailboxAssistants_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxAssistants_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxAssistants_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxAssistants_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string MailboxReplication_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxReplication_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxReplication_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string MailboxReplication_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string Monitoring_PR_Mode
        {
            get;
            set;
        }

        [DataMember]
        public string Monitoring_PR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string Monitoring_DR_Mode
        {
            get;
            set;
        }

        [DataMember]
        public string Monitoring_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string POP3_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string POP3_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string POP3_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string POP3_DR_Status
        {
            get;
            set;
        }



        [DataMember]
        public string ProtectedServiceHost_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ProtectedServiceHost_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string ProtectedServiceHost_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ProtectedServiceHost_DR_Status
        {
            get;
            set;
        }


        [DataMember]
        public string Replication_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Replication_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string Replication_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Replication_DR_Status
        {
            get;
            set;
        }



        [DataMember]
        public string RPCClientAccess_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string RPCClientAccess_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string RPCClientAccess_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string RPCClientAccess_DR_Status
        {
            get;
            set;
        }


        [DataMember]
        public string SearchIndexer_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string SearchIndexer_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string SearchIndexer_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string SearchIndexer_DR_Status
        {
            get;
            set;
        }



        [DataMember]
        public string WindowsServerBackup_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsServerBackup_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsServerBackup_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsServerBackup_DR_Status
        {
            get;
            set;
        }


        [DataMember]
        public string ServiceHost_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ServiceHost_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string ServiceHost_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ServiceHost_DR_Status
        {
            get;
            set;
        }


        [DataMember]
        public string SystemAttendant_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string SystemAttendant_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string SystemAttendant_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string SystemAttendant_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string Throttling_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Throttling_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string Throttling_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Throttling_DR_Status
        {
            get;
            set;
        }


        [DataMember]
        public string Transport_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Transport_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string Transport_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string Transport_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string TransportLogSearch_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string TransportLogSearch_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string TransportLogSearch_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string TransportLogSearch_DR_Status
        {
            get;
            set;
        }

        [DataMember]
        public string ClusterService_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ClusterService_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string ClusterService_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string ClusterService_DR_Status
        {
            get;
            set;
        }
        
        [DataMember]
        public string WindowsFirewall_PR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsFirewall_PR_Status
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsFirewall_DR_Mode
        {
            get;
            set;
        }
        [DataMember]
        public string WindowsFirewall_DR_Status
        {
            get;
            set;
        }

        #endregion Properties
    }
}
