﻿namespace Bcms.Replication.Shared
{
    public sealed partial class Constants
    {
        public static class QueryConstants
        {
            public const string LicenseKey = "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";

            public const string GlobalName = ". oraenv";

            public const string SudoUser = "sudo su - ";

            public const string ExportOracle = "export ORACLE_SID=";

            public const string ConnectDbaSingle = "sqlplus '/as sysdba'";

            public const string SqlPlusNoLog = "sqlplus /nolog";

            public const string ConnectSysDba = "connect sys as sysdba";

            public const string ConnectDba = "connect /as sysdba";

            public const string ExpectPassword = "change_on_install";

            public const string DatabaseModeAndRole = "select name, open_mode, database_role from v$database;";

            public const string WindowsDatabaseModeAndRole = "select name, open_mode, database_role from v$database";

            public const string ActiveUserCount = "select count(*) from  v$session where status='ACTIVE' and username NOT IN('SYS','SYSTEM');";

            public const string WindowsActiveUserCount = "select count(*) from  v$session where status='ACTIVE' and username NOT IN('SYS','SYSTEM')";

            public const string LogCount = "select count(*) from v$logfile";

            public const string AlterLogfiles = "alter system switch logfile;";

            public const string ProcessCount = "select name, value from v$parameter where name='job_queue_processes';";

            public const string WindowsProcessCount = "select name, value from v$parameter where name='job_queue_processes'";

            public const string KillProcess = "alter system set Job_queue_processes=0;";

            public const string WindowsKillProcess = "alter system set Job_queue_processes=0";

            public const string MaxSequenceNo = "select max(sequence#) maxseq from v$log_history;";

            public const string WindowsMaxSequenceNo = "select max(sequence#) maxseq from v$log_history";

            public const string SwitchToStandbyMode = "ALTER DATABASE COMMIT TO SWITCHOVER TO PHYSICAL STANDBY WITH SESSION SHUTDOWN;";

            public const string WindowsSwitchToStandbyMode = "ALTER DATABASE COMMIT TO SWITCHOVER TO PHYSICAL STANDBY WITH SESSION SHUTDOWN";

            public const string SwitchToPrimaryMode = "ALTER DATABASE COMMIT TO SWITCHOVER TO PRIMARY;";

            //added by jeyapandi

            public const string CancelRecoverMode = "alter database recover managed standby database finish force;";

            //end 

            public const string WindowsSwitchToPrimaryMode = "ALTER DATABASE COMMIT TO SWITCHOVER TO PRIMARY";

            public const string StartPrimaryDatabase = "ALTER DATABASE OPEN;";

            public const string WindowsStartPrimaryDatabase = "ALTER DATABASE OPEN";

            public const string MountStandby = "STARTUP MOUNT;";

            public const string WindowsStartupNoMount = "STARTUP NOMOUNT;";

            public const string WindowsMountStandby = "ALTER DATABASE MOUNT";

            public const string ShutdownPrimary = "SHUTDOWN IMMEDIATE;";

            public const string WindowsShutdownPrimary = "SHUTDOWN IMMEDIATE";

            public const string PrimarySwitchtoLogfile = "ALTER SYSTEM SWITCH LOGFILE;";

            public const string WindowsPrimarySwitchtoLogfile = "ALTER SYSTEM SWITCH LOGFILE";

            public const string StandbyRecovery = "ALTER DATABASE RECOVER MANAGED STANDBY DATABASE DISCONNECT FROM SESSION;";

            public const string WindowsStandbyRecovery = "ALTER DATABASE RECOVER MANAGED STANDBY DATABASE DISCONNECT FROM SESSION";

            public const string PRDataGuardStatus = "select status, error from v$archive_dest where dest_id = 2;";

            public const string WindowsPRDataGuardStatus = "select status, error from v$archive_dest where dest_id = 2";

            public const string DRDataGuardStatus ="select client_process, process, sequence#, status from v$managed_standby;";

            public const string WindowsDRDataGuardStatus = "select client_process, process, sequence#, status from v$managed_standby";

            public const string DataGuardInRecovery = "select recovery_mode from v$archive_dest_status;";

            public const string WinDataGuardInRecovery = "select recovery_mode from v$archive_dest_status";

            public const string VerifyArchiveGap = "select * from v$archive_gap;";

            public const string WinVerifyArchiveGap = "select * from v$archive_gap";

            public const string ChangeFastCopyDirectory = "cd FastCopy";

            public const string StartFastCopyReplication = "java -jar DataSync.jar";

            public const string FastCopyExe = "fastcopy.exe";

            public const string DataFileCount = "select count(*) from v$datafile;";

            public const string DataFileError = "select count(1),error from v$recover_file group by error;";

            public const string DataFileInRecoveryMode = "select file#, unrecoverable_change# from v$datafile;";

            public const string DataFileInBackupMode = "select STATUS from v$backup group by STATUS;";

            public const string DataFileStatus = "select distinct status,count(1) from v$datafile group by status;";

            public const string DataFileInReadMode = "select distinct enabled from v$datafile group by enabled;";

            public const string DataFileInCorrupted = "select substr(name,1,60), recover, fuzzy, checkpoint_change# from v$datafile_header where fuzzy='YES' and recover<>'NO';";

            public const string CheckPointChange = "select distinct(CHECKPOINT_CHANGE#) from  v$datafile_header;";

            public const string CurrentSCNNumber = "select to_char(CURRENT_SCN) from v$database;";

            public const string NoLoggingOperation = "select file#, first_nonlogged_scn from v$datafile where first_nonlogged_scn > 0;";

            public const string CheckForceLogging = "select force_logging from v$database;";

            public const string WinDRLatestLogSeqno ="select MAX(SEQUENCE#) maxseq from gv$log_history where (select min(distinct checkpoint_change#) from gv$datafile_header) between first_change# and next_change#";

            public const string WinDRLogLatestSeqInPrimary ="select name,sequence#,to_char(COMPLETION_TIME,'mm-dd-yyyy hh24:mi:ss'),to_char(first_Change#)  from gv$archived_log where sequence#=";

            public const string WinPRLatestLogSeq ="select name,sequence#,to_char(COMPLETION_TIME,'mm-dd-yyyy hh24:mi:ss'),to_char(first_Change#) from gv$archived_log where sequence#=(select MAX(SEQUENCE#) from gv$log_history)";
            
            public const string GreaterThan = ">";

            public const string Dolar = "$";

            public const string Hash = "#";

            public const string QuestionMark = "?";

            // 1.	log_archive_dest_n=’defer’ value discovery query

            public const string discoveryqueryondr = "select db_unique_name from v$database;";

            public const string discoveryqueryonprdeffer = "select ' alter system set log_archive_dest_state_'||DEST_ID||'=DEFER;' from v$archive_dest where DB_UNIQUE_Name=";

            public const string discoveryqueryonprenable = "select ' alter system set log_archive_dest_state_'||DEST_ID||'=ENABLE;' from v$archive_dest where DB_UNIQUE_Name=";
        
        }

    }
}