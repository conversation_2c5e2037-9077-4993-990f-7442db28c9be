﻿using System;
using System.Collections.Generic;
using Bcms.Common;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using Bcms.DataAccess.Base;
using Bcms.ExceptionHandler;

namespace Bcms.DataAccess
{
    public class ExchageSCRServiceStatusDataAccess : BaseDataAccess
    {
      public static ExchangeService Update(ExchangeService exchange)
      {
          const string SP = "ExchnageService_Update";
         

          using (var dbCommand = Database.GetStoredProcCommand(SP))
          {

              //db.AddOutParameter(cmd, "@ReturnCode", DbType.Int32, 4);
              Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, exchange.InfraObjectId);
              Database.AddInParameter(dbCommand, Dbstring+"iServiceName", DbType.AnsiString, exchange.ServiceName);
              Database.AddInParameter(dbCommand, Dbstring+"iPRServiceStatus", DbType.AnsiString, exchange.PRServiceStatus);
              Database.AddInParameter(dbCommand, Dbstring+"iPRServiceStartMode", DbType.AnsiString, exchange.PRServiceStartMode);
              Database.AddInParameter(dbCommand, Dbstring+"iDRServiceStatus", DbType.AnsiString, exchange.DRServiceStatus);
              Database.AddInParameter(dbCommand, Dbstring+"iDRServiceStartMode", DbType.AnsiString, exchange.DRServiceStartMode);
              try
              {

                  using (IDataReader myDROperationResultReader = Database.ExecuteReader(dbCommand))
                  {
                      if (myDROperationResultReader.Read())
                      {
                          exchange.ServiceId = Convert.ToInt32(myDROperationResultReader[0]);

                      }
                      else
                      {
                          exchange = null;
                      }
                  }

                  return exchange;


              }
              catch
              {
                  return exchange;
              }
          }
      }

      public static ExchangeService Add(ExchangeService exchange)
      {
          const string SP = "ExchangeServiceLog_Create";
         

          using (var dbCommand = Database.GetStoredProcCommand(SP))
          {

              //db.AddOutParameter(cmd, "@ReturnCode", DbType.Int32, 4);
              Database.AddInParameter(dbCommand, Dbstring+"iInfraObjectId", DbType.Int32, exchange.InfraObjectId);
              Database.AddInParameter(dbCommand, Dbstring+"iServiceName", DbType.AnsiString, exchange.ServiceName);
              Database.AddInParameter(dbCommand, Dbstring+"iPRServiceStatus", DbType.AnsiString, exchange.PRServiceStatus);
              Database.AddInParameter(dbCommand, Dbstring+"iPRServiceStartMode", DbType.AnsiString, exchange.PRServiceStartMode);
              Database.AddInParameter(dbCommand, Dbstring+"iDRServiceStatus", DbType.AnsiString, exchange.DRServiceStatus);
              Database.AddInParameter(dbCommand, Dbstring+"iDRServiceStartMode", DbType.AnsiString, exchange.DRServiceStartMode);
              try
              {

                  using (IDataReader myDROperationResultReader = Database.ExecuteReader(dbCommand))
                  {
                      if (myDROperationResultReader.Read())
                      {
                          exchange.ServiceId = Convert.ToInt32(myDROperationResultReader[0]);

                      }
                      else
                      {
                          exchange = null;
                      }
                  }

                  return exchange;


              }
              catch(Exception ex)
              {
                  var txt= ex.ToString();
              }
          }
          return exchange;
      }
    }
}
