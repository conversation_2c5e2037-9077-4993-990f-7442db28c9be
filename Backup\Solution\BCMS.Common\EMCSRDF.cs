﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;


namespace BCMS.Common
{
    [Serializable]
    [DataContract(Name = "EmcSrdf", Namespace = "http://www.BCMS.com/types")]
   public class EMCSRDF: BaseEntity
    {
      



        #region Properties

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

       [DataMember]
        public int ServerId
        {
            get; 
            set;
        }

        [DataMember]
        public string DGroupName
        {
            get;
            set;
        }

        [DataMember]
        public string DGType
        {
            get;
            set;
        }

        [DataMember]
        public string DGSummetrixID
        {
            get;
            set;
        }

        [DataMember]
        public string RemoteSymmetrixID
        {
            get;
            set;
        }

        [DataMember]
        public string RdfRaGroupNumber
        {
            get;
            set;
        }


        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public string PendingTracks
        {
            get;
            set;
        }

        [DataMember]
        public string DataLag
        {
            get;
            set;
        }

      


        #endregion

        #region Constructor

        public EMCSRDF()
            : base()
        { }

        #endregion
    }
}
