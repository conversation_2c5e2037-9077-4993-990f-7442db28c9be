﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;

namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Group", Namespace = "http://www.Bcms.com/types")]
    public class Group : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessServiceId
        {
            get;
            set;
        }

        [DataMember]
        public int BusinessFunctionId
        {
            get;
            set;
        }

        [DataMember]
        public bool DRReady
        {
            get;
            set;
        }

        [DataMember]
        public int PRServerId
        {
            get;
            set;
        }

        [DataMember]
        public int DRServerId
        {
            get;
            set;
        }

        [DataMember]
        public int PRDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int DRDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int PRReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int DRReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public int Type
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationType
        {
            get;
            set;
        }

        [DataMember]
        public DateTime RecoveryTime
        {
            get;
            set;
        }

        [DataMember]
        public string DataLag
        {
            get;
            set;
        }

        [DataMember]
        public string ConfigureDataLag
        {
            get;
            set;
        }

        [DataMember]
        public int MTPOD
        {
            get;
            set;
        }

        [DataMember]
        public string State
        {
            get;
            set;
        }

        [DataMember]
        public string PairGroupId
        {
            get;
            set;
        }

        [DataMember]
        public int IsReplication
        {
            get;
            set;
        }

        [DataMember]
        public int DROperationId
        {
            get;
            set;
        }

        [DataMember]
        public int IsWorkflow
        {
            get;
            set;
        }

        [DataMember]
        public int Health
        {
            get;
            set;
        }

        [DataMember]
        public int SiteSolutionTypeId
        {
            get;
            set;
        }

        [DataMember]
        public int NearGroupId
        {
            get;
            set;
        }

        #endregion Properties
    }
}