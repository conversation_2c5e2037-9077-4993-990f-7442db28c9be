﻿using System;
using System.Runtime.Serialization;
using Bcms.Common.Base;
namespace Bcms.Common
{
    [Serializable]
    [DataContract(Name = "Event", Namespace = "http://www.Bcms.com/types")]
    public class Event : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int EventId
        {
            get;
            set;
        }

        [DataMember]
        public string Events
        {
            get;
            set;
        }


        [DataMember]
        public int WorkFlowId
        {
            get;
            set;
        }
        #endregion Properties
    }
}
